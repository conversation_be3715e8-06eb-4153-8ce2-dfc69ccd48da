<?php

declare(strict_types=1);

namespace App\Tests\Mocks;

use Exception;
use Superbrave\AnamnesisServiceClient\ClientInterface;
use Superbrave\AnamnesisServiceClient\Model\Request\CreateQuestionnaireSessionThroughImport;
use Superbrave\AnamnesisServiceClient\Model\Request\UpdateQuestionnaireSession;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Contracts\HttpClient\ResponseInterface;

class AnamnesisServiceClient implements ClientInterface
{
    public const string EXISTING_UUID = '69e5a697-00fb-4087-bf9e-880a6bcf58e1';
    public const string FINISHED_UUID = '69e5a697-00fb-4087-bf9e-880a6bcf58e2';
    public const string EXCEPTION_UUID = '69e5a697-00fb-4087-bf9e-880a6bcf58e3';
    public const string VALID_UUID = '69e5a697-00fb-4087-bf9e-880a6bcf58e4';

    public static bool $expectedResult;

    public function questionnaireExists(string $uuid): bool
    {
        if (isset(self::$expectedResult)) {
            return self::$expectedResult;
        }

        return $uuid === self::EXISTING_UUID;
    }

    public function questionnaireValidate(string $uuid): bool
    {
        if (isset(self::$expectedResult)) {
            return self::$expectedResult;
        }

        return $uuid === self::VALID_UUID;
    }

    public function questionnaireFinished(string $uuid): bool
    {
        if (isset(self::$expectedResult)) {
            return self::$expectedResult;
        }

        return $uuid === self::FINISHED_UUID;
    }

    public function finishQuestionnaireSession(string $uuid): bool
    {
        if (isset(self::$expectedResult)) {
            return self::$expectedResult;
        }

        if ($uuid === self::EXCEPTION_UUID) {
            throw new Exception('Something went wrong during api call.');
        }

        return $uuid === self::EXISTING_UUID;
    }

    public function getQuestionnaireSession(string $uuid): array
    {
        if ($uuid === self::FINISHED_UUID) {
            return json_decode(
                '{
                "uuid": "69e5a697-00fb-4087-bf9e-880a6bcf58e2",
                "localeCode": "nl",
                "medicalConditionCodes": [
                    "3922"
                ],
                "productCodes": [
                    "439"
                ],
                "questionnaire": [
                    {
                        "id": 6,
                        "sectionType": "generalHealth",
                        "type": "short-text",
                        "text": "Waarom, en voor welke diagnose wenst u een behandeling?",
                        "description": "",
                        "tooltip": "",
                        "choices": [],
                        "response": {
                            "text": "Lorizzle ipsum stuff sizzle nizzle, consectetuer break it down elit. Ghetto sapizzle yo, daahng dawg volutpat, suscipit quis, gravida boom shackalack, arcu. Pellentesque gizzle tortizzle. Sed erizzle. Stuff izzle dolizzle phat dawg tempizzle bling bling. Maurizzle nizzle my shizz get down get down turpizzle. I’m in the shizzle izzle yo. Pellentesque for sure rhoncus nisi. In hac shizzlin dizzle platea dictumst. Cool dapibizzle. Curabitur dizzle dope, pretizzle eu, mattizzle ac, eleifend vitae, nunc. Fo shizzle suscipit. Integizzle crazy fo shizzle sed purus."
                        }
                    },
                    {
                        "id": 7,
                        "sectionType": "generalHealth",
                        "type": "polar",
                        "text": "Is deze diagnose gesteld door een arts, en heeft deze arts een behandeling aangeraden?",
                        "description": "",
                        "tooltip": "",
                        "choices": [
                            {
                                "id": 92,
                                "text": "Ja",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 102,
                                "text": "Nee",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            }
                        ],
                        "response": {
                            "choiceId": 92,
                            "additionalResponse": "Etiam a magna dope augue hendrerit accumsizzle"
                        }
                    },
                    {
                        "id": 11,
                        "sectionType": "generalHealth",
                        "type": "single-choice",
                        "text": "Beoordeelt u zelf uw lichamelijke conditie op dit moment als:\nUitstekend /Goed/Matig/Slecht",
                        "description": "",
                        "tooltip": "",
                        "choices": [
                            {
                                "id": 155,
                                "text": "Uitstekend",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 156,
                                "text": "Goed",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 157,
                                "text": "Matig",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 158,
                                "text": "Slecht",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            }
                        ],
                        "response": {
                            "choiceId": 155,
                            "additionalResponse": "Etiam a magna dope augue hendrerit accumsizzle"
                        }
                    },
                    {
                        "id": 1,
                        "sectionType": "generalHealth",
                        "type": "multiple-choice",
                        "text": "Zijn er medische zaken uit onderstaande lijst op u van toepassing?",
                        "description": "",
                        "tooltip": "",
                        "choices": [
                            {
                                "id": 214,
                                "text": "in verwachting, plant een zwangerschap of geeft borstvoeding.",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 224,
                                "text": "recent een zware operatie ondergaan.\nZo ja: waarvoor en wanneer?",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 1,
                                "wrongAnswer": false
                            },
                            {
                                "id": 234,
                                "text": "hartritmestoornissen of een andere hartaandoening.",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 244,
                                "text": "TIA of CVA (hart- of herseninfarct) gehad.",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 254,
                                "text": "diabetes (suikerziekte).",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 264,
                                "text": "aandoening aan de lever, nieren of galblaas.",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 274,
                                "text": "astma, bronchitis, COPD, of longemfyseem.",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 284,
                                "text": "maag- of darmziekte (b.v. Crohn of colitis ulcerosa).",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 294,
                                "text": "te hoog cholesterol of triglyceride gehalte in uw bloed.",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 304,
                                "text": "schildklierafwijking.",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 314,
                                "text": "suïcidale neigingen, of psychische problemen.\nZo ja: kunt u een omschrijving geven?",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 1,
                                "wrongAnswer": false
                            },
                            {
                                "id": 324,
                                "text": "een andere ziekte, aandoening, handicap of lichamelijke afwijking of heeft u de arts nog iets mede te delen?\nZo ja: a.u.b. specificeren.",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 1,
                                "wrongAnswer": false
                            },
                            {
                                "id": 334,
                                "text": "een infectie*, of symptomen** die op een infectie wijzen.\nZo ja: vermeld dan alle symptomen en de plaats van de infectie",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 1,
                                "wrongAnswer": false
                            },
                            {
                                "id": 344,
                                "text": "geen van de medische klachten uit deze lijst is op mij van toepassing.",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            }
                        ],
                        "response": {
                            "choices": [
                                {
                                    "choiceId": 214,
                                    "additionalResponse": "Etiam a magna dope augue hendrerit accumsizzle"
                                }
                            ]
                        }
                    },
                    {
                        "id": 12,
                        "sectionType": "generalHealth",
                        "type": "single-choice",
                        "text": "Is uw bloeddruk:",
                        "description": "",
                        "tooltip": "",
                        "choices": [
                            {
                                "id": 352,
                                "text": "Hoog: > 150/100",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 353,
                                "text": "Normaal: 90/60 tot 150/100",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 354,
                                "text": "Laag: < 90/60",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            }
                        ],
                        "response": {
                            "choiceId": 352,
                            "additionalResponse": "Etiam a magna dope augue hendrerit accumsizzle"
                        }
                    },
                    {
                        "id": 14,
                        "sectionType": "generalHealth",
                        "type": "polar",
                        "text": "Gebruikt u op dit moment andere medicijnen? (zoals bijv. antibiotica of de pil)\nZo ja, a.u.b. specificeren, wees zo zorgvuldig en compleet mogelijk:",
                        "description": "",
                        "tooltip": "",
                        "choices": [
                            {
                                "id": 411,
                                "text": "Ja",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 1,
                                "wrongAnswer": false
                            },
                            {
                                "id": 421,
                                "text": "Nee",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            }
                        ],
                        "response": {
                            "choiceId": 411,
                            "additionalResponse": "Etiam a magna dope augue hendrerit accumsizzle"
                        }
                    },
                    {
                        "id": 15,
                        "sectionType": "generalHealth",
                        "type": "polar",
                        "text": "Heeft u  problemen ondervonden bij het gebruik van medicijnen, zoals een allergische reactie, ernstige bijwerkingen of afhankelijkheid?                                             \nZo ja, welk probleem, wanneer en met welk middel?",
                        "description": "",
                        "tooltip": "",
                        "choices": [
                            {
                                "id": 433,
                                "text": "Ja",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 1,
                                "wrongAnswer": false
                            },
                            {
                                "id": 443,
                                "text": "Nee",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            }
                        ],
                        "response": {
                            "choiceId": 433,
                            "additionalResponse": "Etiam a magna dope augue hendrerit accumsizzle"
                        }
                    },
                    {
                        "id": 86,
                        "sectionType": "generalHealth",
                        "type": "single-choice",
                        "text": "Bent u ingeschreven bij een huisarts en geeft u de voorschrijvende arts toestemming om contact op te nemen met uw huisarts en/of (medische) gegevens te delen met uw huisarts met betrekking tot voorgeschreven behandelingen, indien nodig?",
                        "description": "",
                        "tooltip": "",
                        "choices": [
                            {
                                "id": 1838,
                                "text": "Ja, ik heb een huisarts en geef toestemming.",
                                "additionalInformationText": "Gelieve de contactgegevens en het adres van uw huisarts in te vullen.",
                                "choiceRequiresAdditionalInformation": 1,
                                "wrongAnswer": false
                            },
                            {
                                "id": 1839,
                                "text": "Ja, ik heb een huisarts en ik informeer mijn huisarts zelf over mijn aandoening en de behandeling daarvan.",
                                "additionalInformationText": "",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 1840,
                                "text": "Ja, ik heb een huisarts, maar ik informeer mijn huisarts niet zelf over mijn aandoening en de behandeling daarvan.",
                                "additionalInformationText": "We willen u er graag op wijzen dat de voorschrijvende arts contact met u kan opnemen als de arts meer informatie nodig heeft voordat hij/zij uw recept kan uitschrijven. De arts kan besluiten geen recept uit te schrijven als het naar zijn/haar mening noodzakelijk is om eerst te overleggen met uw huisarts.",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            },
                            {
                                "id": 1841,
                                "text": "Nee, ik heb geen huisarts.",
                                "additionalInformationText": "We willen u er graag op wijzen dat de voorschrijvende arts contact met u kan opnemen als de arts meer informatie nodig heeft voordat hij/zij uw recept kan uitschrijven. De arts kan besluiten geen recept uit te schrijven als het naar zijn/haar mening noodzakelijk is om eerst te overleggen met uw huisarts.",
                                "choiceRequiresAdditionalInformation": 0,
                                "wrongAnswer": false
                            }
                        ],
                        "response": {
                            "choiceId": 1838,
                            "additionalResponse": "Etiam a magna dope augue hendrerit accumsizzle"
                        }
                    }
                ]
            }',
                true,
                512,
                JSON_THROW_ON_ERROR
            );
        }

        return json_decode(
            '{
            "uuid": "69e5a697-00fb-4087-bf9e-880a6bcf58e1",
            "localeCode": "nl",
            "medicalConditionCodes": [
                "3922"
            ],
            "productCodes": [
                "439"
            ],
            "questionnaire": [
                {
                    "id": 6,
                    "sectionType": "generalHealth",
                    "type": "short-text",
                    "text": "Waarom, en voor welke diagnose wenst u een behandeling?",
                    "description": "",
                    "tooltip": "",
                    "choices": []
                },
                {
                    "id": 7,
                    "sectionType": "generalHealth",
                    "type": "polar",
                    "text": "Is deze diagnose gesteld door een arts, en heeft deze arts een behandeling aangeraden?",
                    "description": "",
                    "tooltip": "",
                    "choices": [
                        {
                            "id": 92,
                            "text": "Ja",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 102,
                            "text": "Nee",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        }
                    ]
                },
                {
                    "id": 11,
                    "sectionType": "generalHealth",
                    "type": "single-choice",
                    "text": "Beoordeelt u zelf uw lichamelijke conditie op dit moment als:\nUitstekend /Goed/Matig/Slecht",
                    "description": "",
                    "tooltip": "",
                    "choices": [
                        {
                            "id": 155,
                            "text": "Uitstekend",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 156,
                            "text": "Goed",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 157,
                            "text": "Matig",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 158,
                            "text": "Slecht",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        }
                    ]
                },
                {
                    "id": 1,
                    "sectionType": "generalHealth",
                    "type": "multiple-choice",
                    "text": "Zijn er medische zaken uit onderstaande lijst op u van toepassing?",
                    "description": "",
                    "tooltip": "",
                    "choices": [
                        {
                            "id": 214,
                            "text": "in verwachting, plant een zwangerschap of geeft borstvoeding.",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 224,
                            "text": "recent een zware operatie ondergaan.\nZo ja: waarvoor en wanneer?",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 1,
                            "wrongAnswer": false
                        },
                        {
                            "id": 234,
                            "text": "hartritmestoornissen of een andere hartaandoening.",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 244,
                            "text": "TIA of CVA (hart- of herseninfarct) gehad.",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 254,
                            "text": "diabetes (suikerziekte).",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 264,
                            "text": "aandoening aan de lever, nieren of galblaas.",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 274,
                            "text": "astma, bronchitis, COPD, of longemfyseem.",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 284,
                            "text": "maag- of darmziekte (b.v. Crohn of colitis ulcerosa).",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 294,
                            "text": "te hoog cholesterol of triglyceride gehalte in uw bloed.",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 304,
                            "text": "schildklierafwijking.",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 314,
                            "text": "suïcidale neigingen, of psychische problemen.\nZo ja: kunt u een omschrijving geven?",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 1,
                            "wrongAnswer": false
                        },
                        {
                            "id": 324,
                            "text": "een andere ziekte, aandoening, handicap of lichamelijke afwijking of heeft u de arts nog iets mede te delen?\nZo ja: a.u.b. specificeren.",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 1,
                            "wrongAnswer": false
                        },
                        {
                            "id": 334,
                            "text": "een infectie*, of symptomen** die op een infectie wijzen.\nZo ja: vermeld dan alle symptomen en de plaats van de infectie",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 1,
                            "wrongAnswer": false
                        },
                        {
                            "id": 344,
                            "text": "geen van de medische klachten uit deze lijst is op mij van toepassing.",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        }
                    ]
                },
                {
                    "id": 12,
                    "sectionType": "generalHealth",
                    "type": "single-choice",
                    "text": "Is uw bloeddruk:",
                    "description": "",
                    "tooltip": "",
                    "choices": [
                        {
                            "id": 352,
                            "text": "Hoog: > 150/100",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 353,
                            "text": "Normaal: 90/60 tot 150/100",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 354,
                            "text": "Laag: < 90/60",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        }
                    ]
                },
                {
                    "id": 14,
                    "sectionType": "generalHealth",
                    "type": "polar",
                    "text": "Gebruikt u op dit moment andere medicijnen? (zoals bijv. antibiotica of de pil)\nZo ja, a.u.b. specificeren, wees zo zorgvuldig en compleet mogelijk:",
                    "description": "",
                    "tooltip": "",
                    "choices": [
                        {
                            "id": 411,
                            "text": "Ja",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 1,
                            "wrongAnswer": false
                        },
                        {
                            "id": 421,
                            "text": "Nee",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        }
                    ]
                },
                {
                    "id": 15,
                    "sectionType": "generalHealth",
                    "type": "polar",
                    "text": "Heeft u  problemen ondervonden bij het gebruik van medicijnen, zoals een allergische reactie, ernstige bijwerkingen of afhankelijkheid?                                             \nZo ja, welk probleem, wanneer en met welk middel?",
                    "description": "",
                    "tooltip": "",
                    "choices": [
                        {
                            "id": 433,
                            "text": "Ja",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 1,
                            "wrongAnswer": false
                        },
                        {
                            "id": 443,
                            "text": "Nee",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        }
                    ]
                },
                {
                    "id": 86,
                    "sectionType": "generalHealth",
                    "type": "single-choice",
                    "text": "Bent u ingeschreven bij een huisarts en geeft u de voorschrijvende arts toestemming om contact op te nemen met uw huisarts en/of (medische) gegevens te delen met uw huisarts met betrekking tot voorgeschreven behandelingen, indien nodig?",
                    "description": "",
                    "tooltip": "",
                    "choices": [
                        {
                            "id": 1838,
                            "text": "Ja, ik heb een huisarts en geef toestemming.",
                            "additionalInformationText": "Gelieve de contactgegevens en het adres van uw huisarts in te vullen.",
                            "choiceRequiresAdditionalInformation": 1,
                            "wrongAnswer": false
                        },
                        {
                            "id": 1839,
                            "text": "Ja, ik heb een huisarts en ik informeer mijn huisarts zelf over mijn aandoening en de behandeling daarvan.",
                            "additionalInformationText": "",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 1840,
                            "text": "Ja, ik heb een huisarts, maar ik informeer mijn huisarts niet zelf over mijn aandoening en de behandeling daarvan.",
                            "additionalInformationText": "We willen u er graag op wijzen dat de voorschrijvende arts contact met u kan opnemen als de arts meer informatie nodig heeft voordat hij/zij uw recept kan uitschrijven. De arts kan besluiten geen recept uit te schrijven als het naar zijn/haar mening noodzakelijk is om eerst te overleggen met uw huisarts.",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        },
                        {
                            "id": 1841,
                            "text": "Nee, ik heb geen huisarts.",
                            "additionalInformationText": "We willen u er graag op wijzen dat de voorschrijvende arts contact met u kan opnemen als de arts meer informatie nodig heeft voordat hij/zij uw recept kan uitschrijven. De arts kan besluiten geen recept uit te schrijven als het naar zijn/haar mening noodzakelijk is om eerst te overleggen met uw huisarts.",
                            "choiceRequiresAdditionalInformation": 0,
                            "wrongAnswer": false
                        }
                    ]
                }
            ]
        }',
            true,
            512,
            JSON_THROW_ON_ERROR
        );
    }

    public function getFileDownloadStream(string $uuid, int $questionId): ResponseInterface
    {
        return new MockResponse('This is a test response');
    }

    public function updateQuestionnaireSession(
        string $uuid,
        UpdateQuestionnaireSession $updateQuestionnaireSession,
    ): array {
        return [];
    }

    /**
     * @return array<mixed>
     */
    public function importQuestionnaireSession(
        CreateQuestionnaireSessionThroughImport $createQuestionnaireSessionThroughImport,
    ): array {
        return [];
    }
}
