<?php

declare(strict_types=1);

namespace App\Tests\Command;

use App\Command\CreateAdminUserCommand;
use App\Entity\User\AdminUser;
use App\Security\Rbac\PermissionMap;
use App\Security\Rbac\Scope;
use PHPUnit\Framework\MockObject\MockObject;
use Sylius\Component\User\Model\UserInterface;
use Sylius\Component\User\Repository\UserRepositoryInterface;
use Sylius\Resource\Factory\FactoryInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Tester\CommandTester;
use Symfony\Component\Yaml\Parser;

final class CreateAdminUserCommandTest extends CommandTestCase
{
    private const string MAPPING_CONFIG_FILE = __DIR__.'/../../config/packages/app.yaml';

    /** @var FactoryInterface<AdminUser>&MockObject */
    private FactoryInterface&MockObject $adminUserFactoryMock;
    /** @var UserRepositoryInterface<UserInterface>&MockObject */
    private UserRepositoryInterface&MockObject $adminUserRepositoryMock;
    private CommandTester $command;

    protected function setUp(): void
    {
        parent::setUp();

        $yamlParser = new Parser();
        $appConfig = $yamlParser->parseFile(self::MAPPING_CONFIG_FILE);

        $this->adminUserFactoryMock = $this->createMock(FactoryInterface::class);
        $this->adminUserRepositoryMock = $this->createMock(UserRepositoryInterface::class);

        $this->command = new CommandTester(
            new CreateAdminUserCommand(
                $this->adminUserFactoryMock,
                $this->adminUserRepositoryMock,
                new PermissionMap($appConfig['app']['route_to_scope_authorization']),
            )
        );
    }

    public function testCanCreateAdminUser(): void
    {
        $this->adminUserRepositoryMock
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['username' => 'admin'])
            ->willReturn(null);

        $this->adminUserFactoryMock
            ->expects($this->once())
            ->method('createNew')
            ->willReturn(new AdminUser());

        $this->adminUserRepositoryMock
            ->expects($this->once())
            ->method('add')
            ->with(
                $this->callback(
                    static function (AdminUser $adminUser): bool {
                        self::assertSame($adminUser->getUsername(), 'admin');
                        self::assertSame($adminUser->getPlainPassword(), 'ilovesylius');
                        self::assertSame($adminUser->getRoles(), [
                            'ROLE_ADMINISTRATION_ACCESS',
                            'ROLE_API_ACCESS',
                            ...array_map(static function (Scope $scope): string {
                                return $scope->getAsRole();
                            }, Scope::cases()),
                        ]);
                        self::assertSame($adminUser->getLocaleCode(), 'en');
                        self::assertTrue($adminUser->isEnabled());
                        self::assertTrue($adminUser->isEnablePermissionChecker());

                        return true;
                    }
                )
            );

        $this->command->execute([
            '--username' => 'admin',
            '--password' => 'ilovesylius',
        ]);

        self::assertCommandOutputDisplayContainsLines(
            ["[OK] AdminUser 'admin' was successfully created."],
            $this->command->getDisplay()
        );
    }

    public function testCannotCreateAdminUserWhenUserAlreadyExists(): void
    {
        $this->adminUserRepositoryMock
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['username' => 'admin'])
            ->willReturn(new AdminUser());

        $this->command->execute([
            '--username' => 'admin',
            '--password' => 'ilovesylius',
        ]);

        self::assertSame($this->command->getStatusCode(), Command::FAILURE);
        self::assertCommandOutputDisplayContainsLines(
            ["[ERROR] AdminUser 'admin' already exists."],
            $this->command->getDisplay()
        );
    }
}
