<?php

declare(strict_types=1);

namespace App\Tests\Command;

use PHPUnit\Framework\TestCase;

class CommandTestCase extends TestCase
{
    public static function assertCommandOutputDisplayContainsLines(
        array $expectedOutputLines,
        string $actualCommandOutputDisplay,
    ): void {
        $actualCommandOutputLines = array_map('trim', array_filter(explode("\n", $actualCommandOutputDisplay)));

        self::assertSame(
            $expectedOutputLines,
            array_values($actualCommandOutputLines)
        );
    }
}
