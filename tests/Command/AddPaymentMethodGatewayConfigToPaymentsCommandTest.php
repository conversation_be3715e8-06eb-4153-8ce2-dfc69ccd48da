<?php

declare(strict_types=1);

namespace App\Tests\Command;

use App\Command\AddPaymentMethodGatewayConfigToPaymentsCommand;
use App\Entity\Payment\PaymentMethodGatewayConfig;
use App\Factory\Payment\PaymentMethodGatewayConfigFactory;
use App\Repository\PaymentMethodGatewayConfigRepositoryInterface;
use App\Repository\PaymentMethodRepositoryInterface;
use App\Tests\Util\Factory\GatewayConfigFactory;
use App\Tests\Util\Factory\PaymentMethodFactory;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Result;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Console\Tester\CommandTester;

final class AddPaymentMethodGatewayConfigToPaymentsCommandTest extends CommandTestCase
{
    private MockObject&EntityManagerInterface $entityManagerMock;
    private MockObject&PaymentMethodRepositoryInterface $paymentMethodRepositoryMock;
    private MockObject&PaymentMethodGatewayConfigRepositoryInterface $paymentMethodGatewayConfigRepositoryMock;
    private MockObject&Connection $defaultConnectionMock;

    private CommandTester $command;

    protected function setUp(): void
    {
        parent::setUp();

        $this->entityManagerMock = $this->createMock(EntityManagerInterface::class);
        $this->paymentMethodRepositoryMock = $this->createMock(PaymentMethodRepositoryInterface::class);
        $this->paymentMethodGatewayConfigRepositoryMock = $this->createMock(PaymentMethodGatewayConfigRepositoryInterface::class);
        $this->defaultConnectionMock = $this->createMock(Connection::class);

        $this->command = new CommandTester(new AddPaymentMethodGatewayConfigToPaymentsCommand(
            $this->entityManagerMock,
            $this->paymentMethodRepositoryMock,
            $this->paymentMethodGatewayConfigRepositoryMock,
            $this->defaultConnectionMock,
            PaymentMethodGatewayConfigFactory::build(),
        ));
    }

    public function testCreatesNewCreateNewPaymentMethodGatewayConfig(): void
    {
        // Arrange
        $gatewayConfig = GatewayConfigFactory::create([
            'id' => 7331,
        ]);

        $paymentMethod = PaymentMethodFactory::create([
            'id' => 1337,
            'gatewayConfig' => $gatewayConfig,
            'code' => 'ideal_dokteronline',
        ]);

        $this->paymentMethodRepositoryMock->method('findAll')
            ->willReturn([$paymentMethod]);

        $this->paymentMethodGatewayConfigRepositoryMock->method('findOneBy')
            ->willReturn(null);

        // Assert
        $this->defaultConnectionMock->expects(self::once())
            ->method('executeQuery')
            ->willReturn($this->createStub(Result::class));

        $this->entityManagerMock->expects(self::once())
            ->method('persist')
            ->with(self::isInstanceOf(PaymentMethodGatewayConfig::class));
        $this->entityManagerMock->expects(self::once())
            ->method('flush');

        // Act
        $this->command->execute([]);
    }
}
