<?php

declare(strict_types=1);

namespace App\Tests\Command;

use App\Command\ForceOrderCancelCommand;
use Symfony\Component\Console\Tester\CommandTester;

final class ForceOrderCancelCommandTest extends CommandKernelTestCase
{
    private CommandTester $command;

    protected function setUp(): void
    {
        /** @var ForceOrderCancelCommand $command */
        $command = self::getContainer()->get(ForceOrderCancelCommand::class);

        $this->command = new CommandTester($command);
    }

    public function testCannotCancelOrderWithForceWithInvalidNumber(): void
    {
        $this->command->execute([
            'order-number' => '200001',
        ]);

        self::assertCommandOutputDisplayContainsLines(
            [
                '[ERROR] Unexpected order number received from input. Expected a valid order',
                'number with prefix.',
            ],
            $this->command->getDisplay()
        );
    }

    public function testCannotCancelOrderWithForceWithUnknownOrderNumber(): void
    {
        $this->command->execute([
            'order-number' => 'DO200001',
        ]);

        self::assertCommandOutputDisplayContainsLines(
            ["[ERROR] The requested order with number 'DO200001' could not be found."],
            $this->command->getDisplay()
        );
    }
}
