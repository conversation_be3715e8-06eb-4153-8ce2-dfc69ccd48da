<?php

declare(strict_types=1);

namespace App\Tests\Command;

use App\Command\ForgetAccountCommand;
use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\MarketingSubscription\MarketingSubscription;
use App\Entity\User\ShopUserInterface;
use App\Tests\Util\Builder\CustomerBuilder;
use App\Tests\Util\Builder\ShopUserBuilder;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Console\Tester\CommandTester;

final class ForgetAccountCommandTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private CommandTester $command;

    protected function setUp(): void
    {
        $this->entityManager = self::getContainer()->get(EntityManagerInterface::class);

        $this->command = new CommandTester(
            self::getContainer()->get(ForgetAccountCommand::class)
        );
    }

    public function testCanForgetAccountInAvailableCustomerPools(): void
    {
        /** @var CustomerPool[] $customerPools */
        $customerPools = $this->entityManager
            ->getRepository(CustomerPool::class)
            ->findBy(['code' => ['default', 'dokteronline', 'blueclinic']]);

        $this->createAccountsInCustomerPools(...$customerPools);

        $this->command->execute(
            [
                'email-address' => '<EMAIL>',
            ]
        );

        /** @var Customer[] $customers */
        $customers = $this->entityManager
            ->getRepository(Customer::class)
            ->findAll();

        self::assertStringContainsString(
            '[OK] The account has successfully been forgotten.',
            $this->command->getDisplay()
        );

        self::assertCount(3, $customers);
        foreach ($customers as $customer) {
            self::assertNull($customer->getUser());
        }
    }

    public function testCanForgetAccountInSpecificDokteronlineCustomerPool(): void
    {
        /** @var CustomerPool $customerPoolDokteronline */
        $customerPoolDokteronline = $this->entityManager
            ->getRepository(CustomerPool::class)
            ->findOneBy(['code' => 'dokteronline']);

        /** @var CustomerPool $customerPoolBlueclinic */
        $customerPoolBlueclinic = $this->entityManager
            ->getRepository(CustomerPool::class)
            ->findOneBy(['code' => 'blueclinic']);

        $this->createAccountsInCustomerPools($customerPoolDokteronline, $customerPoolBlueclinic);

        $this->command->execute(
            [
                'email-address' => '<EMAIL>',
                '--customer-pool-code' => 'dokteronline',
            ],
        );

        $customerDokteronline = $this->entityManager
            ->getRepository(Customer::class)
            ->findOneBy([
                'customerPool' => $customerPoolDokteronline,
            ]);

        $customerBlueclinic = $this->entityManager
            ->getRepository(Customer::class)
            ->findOneBy([
                'customerPool' => $customerPoolBlueclinic,
            ]);

        self::assertStringContainsString(
            '[OK] The account has successfully been forgotten.',
            $this->command->getDisplay()
        );
        self::assertInstanceOf(Customer::class, $customerDokteronline);
        self::assertNull($customerDokteronline->getUser());

        self::assertInstanceOf(Customer::class, $customerBlueclinic);
        self::assertInstanceOf(ShopUserInterface::class, $customerBlueclinic->getUser());
    }

    private function createAccountsInCustomerPools(CustomerPool ...$customerPools): void
    {
        $defaultBusinessUnit = new BusinessUnit();
        $defaultBusinessUnit->setCompanyName('Dokteronline');
        $defaultBusinessUnit->setCode('default');
        $defaultBusinessUnit->setUrl('http://test-url');
        $defaultBusinessUnit->setOrderNumberPrefix('DO');

        $this->entityManager->persist($defaultBusinessUnit);
        $this->entityManager->flush();

        foreach ($customerPools as $customerPool) {
            /** @var BusinessUnit $businessUnit */
            $businessUnit = $this->entityManager
                ->getRepository(BusinessUnit::class)
                ->findOneBy(['code' => $customerPool->getCode()]);

            $marketingSubscription = new MarketingSubscription('<EMAIL>', 'en', 'nl', $businessUnit);

            $shopUser = ShopUserBuilder::create()
                ->getShopUser();

            $customer = CustomerBuilder::create()
                ->withShopUser($shopUser)
                ->withCustomerPool($customerPool)
                ->withMarketingSubscription($marketingSubscription)
                ->getCustomer();

            $this->entityManager->persist($marketingSubscription);
            $this->entityManager->persist($customer);
            $this->entityManager->persist($shopUser);
        }

        $this->entityManager->flush();
    }
}
