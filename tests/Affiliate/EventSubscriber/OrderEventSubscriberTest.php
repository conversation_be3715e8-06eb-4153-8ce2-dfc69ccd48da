<?php

declare(strict_types=1);

namespace App\Tests\Affiliate\EventSubscriber;

use App\Affiliate\EventSubscriber\OrderEventSubscriber;
use App\Affiliate\Message\AbstractConversion;
use App\Affiliate\Message\ConversionApproved;
use App\Affiliate\Message\ConversionCreated;
use App\Affiliate\Message\ConversionRejected;
use App\Entity\Order\Enum\AffiliateConversionStatus;
use App\Event\Enum\OrderEventName;
use App\Event\OrderEvent;
use App\Tests\Mocks\Entity\TestOrder;
use PHPUnit\Framework\TestCase;
use Symfony\Component\EventDispatcher\EventDispatcher;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

final class OrderEventSubscriberTest extends TestCase
{
    private const int ORDER_ID = 1337;
    private const string AFFILIATE_ID = 'test-affiliate-id';
    private const string AFFILIATE_CONVERSION_ID = 'test-affiliate-conversion-id';

    /**
     * @dataProvider getOrderEvent
     */
    public function testOnOrderEventIsTriggered(
        OrderEvent $orderEvent,
        AbstractConversion $expectedConversion,
        bool $expectMessageDispatched = true,
    ): void {
        $messageBus = $this->createMock(MessageBusInterface::class);

        if ($expectMessageDispatched) {
            $messageBus->expects($this->once())
                ->method('dispatch')
                ->with($expectedConversion)
                ->willReturn(new Envelope($expectedConversion));
        }

        if (!$expectMessageDispatched) {
            $messageBus->expects($this->never())
                ->method('dispatch');
        }

        $eventDispatcher = new EventDispatcher();
        $eventDispatcher->addSubscriber(new OrderEventSubscriber($messageBus));
        $eventDispatcher->dispatch($orderEvent, $orderEvent->eventName->name);
    }

    public function getOrderEvent(): iterable
    {
        // A conversion can only be created if the order has:
        // 1. an affiliate id
        // 2. an affiliate conversion status NONE
        $order = new TestOrder();
        $order->setId(self::ORDER_ID);
        $order->setAffiliateId(self::AFFILIATE_ID);
        $order->setAffiliateConversionStatus(AffiliateConversionStatus::NONE);

        yield 'Order was created' => [
            new OrderEvent(OrderEventName::OrderWasCreated, $order),
            new ConversionCreated((string) $order->getAffiliateId(), $order->getId()),
        ];

        // A conversion can only be approved or rejected if the order has:
        // 1. an affiliate conversion id
        // 2. an affiliate conversion status PENDING
        $order = new TestOrder();
        $order->setId(self::ORDER_ID);
        $order->setAffiliateId(self::AFFILIATE_ID);
        $order->setAffiliateConversionId(self::AFFILIATE_CONVERSION_ID);
        $order->setAffiliateConversionStatus(AffiliateConversionStatus::PENDING);

        yield 'Order prescription was declined by doctor' => [
            new OrderEvent(OrderEventName::OrderPrescriptionWasDeclinedByDoctor, $order),
            new ConversionRejected((string) $order->getAffiliateId(), $order->getId()),
        ];

        yield 'Order was cancelled by customer' => [
            new OrderEvent(OrderEventName::OrderWasCancelled, $order),
            new ConversionRejected((string) $order->getAffiliateId(), $order->getId()),
        ];

        yield 'Order prescription was prescribed by doctor' => [
            new OrderEvent(OrderEventName::OrderPrescriptionWasPrescribedByDoctor, $order),
            new ConversionApproved((string) $order->getAffiliateId(), $order->getId()),
        ];

        yield 'Order shipment was sent by supplier' => [
            new OrderEvent(OrderEventName::OrderShipmentWasSentBySupplier, $order),
            new ConversionApproved((string) $order->getAffiliateId(), $order->getId()),
        ];

        $order = new TestOrder();
        $order->setId(self::ORDER_ID);

        yield 'Order was created but has no affiliate id' => [
            new OrderEvent(OrderEventName::OrderWasCreated, $order),
            new ConversionCreated((string) $order->getAffiliateId(), $order->getId()),
            false,
        ];

        $order = new TestOrder();
        $order->setId(self::ORDER_ID);
        $order->setAffiliateId(self::AFFILIATE_ID);
        $order->setAffiliateConversionStatus(AffiliateConversionStatus::PENDING);

        yield 'Order was created but has wrong affiliate conversion status' => [
            new OrderEvent(OrderEventName::OrderWasCreated, $order),
            new ConversionCreated((string) $order->getAffiliateId(), $order->getId()),
            false,
        ];

        $order = new TestOrder();
        $order->setId(self::ORDER_ID);
        $order->setAffiliateId(self::AFFILIATE_ID);
        $order->setAffiliateConversionId(null);

        yield 'Order prescription was declined by doctor but has no conversion id' => [
            new OrderEvent(OrderEventName::OrderPrescriptionWasDeclinedByDoctor, $order),
            new ConversionRejected((string) $order->getAffiliateId(), $order->getId()),
            false,
        ];

        $order = new TestOrder();
        $order->setId(self::ORDER_ID);
        $order->setAffiliateId(self::AFFILIATE_ID);
        $order->setAffiliateConversionId(self::AFFILIATE_CONVERSION_ID);
        $order->setAffiliateConversionStatus(AffiliateConversionStatus::NONE);

        yield 'Order prescription was declined by doctor but has wrong affiliate conversion status' => [
            new OrderEvent(OrderEventName::OrderPrescriptionWasDeclinedByDoctor, $order),
            new ConversionRejected((string) $order->getAffiliateId(), $order->getId()),
            false,
        ];

        $order = new TestOrder();
        $order->setId(self::ORDER_ID);
        $order->setAffiliateId(self::AFFILIATE_ID);
        $order->setAffiliateConversionId(null);

        yield 'Order prescription was prescribed by doctor but has no conversion id' => [
            new OrderEvent(OrderEventName::OrderPrescriptionWasPrescribedByDoctor, $order),
            new ConversionApproved((string) $order->getAffiliateId(), $order->getId()),
            false,
        ];

        $order = new TestOrder();
        $order->setId(self::ORDER_ID);
        $order->setAffiliateId(self::AFFILIATE_ID);
        $order->setAffiliateConversionId(self::AFFILIATE_CONVERSION_ID);
        $order->setAffiliateConversionStatus(AffiliateConversionStatus::REJECTED);

        yield 'Order prescription was prescribed by doctor but has wrong affiliate conversion status' => [
            new OrderEvent(OrderEventName::OrderPrescriptionWasPrescribedByDoctor, $order),
            new ConversionApproved((string) $order->getAffiliateId(), $order->getId()),
            false,
        ];

        $order = new TestOrder();
        $order->setId(self::ORDER_ID);
        $order->setAffiliateId(self::AFFILIATE_ID);
        $order->setAffiliateConversionId(self::AFFILIATE_CONVERSION_ID);
        $order->setAffiliateConversionStatus(AffiliateConversionStatus::APPROVED);

        yield 'Order shipment was sent by supplier but has approved affiliate conversion status' => [
            new OrderEvent(OrderEventName::OrderShipmentWasSentBySupplier, $order),
            new ConversionApproved((string) $order->getAffiliateId(), $order->getId()),
            false,
        ];
    }
}
