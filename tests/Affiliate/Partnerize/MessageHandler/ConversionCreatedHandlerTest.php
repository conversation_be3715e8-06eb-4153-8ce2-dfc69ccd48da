<?php

declare(strict_types=1);

namespace App\Tests\Affiliate\Partnerize\MessageHandler;

use App\Affiliate\Client\AffiliateClientInterface;
use App\Affiliate\Message\ConversionApproved;
use App\Affiliate\Message\ConversionCreated;
use App\Affiliate\Partnerize\MessageHandler\ConversionCreatedHandler;
use App\Entity\Order\Enum\AffiliateConversionStatus;
use App\Tests\Mocks\Entity\TestOrder;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Exception\RecoverableMessageHandlingException;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DispatchAfterCurrentBusStamp;
use Symfony\Component\Serializer\Encoder\EncoderInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Webmozart\Assert\InvalidArgumentException;

final class ConversionCreatedHandlerTest extends TestCase
{
    private const string PARTNERIZE_CAMPAIGN_ID = 'partnerize-campaign-id';
    private const string AFFILIATE_ID = 'affiliate-test-id';
    private const int ORDER_ID = 1337;
    private const string AFFILIATE_CONVERSION_ID = 'test-conversion-id';

    private EntityManagerInterface&MockObject $entityManager;
    private AffiliateClientInterface&MockObject $affiliateClient;
    private LoggerInterface&MockObject $logger;
    private EncoderInterface&MockObject $uriEncoder;
    private ConversionCreatedHandler $conversionCreatedHandler;
    private MessageBusInterface&MockObject $messageBus;

    protected function setUp(): void
    {
        parent::setUp();

        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->affiliateClient = $this->createMock(AffiliateClientInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->uriEncoder = $this->createMock(EncoderInterface::class);
        $this->messageBus = $this->createMock(MessageBusInterface::class);

        $this->conversionCreatedHandler = new ConversionCreatedHandler(
            $this->affiliateClient,
            $this->entityManager,
            $this->logger,
            $this->uriEncoder,
            self::PARTNERIZE_CAMPAIGN_ID,
            $this->messageBus,
        );
    }

    public function testItHandlesMessage(): void
    {
        $order = new TestOrder();
        $order->setId(self::ORDER_ID);

        $orderRepository = $this->createMock(EntityRepository::class);
        $this->entityManager->method('getRepository')->willReturn($orderRepository);
        $orderRepository->method('find')->willReturn($order);
        $this->uriEncoder->method('encode')->willReturn('/encoded/conversion/url');
        $this->affiliateClient->method('createConversion')->willReturn(self::AFFILIATE_CONVERSION_ID);

        $conversionCreated = new ConversionCreated(self::AFFILIATE_ID, self::ORDER_ID);

        ($this->conversionCreatedHandler)($conversionCreated);

        $this->assertEquals(self::AFFILIATE_CONVERSION_ID, $order->getAffiliateConversionId());
        $this->assertSame(AffiliateConversionStatus::PENDING, $order->getAffiliateConversionStatus());
    }

    public function testItThrowsExceptionIfAffiliateConversionIdIsAlreadySet(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Order must not have an affiliate conversion id.');

        $order = new TestOrder();
        $order->setAffiliateConversionId(self::AFFILIATE_CONVERSION_ID);

        $orderRepository = $this->createMock(EntityRepository::class);
        $this->entityManager->method('getRepository')->willReturn($orderRepository);
        $orderRepository->method('find')->willReturn($order);
        $this->logger->expects($this->once())->method('error');

        $conversionCreated = new ConversionCreated(self::AFFILIATE_ID, self::ORDER_ID);

        ($this->conversionCreatedHandler)($conversionCreated);
    }

    public function testItThrowsExceptionIfPartnerizeCannotCreateConversion(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Conversion could not be created by Partnerize.');

        $order = new TestOrder();

        $orderRepository = $this->createMock(EntityRepository::class);
        $this->entityManager->method('getRepository')->willReturn($orderRepository);
        $orderRepository->method('find')->willReturn($order);
        $this->uriEncoder->method('encode')->willReturn('/encoded/conversion/url');
        $this->affiliateClient->method('createConversion')->willReturn('');
        $this->logger->expects($this->once())->method('error');

        $conversionCreated = new ConversionCreated(self::AFFILIATE_ID, self::ORDER_ID);

        ($this->conversionCreatedHandler)($conversionCreated);
    }

    public function testItThrowsRecoverableMessageHandlingExceptionIfPartnerizeFails(): void
    {
        $this->expectException(RecoverableMessageHandlingException::class);
        $this->expectExceptionMessage('Received error from Partnerize API.');

        $order = new TestOrder();

        $orderRepository = $this->createMock(EntityRepository::class);
        $this->entityManager->method('getRepository')->willReturn($orderRepository);
        $orderRepository->method('find')->willReturn($order);
        $this->uriEncoder->method('encode')->willReturn('/encoded/conversion/url');

        $clientException = $this->createMock(ClientExceptionInterface::class);
        $clientResponse = $this->createMock(ResponseInterface::class);
        $clientResponse->method('getStatusCode')->willReturn(502);
        $clientException->method('getResponse')->willReturn($clientResponse);
        $this->affiliateClient->method('createConversion')->willThrowException($clientException);

        $conversionCreated = new ConversionCreated(self::AFFILIATE_ID, self::ORDER_ID);

        ($this->conversionCreatedHandler)($conversionCreated);
    }

    public function testItDispatchesTargetConversionIfProvided(): void
    {
        // Arrange
        $order = new TestOrder();
        $order->setId(self::ORDER_ID);

        $orderRepository = $this->createMock(EntityRepository::class);
        $this->entityManager->method('getRepository')->willReturn($orderRepository);
        $orderRepository->method('find')->willReturn($order);
        $this->uriEncoder->method('encode')->willReturn('/encoded/conversion/url');
        $this->affiliateClient->method('createConversion')->willReturn(self::AFFILIATE_CONVERSION_ID);

        $targetConversion = new ConversionApproved(self::AFFILIATE_ID, self::ORDER_ID);
        $conversionCreated = new ConversionCreated(
            self::AFFILIATE_ID,
            self::ORDER_ID,
            null,
            $targetConversion
        );

        // Assert
        $this->messageBus->expects($this->once())
            ->method('dispatch')
            ->with($targetConversion)
            ->willReturn(new Envelope($targetConversion, [new DispatchAfterCurrentBusStamp()]));

        // Act
        ($this->conversionCreatedHandler)($conversionCreated);
    }
}
