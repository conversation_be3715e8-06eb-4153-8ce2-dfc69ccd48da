<?php

declare(strict_types=1);

namespace App\Tests\Affiliate\Partnerize\MessageHandler;

use App\Affiliate\Client\AffiliateClientInterface;
use App\Affiliate\Message\ConversionRejected;
use App\Affiliate\Partnerize\MessageHandler\ConversionRejectedHandler;
use App\Entity\Order\Enum\AffiliateConversionStatus;
use App\Tests\Mocks\Entity\TestOrder;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Exception\RecoverableMessageHandlingException;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Serializer\Encoder\EncoderInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Webmozart\Assert\InvalidArgumentException;

final class ConversionRejectedHandlerTest extends TestCase
{
    private const string PARTNERIZE_CAMPAIGN_ID = 'partnerize-campaign-id';
    private const int ORDER_ID = 1337;
    private const string AFFILIATE_ID = 'affiliate-test-id';
    private const string AFFILIATE_CONVERSION_ID = 'affiliate-conversion-test-id';

    private EntityManagerInterface&MockObject $entityManager;
    private AffiliateClientInterface&MockObject $affiliateClient;
    private LoggerInterface&MockObject $logger;
    private ConversionRejectedHandler $conversionRejectedHandler;

    protected function setUp(): void
    {
        parent::setUp();

        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->affiliateClient = $this->createMock(AffiliateClientInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->conversionRejectedHandler = new ConversionRejectedHandler(
            $this->affiliateClient,
            $this->entityManager,
            $this->logger,
            $this->createMock(EncoderInterface::class),
            self::PARTNERIZE_CAMPAIGN_ID,
            $this->createMock(MessageBusInterface::class)
        );
    }

    public function testItHandlesMessage(): void
    {
        $order = new TestOrder();
        $order->setId(self::ORDER_ID);
        $order->setAffiliateId(self::AFFILIATE_ID);
        $order->setAffiliateConversionId(self::AFFILIATE_CONVERSION_ID);

        $orderRepository = $this->createMock(EntityRepository::class);
        $this->entityManager->method('getRepository')->willReturn($orderRepository);
        $orderRepository->method('find')->willReturn($order);
        $this->affiliateClient->expects(self::once())
            ->method('rejectConversion')
            ->with(self::AFFILIATE_CONVERSION_ID, self::PARTNERIZE_CAMPAIGN_ID);

        $conversionRejected = new ConversionRejected(self::AFFILIATE_ID, self::ORDER_ID);

        ($this->conversionRejectedHandler)($conversionRejected);

        $this->assertSame(AffiliateConversionStatus::REJECTED, $order->getAffiliateConversionStatus());
    }

    public function testItThrowsExceptionIfAffiliateConversionIdIsNotSet(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Order must have an affiliate conversion id.');

        $order = new TestOrder();
        $order->setId(self::ORDER_ID);

        $orderRepository = $this->createMock(EntityRepository::class);
        $this->entityManager->method('getRepository')->willReturn($orderRepository);
        $orderRepository->method('find')->willReturn($order);
        $this->logger->expects($this->once())->method('error');

        $conversionRejected = new ConversionRejected(self::AFFILIATE_ID, self::ORDER_ID);

        ($this->conversionRejectedHandler)($conversionRejected);
    }

    public function testItThrowsRecoverableMessageHandlingExceptionIfPartnerizeFails(): void
    {
        $this->expectException(RecoverableMessageHandlingException::class);
        $this->expectExceptionMessage('Received error from Partnerize API.');

        $order = new TestOrder();
        $order->setAffiliateConversionId(self::AFFILIATE_CONVERSION_ID);

        $orderRepository = $this->createMock(EntityRepository::class);
        $this->entityManager->method('getRepository')->willReturn($orderRepository);
        $orderRepository->method('find')->willReturn($order);

        $clientException = $this->createMock(ClientExceptionInterface::class);
        $clientResponse = $this->createMock(ResponseInterface::class);
        $clientResponse->method('getStatusCode')->willReturn(502);
        $clientException->method('getResponse')->willReturn($clientResponse);
        $this->affiliateClient->method('rejectConversion')->willThrowException($clientException);

        $conversionRejected = new ConversionRejected(self::AFFILIATE_ID, self::ORDER_ID);

        ($this->conversionRejectedHandler)($conversionRejected);
    }
}
