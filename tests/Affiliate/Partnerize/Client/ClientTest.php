<?php

declare(strict_types=1);

namespace App\Tests\Affiliate\Partnerize\Client;

use App\Affiliate\Partnerize\Client\Client;
use App\Affiliate\Partnerize\Model\Job;
use App\Affiliate\Partnerize\Model\JobResponse;
use ArrayIterator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpClient\MockHttpClient;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

final class ClientTest extends TestCase
{
    private const string CONVERSION_ID = 'conversion-test-id';
    private const string CAMPAIGN_ID = 'campaign-test-id';

    private Client $client;

    /**
     * @var ArrayIterator<int, MockResponse>
     */
    private ArrayIterator $mockResponses;

    private SerializerInterface $serializer;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockResponses = new ArrayIterator();
        $mockHttpClient = new MockHttpClient(
            $this->mockResponses,
            'https://consult.system.url'
        );

        $this->serializer = $this->createMock(SerializerInterface::class);

        $this->client = new Client(
            $this->createMock(HttpClientInterface::class),
            $mockHttpClient,
            $this->serializer,
        );
    }

    public function testItCanApproveConversion(): void
    {
        $this->mockResponses[] = new MockResponse(
            json_encode([
                'data' => [
                    'id' => 'test-job-id',
                    'item_error_count' => 0,
                ],
            ], JSON_THROW_ON_ERROR)
        );

        $expectedJobResponse = new JobResponse(new Job('test-job-id', 0));
        $this->serializer->method('deserialize')->willReturn($expectedJobResponse);

        $job = $this->client->approveConversion(self::CONVERSION_ID, self::CAMPAIGN_ID);

        $this->assertSame($expectedJobResponse->data, $job);
    }

    public function testItCanRejectConversion(): void
    {
        $this->mockResponses[] = new MockResponse(
            json_encode([
                'data' => [
                    'id' => 'test-job-id',
                    'item_error_count' => 0,
                ],
            ], JSON_THROW_ON_ERROR)
        );

        $expectedJobResponse = new JobResponse(new Job('test-job-id', 0));
        $this->serializer->method('deserialize')->willReturn($expectedJobResponse);

        $job = $this->client->rejectConversion(self::CONVERSION_ID, self::CAMPAIGN_ID);

        $this->assertSame($expectedJobResponse->data, $job);
    }
}
