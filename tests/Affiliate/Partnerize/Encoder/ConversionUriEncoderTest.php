<?php

declare(strict_types=1);

namespace App\Tests\Affiliate\Partnerize\Encoder;

use App\Affiliate\Partnerize\Encoder\ConversionUriEncoder;
use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Channel\Channel;
use App\Entity\Customer\Customer;
use App\Entity\Order\Order;
use App\Entity\Product\ProductType;
use App\Repository\OrderRepositoryInterface;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\OrderItemFactory;
use App\Tests\Util\Factory\ProductVariantFactory;
use App\Tests\Util\Factory\PromotionCouponFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Webmozart\Assert\InvalidArgumentException;

final class ConversionUriEncoderTest extends TestCase
{
    public const string LOCALE = 'en';

    private OrderRepositoryInterface&MockObject $orderRepository;
    private ConversionUriEncoder $conversionUriEncoder;

    protected function setUp(): void
    {
        parent::setUp();

        $this->orderRepository = $this->createMock(OrderRepositoryInterface::class);
        $this->conversionUriEncoder = new ConversionUriEncoder($this->orderRepository);
    }

    public function testItDoesNotSupportEncodingWrongFormat(): void
    {
        $this->assertFalse($this->conversionUriEncoder->supportsEncoding(
            'wrong-format',
            [
                'campaign_id' => 'test-campaign-id',
            ]
        ));
    }

    public function testItDoesNotSupportEncodingWrongContext(): void
    {
        $this->assertFalse($this->conversionUriEncoder->supportsEncoding(
            'partnerize_conversion_uri',
        ));
    }

    public function testItSupportsEncoding(): void
    {
        $this->assertTrue($this->conversionUriEncoder->supportsEncoding(
            'partnerize_conversion_uri',
            [
                'campaign_id' => 'test-campaign-id',
            ]
        ));
    }

    public function testItEncodesOrderToConversionUri(): void
    {
        $order = OrderFactory::createPrefilled(['affiliateId' => 'affiliate-test-id', 'number' => 'DO1337']);

        $this->setVariantCodes($order);

        $uri = $this->conversionUriEncoder->encode(
            $order,
            'partnerize_conversion_uri',
            ['campaign_id' => 'campaign-test-id']
        );

        $expectedOrderItem1 = implode('/', [
            'category:Test%20consult%20product%20%28en%29',
            'sku:7_25_test-variant-code-consult',
            'value:2',
            'quantity:1',
            'product_name:Test%20consult%20product%20%28nl%29',
            'product_variant_name:Test%20consult%20product%20variant%20%28nl%29',
            'supplier_name:',
        ]);
        $expectedOrderItem2 = implode('/', [
            'category:Test%20taxon%20%28en%29',
            'sku:1_1',
            'value:3',
            'quantity:1',
            'product_name:Test%20medication%20product%20%28nl%29',
            'product_variant_name:Test%20medication%20product%20variant%20%28nl%29',
            'supplier_name:Test%20Supplier',
        ]);

        $this->assertSame(
            implode('/', [
                'tracking_mode:api',
                'campaign:campaign-test-id',
                'clickref:affiliate-test-id',
                'conversionref:DO1337',
                'country:NL',
                'currency:EUR',
                'customertype:new',
                '['.$expectedOrderItem1.']['.$expectedOrderItem2.']',
            ]),
            $uri
        );
    }

    public function testItEncodesOrderWithPromotionCouponToConversionUri(): void
    {
        $order = OrderFactory::createPrefilled([
            'affiliateId' => 'affiliate-test-id',
            'promotionCoupon' => PromotionCouponFactory::create(['code' => 'test-coupon-code']),
        ]);

        $this->setVariantCodes($order);

        $uri = $this->conversionUriEncoder->encode(
            $order,
            'partnerize_conversion_uri',
            ['campaign_id' => 'campaign-test-id']
        );

        $expectedOrderItem1 = implode('/', [
            'category:Test%20consult%20product%20%28en%29',
            'sku:7_25_test-variant-code-consult',
            'value:2',
            'quantity:1',
            'product_name:Test%20consult%20product%20%28nl%29',
            'product_variant_name:Test%20consult%20product%20variant%20%28nl%29',
            'supplier_name:',
        ]);

        $expectedOrderItem2 = implode('/', [
            'category:Test%20taxon%20%28en%29',
            'sku:1_1',
            'value:3',
            'quantity:1',
            'product_name:Test%20medication%20product%20%28nl%29',
            'product_variant_name:Test%20medication%20product%20variant%20%28nl%29',
            'supplier_name:Test%20Supplier',
        ]);

        $this->assertEquals(
            implode('/', [
                'tracking_mode:api',
                'campaign:campaign-test-id',
                'clickref:affiliate-test-id',
                'conversionref:DO1337',
                'country:NL',
                'currency:EUR',
                'customertype:new',
                'voucher:test-coupon-code',
                '['.$expectedOrderItem1.']['.$expectedOrderItem2.']',
            ]),
            $uri
        );
    }

    public function testItFallsBackToFirstTaxonForMissingMainTaxon(): void
    {
        $order = OrderFactory::createPrefilled(['affiliateId' => 'affiliate-test-id']);

        $this->setVariantCodes($order);

        foreach ($order->getItems() as $item) {
            $item->getProduct()?->setMainTaxon(null);
        }

        $uri = $this->conversionUriEncoder->encode(
            $order,
            'partnerize_conversion_uri',
            ['campaign_id' => 'campaign-test-id']
        );

        $expectedOrderItem1 = implode('/', [
            'category:Test%20consult%20product%20%28en%29',
            'sku:7_25_test-variant-code-consult',
            'value:2',
            'quantity:1',
            'product_name:Test%20consult%20product%20%28nl%29',
            'product_variant_name:Test%20consult%20product%20variant%20%28nl%29',
            'supplier_name:',
        ]);

        $expectedOrderItem2 = implode('/', [
            'category:Test%20taxon%20%28en%29',
            'sku:1_1',
            'value:3',
            'quantity:1',
            'product_name:Test%20medication%20product%20%28nl%29',
            'product_variant_name:Test%20medication%20product%20variant%20%28nl%29',
            'supplier_name:Test%20Supplier',
        ]);

        $this->assertSame(
            implode('/', [
                'tracking_mode:api',
                'campaign:campaign-test-id',
                'clickref:affiliate-test-id',
                'conversionref:DO1337',
                'country:NL',
                'currency:EUR',
                'customertype:new',
                '['.$expectedOrderItem1.']['.$expectedOrderItem2.']',
            ]),
            $uri
        );
    }

    public function testItThrowsExceptionForInvalidData(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('This encoder only supports Orders.');

        $this->conversionUriEncoder->encode(
            'invalid-data',
            'partnerize_conversion_uri',
            ['campaign_id' => 'campaign-test-id']
        );
    }

    public function testItThrowsExceptionForOrderWithoutCustomer(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('The order must have a customer.');

        $order = OrderFactory::createPrefilled(['affiliateId' => 'affiliate-test-id']);
        $order->setCustomer(null);

        $this->conversionUriEncoder->encode(
            $order,
            'partnerize_conversion_uri',
            ['campaign_id' => 'campaign-test-id']
        );
    }

    public function testItThrowsExceptionForOrderWithoutItems(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('The order must have at least one order item.');

        $order = OrderFactory::createPrefilled(['affiliateId' => 'affiliate-test-id']);
        $order->getItems()->clear();

        $this->conversionUriEncoder->encode(
            $order,
            'partnerize_conversion_uri',
            ['campaign_id' => 'campaign-test-id']
        );
    }

    public function testItThrowsExceptionForOrderWithEmptyAffiliateId(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('The order must have an affiliate id.');

        $order = OrderFactory::createPrefilled(['affiliateId' => null]);
        $order->setAffiliateId(null);

        $this->conversionUriEncoder->encode(
            $order,
            'partnerize_conversion_uri',
            ['campaign_id' => 'campaign-test-id']
        );
    }

    public function testItThrowsExceptionForOrderWithEmptyOrderNumber(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('The order must have an order number.');

        $order = OrderFactory::createPrefilled(['affiliateId' => 'affiliate-test-id']);
        $order->setNumber(null);

        $this->conversionUriEncoder->encode(
            $order,
            'partnerize_conversion_uri',
            ['campaign_id' => 'campaign-test-id']
        );
    }

    public function testItReturnsExistingCustomerType(): void
    {
        $this->orderRepository->method('countOrders')->willReturn(2);

        $order = OrderFactory::createPrefilled(['affiliateId' => 'affiliate-test-id']);

        $uri = $this->conversionUriEncoder->encode(
            $order,
            'partnerize_conversion_uri',
            ['campaign_id' => 'campaign-test-id']
        );

        $this->assertStringContainsString('customertype:existing', $uri);
    }

    public function testItReturnsNewCustomerType(): void
    {
        $this->orderRepository->method('countOrders')->willReturn(0);

        $order = OrderFactory::createPrefilled(['affiliateId' => 'affiliate-test-id']);

        $uri = $this->conversionUriEncoder->encode(
            $order,
            'partnerize_conversion_uri',
            ['campaign_id' => 'campaign-test-id']
        );

        $this->assertStringContainsString('customertype:new', $uri);
    }

    public function testItDoesNotIncludeServiceProducts(): void
    {
        $order = OrderFactory::createPrefilled(['affiliateId' => 'affiliate-test-id']);

        $this->setVariantCodes($order);

        $serviceProductVariant = ProductVariantFactory::createPrefilled(
            ['id' => 3],
            ProductType::SERVICE,
        );

        $serviceOrderItem = OrderItemFactory::create([
            'id' => 3,
            'unitPrice' => 400,
            'unitCostPrice' => 200,
            'originalUnitPrice' => 400,
            'variant' => $serviceProductVariant,
        ]);

        $serviceOrderItem->addUnit(
            OrderFactory::createOrderItemUnit([
                'orderItem' => $serviceOrderItem,
            ])
        );

        $order->addItem($serviceOrderItem);

        $uri = $this->conversionUriEncoder->encode(
            $order,
            'partnerize_conversion_uri',
            ['campaign_id' => 'campaign-test-id']
        );

        $unit = new BusinessUnit();
        $unit->setCode('dokteronline');
        $channel = new Channel();
        $channel->setBusinessUnit($unit);

        $order = new Order();
        $order->setChannel($channel);
        $order->setNumber('12345');
        $order->setAffiliateId('affiliate-test-id');
        $order->setCurrencyCode('EUR');
        $order->setCustomer(new Customer());

        $expectedOrderItem1 = implode('/', [
            'category:Test%20consult%20product%20%28en%29',
            'sku:7_25_test-variant-code-consult',
            'value:2',
            'quantity:1',
            'product_name:Test%20consult%20product%20%28nl%29',
            'product_variant_name:Test%20consult%20product%20variant%20%28nl%29',
            'supplier_name:',
        ]);

        $expectedOrderItem2 = implode('/', [
            'category:Test%20taxon%20%28en%29',
            'sku:1_1',
            'value:3',
            'quantity:1',
            'product_name:Test%20medication%20product%20%28nl%29',
            'product_variant_name:Test%20medication%20product%20variant%20%28nl%29',
            'supplier_name:Test%20Supplier',
        ]);

        $this->assertEquals(
            implode('/', [
                'tracking_mode:api',
                'campaign:campaign-test-id',
                'clickref:affiliate-test-id',
                'conversionref:DO1337',
                'country:NL',
                'currency:EUR',
                'customertype:new',
                '['.$expectedOrderItem1.']['.$expectedOrderItem2.']',
            ]),
            $uri
        );
    }

    public function testItDoesNotIncludeFreeProducts(): void
    {
        $order = OrderFactory::createPrefilled(['affiliateId' => 'affiliate-test-id']);

        $this->setVariantCodes($order);

        foreach ($order->getItems() as $item) {
            if ($item->getVariant()?->getCode() === '7_25_test-variant-code-consult') {
                $item->setUnitPrice(0);
            }
        }

        $uri = $this->conversionUriEncoder->encode(
            $order,
            'partnerize_conversion_uri',
            ['campaign_id' => 'campaign-test-id']
        );

        $expectedOrderItem = implode('/', [
            'category:Test%20taxon%20%28en%29',
            'sku:1_1',
            'value:3',
            'quantity:1',
            'product_name:Test%20medication%20product%20%28nl%29',
            'product_variant_name:Test%20medication%20product%20variant%20%28nl%29',
            'supplier_name:Test%20Supplier',
        ]);

        $this->assertEquals(
            implode('/', [
                'tracking_mode:api',
                'campaign:campaign-test-id',
                'clickref:affiliate-test-id',
                'conversionref:DO1337',
                'country:NL',
                'currency:EUR',
                'customertype:new',
                '['.$expectedOrderItem.']',
            ]),
            $uri
        );
    }

    /**
     * Set realistic variant code for medication.
     */
    private function setVariantCodes(Order $order): void
    {
        foreach ($order->getItems() as $key => $item) {
            if (!$item->getProduct()->isOfType(ProductType::MEDICATION)) {
                continue;
            }

            $code = sprintf('%d_1_supplier_country', $key);
            $item->getVariant()?->setCode($code);
        }
    }
}
