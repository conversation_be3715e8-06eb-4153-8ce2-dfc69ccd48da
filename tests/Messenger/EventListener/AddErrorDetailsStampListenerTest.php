<?php

declare(strict_types=1);

namespace App\Tests\Messenger\EventListener;

use App\Messenger\EventListener\AddErrorDetailsStampListener;
use Exception;
use PHPUnit\Framework\TestCase;
use stdClass;
use Symfony\Component\ErrorHandler\Exception\FlattenException;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Event\WorkerMessageFailedEvent;
use Symfony\Component\Messenger\Exception\HandlerFailedException;
use Symfony\Component\Messenger\Stamp\ErrorDetailsStamp;
use Throwable;

final class AddErrorDetailsStampListenerTest extends TestCase
{
    private AddErrorDetailsStampListener $listener;

    public function setUp(): void
    {
        $this->listener = new AddErrorDetailsStampListener();
    }

    public function testGetSubscribedEventsCallsExistingMethods(): void
    {
        self::assertSame(
            [
                WorkerMessageFailedEvent::class => ['onMessageFailed', 200],
            ],
            AddErrorDetailsStampListener::getSubscribedEvents()
        );
    }

    /**
     * @dataProvider messageFailedProvider
     */
    public function testOnMessageFailed(Throwable $throwable, ErrorDetailsStamp $expectedStamp): void
    {
        // Arrange
        $envelope = new Envelope(new stdClass());
        $event = new WorkerMessageFailedEvent($envelope, 'receiver', $throwable);

        // Act
        $this->listener->onMessageFailed($event);

        $envelope = $event->getEnvelope();
        $stamps = $envelope->all(ErrorDetailsStamp::class);

        // Assert
        self::assertCount(1, $stamps);
        self::assertEquals($expectedStamp, $stamps[0]);
        self::assertNotInstanceOf(FlattenException::class, $stamps[0]->getFlattenException());
    }

    /**
     * @return iterable<string, array{Throwable, ErrorDetailsStamp}>
     */
    public function messageFailedProvider(): iterable
    {
        yield 'regular exception without previous exception' => [
            new Exception('Test exception', 123),
            new ErrorDetailsStamp(Exception::class, 123, 'Test exception'),
        ];

        yield 'handler failed exception with previous exception' => [
            new HandlerFailedException(new Envelope(new stdClass()), [new Exception('Previous exception', 456)]),
            new ErrorDetailsStamp(Exception::class, 456, 'Previous exception'),
        ];
    }
}
