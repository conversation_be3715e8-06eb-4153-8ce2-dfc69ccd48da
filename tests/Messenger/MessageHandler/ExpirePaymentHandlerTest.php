<?php

declare(strict_types=1);

namespace App\Tests\Messenger\MessageHandler;

use App\Entity\Payment\Payment;
use App\Entity\Payment\PaymentInterface;
use App\Messenger\Message\ExpirePayment;
use App\Messenger\MessageHandler\ExpirePaymentHandler;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\MockObject\Stub;
use PHPUnit\Framework\TestCase;
use SM\Factory\FactoryInterface;
use SM\StateMachine\StateMachineInterface;
use Sylius\Component\Core\Repository\PaymentRepositoryInterface;

final class ExpirePaymentHandlerTest extends TestCase
{
    /** @var PaymentRepositoryInterface<PaymentInterface>&Stub */
    private PaymentRepositoryInterface&Stub $paymentRepositoryStub;
    private FactoryInterface&MockObject $stateMachineFactoryMock;

    private ExpirePaymentHandler $expirePaymentHandler;

    protected function setUp(): void
    {
        $this->paymentRepositoryStub = $this->createStub(PaymentRepositoryInterface::class);
        $this->stateMachineFactoryMock = $this->createMock(FactoryInterface::class);

        $this->expirePaymentHandler = new ExpirePaymentHandler(
            $this->paymentRepositoryStub,
            $this->stateMachineFactoryMock,
        );
    }

    /**
     * @dataProvider provideInvalidPayment
     */
    public function testInvokeDoesNotExpirePayment(?PaymentInterface $payment): void
    {
        // Arrange
        $this->paymentRepositoryStub->method('find')
            ->willReturn($payment);

        $stateMachineMock = $this->createMock(StateMachineInterface::class);
        $this->stateMachineFactoryMock->method('get')
            ->willReturn($stateMachineMock);

        // Assert
        $stateMachineMock->expects(self::never())
            ->method('apply');

        // Act
        ($this->expirePaymentHandler)(new ExpirePayment(1337));
    }

    public function testInvokeExpiresPayment(): void
    {
        // Arrange
        $payment = new Payment();

        $this->paymentRepositoryStub->method('find')
            ->willReturn($payment);

        $stateMachineMock = $this->createMock(StateMachineInterface::class);
        $this->stateMachineFactoryMock->method('get')
            ->with($payment, 'sylius_payment')
            ->willReturn($stateMachineMock);

        $stateMachineMock->method('can')
            ->willReturn(true);

        // Assert
        $stateMachineMock->expects(self::once())
            ->method('apply')
            ->with('expire');

        // Act
        ($this->expirePaymentHandler)(new ExpirePayment(1337));
    }

    /**
     * @return iterable<string, array{0: PaymentInterface|null}>
     */
    protected function provideInvalidPayment(): iterable
    {
        yield 'Payment that is null' => [null];

        yield 'Payment with an incorrect state.' => [new Payment()];
    }
}
