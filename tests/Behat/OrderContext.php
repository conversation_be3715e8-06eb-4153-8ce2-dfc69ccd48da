<?php

declare(strict_types=1);

namespace App\Tests\Behat;

use App\Entity\Order\Cancellation;
use App\Entity\Order\Enum\AffiliateConversionStatus;
use App\Entity\Order\Enum\CancellationBy;
use App\Entity\Order\Order;
use App\Entity\Order\OrderAuditEntry;
use App\Entity\Order\OrderSequence;
use App\Entity\Payment\Payment;
use App\Entity\Payment\PaymentInterface;
use App\Entity\Payment\PaymentMethod;
use App\Entity\Payment\PaymentMethodInterface;
use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShippingMethod;
use App\Repository\OrderRepositoryInterface;
use App\StateMachine\OrderPrescriptionTransitions;
use Behat\Behat\Context\Context;
use Behat\Step\Given;
use Behat\Step\Then;
use DateTime;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\Assert;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;
use SM\Factory\FactoryInterface;
use SuperBrave\ConsultSystemClient\Model\Doctor;
use Sylius\Component\Order\OrderTransitions as SyliusOrderTransitions;
use Sylius\Component\Payment\PaymentTransitions;
use Sylius\Component\Shipping\Model\ShipmentInterface as SyliusShipmentInterface;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;

final class OrderContext extends Assert implements Context
{
    public function __construct(
        private readonly ContextStorage $contextStorage,
        private readonly EntityManagerInterface $entityManager,
        private readonly FactoryInterface $stateMachineFactory,
        private readonly PropertyAccessorInterface $propertyAccessor,
        private readonly SupplierContext $supplierContext,
    ) {
    }

    public function getOrderByCurrentTokenValue(?string $state = null): Order
    {
        $tokenValue = $this->contextStorage->get('tokenValue');
        self::assertIsString($tokenValue);

        $parameters = [
            'tokenValue' => $tokenValue,
        ];

        if (!empty($state)) {
            $parameters['state'] = $state;
        }

        $this->entityManager->clear();

        $order = $this->entityManager->getRepository(Order::class)->findOneBy($parameters);
        self::assertInstanceOf(Order::class, $order, 'The order should exist.');

        return $order;
    }

    /**
     * Suggestion: Use the `theOrderIsCreatedInConsultSystem` from the ConsultSystemContext.
     */
    #[Given('the order is paid')]
    public function theOrderIsPaid(): void
    {
        $order = $this->getOrderByCurrentTokenValue(state: 'new');

        $payment = $order->getPayments()->first();
        self::assertInstanceOf(Payment::class, $payment);

        $this->stateMachineFactory->get($payment, PaymentTransitions::GRAPH)
            ->apply(PaymentTransitions::TRANSITION_COMPLETE);

        $this->entityManager->flush();
    }

    #[Given('the checkout is completed at :dateTime')]
    public function setCheckoutCompletedAt(string $dateTime): void
    {
        $order = $this->getOrderByCurrentTokenValue(state: 'new');
        $order->setCheckoutCompletedAt(new DateTime($dateTime));

        $this->entityManager->flush();
    }

    #[Given('the order payment is authorized')]
    public function theOrderPaymentIsAuthorized(): void
    {
        $order = $this->getOrderByCurrentTokenValue(state: 'new');

        $payment = $order->getPayments()->first();
        Assert::assertInstanceOf(Payment::class, $payment);

        $this->stateMachineFactory->get($payment, PaymentTransitions::GRAPH)
            ->apply(PaymentTransitions::TRANSITION_AUTHORIZE);

        $this->entityManager->flush();
    }

    #[Given('the order is paid with payment method :paymentMethod')]
    public function theOrderIsPaidWithPaymentMethod(string $paymentMethod): void
    {
        $order = $this->getOrderByCurrentTokenValue();

        $paymentMethod = $this->entityManager
            ->getRepository(PaymentMethod::class)
            ->findOneBy(['code' => $paymentMethod]);

        self::assertInstanceOf(PaymentMethodInterface::class, $paymentMethod);

        $payment = $order->getPayments()->last();
        self::assertInstanceOf(Payment::class, $payment);

        $payment->setMethod($paymentMethod);

        $this->stateMachineFactory->get($payment, PaymentTransitions::GRAPH)
            ->apply(PaymentTransitions::TRANSITION_COMPLETE);

        $this->entityManager->flush();
    }

    #[Given('the order payment has reference :reference in details for factoryName :factoryName in referenceKey :referenceKey')]
    public function theOrderPaymentHasReferenceInDetails(string $reference, string $factoryName, string $referenceKey): void
    {
        $order = $this->getOrderByCurrentTokenValue();

        $payment = $order->getPayments()->last();
        self::assertInstanceOf(Payment::class, $payment);

        $payment->setDetails([
            $factoryName => [
                $referenceKey => $reference,
            ],
        ]);

        $this->entityManager->flush();
    }

    #[Given('the order payment is cancelled')]
    public function theOrderPaymentIsCancelled(): void
    {
        $order = $this->getOrderByCurrentTokenValue(state: 'new');

        $payment = $order->getPayments()->last();
        self::assertInstanceOf(Payment::class, $payment);

        $this->stateMachineFactory->get($payment, PaymentTransitions::GRAPH)
            ->apply(PaymentTransitions::TRANSITION_CANCEL);

        $this->entityManager->flush();
    }

    #[Then('the order conversion status should be :status')]
    public function theOrderConversionStatusShouldBe(string $status): void
    {
        $order = $this->getOrderByCurrentTokenValue();
        self::assertEquals($status, $order->getAffiliateConversionStatus()->value);
    }

    #[Given('the order has affiliate id :affiliateId and conversion id :conversionId')]
    public function theOrderHasAffiliateIdAndConversionId(string $affiliateId, string $conversionId): void
    {
        $order = $this->getOrderByCurrentTokenValue(state: 'new');

        $order->setAffiliateId($affiliateId);
        $order->setAffiliateConversionId($conversionId);
        $order->setAffiliateConversionStatus(AffiliateConversionStatus::PENDING);

        $this->entityManager->flush();
    }

    #[Given('the order is cancelled by the system with reason :cancellationReason')]
    public function theOrderIsCancelledByTheSystemWithReason(string $cancellationReason): void
    {
        $order = $this->getOrderByCurrentTokenValue(state: 'new');

        $order->setCancellation(new Cancellation(CancellationBy::SYSTEM, $cancellationReason));

        $this->stateMachineFactory->get($order, SyliusOrderTransitions::GRAPH)
            ->apply(SyliusOrderTransitions::TRANSITION_CANCEL);

        $this->entityManager->flush();
    }

    #[Then('the order should have a consult system reference')]
    public function theOrderShouldHaveConsultSystemReference(): void
    {
        $order = $this->getOrderByCurrentTokenValue();
        self::assertInstanceOf(UuidInterface::class, $order->getConsultSystemReference());
    }

    #[Given('the order has a property :propertyPath with value :expectedValue')]
    #[Then('the order should have a property :propertyPath with value :expectedValue')]
    public function theOrderShouldHaveAPropertyWithValue(string $propertyPath, mixed $expectedValue): void
    {
        $order = $this->getOrderByCurrentTokenValue();

        $actual = $this->propertyAccessor->getValue($order, $propertyPath);
        self::assertEquals(
            $expectedValue,
            $actual,
            "Failed asserting that two string are equal. Expected {$expectedValue}, Actual: {$actual} for property {$propertyPath}"
        );
    }

    #[Then('the order should have a property :propertyPath with a non empty value')]
    public function theOrderHasAPropertyWithANonEmptyValue(string $propertyPath): void
    {
        $order = $this->getOrderByCurrentTokenValue();

        $actual = $this->propertyAccessor->getValue($order, $propertyPath);

        self::assertNotEmpty(
            $actual,
            "Failed asserting that property exists and is not empty. Actual: {$actual} for property {$propertyPath}"
        );
    }

    #[Then('the order sequence is incremented for prefix :prefix with version :version')]
    public function theOrderSequenceIsIncrementedForPrefix(string $prefix, string $version): void
    {
        $orderSequence = $this->entityManager
            ->getRepository(OrderSequence::class)
            ->findOneBy(['prefix' => $prefix]);

        $order = $this->getOrderByCurrentTokenValue();

        self::assertInstanceOf(OrderSequence::class, $orderSequence);
        self::assertSame((int) $order->getNumberWithoutPrefix(), $orderSequence->getIndex());
        self::assertSame((int) $version, $orderSequence->getVersion());
    }

    #[Given('the order has an empty shipment added for supplier :supplierIdentifier and method :shippingMethodCode')]
    public function theOrderHasAnEmptyShipment(string $supplierIdentifier, string $shippingMethodCode): void
    {
        $order = $this->getOrderByCurrentTokenValue();

        $shipment = new Shipment();
        $shipment->setOrder($order);
        $shipment->setSupplier($this->supplierContext->thereIsASupplierWithIdentifier($supplierIdentifier));
        $shipment->setMethod($this->entityManager->getRepository(ShippingMethod::class)->findOneBy([
            'code' => $shippingMethodCode,
        ]));
        $shipment->setState(SyliusShipmentInterface::STATE_CART);

        $order->addShipment($shipment);

        $this->entityManager->persist($shipment);
        $this->entityManager->flush();

        self::assertTrue($order->hasShipment($shipment));
    }

    #[Then('the order should have a property :propertyPath with count :count')]
    public function theOrderShouldHaveAPropertyWithCount(string $propertyPath, int $count): void
    {
        $order = $this->getOrderByCurrentTokenValue();

        $actual = $this->propertyAccessor->getValue($order, $propertyPath);
        if (!$actual instanceof Collection && !is_array($actual) && !is_countable($actual)) {
            self::fail("The property {$propertyPath} should be a collection or array.");
        }

        self::assertCount($count, $actual);
    }

    #[Then('the order should not have a property :propertyPath')]
    public function theOrderShouldNotHaveProperty(string $propertyPath): void
    {
        $order = $this->getOrderByCurrentTokenValue();

        self::assertNull($this->propertyAccessor->getValue($order, $propertyPath));
    }

    #[Then('the doctor is assigned on the order')]
    #[Then('the doctor is assigned on the order with uuid :uuid, name :name and registration number :registrationNumber')]
    public function theDoctorIsAssignedOnTheOrder(?string $uuid = null, ?string $name = null, ?string $registrationNumber = null): void
    {
        $doctor = null;
        if (empty($uuid)) {
            $doctor = $this->contextStorage->get('consultRequestDoctor');
            Assert::assertInstanceOf(Doctor::class, $doctor, 'Expected a Doctor instance in context storage under "consultRequestDoctor".');

            $doctor = [
                'uuid' => $doctor->uuid,
                'name' => $doctor->name,
                'registrationNumber' => $doctor->registrationNumber,
            ];
        }

        if ($doctor === null) {
            $doctor = [
                'uuid' => $uuid,
                'name' => $name,
                'registrationNumber' => $registrationNumber,
            ];
        }

        $this->entityManager->clear();

        $tokenValue = $this->contextStorage->get('tokenValue');
        Assert::assertIsString($tokenValue);

        $order = $this->entityManager->getRepository(Order::class)->findOneBy([
            'tokenValue' => $tokenValue,
        ]);
        Assert::assertInstanceOf(Order::class, $order);

        Assert::assertEquals(
            $doctor['name'],
            $order->getDoctorName()
        );

        Assert::assertEquals(
            $doctor['registrationNumber'],
            $order->getDoctorRegistrationNumber()
        );

        Assert::assertEquals(
            $doctor['uuid'],
            $order->getDoctorUuid()
        );
    }

    /**
     * @Then I should have an audit log with message :message
     */
    public function iShouldHaveAnAuditLogWithMessage(string $message): void
    {
        $this->entityManager->clear();

        $tokenValue = $this->contextStorage->get('tokenValue');
        Assert::assertIsString($tokenValue);

        $auditLogs = $this->entityManager
            ->getRepository(OrderAuditEntry::class)
            ->createQueryBuilder('a')
            ->innerJoin('a.order', 'o')
            ->andWhere('o.tokenValue = :tokenValue')
            ->setParameters([
                'tokenValue' => $tokenValue,
            ])
            ->orderBy('a.createdAt', Criteria::DESC)
            ->getQuery()
            ->getResult();

        foreach ($auditLogs as $auditLog) {
            if ($auditLog->getmessageWithInterpolatedContext() === $message) {
                return;
            }

            $lastMessage = $auditLog->getmessageWithInterpolatedContext();
        }

        $lastMessage ??= 'no audit log found';

        Assert::assertEquals(
            $message,
            $lastMessage,
            "Message should be '{$message}' but is '{$lastMessage}'"
        );
    }

    /**
     * @Then I should not have an audit log
     */
    public function iShouldNotHaveAnAuditLog(): void
    {
        $this->entityManager->clear();

        $tokenValue = $this->contextStorage->get('tokenValue');
        Assert::assertIsString($tokenValue);

        $auditLogs = $this->entityManager
            ->getRepository(OrderAuditEntry::class)
            ->createQueryBuilder('a')
            ->innerJoin('a.order', 'o')
            ->andWhere('o.tokenValue = :tokenValue')
            ->setParameters([
                'tokenValue' => $tokenValue,
            ])
            ->getQuery()
            ->getResult();

        $auditLog = array_values($auditLogs)[0] ?? null;

        Assert::assertEmpty(
            $auditLogs,
            "There should be no audit logs for the order with token value '{$tokenValue}', but found ".count($auditLogs).' audit logs. '.($auditLog ? $auditLog->getMessageWithInterpolatedContext() : 'No audit log found.')
        );
    }

    #[Given('the order is awaiting customer service')]
    public function theOrderIsAwaitingCustomerService(): void
    {
        $tokenValue = $this->contextStorage->get('tokenValue');
        Assert::assertIsString($tokenValue);

        /** @var OrderRepositoryInterface $orderRepository */
        $orderRepository = $this->entityManager->getRepository(Order::class);
        $order = $orderRepository->findOneByTokenValue($tokenValue);
        Assert::assertInstanceOf(Order::class, $order);

        $stateMachine = $this->stateMachineFactory->get($order, OrderPrescriptionTransitions::GRAPH);
        $stateMachine->apply(OrderPrescriptionTransitions::TO_AWAITING_CUSTOMER_SERVICE);

        $order->setConsultSystemReference(Uuid::uuid4());

        $this->entityManager->flush();
    }

    #[Given('the order has postcode :postcode and payment method :paymentMethod')]
    public function theOrderHasPostcodeAndPaymentMethod(string $postcode, string $paymentMethod): void
    {
        $order = $this->getOrderByCurrentTokenValue();

        $order->getShippingAddress()?->setPostcode($postcode);

        $paymentMethod = $this->entityManager
            ->getRepository(PaymentMethod::class)
            ->findOneBy(['code' => $paymentMethod]);

        Assert::assertInstanceOf(PaymentMethodInterface::class, $paymentMethod);

        /** @var PaymentInterface $payment */
        $payment = $order->getPayments()->first();
        $payment->setMethod($paymentMethod);

        $this->entityManager->flush();
    }
}
