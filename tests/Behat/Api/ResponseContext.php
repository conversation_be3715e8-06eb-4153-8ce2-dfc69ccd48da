<?php

declare(strict_types=1);

namespace App\Tests\Behat\Api;

use App\Tests\Behat\ContextStorage;
use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\TableNode;
use Behat\Step\Then;
use Flow\JSONPath\JSONPath;
use <PERSON><PERSON><PERSON>\JsonAssert\JsonAssertions;
use JsonPath\JsonObject;
use PHPUnit\Framework\Assert;
use PHPUnit\Framework\Constraint\IsEqual;
use PHPUnit\Framework\Constraint\LogicalNot;
use RuntimeException;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\BrowserKitAssertionsTrait;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;

final class ResponseContext extends Assert implements Context
{
    use BrowserKitAssertionsTrait;
    use J<PERSON>Assertions;

    public function __construct(
        private readonly ContextStorage $contextStorage,
        private readonly OpenApiContext $openApiContext,
        private readonly PropertyAccessorInterface $propertyAccessor,
    ) {
    }

    /**
     * @Transform /^(false|true)$/
     */
    public function castStringToBoolean(string $string): bool
    {
        return $string === 'true';
    }

    /**
     * @Transform /^(null)$/
     */
    public function castStringToNull(): ?string
    {
        return null;
    }

    /**
     * @Then I should get a valid JSON response with status code :statusCode
     */
    public function iShouldGetAValidJsonResponseWithStatusCode(string $statusCode): void
    {
        $statusCode = (int) $statusCode;

        /** @var KernelBrowser $client */
        $client = $this->contextStorage->get('client');
        Assert::assertInstanceOf(KernelBrowser::class, $client);

        self::getClient($client);

        self::assertResponseStatusCodeSame($statusCode);

        $response = $client->getResponse();
        if ($response instanceof StreamedResponse) {
            return;
        }

        $content = (string) $client->getResponse()->getContent();
        self::assertJson($content);

        $this->openApiContext->theResponseValidatesWithTheOpenapiSchema();
    }

    /**
     * @Then I should have a JSON response property :jsonPropertyPath with value :jsonPropertyValue
     * @Then I should have a JSON response property :jsonPropertyPath with the value :jsonPropertyValue
     */
    public function iShouldHaveAResponsePropertyWithValue(string $jsonPropertyPath, mixed $jsonPropertyValue): void
    {
        /** @var KernelBrowser $client */
        $client = $this->contextStorage->get('client');
        Assert::assertInstanceOf(KernelBrowser::class, $client);

        $response = $client->getResponse()->getContent();

        self::assertJsonValueEquals($response, $jsonPropertyPath, $jsonPropertyValue);
    }

    /**
     * @Then I should have a JSON response property :jsonPropertyPath
     */
    public function iShouldHaveAResponseProperty(string $jsonPropertyPath): void
    {
        /** @var KernelBrowser $client */
        $client = $this->contextStorage->get('client');
        Assert::assertInstanceOf(KernelBrowser::class, $client);

        $response = $client->getResponse()->getContent();

        self::assertNotEmpty($response, $jsonPropertyPath);
    }

    /**
     * @Then I should not have a JSON response property :jsonPropertyPath
     */
    public function iShouldNotHaveAResponseProperty(string $jsonPropertyPath): void
    {
        /** @var KernelBrowser $client */
        $client = $this->contextStorage->get('client');
        Assert::assertInstanceOf(KernelBrowser::class, $client);

        Assert::assertCount(
            0,
            (new JSONPath($client->getResponse()->getContent()))->find($jsonPropertyPath)
        );
    }

    /**
     * @Then I should not have a JSON response property :jsonPropertyPath with the value :jsonPropertyValue
     */
    public function iShouldNotHaveAResponsePropertyWithValue(string $jsonPropertyPath, string $jsonPropertyValue): void
    {
        /** @var KernelBrowser $client */
        $client = $this->contextStorage->get('client');
        Assert::assertInstanceOf(KernelBrowser::class, $client);

        self::assertAllJsonValuesMatch(
            $client->getResponse()->getContent(),
            $jsonPropertyPath,
            new LogicalNot(
                new IsEqual($jsonPropertyValue)
            )
        );
    }

    #[Then('I should have a JSON response property :jsonPropertyPath with the value items :jsonPropertyItems')]
    public function iShouldHaveAJsonResponsePropertyWithValueItems(string $jsonPropertyPath, string $jsonPropertyItems): void
    {
        $expectedItems = array_map('trim', explode(',', $jsonPropertyItems));

        /** @var KernelBrowser $client */
        $client = $this->contextStorage->get('client');
        Assert::assertInstanceOf(KernelBrowser::class, $client);

        $responseBody = $client->getResponse()->getContent();
        self::assertNotEmpty($responseBody);

        $responseData = json_decode($responseBody, false, 512, JSON_THROW_ON_ERROR);

        $actual = (new JSONPath($responseData, true))->find($jsonPropertyPath);

        foreach ($expectedItems as $expectedItem) {
            self::assertContains($expectedItem, $actual->getData());
        }
    }

    /**
     * @Then I should have a JSON response count for :jsonPropertyPath of :expectedCount
     */
    public function iShouldHaveAResponsePropertyCount(string $jsonPropertyPath, int $expectedCount): void
    {
        /** @var KernelBrowser $client */
        $client = $this->contextStorage->get('client');
        Assert::assertInstanceOf(KernelBrowser::class, $client);

        $responseBody = $client->getResponse()->getContent();
        self::assertNotEmpty($responseBody);

        $responseData = json_decode($responseBody, false, 512, JSON_THROW_ON_ERROR);

        $actual = (new JSONPath($responseData, true))->find($jsonPropertyPath);

        self::assertSame($expectedCount, $actual->count());
    }

    /**
     * @Then I should have a JSON response property :jsonPropertyPath with the value of :contextStorageKey from the context
     */
    public function iShouldHaveAJSONResponsePropertyWithTheValueOfFromTheContext(
        string $jsonPropertyPath,
        string $contextStorageKey,
    ): void {
        /** @var KernelBrowser $client */
        $client = $this->contextStorage->get('client');
        Assert::assertInstanceOf(KernelBrowser::class, $client);

        $value = $this->contextStorage->get($contextStorageKey);

        self::assertJsonValueEquals($client->getResponse()->getContent(), $jsonPropertyPath, $value);
    }

    /**
     * @Then There should be the following properties in the response JSON:
     * @Then There should be the following properties at :propertyPath in the response JSON:
     */
    public function thereShouldBeTheFollowingPropertiesInTheResponseJson(
        TableNode $table,
        ?string $propertyPath = null,
    ): void {
        /** @var KernelBrowser $client */
        $client = $this->contextStorage->get('client');
        Assert::assertInstanceOf(KernelBrowser::class, $client);

        $responseContent = json_decode((string) $client->getResponse()->getContent(), true);
        if (is_string($propertyPath)) {
            $responseContent = $this->getPropertyByKeyOrPropertyAccess($propertyPath, $responseContent);
        }

        foreach ($table->getHash() as $tableRow) {
            $actualValue = $this->getPropertyByKeyOrPropertyAccess($tableRow['property'], $responseContent);

            Assert::assertSame(
                $this->convertType($tableRow['type'], $tableRow['value']),
                $this->convertType($tableRow['type'], $actualValue),
                sprintf("Failed asserting that value '%s' equals '%s' at property path '%s'.", $actualValue, $tableRow['value'], $tableRow['property'])
            );
        }
    }

    /**
     * @Then The response should return the following sort order :sortOrder for :jsonPropertyPath
     */
    public function theResponseShouldReturnTheFollowingSortOrder(string $sortOrder, string $jsonPropertyPath): void
    {
        $client = $this->contextStorage->get('client');
        Assert::assertInstanceOf(KernelBrowser::class, $client);

        $responseContent = json_decode($client->getResponse()->getContent(), true);

        $jsonPathData = (new JsonObject($responseContent, true))->get($jsonPropertyPath);

        if (empty($jsonPathData)) {
            throw new RuntimeException(sprintf('No data found for JSON path "%s"', $jsonPropertyPath));
        }

        $actualItems = $expectedItems = $jsonPathData;

        match ($sortOrder) {
            'asc', 'ascending' => sort($expectedItems),
            'desc', 'descending' => rsort($expectedItems),
            default => throw new RuntimeException(sprintf('Unknown sort order "%s"', $sortOrder)),
        };

        Assert::assertSame(
            $expectedItems,
            $actualItems,
            implode(
                PHP_EOL,
                [
                    sprintf('Expected item order: %s', json_encode($expectedItems, JSON_PRETTY_PRINT)),
                    sprintf('Actual item order: %s', json_encode($actualItems, JSON_PRETTY_PRINT)),
                ]
            )
        );
    }

    #[Then('I should get a JSON response with status code :statusCode')]
    public function iShouldGetAJsonResponseWithStatusCode(string $statusCode): void
    {
        $statusCode = (int) $statusCode;

        /** @var KernelBrowser $client */
        $client = $this->contextStorage->get('client');
        Assert::assertInstanceOf(KernelBrowser::class, $client);

        self::getClient($client);
        self::assertResponseStatusCodeSame($statusCode);
        self::assertJson((string) $client->getResponse()->getContent());
    }

    private function convertType(string $type, string|int|bool $value): string|bool|int
    {
        if (empty($type) || !is_string($value)) {
            return $value;
        }

        return match ($type) {
            'bool', 'boolean' => $value !== 'false',
            'int', 'integer' => (int) $value,
            default => $value,
        };
    }

    private function getPropertyByKeyOrPropertyAccess(string $propertyPath, array $responseContent): mixed
    {
        if (array_key_exists($propertyPath, $responseContent)) {
            return $responseContent[$propertyPath];
        }

        return $this->propertyAccessor->getValue($responseContent, $propertyPath);
    }
}
