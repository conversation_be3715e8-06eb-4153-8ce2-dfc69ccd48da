<?php

declare(strict_types=1);

namespace App\Tests\Logging\Sentry;

use App\Logging\Sentry\DBALDataScrubber;
use App\Logging\Sentry\SentryScrubberInterface;
use Doctrine\DBAL\Exception;
use PHPUnit\Framework\TestCase;
use RuntimeException;
use Sentry\Event;
use Sentry\EventHint;
use Sentry\ExceptionDataBag;
use Sentry\Frame;
use Sentry\Stacktrace;

final class DBALDataScrubberTest extends TestCase
{
    private readonly DBALDataScrubber $scrubber;

    protected function setUp(): void
    {
        $this->scrubber = new DBALDataScrubber();
    }

    public function testItScrubsSensitiveData(): void
    {
        // Arrange
        $frame = new Frame(
            'testFunctionName',
            'test-file.php',
            1337,
            'rawTestFunctionName',
            '/absolute/file/path',
        );

        $stacktrace = new Stacktrace([$frame]);

        $previousException = new Exception(
            'SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry \'00001\' for key \'UNIQ_6196A1F9BEA95C75\''
        );

        $firstException = new Exception(
            'Uncaught PHP Exception Nijens\OpenapiBundle\ExceptionHandling\Exception\ProblemException: "An exception occurred while executing \'INSERT INTO sylius_order (number, notes, state) VALUES (?, ?, ?)\' with params [\'00001\', \'take one pill a day\', \'new\']:

            SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry \'00001\' for key \'UNIQ_6196A1F9BEA95C75\'" at /var/www/vendor/nijens/openapi-bundle/src/ExceptionHandling/Exception/ProblemException.php line 184',
            previous: $previousException
        );

        $previousDataBag = new ExceptionDataBag($previousException);
        $exceptionDataBag = new ExceptionDataBag($firstException, $stacktrace);

        $event = Event::createEvent();
        $event->setMessage($exceptionDataBag->getValue());
        $event->setStacktrace($stacktrace);
        $event->setExceptions([$exceptionDataBag, $previousDataBag]);

        // Act
        $this->scrubber->scrub($event, EventHint::fromArray(['exception' => $firstException]));

        // Assert
        $frame = $event->getStacktrace()?->getFrame(0);
        $this->assertInstanceOf(Frame::class, $frame);

        $exceptions = $event->getExceptions();
        $this->assertCount(2, $exceptions);
        foreach ($exceptions as $exception) {
            self::assertStringNotContainsString('00001', $exception->getValue());
            self::assertStringNotContainsString('take one pill a day', $exception->getValue());
            self::assertStringNotContainsString('new', $exception->getValue());
        }
    }

    public function testItDoesntScrubsDataWhenNoDBALExceptionIsGiven(): void
    {
        // Arrange
        $frame = new Frame(
            'testFunctionName',
            'test-file.php',
            1337,
            'rawTestFunctionName',
            '/absolute/file/path',
        );

        $stacktrace = new Stacktrace([$frame]);

        $previousException = new RuntimeException(
            'SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry \'00001\' for key \'UNIQ_6196A1F9BEA95C75\''
        );

        $firstException = new RuntimeException(
            'Uncaught PHP Exception Nijens\OpenapiBundle\ExceptionHandling\Exception\ProblemException: "An exception occurred while executing \'INSERT INTO sylius_order (number, notes, state) VALUES (?, ?, ?)\' with params [\'00001\', \'take one pill a day\', \'new\']:

            SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry \'00001\' for key \'UNIQ_6196A1F9BEA95C75\'" at /var/www/vendor/nijens/openapi-bundle/src/ExceptionHandling/Exception/ProblemException.php line 184',
            previous: $previousException
        );

        $previousDataBag = new ExceptionDataBag($previousException);
        $exceptionDataBag = new ExceptionDataBag($firstException, $stacktrace);

        $event = Event::createEvent();
        $event->setMessage($exceptionDataBag->getValue());
        $event->setStacktrace($stacktrace);
        $event->setExceptions([$exceptionDataBag, $previousDataBag]);

        // Act
        $this->scrubber->scrub($event, EventHint::fromArray(['exception' => $firstException]));

        // Assert
        $frame = $event->getStacktrace()?->getFrame(0);
        $this->assertInstanceOf(Frame::class, $frame);

        $exceptions = $event->getExceptions();
        $this->assertCount(2, $exceptions);
        foreach ($exceptions as $exception) {
            self::assertStringNotContainsString(SentryScrubberInterface::DEFAULT_FILTERED_REPLACEMENT, $exception->getValue());
        }
    }
}
