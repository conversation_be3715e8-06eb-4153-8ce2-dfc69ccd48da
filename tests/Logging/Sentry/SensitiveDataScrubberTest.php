<?php

declare(strict_types=1);

namespace App\Tests\Logging\Sentry;

use App\Logging\Sentry\SensitiveDataScrubber;
use LogicException;
use PHPUnit\Framework\TestCase;
use Sentry\Event;
use Sentry\ExceptionDataBag;
use Sentry\Frame;
use Sentry\Stacktrace;

final class SensitiveDataScrubberTest extends TestCase
{
    private readonly SensitiveDataScrubber $scrubber;

    protected function setUp(): void
    {
        $this->scrubber = new SensitiveDataScrubber();
    }

    public function testItScrubsSensitiveData(): void
    {
        // Arrange
        $frame = new Frame(
            'testFunctionName',
            'test-file.php',
            1337,
            'rawTestFunctionName',
            '/absolute/file/path',
        );

        $frame->setVars(['password' => 'Th1s!sR34lly5ens1t1v3D4t@', 'some-key' => ['arrays are not supported']]);
        $expectedVars = ['password' => '[Filtered]', 'some-key' => ['arrays are not supported']];

        $frame->setPreContext(['some-key' => 'This string contains Th1s!sR34lly5ens1t1v3D4t@']);
        $expectedPreContext = ['some-key' => '[Filtered]'];

        $frame->setPostContext(['some-other-key' => 'This string contains Th1s!sR34lly5ens1t1v3D4t@']);
        $expectedPostContext = ['some-other-key' => '[Filtered]'];

        $stacktrace = new Stacktrace([$frame]);

        $exception = new ExceptionDataBag(new LogicException('message'), $stacktrace);

        $event = Event::createEvent();
        $event->setStacktrace($stacktrace);
        $event->setExceptions([$exception]);

        // Act
        $this->scrubber->scrub($event);

        // Assert
        $frame = $event->getStacktrace()?->getFrame(0);
        $this->assertInstanceOf(Frame::class, $frame);
        $this->assertSame($expectedVars, $frame->getVars());
        $this->assertSame($expectedPreContext, $frame->getPreContext());
        $this->assertSame($expectedPostContext, $frame->getPostContext());

        /** @var Frame $exceptionFrame */
        $exceptionFrame = $event->getExceptions()[0]->getStacktrace()?->getFrame(0);
        $this->assertSame($expectedVars, $exceptionFrame->getVars());
        $this->assertSame($expectedPreContext, $exceptionFrame->getPreContext());
        $this->assertSame($expectedPostContext, $exceptionFrame->getPostContext());
    }
}
