<?php

declare(strict_types=1);

namespace App\Tests\Logging\Sentry\EventSubscriber;

use App\Logging\Sentry\EventSubscriber\CommandTracingSubscriber;
use App\Tests\Mocks\SentryTracingCommand;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Sentry\State\HubInterface;
use Symfony\Component\EventDispatcher\EventDispatcher;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;

class SentryCommandTracingSubscriberTest extends TestCase
{
    private EventDispatcherInterface $eventDispatcher;
    private HubInterface&MockObject $hub;
    private LoggerInterface&MockObject $logger;
    private CommandTracingSubscriber $sentryCommandTracingSubscriber;

    public function setUp(): void
    {
        $this->eventDispatcher = new EventDispatcher();
        $this->hub = $this->createMock(HubInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->sentryCommandTracingSubscriber = new CommandTracingSubscriber($this->hub, $this->logger, [
            SentryTracingCommand::class => '1337',
        ]);
        $this->eventDispatcher->addSubscriber($this->sentryCommandTracingSubscriber);
    }

    public function testGetSubscribedEventsCallsExistingMethods(): void
    {
        /** @var array{array-key, 0: string, 1: int} $subscribedEvent */
        foreach ($this->sentryCommandTracingSubscriber::getSubscribedEvents() as $subscribedEvent) {
            self::assertTrue(method_exists($this->sentryCommandTracingSubscriber::class, reset($subscribedEvent)));
        }
    }
}
