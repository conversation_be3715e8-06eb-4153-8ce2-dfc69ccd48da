<?php

declare(strict_types=1);

namespace App\Tests\Repository;

use App\Entity\Product\ProductTranslation;
use App\Repository\ProductTranslationRepository;
use Doctrine\ORM\EntityManagerInterface;
use Generator;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ProductTranslationRepositoryTest extends KernelTestCase
{
    private ProductTranslationRepository $productTranslationRepository;

    protected function setUp(): void
    {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = static::getContainer()->get(EntityManagerInterface::class);

        /** @var ProductTranslationRepository $productTranslationRepository */
        $productTranslationRepository = $entityManager->getRepository(ProductTranslation::class);
        $this->productTranslationRepository = $productTranslationRepository;
    }

    public function testFindOneBySlugAndLocaleReturnsNullResult(): void
    {
        self::assertNull(
            $this->productTranslationRepository->findOneBySlugAndLocale('consult_hemorrhoids', 'nl')
        );
    }

    /**
     * @return Generator<string, array{string, string, string}>
     */
    public function provideSlugWithLocaleAndExpectedSlug(): iterable
    {
        yield 'no incremented slug' => [
            'consult-aambeien',
            'nl',
            'consult-aambeien-2',
        ];

        yield 'incremented slug' => [
            'consult-aambeien-1',
            'nl',
            'consult-aambeien-1',
        ];
    }
}
