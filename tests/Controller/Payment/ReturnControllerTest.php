<?php

declare(strict_types=1);

namespace App\Tests\Controller\Payment;

use App\Entity\Addressing\Country;
use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Channel\Channel;
use App\Entity\Order\Order;
use App\Entity\Payment\Payment;
use App\Payum\Controller\ReturnController;
use App\Payum\RedirectUrlGeneratorInterface;
use App\Payum\Request\CompleteAuthorize;
use App\Tests\Mocks\Entity\TestOrder;
use Doctrine\ORM\EntityManager;
use Payum\Core\GatewayInterface;
use Payum\Core\Payum;
use Payum\Core\Security\HttpRequestVerifierInterface;
use Payum\Core\Security\TokenInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Bundle\PayumBundle\Request\GetStatus;
use Sylius\Component\Payment\Model\PaymentInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;

class ReturnControllerTest extends TestCase
{
    private Payum&MockObject $payumMock;

    private RedirectUrlGeneratorInterface&MockObject $redirectUrlGeneratorMock;

    private EntityManager&MockObject $entityManager;

    private ReturnController $controller;

    protected function setUp(): void
    {
        $this->payumMock = $this->createMock(Payum::class);
        $this->redirectUrlGeneratorMock = $this->createMock(RedirectUrlGeneratorInterface::class);
        $this->entityManager = $this->createMock(EntityManager::class);

        $this->controller = new ReturnController(
            $this->payumMock,
            $this->redirectUrlGeneratorMock,
            $this->entityManager,
        );
    }

    /**
     * @dataProvider paymentStatusProvider
     */
    public function testInvokeReturnsRedirectResponse(
        string $expectedPaymentStatus,
        string $expectedUrl,
    ): void {
        $httpRequestVerifierMock = $this->createMock(HttpRequestVerifierInterface::class);

        $this->entityManager
            ->expects(self::once())
            ->method('beginTransaction');

        $this->payumMock
            ->expects(self::exactly(2))
            ->method('getHttpRequestVerifier')
            ->willReturn($httpRequestVerifierMock);

        $requestMock = $this->createMock(Request::class);
        $tokenMock = $this->createMock(TokenInterface::class);

        $httpRequestVerifierMock->expects(self::once())
            ->method('verify')
            ->with($requestMock)
            ->willReturn($tokenMock);

        $gatewayMock = $this->createMock(GatewayInterface::class);

        $gatewayName = 'payment-service';

        $tokenMock->expects(self::once())
            ->method('getGatewayName')
            ->willReturn($gatewayName);

        $this->payumMock
            ->expects(self::once())
            ->method('getGateway')
            ->with($gatewayName)
            ->willReturn($gatewayMock);

        $gatewayMock->expects(self::exactly(2))
            ->method('execute')
            ->willReturnCallback(function ($argument) use ($expectedPaymentStatus) {
                static $callCount = 0;
                ++$callCount;

                if ($callCount === 1) {
                    self::assertInstanceOf(CompleteAuthorize::class, $argument);

                    return;
                }

                if ($callCount === 2) {
                    self::assertInstanceOf(GetStatus::class, $argument);

                    $order = $this->createOrder();

                    $payment = new Payment();
                    $payment->setState($expectedPaymentStatus);
                    $payment->setOrder($order);
                    $payment->addDetails(['payment_started_from' => 'checkout']);

                    $argument->setModel($payment);
                }
            });

        $httpRequestVerifierMock->expects(self::once())
            ->method('invalidate')
            ->with($tokenMock);

        $this->redirectUrlGeneratorMock
            ->expects(self::once())
            ->method('generateReturnUrl')
            ->willReturn($expectedUrl);

        $expectedResponse = new RedirectResponse($expectedUrl);

        $actualResponse = ($this->controller)($requestMock);

        self::assertEquals($expectedResponse->getContent(), $actualResponse->getContent());
        self::assertSame($expectedResponse->getStatusCode(), $actualResponse->getStatusCode());
    }

    /**
     * @return iterable<string, array<string>>
     */
    public function paymentStatusProvider(): iterable
    {
        yield 'PaymentInterface::STATE_PROCESSING returns RedirectResponse' => [
            PaymentInterface::STATE_PROCESSING,
            'http://localhost/en-gb/redirect?name=checkout.payment-status&id=1337&tokenValue=abc123&paymentState=processing',
        ];

        yield 'PaymentInterface::STATE_AUTHORIZED returns RedirectResponse' => [
            PaymentInterface::STATE_AUTHORIZED,
            'http://localhost/en-gb/redirect?name=checkout.payment-status&id=1337&tokenValue=abc123&paymentState=authorized',
        ];

        yield 'PaymentInterface::STATE_COMPLETED returns RedirectResponse' => [
            PaymentInterface::STATE_COMPLETED,
            'http://localhost/en-gb/redirect?name=checkout.payment-status&id=1337&tokenValue=abc123&paymentState=completed',
        ];

        yield 'PaymentInterface::STATE_CART returns RedirectResponse' => [
            PaymentInterface::STATE_CART,
            'http://localhost/en-gb/redirect?name=checkout.payment-status&id=1337&tokenValue=abc123&paymentState=cart',
        ];

        yield 'PaymentInterface::STATE_NEW returns RedirectResponse' => [
            PaymentInterface::STATE_NEW,
            'http://localhost/en-gb/redirect?name=checkout.payment-status&id=1337&tokenValue=abc123&paymentState=new',
        ];

        yield 'PaymentInterface::STATE_CANCELLED returns RedirectResponse' => [
            PaymentInterface::STATE_CANCELLED,
            'http://localhost/en-gb/redirect?name=checkout.payment-status&id=1337&tokenValue=abc123&paymentState=cancelled',
        ];
    }

    private function createOrder(): Order
    {
        $businessUnit = new BusinessUnit();
        $businessUnit->setUrl('http://localhost');

        $country = new Country();
        $country->setCode('GB');

        $channel = new Channel();
        $channel->setBusinessUnit($businessUnit);
        $channel->addCountry($country);

        $order = new TestOrder();
        $order->setId(1337);
        $order->setChannel($channel);
        $order->setTokenValue('abc123');
        $order->setLocaleCode('en');

        return $order;
    }
}
