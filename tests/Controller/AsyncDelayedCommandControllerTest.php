<?php

declare(strict_types=1);

namespace App\Tests\Controller;

use App\Api\Command\AsyncDelayedCommandInterface;
use App\Api\Controller\AsyncDelayedCommandController;
use Nijens\OpenapiBundle\Serialization\SerializationContextBuilderInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Cache\CacheItemPoolInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DelayStamp;
use Symfony\Component\Serializer\SerializerInterface;

final class AsyncDelayedCommandControllerTest extends TestCase
{
    private AsyncDelayedCommandController $commandController;

    private MessageBusInterface&MockObject $messageBusMock;

    private RequestStack&MockObject $requestStackMock;

    protected function setUp(): void
    {
        $this->messageBusMock = $this->createMock(MessageBusInterface::class);
        $this->requestStackMock = $this->createMock(RequestStack::class);

        $this->commandController = new AsyncDelayedCommandController(
            $this->createStub(SerializationContextBuilderInterface::class),
            $this->createStub(SerializerInterface::class),
            $this->createStub(CacheItemPoolInterface::class),
            $this->messageBusMock,
            $this->requestStackMock
        );
    }

    public function testInvokeAddsDelayStampToMessage(): void
    {
        // Arrange
        $expectedDelayInSeconds = 1337;

        $command = $this->createStub(AsyncDelayedCommandInterface::class);
        $command->method('getDelayInSeconds')
            ->willReturn($expectedDelayInSeconds);

        $requestMock = $this->createMock(Request::class);
        $requestMock->attributes = $this->createStub(ParameterBagInterface::class);

        $this->requestStackMock->method('getCurrentRequest')
            ->willReturn($requestMock);

        // Assert
        $this->messageBusMock->expects(self::once())
            ->method('dispatch')
            ->with(
                $command,
                [
                    new DelayStamp($expectedDelayInSeconds * 1000),
                ],
            )
            ->willReturn(new Envelope($command));

        // Act
        ($this->commandController)($requestMock, $command);
    }
}
