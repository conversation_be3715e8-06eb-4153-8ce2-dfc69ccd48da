<?php

declare(strict_types=1);

namespace App\Tests\Controller;

use App\Controller\FrontendController;
use PHPUnit\Framework\TestCase;
use Twig\Environment;

class FrontendControllerTest extends TestCase
{
    private Environment $twigEnvironmentMock;
    private FrontendController $controller;

    protected function setUp(): void
    {
        $this->twigEnvironmentMock = $this->createMock(Environment::class);

        $this->controller = new FrontendController($this->twigEnvironmentMock);
    }

    public function testInvokeReturnsResponse(): void
    {
        $this->twigEnvironmentMock
            ->expects(self::once())
            ->method('render')
            ->with('app.html.twig');

        ($this->controller)();
    }
}
