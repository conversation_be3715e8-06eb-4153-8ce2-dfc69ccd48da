<?php

declare(strict_types=1);

namespace App\Tests\Factory;

use App\Entity\Order\Adjustment;
use App\Entity\Supplier\Supplier;
use App\Entity\Supplier\SupplierCountryShipping;
use App\Factory\Order\AdjustmentFactory;
use PHPUnit\Framework\TestCase;
use Sylius\Resource\Factory\Factory;

class AdjustmentFactoryTest extends TestCase
{
    private AdjustmentFactory $adjustmentFactory;

    protected function setUp(): void
    {
        $this->adjustmentFactory = new AdjustmentFactory(
            new Factory(Adjustment::class)
        );
    }

    public function testCanCreateForCostPrice(): void
    {
        $actualAdjustment = $this->adjustmentFactory->createForCostPrice(
            'test_label',
            100,
            ['testKey' => 'Value'],
        );

        $expectedAdjustment = new Adjustment();
        $expectedAdjustment->setCostPriceAmount(100);
        $expectedAdjustment->setLabel('test_label');
        $expectedAdjustment->setDetails(['testKey' => 'Value']);
        $expectedAdjustment->setType(Adjustment::COST_PRICE_ADJUSTMENT);

        // remove time sensitivity
        $actualAdjustment->setCreatedAt(null);
        $expectedAdjustment->setCreatedAt(null);

        self::assertEquals($expectedAdjustment, $actualAdjustment);
    }

    public function testCanCreateHandlingFeeCostPriceAdjustment(): void
    {
        $supplier = $this->createMock(Supplier::class);
        $supplier->method('getHandlingFee')
            ->willReturn(100);
        $supplier->method('getId')
            ->willReturn(1);

        $actualAdjustment = $this->adjustmentFactory->createHandlingFeeAdjustment($supplier);

        $expectedAdjustment = new Adjustment();
        $expectedAdjustment->setType(Adjustment::COST_PRICE_ADJUSTMENT);
        $expectedAdjustment->setLabel(Adjustment::HANDLING_FEE_LABEL);
        $expectedAdjustment->setCostPriceAmount(100);
        $expectedAdjustment->setDetails(['supplierId' => 1]);

        // remove time sensitivity
        $actualAdjustment->setCreatedAt(null);
        $expectedAdjustment->setCreatedAt(null);

        self::assertEquals($expectedAdjustment, $actualAdjustment);
    }

    public function testCreateShippingCostAdjustment(): void
    {
        $supplier = $this->createMock(Supplier::class);
        $supplier->method('getId')
            ->willReturn(1);

        $supplierCountryShipping = $this->createMock(SupplierCountryShipping::class);
        $supplierCountryShipping->method('getShippingCost')
            ->willReturn(200);
        $supplierCountryShipping->method('getSupplier')
            ->willReturn($supplier);

        $actualAdjustment = $this->adjustmentFactory->createShippingCostAdjustment($supplierCountryShipping);

        $expectedAdjustment = new Adjustment();
        $expectedAdjustment->setType(Adjustment::COST_PRICE_ADJUSTMENT);
        $expectedAdjustment->setLabel(Adjustment::SHIPPING_LABEL);
        $expectedAdjustment->setCostPriceAmount(200);
        $expectedAdjustment->setDetails(['supplierId' => 1]);

        // remove time sensitivity
        $actualAdjustment->setCreatedAt(null);
        $expectedAdjustment->setCreatedAt(null);

        self::assertEquals($expectedAdjustment, $actualAdjustment);
    }

    public function testCreateTaxRateAdjustment(): void
    {
        $supplier = $this->createMock(Supplier::class);
        $supplier->method('getId')
            ->willReturn(1);

        $supplierCountryShipping = $this->createMock(SupplierCountryShipping::class);
        $supplierCountryShipping->method('getTaxRateInPercentage')
            ->willReturn(20.0);
        $supplierCountryShipping->method('getSupplier')
            ->willReturn($supplier);

        $productVariantCostPrice = 1000;

        $actualAdjustment = $this->adjustmentFactory
            ->createTaxRateAdjustment($supplierCountryShipping, $productVariantCostPrice);

        $expectedAdjustment = new Adjustment();
        $expectedAdjustment->setType(Adjustment::COST_PRICE_ADJUSTMENT);
        $expectedAdjustment->setLabel(Adjustment::ORDER_ITEM_UNIT_TAX_LABEL);
        $expectedAdjustment->setCostPriceAmount((int) round((20 / 100) * $productVariantCostPrice));
        $expectedAdjustment->setDetails([
            'supplierId' => 1,
            'taxRateInPercentage' => 20,
        ]);

        // remove time sensitivity
        $actualAdjustment->setCreatedAt(null);
        $expectedAdjustment->setCreatedAt(null);

        self::assertEquals($expectedAdjustment, $actualAdjustment);
    }
}
