<?php

declare(strict_types=1);

namespace App\Tests\Factory\Order;

use App\Admin\Form\OrderItemRefundReasons;
use App\Entity\Order\OrderItem;
use App\Entity\Refund\RefundPayment;
use App\Factory\Order\OrderItemRefundFactory;
use App\Factory\Order\OrderItemRefundFactoryInterface;
use PHPUnit\Framework\TestCase;

final class OrderItemRefundFactoryTest extends TestCase
{
    private OrderItemRefundFactoryInterface $factory;

    protected function setUp(): void
    {
        $this->factory = new OrderItemRefundFactory();
    }

    public function testCreate(): void
    {
        // Arrange
        $orderItem = $this->createStub(OrderItem::class);
        $refundPayment = $this->createStub(RefundPayment::class);

        // Act
        $orderItemRefund = $this->factory->create(
            $orderItem,
            $refundPayment,
            OrderItemRefundReasons::OutOfStock,
        );

        // Assert
        self::assertSame($orderItem, $orderItemRefund->getOrderItem());
        self::assertSame($refundPayment, $orderItemRefund->getRefundPayment());
        self::assertSame(OrderItemRefundReasons::OutOfStock->value, $orderItemRefund->getReason());
    }
}
