<?php

declare(strict_types=1);

namespace App\Tests\Factory;

use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Order\OrderSequence;
use App\Factory\Order\OrderSequenceFactory;
use App\Tests\Util\Factory\BusinessUnitFactory;
use PHPUnit\Framework\TestCase;

class OrderSequenceFactoryTest extends TestCase
{
    private OrderSequenceFactory $orderSequenceFactory;

    protected function setUp(): void
    {
        $this->orderSequenceFactory = new OrderSequenceFactory();
    }

    public function testCanCreateNew(): void
    {
        $expectedSequence = new OrderSequence();
        $actualSequence = $this->orderSequenceFactory->createNew();

        self::assertEquals($expectedSequence, $actualSequence);
    }

    /**
     * @dataProvider createNewFromBusinessUnitProvider
     */
    public function testCanCreateNewFromBusinessUnit(BusinessUnit $businessUnit, ?int $initialIndex): void
    {
        $orderSequence = $this->orderSequenceFactory->createNewForBusinessUnit($businessUnit, $initialIndex);

        self::assertSame($initialIndex ?? 0, $orderSequence->getIndex());
    }

    /**
     * @return iterable<array{0: BusinessUnit, 1: int|null}>
     */
    public function createNewFromBusinessUnitProvider(): iterable
    {
        $businessUnit = BusinessUnitFactory::create();

        yield [$businessUnit, null];
        yield [$businessUnit, 2000];
    }
}
