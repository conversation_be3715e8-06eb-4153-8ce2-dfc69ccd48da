<?php

declare(strict_types=1);

namespace App\Tests\Factory;

use App\Entity\Customer\Customer;
use App\Factory\Customer\CustomerFactory;
use PHPUnit\Framework\TestCase;
use stdClass;
use Sylius\Resource\Factory\FactoryInterface;
use Webmozart\Assert\InvalidArgumentException;

class CustomerFactoryTest extends TestCase
{
    private CustomerFactory $customerFactory;
    private FactoryInterface $customerFactoryMock;

    protected function setUp(): void
    {
        $this->customerFactoryMock = $this->createMock(FactoryInterface::class);
        $this->customerFactory = new CustomerFactory($this->customerFactoryMock);
    }

    public function testCreateNewCreatesANewCustomer(): void
    {
        $this->customerFactoryMock->expects($this->once())
            ->method('createNew')
            ->willReturn(new Customer());

        $order = $this->customerFactory->createNew();

        $this->assertInstanceOf(Customer::class, $order);
    }

    public function testCreateNewGivesErrorWhenFactoryDoesntCreateACustomer(): void
    {
        $this->customerFactoryMock->expects($this->once())
            ->method('createNew')
            ->willReturn(new stdClass());

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage(sprintf('Expected an instance of %s. Got: %s', Customer::class, stdClass::class));

        $this->customerFactory->createNew();
    }
}
