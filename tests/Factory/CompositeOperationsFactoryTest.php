<?php

declare(strict_types=1);

namespace App\Tests\Factory;

use App\Api\Command\Admin\Order\AddOrderItemOperation;
use App\Api\Command\Admin\Order\CompositeOrderItemOperations;
use App\Api\Command\Admin\Order\RemoveOrderItemOperation;
use App\Api\Command\Admin\Order\UpdateOrderItemOperation;
use App\ConsultSystem\CompositeOrderItemOperationsBuilder;
use App\Tests\Util\Factory\OrderFactory;
use PHPUnit\Framework\TestCase;
use SuperBrave\ConsultSystemClient\Model\Enum\ProductType;
use SuperBrave\ConsultSystemClient\Model\Item;
use SuperBrave\ConsultSystemClient\Model\ParentItem;
use SuperBrave\ConsultSystemClient\Model\Price;

class CompositeOperationsFactoryTest extends TestCase
{
    private CompositeOrderItemOperationsBuilder $compositeOperationsFactory;

    protected function setUp(): void
    {
        $this->compositeOperationsFactory = new CompositeOrderItemOperationsBuilder();
    }

    public function testItCanGenerateRemoveOperations(): void
    {
        $order = OrderFactory::createPrefilled();
        $expectedCompositeOrderItemOperations = new CompositeOrderItemOperations([
            new RemoveOrderItemOperation(
                2,
                '7_25_test-variant-code-medication',
            ),
        ]);

        $expectedCompositeOrderItemOperations->setOrder($order);

        $compositeOrderItemOperations = $this->compositeOperationsFactory->buildCompositeOperationsByItems(
            [
                new Item(
                    '7_25_test-variant-code-consult',
                    'Consult product',
                    false,
                    ProductType::CONSULT,
                    null,
                    1,
                    new Price(200, 'EUR'),
                    null
                ),
            ],
            $order
        );

        $this->assertEquals($expectedCompositeOrderItemOperations, $compositeOrderItemOperations);
    }

    public function testItCanGenerateAddOperations(): void
    {
        $order = OrderFactory::createPrefilled();

        $expectedCompositeOrderItemOperations = new CompositeOrderItemOperations([
            new AddOrderItemOperation(
                'One a day',
                '7_26_test-variant-code-medication',
                10,
                '7_25_test-variant-code-consult'
            ),
        ]);

        $expectedCompositeOrderItemOperations->setOrder($order);

        $definitiveItems = [
            new Item(
                '7_25_test-variant-code-consult',
                'Consult product',
                false,
                ProductType::CONSULT,
                null,
                1,
                new Price(200, 'EUR'),
                null
            ),
            new Item(
                '7_25_test-variant-code-medication',
                'Medication product',
                true,
                ProductType::MEDICATION,
                null,
                1,
                new Price(200, 'EUR'),
                new ParentItem('7_25_test-variant-code-consult'),
            ),
            new Item(
                '7_26_test-variant-code-medication',
                'Medication product',
                false,
                ProductType::MEDICATION,
                'One a day',
                10,
                new Price(200, 'EUR'),
                new ParentItem('7_25_test-variant-code-consult'),
            ),
        ];

        $compositeOrderItemOperations = $this->compositeOperationsFactory->buildCompositeOperationsByItems(
            $definitiveItems,
            $order
        );

        $this->assertEquals($expectedCompositeOrderItemOperations, $compositeOrderItemOperations);
    }

    public function testItCanGenerateUpdateOperations(): void
    {
        $order = OrderFactory::createPrefilled();

        $expectedCompositeOrderItemOperations = new CompositeOrderItemOperations([
            new UpdateOrderItemOperation(
                2,
                '7_25_test-variant-code-medication',
                10,
                'Twice a day'
            ),
        ]);

        $expectedCompositeOrderItemOperations->setOrder($order);

        $definitiveItems = [
            new Item(
                '7_25_test-variant-code-consult',
                'Consult product',
                false,
                ProductType::CONSULT,
                null,
                1,
                new Price(200, 'EUR'),
                null
            ),
            new Item(
                '7_25_test-variant-code-medication',
                'Medication product',
                true,
                ProductType::MEDICATION,
                'Twice a day',
                10,
                new Price(200, 'EUR'),
                new ParentItem('7_25_test-variant-code-consult'),
            ),
        ];

        $compositeOrderItemOperations = $this->compositeOperationsFactory->buildCompositeOperationsByItems(
            $definitiveItems,
            $order
        );

        $this->assertEquals($expectedCompositeOrderItemOperations, $compositeOrderItemOperations);
    }

    public function testItCanGenerateMultipleOperations(): void
    {
        $order = OrderFactory::createPrefilled();

        $expectedCompositeOrderItemOperations = new CompositeOrderItemOperations([
            new AddOrderItemOperation(
                'Five a day',
                '7_26_test-variant-code-medication',
                10,
                '7_25_test-variant-code-consult'
            ),
            new UpdateOrderItemOperation(
                2,
                '7_25_test-variant-code-medication',
                3,
                'Twice a day'
            ),
        ]);

        $expectedCompositeOrderItemOperations->setOrder($order);

        $definitiveItems = [
            new Item(
                '7_25_test-variant-code-consult',
                'Consult product',
                false,
                ProductType::CONSULT,
                null,
                1,
                new Price(200, 'EUR'),
                null
            ),
            new Item(
                '7_25_test-variant-code-medication',
                'Medication product',
                true,
                ProductType::MEDICATION,
                'Twice a day',
                3,
                new Price(200, 'EUR'),
                new ParentItem('7_25_test-variant-code-consult'),
            ),
            new Item(
                '7_26_test-variant-code-medication',
                'Medication product',
                true,
                ProductType::MEDICATION,
                'Five a day',
                10,
                new Price(200, 'EUR'),
                new ParentItem('7_25_test-variant-code-consult'),
            ),
        ];

        $compositeOrderItemOperations = $this->compositeOperationsFactory->buildCompositeOperationsByItems(
            $definitiveItems,
            $order
        );

        $this->assertEquals($expectedCompositeOrderItemOperations, $compositeOrderItemOperations);
    }
}
