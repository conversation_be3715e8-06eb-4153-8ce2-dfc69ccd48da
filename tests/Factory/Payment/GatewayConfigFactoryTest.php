<?php

declare(strict_types=1);

namespace App\Tests\Factory\Payment;

use App\Factory\Payment\GatewayConfigFactory;
use PHPUnit\Framework\TestCase;

final class GatewayConfigFactoryTest extends TestCase
{
    /**
     * @return iterable<string, array{0: array<string, string>}>
     */
    public function provideConfigForGateway(): iterable
    {
        yield 'no config' => [[]];

        yield 'some config' => [['key' => 'value']];
    }

    /**
     * @dataProvider provideConfigForGateway
     *
     * @param array<string, array{0: array<string, string>}> $expectedConfig
     */
    public function testCreate(array $expectedConfig): void
    {
        // Arrange
        $factory = GatewayConfigFactory::build();

        // Act
        $gatewayConfig = $factory->create('test_gateway', $expectedConfig);

        // Assert
        self::assertSame('test_gateway', $gatewayConfig->getGatewayName());
        self::assertSame('test_gateway', $gatewayConfig->getFactoryName());
        self::assertSame($expectedConfig, $gatewayConfig->getConfig());
    }
}
