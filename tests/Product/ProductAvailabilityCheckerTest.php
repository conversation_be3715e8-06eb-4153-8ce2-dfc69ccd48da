<?php

declare(strict_types=1);

namespace App\Tests\Product;

use App\Entity\Channel\Channel;
use App\Entity\Product\Product;
use App\Product\AvailabilityCheckerFactory;
use App\Product\AvailabilityCheckerFactoryInterface;
use App\Repository\BusinessUnitRepository;
use App\Repository\ChannelPricingRepository;
use App\Tests\Util\Factory\BusinessUnitFactory;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\ChannelPricingFactory;
use App\Tests\Util\Factory\ProductFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class ProductAvailabilityCheckerTest extends TestCase
{
    private BusinessUnitRepository&MockObject $businessUnitRepository;
    private AvailabilityCheckerFactoryInterface $productBackInStockFactory;

    protected function setUp(): void
    {
        $this->businessUnitRepository = $this->createMock(BusinessUnitRepository::class);

        $channelPricingRepository = $this->createMock(ChannelPricingRepository::class);
        $channelPricingRepository->expects(self::never())
            ->method('getBusinessUnitsByChannelPricings');

        $this->productBackInStockFactory = new AvailabilityCheckerFactory($this->businessUnitRepository, $channelPricingRepository);
    }

    /**
     * @dataProvider unhappyProductProvider
     */
    public function testUnhappyProductBackInStock(Product $product): void
    {
        $businessUnits = $product->getChannels()
            ->map(static fn (Channel $channel) => $channel->getBusinessUnit())
            ->toArray();
        $this->businessUnitRepository->method('findAll')->willReturn($businessUnits);

        // Act
        $businessUnits = $this->productBackInStockFactory->create($product)->getInStockBusinessUnits();

        // Assert
        self::assertEmpty($businessUnits);
    }

    /**
     * @return iterable<array{product: Product}>
     */
    public function unhappyProductProvider(): iterable
    {
        $product = ProductFactory::createPrefilled();
        $product->setEnabled(false);

        yield 'Is product enabled' => ['product' => $product, 'expectedDispatchCount' => 0];

        $product = ProductFactory::createPrefilled();

        $channelDokGb = ChannelFactory::createPrefilled(['code' => 'dok_gb']);
        $channelDokGb->setBusinessUnit(BusinessUnitFactory::create(['code' => 'doctoronline', 'companyName' => 'Doctoronline']));
        $channelDokGb->setEnabled(false);
        $product->addChannel($channelDokGb);

        $businessUnit = BusinessUnitFactory::create(['code' => 'doctoronline', 'companyName' => 'Doctoronline']);
        $channelDokNl = ChannelFactory::createPrefilled();
        $channelDokNl->setBusinessUnit($businessUnit);
        $channelDokNl->setEnabled(false);
        $product->addChannel($channelDokNl);

        $variant = ProductFactory::addVariant($product);
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channelCode' => $channelDokNl->getCode()]));

        $variant = ProductFactory::addVariant($product);
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channelCode' => $channelDokNl->getCode()]));
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channelCode' => $channelDokGb->getCode()]));

        yield 'product is enabled without enabled channels' => ['product' => $product, 'expectedDispatchCount' => 0];

        $product = ProductFactory::createPrefilled();

        $channelDokGb = ChannelFactory::createPrefilled(['code' => 'dok_gb']);
        $channelDokGb->setBusinessUnit(BusinessUnitFactory::create(['code' => 'doctoronline', 'companyName' => 'Doctoronline']));
        $product->addChannel($channelDokGb);

        $businessUnit = BusinessUnitFactory::create(['code' => 'doctoronline', 'companyName' => 'Doctoronline']);
        $channelDokNl = ChannelFactory::createPrefilled();
        $channelDokNl->setBusinessUnit($businessUnit);
        $product->addChannel($channelDokNl);

        $variant = ProductFactory::addVariant($product);
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channelCode' => $channelDokNl->getCode()]));
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channelCode' => $channelDokGb->getCode()]));
        $variant->setEnabled(false);

        yield 'product is enabled without enabled variants' => ['product' => $product, 'expectedDispatchCount' => 0];

        $product = ProductFactory::createPrefilled();

        $channelDokGb = ChannelFactory::createPrefilled(['code' => 'dok_gb']);
        $channelDokGb->setBusinessUnit(BusinessUnitFactory::create(['code' => 'doctoronline', 'companyName' => 'Doctoronline']));
        $product->addChannel($channelDokGb);

        $businessUnit = BusinessUnitFactory::create(['code' => 'doctoronline', 'companyName' => 'Doctoronline']);
        $channelDokNl = ChannelFactory::createPrefilled();
        $channelDokNl->setBusinessUnit($businessUnit);
        $product->addChannel($channelDokNl);

        $variant = ProductFactory::addVariant($product);
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channelCode' => $channelDokNl->getCode(), 'enabled' => false]));
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channelCode' => $channelDokGb->getCode(), 'enabled' => false]));
        $variant->setEnabled(true);

        yield 'Variant has enabled channels within product enabled channels' => ['product' => $product, 'expectedDispatchCount' => 0];
    }

    /**
     * @dataProvider productProvider
     */
    public function testDispatchingOfBackInStock(Product $product, int $expectedDispatchCount): void
    {
        $businessUnits = $product->getChannels()
            ->map(static fn (Channel $channel) => $channel->getBusinessUnit())
            ->toArray();
        $this->businessUnitRepository->method('findAll')->willReturn($businessUnits);

        // Act
        $businessUnits = $this->productBackInStockFactory->create($product)->getInStockBusinessUnits();

        // Assert
        self::assertNotFalse($businessUnits);
        self::assertCount($expectedDispatchCount, $businessUnits);
    }

    /**
     * @return iterable<array{product: Product, expectedDispatchCount: int}>
     */
    public function productProvider(): iterable
    {
        $businessUnit = BusinessUnitFactory::create(['code' => 'doctoronline', 'companyName' => 'Doctoronline']);
        $product = ProductFactory::createPrefilled();
        $channelDokNl = ChannelFactory::createPrefilled();
        $channelDokNl->setBusinessUnit($businessUnit);
        $product->addChannel($channelDokNl);
        $variant = ProductFactory::addVariant($product);
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channel' => $channelDokNl]));

        yield 'product is enabled for a business unit' => ['product' => $product, 'expectedDispatchCount' => 1];

        $product = ProductFactory::createPrefilled();

        $channelDokGb = ChannelFactory::createPrefilled(['code' => 'dok_gb']);
        $channelDokGb->setBusinessUnit(BusinessUnitFactory::create(['code' => 'doctoronline', 'companyName' => 'Doctoronline']));
        $product->addChannel($channelDokGb);

        $businessUnit = BusinessUnitFactory::create(['code' => 'doctoronline', 'companyName' => 'Doctoronline']);
        $channelDokNl = ChannelFactory::createPrefilled();
        $channelDokNl->setBusinessUnit($businessUnit);
        $product->addChannel($channelDokNl);

        // Make sure we don't dispatch the event twice for the same business unit
        $channelDokBe = ChannelFactory::createPrefilled(['code' => 'dok_be']);
        $channelDokBe->setBusinessUnit($channelDokNl->getBusinessUnit());
        $product->addChannel($channelDokBe);

        $variant = ProductFactory::addVariant($product);
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channelCode' => $channelDokNl->getCode()]));
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channelCode' => $channelDokGb->getCode()]));
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channelCode' => $channelDokBe->getCode()]));

        yield 'product is enabled for multiple business units from the same variant' => ['product' => $product, 'expectedDispatchCount' => 2];

        $product = ProductFactory::createPrefilled();

        // Make sure disabled channels are skipped
        $channelDokDe = ChannelFactory::createPrefilled(['code' => 'dok_de']);
        $channelDokDe->setBusinessUnit(BusinessUnitFactory::create(['code' => 'doctoronline-de', 'companyName' => 'Doctoronline DE']));
        $channelDokDe->setEnabled(false);
        $product->addChannel($channelDokDe);

        $channelDokGb = ChannelFactory::createPrefilled(['code' => 'dok_gb']);
        $channelDokGb->setBusinessUnit(BusinessUnitFactory::create(['code' => 'doctoronline', 'companyName' => 'Doctoronline']));
        $product->addChannel($channelDokGb);

        $channelDokNl->setBusinessUnit($businessUnit);
        $channelDokNl = ChannelFactory::createPrefilled();
        $channelDokNl->setBusinessUnit($businessUnit);
        $product->addChannel($channelDokNl);

        // Make sure we test the skipping of duplicate business units
        $channelDokBe = ChannelFactory::createPrefilled(['code' => 'dok_be']);
        $channelDokBe->setBusinessUnit($channelDokNl->getBusinessUnit());
        $product->addChannel($channelDokBe);

        $variant = ProductFactory::addVariant($product);
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channelCode' => $channelDokNl->getCode()]));
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channelCode' => $channelDokBe->getCode()]));

        // Make sure the process works with disabled variants
        $variant = ProductFactory::addVariant($product, ['code' => 'disabled-variant', 'enabled' => false]);
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channelCode' => $channelDokNl->getCode()]));

        $variant = ProductFactory::addVariant($product, ['code' => 'uk-variant']);
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channelCode' => $channelDokBe->getCode()]));
        $variant->addChannelPricing(ChannelPricingFactory::createPrefilled(['channelCode' => $channelDokGb->getCode()]));

        yield 'product is enabled for multiple business units from different variants' => ['product' => $product, 'expectedDispatchCount' => 2];
    }
}
