<?php

declare(strict_types=1);

namespace App\Tests\Product;

use App\Entity\Channel\ChannelPricing;
use App\Entity\Product\Product;
use App\Entity\Product\ProductVariant;
use App\Product\AvailabilityCheckableInterface;
use App\Product\AvailabilityCheckerFactory;
use App\Product\AvailabilityCheckerFactoryInterface;
use App\Product\AvailabilityCheckerInterface;
use App\Product\ChannelPricingAvailabilityChecker;
use App\Product\ProductAvailabilityChecker;
use App\Product\ProductVariantAvailabilityChecker;
use App\Repository\BusinessUnitRepository;
use App\Repository\ChannelPricingRepository;
use InvalidArgumentException;
use PHPUnit\Framework\TestCase;

final class AvailabilityCheckerFactoryTest extends TestCase
{
    private AvailabilityCheckerFactoryInterface $availabilityCheckerFactory;

    protected function setUp(): void
    {
        $this->availabilityCheckerFactory = new AvailabilityCheckerFactory(
            $this->createMock(BusinessUnitRepository::class),
            $this->createMock(ChannelPricingRepository::class)
        );
    }

    /**
     * @dataProvider provideEntities
     * @param class-string $expectedClass
     */
    public function testItCanCreateAvailabilityCheckers(AvailabilityCheckableInterface $entity, string $expectedClass): void
    {
        // Act
        $availabilityChecker = $this->availabilityCheckerFactory->create($entity);

        // Assert
        self::assertInstanceOf(AvailabilityCheckerInterface::class, $availabilityChecker);
        self::assertInstanceOf($expectedClass, $availabilityChecker);
    }

    public function testItOnlyAcceptsValidEntities(): void
    {
        // Arrange
        $entity = $this->createMock(AvailabilityCheckableInterface::class);

        // Assert
        $this->expectException(InvalidArgumentException::class);

        // Act
        $this->availabilityCheckerFactory->create($entity);
    }

    /**
     * @return iterable<string, array<mixed>>
     */
    public function provideEntities(): iterable
    {
        yield 'Product' => [
            new Product(),
            ProductAvailabilityChecker::class,
        ];

        yield 'ProductVariant' => [
            new ProductVariant(),
            ProductVariantAvailabilityChecker::class,
        ];

        yield 'ChannelPricing' => [
            new ChannelPricing(),
            ChannelPricingAvailabilityChecker::class,
        ];
    }
}
