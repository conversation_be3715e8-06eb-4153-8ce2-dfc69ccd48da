<?php

declare(strict_types=1);

namespace App\Tests\Product;

use App\Entity\Channel\ChannelPricing;
use App\Product\AvailabilityCheckerFactory;
use App\Product\AvailabilityCheckerFactoryInterface;
use App\Repository\BusinessUnitRepository;
use App\Repository\ChannelPricingRepository;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\ChannelPricingFactory;
use App\Tests\Util\Factory\ProductFactory;
use App\Tests\Util\Factory\ProductVariantFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class ProductVariantChannelAvailabilityCheckerTest extends TestCase
{
    private MockObject&ChannelPricingRepository $channelPricingRepositoryMock;

    private AvailabilityCheckerFactoryInterface $productVariantChannelAvailabilityCheckerFactory;

    protected function setUp(): void
    {
        $this->channelPricingRepositoryMock = $this->createMock(ChannelPricingRepository::class);

        $businessUnitRepository = $this->createMock(BusinessUnitRepository::class);
        $businessUnitRepository->expects(self::never())
            ->method('getEnabledBusinessUnitsForVariant');

        $this->productVariantChannelAvailabilityCheckerFactory = new AvailabilityCheckerFactory(
            $businessUnitRepository,
            $this->channelPricingRepositoryMock,
        );
    }

    /**
     * @return iterable<string, array<ChannelPricing>>
     */
    public function provideInvalidChannelPricing(): iterable
    {
        /** @var ChannelPricing $channelPricing */
        $channelPricing = ChannelPricingFactory::create(
            [
                'enabled' => false,
            ]
        );

        yield 'ChannelPricing that is disabled.' => [$channelPricing];

        /** @var ChannelPricing $channelPricing */
        $channelPricing = ChannelPricingFactory::create(
            [
                'enabled' => true,
            ]
        );

        yield 'ChannelPricing without a product variant.' => [$channelPricing];

        /** @var ChannelPricing $channelPricing */
        $channelPricing = ChannelPricingFactory::create(
            [
                'enabled' => true,
                'productVariant' => ProductVariantFactory::create(
                    [
                        'enabled' => false,
                    ]
                ),
            ]
        );

        yield 'ChannelPricing with a disabled product variant.' => [$channelPricing];

        /** @var ChannelPricing $channelPricing */
        $channelPricing = ChannelPricingFactory::create(
            [
                'enabled' => true,
                'productVariant' => ProductVariantFactory::create(
                    [
                        'enabled' => true,
                    ]
                ),
            ]
        );

        yield 'ChannelPricing without a product.' => [$channelPricing];

        /** @var ChannelPricing $channelPricing */
        $channelPricing = ChannelPricingFactory::create(
            [
                'enabled' => true,
                'productVariant' => ProductVariantFactory::create(
                    [
                        'enabled' => true,
                        'product' => ProductFactory::create(
                            [
                                'enabled' => false,
                            ]
                        ),
                    ]
                ),
            ]
        );

        yield 'ChannelPricing with a disabled product.' => [$channelPricing];

        /** @var ChannelPricing $channelPricing */
        $channelPricing = ChannelPricingFactory::create(
            [
                'enabled' => true,
                'productVariant' => ProductVariantFactory::create(
                    [
                        'enabled' => true,
                        'product' => ProductFactory::create(
                            [
                                'enabled' => true,
                            ]
                        ),
                    ]
                ),
            ]
        );

        yield 'ChannelPricing with a product without channels.' => [$channelPricing];

        /** @var ChannelPricing $channelPricing */
        $channelPricing = ChannelPricingFactory::create(
            [
                'enabled' => true,
                'productVariant' => ProductVariantFactory::create(
                    [
                        'enabled' => true,
                        'product' => ProductFactory::create(
                            [
                                'enabled' => true,
                                'channels' => [
                                    ChannelFactory::create(
                                        [
                                            'code' => 'dok_de',
                                        ]
                                    ),
                                ],
                            ]
                        ),
                    ]
                ),
                'channelCode' => 'dok_nl',
            ]
        );

        yield 'ChannelPricing with a product with a different channel.' => [$channelPricing];

        /** @var ChannelPricing $channelPricing */
        $channelPricing = ChannelPricingFactory::create(
            [
                'enabled' => true,
                'productVariant' => ProductVariantFactory::create(
                    [
                        'enabled' => true,
                        'product' => ProductFactory::create(
                            [
                                'enabled' => true,
                                'channels' => [
                                    ChannelFactory::create(
                                        [
                                            'code' => 'dok_de',
                                        ]
                                    ),
                                    ChannelFactory::create(
                                        [
                                            'code' => 'dok_nl',
                                        ]
                                    ),
                                ],
                            ]
                        ),
                    ]
                ),
                'channelCode' => 'dok_nl',
            ]
        );

        yield 'ChannelPricing with a product with more than 1 enabled channel.' => [$channelPricing];
    }

    /**
     * @dataProvider provideInvalidChannelPricing
     */
    public function testGetInStockBusinessUnitsReturnsEmptyArray(
        ChannelPricing $channelPricing,
    ): void {
        // Arrange
        $productVariantChannelAvailabilityChecker = $this->productVariantChannelAvailabilityCheckerFactory->create($channelPricing);

        // Act & Assert
        self::assertSame([], $productVariantChannelAvailabilityChecker->getInStockBusinessUnits());
    }

    public function testGetInStockBusinessUnitsReturnsArrayWithBusinessUnits(): void
    {
        // Arrange
        $businessUnits = [];

        $channel = ChannelFactory::create(
            [
                'code' => 'dok_de',
            ]
        );

        $product = ProductFactory::create(
            [
                'enabled' => true,
                'channels' => [
                    $channel,
                ],
            ]
        );

        /** @var ChannelPricing $channelPricing */
        $channelPricing = ChannelPricingFactory::create(
            [
                'enabled' => true,
                'productVariant' => ProductVariantFactory::create(
                    [
                        'enabled' => true,
                        'product' => $product,
                    ]
                ),
                'channelCode' => 'dok_nl',
            ]
        );

        $productVariantChannelAvailabilityChecker = $this->productVariantChannelAvailabilityCheckerFactory->create($channelPricing);

        $this->channelPricingRepositoryMock->method('getEnabledChannelPricingByProductAndChannel')
            ->with($product, [$channel])
            ->willReturn([$businessUnits]);

        // Act & Assert
        self::assertSame($businessUnits, $productVariantChannelAvailabilityChecker->getInStockBusinessUnits());
    }
}
