<?php

declare(strict_types=1);

namespace App\Tests\Product\ProductVariant;

use App\Entity\Product\Product;
use App\Entity\Product\ProductVariant;
use App\Entity\Supplier\Supplier;
use App\Product\ValueObject\GroupedProductVariant;
use App\Tests\Util\Factory\ChannelPricingFactory;
use App\Tests\Util\Factory\SupplierFactory;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class GroupedProductVariantTest extends TestCase
{
    private const string PRODUCT_CODE = '10';
    private const string PRODUCT_VARIANT_CODE_A = '10_11';
    private const int PRODUCT_VARIANT_CODE_A_PRICE = 100;
    private const string PRODUCT_VARIANT_CODE_B = '10_34124';
    private const int PRODUCT_VARIANT_CODE_B_PRICE = 400;

    /**
     * Code: 10_11_apotheek_bad_nieuweschans_worldwide is enabled
     * Supplier Bad Nieuweschans
     * Has enabled channel pricings: dok_lu, dok_pl, blueclinic_nl
     * Has disabled channel pricings: dok_gb, dok_nl, dok_de.
     */
    private ProductVariant&MockObject $productVariantA1;

    /**
     * Code: 10_11_apotheek_bad_nieuweschans_de is enabled
     * Supplier Bad Nieuweschans
     * Has enabled channel pricings: dok_de
     * Has disabled channel pricings: dok_gb, dok_lu, dok_nl, dok_pl, blueclinic_nl.
     */
    private ProductVariant&MockObject $productVariantA2;

    /**
     * Code: 10_11_apotheek_culemborg_gb is disabled
     * Supplier Apotheek Culemborg BV
     * Has enabled channel pricings: dok_gb
     * Has disabled channel pricings: dok_de, dok_lu, dok_nl, dok_pl, blueclinic_nl.
     */
    private ProductVariant&MockObject $productVariantA3;

    /**
     * Code: 10_11_prime_pharmacy_gb is enabled
     * Supplier Prime Pharmacy
     * Has enabled channel pricings: dok_lu, dok_nl, dok_pl, blueclinic_nl
     * Has disabled channel pricings: dok_de, dok_gb.
     */
    private ProductVariant&MockObject $productVariantA4;

    /**
     * Code: 10_34124_apotheek_bad_nieuweschans_worldwide is enabled
     * Supplier Bad Nieuweschans
     * Has enabled channel pricings:
     * Has disabled channel pricings: dok_de, dok_gb, dok_lu, dok_nl, blueclinic_nl.
     */
    private ProductVariant&MockObject $productVariantB1;

    /**
     * Code: 10_34124_apotheek_bad_nieuweschans_de is disabled
     * Supplier Bad Nieuweschans
     * Has enabled channel pricings: dok_de
     * Has disabled channel pricings: dok_gb, dok_lu, dok_nl, blueclinic_nl.
     */
    private ProductVariant&MockObject $productVariantB2;

    private Product $product;

    private Supplier $apotheekBadNieuweschans;
    private Supplier $apotheekCulemborg;
    private Supplier $primePharmacy;

    protected function setUp(): void
    {
        $this->createSuppliers();
        $this->createProduct();
        $this->createProductVariantsA();
        $this->createProductVariantsB();
    }

    public function testCreateGroupedVariant(): void
    {
        $groupedVariant = GroupedProductVariant::createFromProduct($this->product, self::PRODUCT_VARIANT_CODE_A);

        self::assertInstanceOf(GroupedProductVariant::class, $groupedVariant);
        self::assertCount(4, $groupedVariant->variants);
        self::assertCount(6, $groupedVariant->pricing);
        self::assertSame(self::PRODUCT_VARIANT_CODE_A, $groupedVariant->code);

        self::assertSame($this->productVariantA1->getCode(), $groupedVariant->variants[0]->getCode());
        self::assertSame($this->productVariantA2->getCode(), $groupedVariant->variants[1]->getCode());
        self::assertSame($this->productVariantA3->getCode(), $groupedVariant->variants[2]->getCode());
        self::assertSame($this->productVariantA4->getCode(), $groupedVariant->variants[3]->getCode());

        self::assertTrue($groupedVariant->pricing['dok_gb']->enabled);
        self::assertSame(self::PRODUCT_VARIANT_CODE_A_PRICE, $groupedVariant->pricing['dok_gb']->price);
        self::assertFalse($groupedVariant->pricing['dok_nl']->enabled);
        self::assertSame(self::PRODUCT_VARIANT_CODE_A_PRICE, $groupedVariant->pricing['dok_nl']->price);
        self::assertTrue($groupedVariant->pricing['dok_de']->enabled);
        self::assertSame(self::PRODUCT_VARIANT_CODE_A_PRICE, $groupedVariant->pricing['dok_de']->price);
        self::assertTrue($groupedVariant->pricing['dok_pl']->enabled);
        self::assertSame(self::PRODUCT_VARIANT_CODE_A_PRICE, $groupedVariant->pricing['dok_pl']->price);
        self::assertTrue($groupedVariant->pricing['dok_lu']->enabled);
        self::assertSame(self::PRODUCT_VARIANT_CODE_A_PRICE, $groupedVariant->pricing['dok_lu']->price);
        self::assertTrue($groupedVariant->pricing['blueclinic_nl']->enabled);
        self::assertSame(self::PRODUCT_VARIANT_CODE_A_PRICE, $groupedVariant->pricing['blueclinic_nl']->price);
    }

    public function testCreateGroupedVariantB(): void
    {
        $groupedVariant = GroupedProductVariant::createFromProduct($this->product, self::PRODUCT_VARIANT_CODE_B);

        self::assertInstanceOf(GroupedProductVariant::class, $groupedVariant);
        self::assertCount(2, $groupedVariant->variants);
        self::assertCount(4, $groupedVariant->pricing);
        self::assertSame(self::PRODUCT_VARIANT_CODE_B, $groupedVariant->code);

        self::assertSame($this->productVariantB1->getCode(), $groupedVariant->variants[0]->getCode());
        self::assertSame($this->productVariantB2->getCode(), $groupedVariant->variants[1]->getCode());

        self::assertTrue($groupedVariant->pricing['dok_de']->enabled);
        self::assertFalse($groupedVariant->pricing['dok_nl']->enabled);
        self::assertNull($groupedVariant->pricing['dok_lu'] ?? null);
        self::assertFalse($groupedVariant->pricing['blueclinic_nl']->enabled);
        self::assertNull($groupedVariant->pricing['dok_pl'] ?? null);
        self::assertFalse($groupedVariant->pricing['dok_gb']->enabled);
    }

    private function createSuppliers(): void
    {
        $this->apotheekBadNieuweschans = SupplierFactory::createPrefilled(['id' => 1, 'name' => 'Apotheek Bad Nieuweschans BV', 'identifier' => 'apotheek-bad-nieuweschans']);
        $this->apotheekCulemborg = SupplierFactory::createPrefilled(['id' => 2, 'name' => 'Apotheek Culemborg BV', 'identifier' => 'apotheek-culemborg']);
        $this->primePharmacy = SupplierFactory::createPrefilled(['id' => 3, 'name' => 'Prime Pharmacy', 'identifier' => 'prime-pharmacy']);
    }

    private function createProductVariantsA(): void
    {
        // Arrange product variant A1
        $channelPricingsA1 = new ArrayCollection();
        $channelPricingsA1->set('dok_de', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_de', 'enabled' => false]));
        $channelPricingsA1->set('dok_nl', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_nl', 'enabled' => false]));
        $channelPricingsA1->set('dok_gb', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_gb', 'enabled' => false]));
        $channelPricingsA1->set('dok_lu', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_lu', 'enabled' => true]));
        $channelPricingsA1->set('dok_pl', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_pl', 'enabled' => true]));
        $channelPricingsA1->set('blueclinic_nl', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'blueclinic_nl', 'enabled' => true]));

        $this->productVariantA1 = $this->createMock(ProductVariant::class);
        $this->productVariantA1->method('isEnabled')->willReturn(true);
        $this->productVariantA1->method('getProduct')->willReturn($this->product);
        $this->productVariantA1->method('getCodeWithoutSupplierCountry')->willReturn(self::PRODUCT_VARIANT_CODE_A);
        $this->productVariantA1->method('getChannelPricings')->willReturn($channelPricingsA1);
        $this->productVariantA1->method('getCode')
            ->willReturn(sprintf('%s_%s_%s',
                self::PRODUCT_VARIANT_CODE_A,
                str_replace('-', '_', $this->apotheekBadNieuweschans->getIdentifier() ?? ''),
                'worldwide'
            )
            );

        $this->product->addVariant($this->productVariantA1);

        // Arrange product variant A2
        $channelPricingsA2 = new ArrayCollection();
        $channelPricingsA2->set('dok_de', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_de', 'enabled' => true]));
        $channelPricingsA2->set('dok_nl', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_nl', 'enabled' => false]));
        $channelPricingsA2->set('dok_gb', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_gb', 'enabled' => false]));
        $channelPricingsA2->set('dok_lu', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_lu', 'enabled' => false]));
        $channelPricingsA2->set('dok_pl', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_pl', 'enabled' => false]));
        $channelPricingsA2->set('blueclinic_nl', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'blueclinic_nl', 'enabled' => false]));

        $this->productVariantA2 = $this->createMock(ProductVariant::class);
        $this->productVariantA2->method('isEnabled')->willReturn(true);
        $this->productVariantA2->method('getProduct')->willReturn($this->product);
        $this->productVariantA2->method('getCodeWithoutSupplierCountry')->willReturn(self::PRODUCT_VARIANT_CODE_A);
        $this->productVariantA2->method('getChannelPricings')->willReturn($channelPricingsA2);
        $this->productVariantA2->method('getCode')
            ->willReturn(sprintf('%s_%s_%s',
                self::PRODUCT_VARIANT_CODE_A,
                str_replace('-', '_', $this->apotheekBadNieuweschans->getIdentifier() ?? ''),
                'de'
            )
            );

        $this->product->addVariant($this->productVariantA2);

        // Arrange product variant A3
        $channelPricingsA3 = new ArrayCollection();
        $channelPricingsA3->set('dok_gb', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_gb', 'enabled' => true]));

        $this->productVariantA3 = $this->createMock(ProductVariant::class);
        $this->productVariantA3->method('isEnabled')->willReturn(false);
        $this->productVariantA3->method('getProduct')->willReturn($this->product);
        $this->productVariantA3->method('getCodeWithoutSupplierCountry')->willReturn(self::PRODUCT_VARIANT_CODE_A);
        $this->productVariantA3->method('getChannelPricings')->willReturn($channelPricingsA3);
        $this->productVariantA3->method('getCode')
            ->willReturn(sprintf('%s_%s_%s',
                self::PRODUCT_VARIANT_CODE_A,
                str_replace('-', '_', $this->apotheekCulemborg->getIdentifier() ?? ''),
                'gb'
            )
            );

        $this->product->addVariant($this->productVariantA3);

        // Arrange product variant A4
        $channelPricingsA4 = new ArrayCollection();
        $channelPricingsA4->set('dok_de', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_de', 'enabled' => false]));
        $channelPricingsA4->set('dok_nl', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_nl', 'enabled' => true]));
        $channelPricingsA4->set('dok_gb', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_gb', 'enabled' => false]));
        $channelPricingsA4->set('dok_lu', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_lu', 'enabled' => true]));
        $channelPricingsA4->set('dok_pl', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'dok_pl', 'enabled' => true]));
        $channelPricingsA4->set('blueclinic_nl', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_A_PRICE, 'channelCode' => 'blueclinic_nl', 'enabled' => true]));

        $this->productVariantA4 = $this->createMock(ProductVariant::class);
        $this->productVariantA4->method('isEnabled')->willReturn(true);
        $this->productVariantA4->method('getProduct')->willReturn($this->product);
        $this->productVariantA4->method('getCodeWithoutSupplierCountry')->willReturn(self::PRODUCT_VARIANT_CODE_A);
        $this->productVariantA4->method('getChannelPricings')->willReturn($channelPricingsA3);
        $this->productVariantA4->method('getCode')
            ->willReturn(sprintf('%s_%s_%s',
                self::PRODUCT_VARIANT_CODE_A,
                str_replace('-', '_', $this->primePharmacy->getIdentifier() ?? ''),
                'gb'
            )
            );

        $this->product->addVariant($this->productVariantA4);
    }

    private function createProductVariantsB(): void
    {
        // Arrange product variant B1
        $channelPricingsB1 = new ArrayCollection();
        $channelPricingsB1->set('dok_de', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_B_PRICE, 'channelCode' => 'dok_de', 'enabled' => false]));
        $channelPricingsB1->set('dok_nl', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_B_PRICE, 'channelCode' => 'dok_nl', 'enabled' => false]));
        $channelPricingsB1->set('dok_gb', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_B_PRICE, 'channelCode' => 'dok_gb', 'enabled' => false]));
        $channelPricingsB1->set('dok_lu', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_B_PRICE, 'channelCode' => 'dok_lu', 'enabled' => false]));
        $channelPricingsB1->set('blueclinic_nl', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_B_PRICE, 'channelCode' => 'blueclinic_nl', 'enabled' => false]));

        $this->productVariantB1 = $this->createMock(ProductVariant::class);
        $this->productVariantB1->method('isEnabled')->willReturn(true);
        $this->productVariantB1->method('getProduct')->willReturn($this->product);
        $this->productVariantB1->method('getCodeWithoutSupplierCountry')->willReturn(self::PRODUCT_VARIANT_CODE_B);
        $this->productVariantB1->method('getChannelPricings')->willReturn($channelPricingsB1);
        $this->productVariantB1->method('getCode')
            ->willReturn(sprintf('%s_%s_%s',
                self::PRODUCT_VARIANT_CODE_B,
                str_replace('-', '_', $this->apotheekBadNieuweschans->getIdentifier() ?? ''),
                'worldwide'
            )
            );

        $this->product->addVariant($this->productVariantB1);

        // Arrange product variant B2
        $channelPricingsB2 = new ArrayCollection();
        $channelPricingsB2->set('dok_de', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_B_PRICE, 'channelCode' => 'dok_de', 'enabled' => true]));
        $channelPricingsB2->set('dok_nl', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_B_PRICE, 'channelCode' => 'dok_nl', 'enabled' => false]));
        $channelPricingsB2->set('dok_gb', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_B_PRICE, 'channelCode' => 'dok_gb', 'enabled' => false]));
        $channelPricingsB2->set('dok_lu', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_B_PRICE, 'channelCode' => 'dok_lu', 'enabled' => false]));
        $channelPricingsB2->set('dok_lu', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_B_PRICE, 'channelCode' => null, 'enabled' => false]));
        $channelPricingsB2->set('blueclinic_nl', ChannelPricingFactory::createPrefilled(['price' => self::PRODUCT_VARIANT_CODE_B_PRICE, 'channelCode' => 'blueclinic_nl', 'enabled' => false]));

        $this->productVariantB2 = $this->createMock(ProductVariant::class);
        $this->productVariantB2->method('isEnabled')->willReturn(false);
        $this->productVariantB2->method('getProduct')->willReturn($this->product);
        $this->productVariantB2->method('getCodeWithoutSupplierCountry')->willReturn(self::PRODUCT_VARIANT_CODE_B);
        $this->productVariantB2->method('getChannelPricings')->willReturn($channelPricingsB2);
        $this->productVariantB2->method('getCode')
            ->willReturn(sprintf('%s_%s_%s',
                self::PRODUCT_VARIANT_CODE_B,
                str_replace('-', '_', $this->apotheekBadNieuweschans->getIdentifier() ?? ''),
                'de'
            )
            );

        $this->product->addVariant($this->productVariantB2);
    }

    private function createProduct(): void
    {
        $product = new Product();
        $product->setEnabled(true);
        $product->setCode(self::PRODUCT_CODE);

        $this->product = $product;
    }
}
