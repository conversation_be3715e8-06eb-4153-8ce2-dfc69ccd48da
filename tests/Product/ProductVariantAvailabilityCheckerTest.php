<?php

declare(strict_types=1);

namespace App\Tests\Product;

use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Product\ProductVariant;
use App\Product\AvailabilityCheckerFactory;
use App\Product\AvailabilityCheckerFactoryInterface;
use App\Repository\BusinessUnitRepository;
use App\Repository\ChannelPricingRepository;
use App\Tests\Util\Factory\BusinessUnitFactory;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\ChannelPricingFactory;
use App\Tests\Util\Factory\ProductFactory;
use App\Tests\Util\Factory\ProductVariantFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class ProductVariantAvailabilityCheckerTest extends TestCase
{
    private BusinessUnitRepository&MockObject $businessUnitRepositoryMock;
    private AvailabilityCheckerFactoryInterface $productVariantBackInStockFactory;

    protected function setUp(): void
    {
        $this->businessUnitRepositoryMock = $this->createMock(BusinessUnitRepository::class);
        $this->businessUnitRepositoryMock->method('findAll')
            ->willReturn([]);

        $channelPricingRepository = $this->createMock(ChannelPricingRepository::class);
        $channelPricingRepository->expects(self::never())
            ->method('getBusinessUnitsByChannelPricings');

        $this->productVariantBackInStockFactory = new AvailabilityCheckerFactory($this->businessUnitRepositoryMock, $channelPricingRepository);
    }

    /**
     * @dataProvider provideUnHappyEvents
     *
     * @param array<BusinessUnit> $expectedEnabledBusinessUnitsForVariant
     */
    public function testUnHappyPostUpdateEvent(
        ProductVariant $productVariant,
        array $expectedEnabledBusinessUnitsForVariant = [],
        int $expectedInvocationCountEnabledBusinessUnitsForVariant = 0,
    ): void {
        // Arrange
        $this->businessUnitRepositoryMock
            ->expects(self::exactly($expectedInvocationCountEnabledBusinessUnitsForVariant))
            ->method('getEnabledBusinessUnitsForVariant')
            ->willReturn($expectedEnabledBusinessUnitsForVariant);

        // Act
        self::assertEmpty($this->productVariantBackInStockFactory->create($productVariant)->getInStockBusinessUnits());
    }

    /**
     * @return iterable<array{ProductVariant}|array{ProductVariant, array<BusinessUnit>, int}>
     */
    public function provideUnHappyEvents(): iterable
    {
        $productVariant = ProductVariantFactory::create(['enabled' => false]);
        yield 'Is Product Variant Enabled' => [
            $productVariant,
        ];

        $productVariant = ProductVariantFactory::create(['enabled' => true]);
        $productVariant->setProduct(null);
        yield 'Product Variant has a Product' => [
            $productVariant,
        ];

        $product = ProductFactory::createPrefilled(['enabled' => false]);
        $productVariant = ProductVariantFactory::create(['enabled' => false]);
        $product->addVariant($productVariant);
        yield 'Is Product enabled' => [$productVariant];

        $product = ProductFactory::createPrefilled(['enabled' => true]);
        $product->addChannel(ChannelFactory::createPrefilled(['enabled' => false]));
        $productVariant = ProductVariantFactory::create(['enabled' => true]);
        $product->addVariant($productVariant);
        yield 'Product has no enabled channels' => [
            $productVariant,
        ];

        $channel = ChannelFactory::createPrefilled(['code' => 'dok_gb', 'enabled' => true]);
        $product = ProductFactory::createPrefilled(['enabled' => true]);
        $product->addChannel($channel);
        $productVariant = ProductVariantFactory::create(
            [
                'enabled' => true,
                'channelPricings' => [ChannelPricingFactory::create(['channelCode' => $channel->getCode(), 'enabled' => false])],
            ]
        );
        $product->addVariant($productVariant);
        yield 'First enabled variant has no valid variant for a business unit' => [
            $productVariant,
        ];

        $channel = ChannelFactory::createPrefilled(['enabled' => true]);
        $product = ProductFactory::createPrefilled(['enabled' => true]);
        $product->addChannel($channel);
        $productVariant = ProductVariantFactory::create(
            [
                'enabled' => true,
                'channelPricings' => [ChannelPricingFactory::create(['channelCode' => $channel->getCode(), 'enabled' => false])],
            ]
        );
        $product->addVariant($productVariant);

        $secondProductVariant = ProductVariantFactory::create(
            [
                'enabled' => true,
                'code' => '7_18_second_variant',
                'channelPricings' => [ChannelPricingFactory::create(['channelCode' => $channel->getCode(), 'enabled' => false])],
            ]
        );
        $product->addVariant($secondProductVariant);
        yield 'Multiple enabled variants has no enabled business units' => [
            $productVariant,
        ];

        $channel = ChannelFactory::createPrefilled(['enabled' => true]);
        $product = ProductFactory::createPrefilled(['enabled' => true]);
        $product->addChannel($channel);
        $product->addChannel($channel);
        $productVariant = ProductVariantFactory::create(
            [
                'enabled' => true,
                'channelPricings' => [ChannelPricingFactory::create(['channelCode' => $channel->getCode(), 'enabled' => true])],
            ]
        );
        $product->addVariant($productVariant);

        $secondProductVariant = ProductVariantFactory::create(
            [
                'enabled' => true,
                'code' => '7_18_second_variant',
                'channelPricings' => [ChannelPricingFactory::create(['channelCode' => $channel->getCode(), 'enabled' => true])],
            ]
        );
        $product->addVariant($secondProductVariant);
        yield 'Multiple enabled variants is not the first enabled variant' => [
            $productVariant,
            'expectedEnabledBusinessUnitsForVariant' => [],
            'expectedInvocationCountEnabledBusinessUnitsForVariant' => 1,
        ];
    }

    /**
     * @dataProvider singleVariantDataProvider
     */
    public function testEnableASingleVariant(bool $dokGbEnabled, int $expectedNumberOfDispatches): void
    {
        // Arrange
        $businessUnitDK = BusinessUnitFactory::create(['code' => 'dokteronline', 'company' => 'Dokteronline']);
        $businessUnitDC = BusinessUnitFactory::create(['code' => 'doctoronline', 'company' => 'Doctoronline']);

        $channelNL = ChannelFactory::createPrefilled(['code' => 'dok_nl', 'businessUnit' => $businessUnitDK]);
        $channelDE = ChannelFactory::createPrefilled(['code' => 'dok_de', 'businessUnit' => $businessUnitDK]);
        $channelGB = ChannelFactory::createPrefilled(['code' => 'dok_gb', 'businessUnit' => $businessUnitDC]);

        $product = ProductFactory::createPrefilled(['code' => '7']);
        $product->addChannel($channelNL);
        $product->addChannel($channelDE);
        $product->addChannel($channelGB);

        // Will be turned on for dok_de
        $productVariant = ProductVariantFactory::createPrefilled(
            [
                'product' => $product,
                'code' => '7_18_prime_pharmacy_worldwide',
                'enabled' => true,
                'channelPricing' => [
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelDE->getCode(), 'enabled' => true]),
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelNL->getCode(), 'enabled' => true]),
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelGB->getCode(), 'enabled' => $dokGbEnabled]),
                ],
            ]
        );
        $product->addVariant($productVariant);

        $expectedBusinessUnits = [$businessUnitDK];
        if ($expectedNumberOfDispatches > 1) {
            $expectedBusinessUnits[] = $businessUnitDC;
        }

        // Act
        $businessUnits = $this->productVariantBackInStockFactory->create($productVariant)->getInStockBusinessUnits();

        // Assert
        self::assertIsArray($businessUnits);
        self::assertEquals($expectedBusinessUnits, $businessUnits);
    }

    /**
     * @return iterable<array{bool, int}>
     */
    public function singleVariantDataProvider(): iterable
    {
        yield 'Dok GB will also be enabled' => [true, 2];
        yield 'Dok GB will not be enabled' => [false, 1];
    }

    /**
     * Tests that the subscriber correctly dispatches the ProductUpdateEvent when a product variant is enabled.
     * - The product variant will be triggert for the dok_de channel and the Dokteronline business unit.
     * - The Doctoronline business unit will not be triggert, because there is already an enabled variant for the dok_gb channel.
     *
     * There are multiple variants with the following logic:
     * - 7_18_prime_pharmacy_uk, is already enabled for dok_gb
     * - 7_18_apotheek_bad_nieuweschans_de, is already enabled within the Dokteronline business unit, but the channel pricing for dok_de is disabled
     * - 7_20_apotheek_bad_nieuweschans_de, dok_de channel price is enabled, but the variant is disabled
     *
     * @dataProvider multipleVariantsDataProvider
     */
    public function testProductHasMultipleEnabledVariants(bool $dokGbPricingEnabled, int $expectedNumberOfDispatches): void
    {
        // Arrange
        $businessUnitDK = BusinessUnitFactory::create(['code' => 'dokteronline', 'company' => 'Dokteronline']);
        $businessUnitDC = BusinessUnitFactory::create(['code' => 'doctoronline', 'company' => 'Doctoronline']);

        $channelNL = ChannelFactory::createPrefilled(['code' => 'dok_nl', 'businessUnit' => $businessUnitDK]);
        $channelDE = ChannelFactory::createPrefilled(['code' => 'dok_de', 'businessUnit' => $businessUnitDK]);
        $channelGB = ChannelFactory::createPrefilled(['code' => 'dok_gb', 'businessUnit' => $businessUnitDC]);

        $product = ProductFactory::createPrefilled(['code' => '7']);
        $product->addChannel($channelNL);
        $product->addChannel($channelDE);
        $product->addChannel($channelGB);

        // Will be turned on for dok_de
        $productVariant = ProductVariantFactory::createPrefilled(
            [
                'product' => $product,
                'code' => '7_18_prime_pharmacy_worldwide',
                'enabled' => true,
                'channelPricing' => [
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelDE->getCode(), 'enabled' => true]),
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelNL->getCode(), 'enabled' => true]),
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelGB->getCode(), 'enabled' => true]),
                ],
            ]
        );
        $product->addVariant($productVariant);

        // Other channel but when dok_gb is disabled the event for the business unit should be dispatched through 7_18_prime_pharmacy_worldwide
        $otherChannelVariant = ProductVariantFactory::createPrefilled(
            [
                'product' => $product,
                'code' => '7_18_prime_pharmacy_uk',
                'enabled' => true,
                'channelPricing' => [
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelGB->getCode(), 'enabled' => $dokGbPricingEnabled]),
                ],
            ]
        );
        $product->addVariant($otherChannelVariant);

        // Same channel but disabled channel pricing, dok_de channel pricing is disabled
        $disabledChannelPriceVariant = ProductVariantFactory::createPrefilled(
            [
                'product' => $product,
                'enabled' => true,
                'code' => '7_18_apotheek_bad_nieuweschans_de',
                'channelPricing' => [
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelDE->getCode(), 'enabled' => false]),
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelNL->getCode(), 'enabled' => true]),
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelGB->getCode(), 'enabled' => $dokGbPricingEnabled]),
                ],
            ]
        );
        $product->addVariant($disabledChannelPriceVariant);

        // Same channel but disabled variant, only the variant is disabled, the channel pricings are enabled
        $disabledVariant = ProductVariantFactory::createPrefilled(
            [
                'product' => $product,
                'enabled' => false,
                'code' => '7_20_apotheek_bad_nieuweschans_de',
                'channelPricing' => [
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelDE->getCode(), 'enabled' => true]),
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelNL->getCode(), 'enabled' => true]),
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelGB->getCode(), 'enabled' => $dokGbPricingEnabled]),
                ],
            ]
        );
        $product->addVariant($disabledVariant);

        $enabledBusinessUnits = [$businessUnitDK];
        if ($expectedNumberOfDispatches > 1) {
            $enabledBusinessUnits[] = $businessUnitDC;
        }

        $this->businessUnitRepositoryMock->expects(self::once())
            ->method('getEnabledBusinessUnitsForVariant')
            ->willReturn($enabledBusinessUnits);

        $expectedBusinessUnits = [$businessUnitDK];
        if ($expectedNumberOfDispatches > 1) {
            $expectedBusinessUnits[] = $businessUnitDC;
        }

        // Assert
        $this->businessUnitRepositoryMock->expects(self::once())
            ->method('getEnabledBusinessUnitsForVariant')
            ->willReturn($expectedBusinessUnits);

        // Act
        $businessUnits = $this->productVariantBackInStockFactory->create($productVariant)->getInStockBusinessUnits();

        // Assert
        self::assertIsArray($businessUnits);
        self::assertEquals($expectedBusinessUnits, $businessUnits);
    }

    /**
     * @return iterable<array{bool, int}>
     */
    public function multipleVariantsDataProvider(): iterable
    {
        yield 'Dok GB is already enabled for another variant' => [true, 1];

        yield 'Dok GB is not yet enabled for any variant' => [false, 2];
    }

    /**
     * Tests that the subscriber correctly dispatches the ProductUpdateEvent when a product variant is enabled.
     * - The product variant will be triggert for the dok_de channel and the Dokteronline business unit.
     * - The Doctoronline business unit will not be triggert, because there is already an enabled variant for the dok_gb channel.
     *
     * There are multiple variants with the following logic:
     * - 7_18_prime_pharmacy_uk, is already enabled for dok_gb
     * - 7_18_apotheek_bad_nieuweschans_de, is already enabled within the Dokteronline business unit, but the channel pricing for dok_de is disabled
     * - 7_20_apotheek_bad_nieuweschans_de, dok_de channel price is enabled, but the variant is disabled
     */
    public function testProductVariantIsNotTheFirstEnabledVariant(): void
    {
        // Arrange
        $businessUnitDK = BusinessUnitFactory::create(['code' => 'dokteronline', 'company' => 'Dokteronline']);
        $businessUnitDC = BusinessUnitFactory::create(['code' => 'doctoronline', 'company' => 'Doctoronline']);

        $channelNL = ChannelFactory::createPrefilled(['code' => 'dok_nl', 'businessUnit' => $businessUnitDK]);
        $channelDE = ChannelFactory::createPrefilled(['code' => 'dok_de', 'businessUnit' => $businessUnitDK]);
        $channelGB = ChannelFactory::createPrefilled(['code' => 'dok_gb', 'businessUnit' => $businessUnitDC]);

        $product = ProductFactory::createPrefilled(['code' => '7']);
        $product->addChannel($channelNL);
        $product->addChannel($channelDE);
        $product->addChannel($channelGB);

        // All the channels already have enabled variants
        $productVariant = ProductVariantFactory::createPrefilled(
            [
                'product' => $product,
                'code' => '7_18_prime_pharmacy_worldwide',
                'enabled' => true,
                'channelPricing' => [
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelDE->getCode(), 'enabled' => true]),
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelNL->getCode(), 'enabled' => true]),
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelGB->getCode(), 'enabled' => true]),
                ],
            ]
        );
        $product->addVariant($productVariant);

        // This variant was enabled before 7_18_prime_pharmacy_worldwide for Doctoronline
        $otherChannelVariant = ProductVariantFactory::createPrefilled(
            [
                'product' => $product,
                'code' => '7_18_prime_pharmacy_uk',
                'enabled' => true,
                'channelPricing' => [
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelGB->getCode(), 'enabled' => true]),
                ],
            ]
        );
        $product->addVariant($otherChannelVariant);

        // This variant was enabled before 7_18_prime_pharmacy_worldwide fpr Dokteronline
        $firstEnabledVariant = ProductVariantFactory::createPrefilled(
            [
                'product' => $product,
                'enabled' => true,
                'code' => '7_18_apotheek_bad_nieuweschans_de',
                'channelPricing' => [
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelDE->getCode(), 'enabled' => true]),
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelNL->getCode(), 'enabled' => true]),
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelGB->getCode(), 'enabled' => false]),
                ],
            ]
        );
        $product->addVariant($firstEnabledVariant);

        // Same channel but disabled variant, only the variant is disabled, the channel pricings are enabled
        $disabledVariant = ProductVariantFactory::createPrefilled(
            [
                'product' => $product,
                'enabled' => false,
                'code' => '7_20_apotheek_bad_nieuweschans_de',
                'channelPricing' => [
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelDE->getCode(), 'enabled' => true]),
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelNL->getCode(), 'enabled' => true]),
                    ChannelPricingFactory::createPrefilled(['channelCode' => $channelGB->getCode(), 'enabled' => false]),
                ],
            ]
        );
        $product->addVariant($disabledVariant);

        // Assert
        $this->businessUnitRepositoryMock->expects(self::once())
            ->method('getEnabledBusinessUnitsForVariant')
            ->willReturn([]);

        // Act
        $businessUnits = $this->productVariantBackInStockFactory->create($productVariant)->getInStockBusinessUnits();

        // Assert
        self::assertEmpty($businessUnits);
    }
}
