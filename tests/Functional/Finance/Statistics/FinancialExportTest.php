<?php

declare(strict_types=1);

namespace App\Tests\Functional\Finance\Statistics;

use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Finance\Statistics\FinancialExport;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Api\ExchangeRateTestFactory;
use App\Tests\Mocks\AnamnesisServiceClient;
use DateTime;
use DateTimeImmutable;
use DateTimeInterface;
use Ramsey\Uuid\Uuid;
use SplTempFileObject;
use Symfony\Component\Serializer\Encoder\CsvEncoder;
use Symfony\Component\Serializer\Serializer;

final class FinancialExportTest extends AbstractWebTestCase
{
    private FinancialExport $financialExport;

    private DateTimeImmutable $currentDateTime;
    private DateTime $fromDateTime;
    private DateTime $toDateTime;

    private Serializer $serializer;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var FinancialExport $financialExport */
        $financialExport = self::getContainer()->get(FinancialExport::class);
        $this->financialExport = $financialExport;

        $this->currentDateTime = new DateTimeImmutable('2023-04-12 07:33:00');
        $this->fromDateTime = new DateTime('2023-04-12 00:00:00');
        $this->toDateTime = new DateTime('2023-04-12 23:59:59');

        $this->serializer = new Serializer([], [new CsvEncoder()]);
    }

    public function provideExpectedCsvForGetCsvContentAsStringReturnsCorrectCsvString(): iterable
    {
        yield 'Expect all orders (expectedCsvString.csv).' => [
            __DIR__.'/resources/expectedCsvString.csv',
        ];

        yield 'Expect only the new orders (expectedCsvStringWithoutOldOrders.csv).' => [
            __DIR__.'/resources/expectedCsvStringWithoutOldOrders.csv',
            '-1 day',
        ];

        yield 'Expect only the orders from blueclinic (expectedCsvStringOnlyBlueclinicBusinessUnit.csv).' => [
            __DIR__.'/resources/expectedCsvStringOnlyBlueclinicBusinessUnit.csv',
            '+0 days',
            [
                'blueclinic',
            ],
        ];

        yield 'Expect orders (expectedCsvString.csv) for multiple business units.' => [
            __DIR__.'/resources/expectedCsvString.csv',
            '+0 days',
            [
                'dokteronline',
                'doctoronline',
                'blueclinic',
            ],
        ];
    }

    /**
     * @dataProvider provideExpectedCsvForGetCsvContentAsStringReturnsCorrectCsvString
     *
     * @param list<string> $businessUnitCodes
     */
    public function testGetCsvContentAsStringReturnsCorrectCsvString(
        string $expectedCsvFile,
        string $modifyDateTimeString = '+0 days',
        array $businessUnitCodes = [],
    ): void {
        // Arrange
        $exchangeRateTestFactory = new ExchangeRateTestFactory(self::getContainer());
        $exchangeRateTestFactory->createExchangeRate();
        $exchangeRateTestFactory->createExchangeRate('GBP', 0.87871);
        $exchangeRateTestFactory->createExchangeRate('SEK', 11.41306);

        $this->prepareDatabaseWithProducts([
            'dok_nl',
            'blueclinic_nl',
            'dok_gb',
            'dok_se',
        ], 'nl');

        $this->registerAccount('dok_nl');

        // One paid dok_nl order
        $tokenValue = $this->createCompletedOrder('dok_nl', 'nl', 'NL');
        $this->forcePayOrder($tokenValue);

        // The dok_nl order is fulfilled and a blueclinic_nl unpaid order is created
        $this->approveMedicationForOrder($tokenValue);
        $this->transport('create_supplier_service_shipment')->processOrFail(1); // Create Blueclinic follow up order.
        $this->forceTimestamps($tokenValue, $this->currentDateTime->modify($modifyDateTimeString));
        $this->fixCustomerIdInCsvString($tokenValue);

        // One paid dok_gb order
        $tokenValue = $this->createCompletedOrder('dok_gb', 'en', 'GB');
        $this->forcePayOrder($tokenValue);
        $this->markOrderAwaitingMedicationReview($tokenValue);
        $this->forceTimestamps($tokenValue);

        // One unpaid dok_se order
        $tokenValue = $this->createCompletedOrder('dok_se', 'sv', 'SE');
        $this->forceTimestamps($tokenValue);

        // One cancelled dok_nl order, the finance export reason will be 'Other'
        $userToken = $this->loginAccount('dok_nl')['token'];
        $tokenValue = $this->createCompletedOrder('dok_nl', 'nl', 'NL', $userToken);
        $this->forcePayOrder($tokenValue);
        $this->cancelOrder($tokenValue, 'free input field', $userToken);
        $this->forceTimestamps($tokenValue);

        // One cancelled dok_gb order, the finance export reason will be 'health_has_improved'
        $userToken = $this->loginAccount('dok_gb')['token'];
        $tokenValue = $this->createCompletedOrder('dok_gb', 'en', 'GB', $userToken);
        $this->forcePayOrder($tokenValue);
        $this->cancelOrder($tokenValue, 'health_has_improved', $userToken);
        $this->forceTimestamps($tokenValue);

        $tempFile = new SplTempFileObject(2 * 1024 * 1024);

        // Act
        $businessUnits = array_map(fn ($code) => $this->getBusinessUnit($code), $businessUnitCodes);
        $this->financialExport->exportOrdersToCsv($tempFile, $this->fromDateTime, $this->toDateTime, ...$businessUnits);

        // Assert
        $tempFile->rewind();
        $csvString = $tempFile->fread(2 * 1024 * 1024);
        self::assertIsString($csvString);
        self::assertNotEmpty($csvString);

        $csvString = $this->fixCustomerIdInCsvString($csvString);
        self::assertStringEqualsFile($expectedCsvFile, $csvString);
    }

    public function testGetCsvContentAsStringReturnsCorrectCsvStringWithOrderItemWithoutVariantId(): void
    {
        // Arrange
        $exchangeRateTestFactory = new ExchangeRateTestFactory(self::getContainer());
        $exchangeRateTestFactory->createExchangeRate();

        $this->prepareDatabaseWithProducts([
            'dok_de',
        ], 'de');

        $this->registerAccount('dok_de');

        $userToken = $this->loginAccount('dok_de')['token'];
        $tokenValue = $this->createCompletedOrder('dok_de', 'de', 'DE', $userToken);
        $this->forceTimestamps($tokenValue);

        $order = $this->getOrderEntityByTokenValue($tokenValue);
        foreach ($order->getItems() as $orderItem) {
            $orderItem->setVariant(null);
        }

        $this->entityManager->flush();

        $tempFile = new SplTempFileObject(2 * 1024 * 1024);

        $businessUnits = [
            $this->getBusinessUnit('dokteronline'),
        ];
        self::assertContainsOnlyInstancesOf(BusinessUnit::class, $businessUnits);

        // Act
        $this->financialExport->exportOrdersToCsv($tempFile, $this->fromDateTime, $this->toDateTime, ...$businessUnits);

        // Assert
        $tempFile->rewind();
        $csvString = $tempFile->fread(2 * 1024 * 1024);
        self::assertIsString($csvString);
        self::assertNotEmpty($csvString);

        $csvString = $this->fixCustomerIdInCsvString($csvString);
        self::assertStringEqualsFile(__DIR__.'/resources/expectedCsvStringWithoutProductVariantId.csv', $csvString);
    }

    /**
     * The dama doctrine-test-bundle doesn't reset the AUTO_INCREMENT to 1 after the tests are completed so we cannot
     * assert on dynamic customer id's, so we need to replace it with a static value.
     */
    private function fixCustomerIdInCsvString(string $csvString): string
    {
        $data = $this->serializer->decode($csvString, 'csv', [
            'csv_delimiter' => FinancialExport::CSV_DELIMITER,
        ]);

        foreach ($data as $index => $row) {
            if ($row['business_unit_name'] === 'Dokteronline') {
                $data[$index]['customer_id'] = 1;

                continue;
            }

            $data[$index]['customer_id'] = 2;
        }

        return $this->serializer->encode($data, 'csv', [
            'csv_delimiter' => FinancialExport::CSV_DELIMITER,
        ]);
    }

    /**
     * We need to force the timestamps to something static instead of the current time so we can perform assertions.
     *
     * If the order has a follow up order linked, it will also update the timestamps for the follow up order.
     */
    private function forceTimestamps(string $tokenValue, ?DateTimeInterface $dateTime = null): void
    {
        if (!$dateTime instanceof DateTimeInterface) {
            $dateTime = $this->currentDateTime;
        }

        $order = $this->getOrderEntityByTokenValue($tokenValue);

        $this->entityManager->createQueryBuilder()
            ->update(Order::class, 'o')
            ->set('o.checkoutCompletedAt', ':dateTime')
            ->set('o.prescriptionStateUpdatedAt', ':dateTime')
            ->set('o.shippingStateUpdatedAt', ':dateTime')
            ->where('o.tokenValue = :tokenValue')
            ->setParameters([
                'tokenValue' => $tokenValue,
                'dateTime' => $dateTime,
            ])
            ->getQuery()
            ->execute();

        foreach ($order->getPayments() as $payment) {
            $payment->setUpdatedAt($dateTime);
        }

        $this->entityManager->flush();

        if ($order->getFollowUpOrder() instanceof Order) {
            $followUpOrder = $order->getFollowUpOrder();

            $tokenValue = $followUpOrder->getTokenValue();
            self::assertIsString($tokenValue);
            self::assertNotEmpty($tokenValue);

            $this->forceTimestamps($tokenValue, $dateTime);
        }
    }

    /**
     * @return string the tokenValue of the created order
     */
    private function createCompletedOrder(string $channelCode, string $localeCode, string $countryCode, ?string $userToken = null): string
    {
        $tokenValue = $this->createCart($localeCode, $channelCode)['tokenValue'];
        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
                'quantity' => 1,
            ],
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_MEDICATION,
                'quantity' => 1,
                'parentProductVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
            ],
        ]);

        if ($channelCode === 'dok_nl') {
            $this->addItemsToCart($tokenValue, $channelCode, [
                [
                    'productVariantCode' => self::PRODUCT_VARIANT_CODE_SERVICE_BLUECLINIC,
                    'quantity' => 1,
                ],
            ]);
        }

        $userToken ??= $this->loginAccount($channelCode)['token'];

        $this->addMedicalQuestionnaire($tokenValue);
        $this->completeMedicalQuestionnaire($tokenValue);

        $this->getOrderEntityByTokenValue($tokenValue);

        $paymentId = $this->setAddress($tokenValue, $countryCode, $channelCode, $userToken)['payments'][0]['id'];

        $paymentMethod = 'mastercard_dokteronline';
        if ($channelCode === 'dok_gb') {
            $paymentMethod = 'mastercard_doctoronline';
        }

        $this->setPaymentMethod($tokenValue, $paymentId, $paymentMethod, $channelCode, $userToken);

        $this->completeOrder($tokenValue, $channelCode, $userToken, [
            'declare_information_truthfully',
            'terms_and_conditions',
            'collect_medical_information',
            'transfer_prescription',
        ]);

        /**
         * We need to set the UUID for the medical questionnaire to a random UUID so we can create other orders with
         * the {@see AnamnesisServiceClient::EXISTING_UUID} uuid.
         */
        $order = $this->getOrderEntityByTokenValue($tokenValue);
        $order->setMedicalQuestionnaire(Uuid::uuid4());
        $this->entityManager->flush();

        return $tokenValue;
    }

    private function approveMedicationForOrder(string $tokenValue): void
    {
        $this->markOrderAwaitingMedicationReview($tokenValue);

        $order = $this->getOrderEntityByTokenValue($tokenValue);
        /** @var OrderItem $orderItem */
        foreach ($order->getItems() as $orderItem) {
            foreach ($orderItem->getPreferredItems() as $preferredOrderItem) {
                $preferredOrderItem->setUsageAdvice('usageAdvice');
            }

            $orderItem->setUsageAdvice('usageAdvice');
        }
        $this->entityManager->flush();

        $this->approveMedication($tokenValue);
    }

    private function getBusinessUnit(string $code): BusinessUnit
    {
        $businessUnit = $this->entityManager?->getRepository(BusinessUnit::class)->findOneBy(['code' => $code]);
        self::assertInstanceOf(BusinessUnit::class, $businessUnit);

        return $businessUnit;
    }
}
