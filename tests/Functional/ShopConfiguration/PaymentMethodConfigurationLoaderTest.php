<?php

declare(strict_types=1);

namespace App\Tests\Functional\ShopConfiguration;

use App\Entity\Channel\Channel;
use App\Entity\Payment\PaymentMethod;
use App\Entity\Payment\PaymentMethodGatewayConfigInterface;
use App\Entity\Payment\PaymentMethodInterface;
use App\Factory\Payment\PaymentMethodGatewayConfigFactory;
use App\ShopConfiguration\CompositeConfigurationLoader;
use App\ShopConfiguration\ConfigurationLoaderInterface;
use App\ShopConfiguration\DefaultMaximumOrderAmount;
use App\ShopConfiguration\PaymentMethodConfigurationLoader;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ObjectManager;
use Payum\Core\Model\GatewayConfigInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Config\Definition\Processor;

final class PaymentMethodConfigurationLoaderTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private ConfigurationLoaderInterface $configurationLoader;

    public function setUp(): void
    {
        /** @var ObjectManager $objectManager */
        $objectManager = self::getContainer()->get(ObjectManager::class);

        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        $this->configurationLoader = new CompositeConfigurationLoader(
            'shop_configuration',
            'test',
            [
                new PaymentMethodConfigurationLoader(
                    $objectManager,
                    PaymentMethodGatewayConfigFactory::build(),
                ),
            ],
        );
    }

    public function testCanLoadWithSupportedGatewaysConfiguration(): void
    {
        $processor = new Processor();
        $configuration = $processor->processConfiguration(
            $this->configurationLoader,
            ['shop_configuration' => $this->provideValidCreateLoadBalancingGatewayConfiguration()]
        );

        $this->configurationLoader->load($configuration);

        $actualPaymentMethod = $this->entityManager
            ->getRepository(PaymentMethod::class)
            ->findOneBy(['code' => 'test_code']);

        self::assertInstanceOf(PaymentMethodInterface::class, $actualPaymentMethod);
        self::assertSame('test_code', $actualPaymentMethod->getCode());
        self::assertSame(
            PaymentMethodInterface::LOAD_BALANCING_GATEWAY_PLACEHOLDER,
            $actualPaymentMethod->getGatewayConfig()?->getGatewayName(),
        );
        self::assertSame(
            [
                [
                    'mollie_payment_dokteronline',
                    true,
                    100,
                    'test_mollie_identifier',
                ],
                [
                    'stripe_dokteronline',
                    true,
                    null,
                    'test_stripe_identifier',
                ],
                [
                    'stripe_blueclinic',
                    false,
                    null,
                    'test_disabled_identifier',
                ],
            ],
            $actualPaymentMethod->getPaymentMethodGatewayConfigs()
                ->map(fn (PaymentMethodGatewayConfigInterface $config) => [
                    $config->getGatewayConfig()->getGatewayName(),
                    $config->isEnabled(),
                    $config->getDailyLimit(),
                    $config->getPaymentServiceProviderIdentifier(),
                ])
                ->toArray()
        );

        self::assertCount(3, $actualPaymentMethod->getPaymentMethodGatewayConfigs());
        self::assertCount(9, $actualPaymentMethod->getChannels());
        self::assertCount(1, $actualPaymentMethod->getTranslations());
        self::assertTrue($actualPaymentMethod->isDifferentAddressAllowed());
        self::assertTrue($actualPaymentMethod->isReshipmentCostsAllowed());
        self::assertTrue($actualPaymentMethod->isEnabled());

        foreach ($actualPaymentMethod->getPaymentMethodChannels() as $paymentMethodChannel) {
            $channel = $paymentMethodChannel->getChannel();
            self::assertInstanceOf(Channel::class, $channel);

            if ($channel->getCode() === 'dok_nl') {
                self::assertSame(30000, $paymentMethodChannel->getMaximumOrderTotal());
                continue;
            }

            if ($channel->getCode() === 'dok_fr') {
                self::assertNull($paymentMethodChannel->getMaximumOrderTotal());
                continue;
            }

            self::assertSame(
                DefaultMaximumOrderAmount::getByChannelCode($channel->getCode())->value,
                $paymentMethodChannel->getMaximumOrderTotal()
            );
        }

        foreach ($actualPaymentMethod->getPaymentMethodGatewayConfigs() as $paymentMethodGatewayConfig) {
            self::assertInstanceOf(GatewayConfigInterface::class, $paymentMethodGatewayConfig->getGatewayConfig());

        }
    }

    public function testCanLoadExistingWithSupportedGatewaysConfiguration(): void
    {
        $processor = new Processor();
        $configuration = $processor->processConfiguration(
            $this->configurationLoader,
            ['shop_configuration' => $this->provideValidCreateLoadBalancingGatewayConfiguration()]
        );

        $this->configurationLoader->load($configuration);

        $configuration = $processor->processConfiguration(
            $this->configurationLoader,
            ['shop_configuration' => $this->provideValidUpdateLoadBalancingGatewayConfiguration()]
        );

        $this->configurationLoader->load($configuration);

        $actualPaymentMethod = $this->entityManager
            ->getRepository(PaymentMethod::class)
            ->findOneBy(['code' => 'test_code']);

        self::assertInstanceOf(PaymentMethodInterface::class, $actualPaymentMethod);
        self::assertSame('test_code', $actualPaymentMethod->getCode());
        self::assertSame(
            PaymentMethodInterface::LOAD_BALANCING_GATEWAY_PLACEHOLDER,
            $actualPaymentMethod->getGatewayConfig()?->getGatewayName(),
        );
        self::assertSame(
            [
                [
                    'mollie_payment_dokteronline',
                    true,
                    200,
                    'test_mollie_identifier',
                ],
                [
                    'stripe_dokteronline',
                    false,
                    null,
                    'test_stripe_identifier',
                ],
                [
                    'stripe_blueclinic',
                    false,
                    null,
                    'test_disabled_identifier',
                ],
            ],
            $actualPaymentMethod->getPaymentMethodGatewayConfigs()
                ->map(fn (PaymentMethodGatewayConfigInterface $config) => [
                    $config->getGatewayConfig()->getGatewayName(),
                    $config->isEnabled(),
                    $config->getDailyLimit(),
                    $config->getPaymentServiceProviderIdentifier(),
                ])
                ->toArray()
        );

        self::assertCount(3, $actualPaymentMethod->getPaymentMethodGatewayConfigs());
        self::assertCount(8, $actualPaymentMethod->getChannels());
        self::assertCount(1, $actualPaymentMethod->getTranslations());
        self::assertTrue($actualPaymentMethod->isDifferentAddressAllowed());
        self::assertTrue($actualPaymentMethod->isReshipmentCostsAllowed());
        self::assertTrue($actualPaymentMethod->isEnabled());

        foreach ($actualPaymentMethod->getPaymentMethodChannels() as $paymentMethodChannel) {
            $channel = $paymentMethodChannel->getChannel();
            self::assertInstanceOf(Channel::class, $channel);
            if ($channel->getCode() === 'dok_nl') {
                self::assertSame(30000, $paymentMethodChannel->getMaximumOrderTotal());
                continue;
            }

            self::assertSame(
                DefaultMaximumOrderAmount::getByChannelCode($channel->getCode())->value,
                $paymentMethodChannel->getMaximumOrderTotal()
            );
        }

        foreach ($actualPaymentMethod->getPaymentMethodGatewayConfigs() as $paymentMethodGatewayConfig) {
            self::assertInstanceOf(GatewayConfigInterface::class, $paymentMethodGatewayConfig->getGatewayConfig());
        }
    }

    /**
     * @return array<'payment_methods', array<array-key, array{
     *     'code': string,
     *     'gateway_name': string,
     *     'factory_name': string,
     *     'channels': list<string>,
     *     'translations': array<string, array{
     *         name: string,
     *         instructions: string|null,
     *     }>,
     *     'reshipment_costs_allowed': bool,
     *     'different_address_allowed': bool,
     *     'maximum_order_amounts': array<string, int>|null,
     *     'enabled': bool,
     *     'icon': string|null,
     *     'supported_gateways': array<array-key, array{
     *         'gateway_name': string,
     *         'enabled': bool,
     *         'daily_limit': int|null,
     *         'payment_service_provider_identifier': string,
     *     }>
     * }>>
     */
    private function provideValidCreateLoadBalancingGatewayConfiguration(): array
    {
        return [
            'payment_methods' => [
                [
                    'code' => 'test_code',
                    'icon' => 'test_icon',
                    'gateway_name' => PaymentMethodInterface::LOAD_BALANCING_GATEWAY_PLACEHOLDER,
                    'factory_name' => PaymentMethodInterface::LOAD_BALANCING_GATEWAY_PLACEHOLDER,
                    'supported_gateways' => [
                        [
                            'gateway_name' => 'mollie_payment_dokteronline',
                            'enabled' => true,
                            'daily_limit' => 100,
                            'payment_service_provider_identifier' => 'test_mollie_identifier',
                        ],
                        [
                            'gateway_name' => 'stripe_dokteronline',
                            'enabled' => true,
                            'daily_limit' => null,
                            'payment_service_provider_identifier' => 'test_stripe_identifier',
                        ],
                        [
                            'gateway_name' => 'stripe_blueclinic',
                            'enabled' => false,
                            'daily_limit' => null,
                            'payment_service_provider_identifier' => 'test_disabled_identifier',
                        ],
                    ],
                    'channels' => [
                        'dok_nl',
                        'dok_be',
                        'dok_fr',
                        'dok_gb',
                        'dok_pl',
                        'dok_ro',
                        'dok_se',
                        'dok_dk',
                        'dok_ch',
                    ],
                    'translations' => [
                        'en' => [
                            'name' => 'Test name',
                            'instructions' => 'Test translation',
                        ],
                    ],
                    'different_address_allowed' => true,
                    'reshipment_costs_allowed' => true,
                    'maximum_order_amounts' => [
                        'dok_nl' => 30000,
                        'dok_fr' => 0,
                    ],
                    'enabled' => true,
                ],
            ],
        ];
    }

    /**
     * @return array<'payment_methods', array<array-key, array{
     *     'code': string,
     *     'gateway_name': string,
     *     'factory_name': string,
     *     'channels': list<string>,
     *     'translations': array<string, array{
     *         name: string,
     *         instructions: string|null,
     *     }>,
     *     'reshipment_costs_allowed': bool,
     *     'different_address_allowed': bool,
     *     'maximum_order_amounts': array<string, int|null>|null,
     *     'enabled': bool,
     *     'icon': string|null,
     *     'supported_gateways': array<array-key, array{
     *         'gateway_name': string,
     *         'enabled': bool,
     *         'daily_limit': int|null,
     *         'payment_service_provider_identifier': string,
     *     }>
     * }>>
     */
    private function provideValidUpdateLoadBalancingGatewayConfiguration(): array
    {
        return [
            'payment_methods' => [
                [
                    'code' => 'test_code',
                    'icon' => 'test_icon',
                    'gateway_name' => PaymentMethodInterface::LOAD_BALANCING_GATEWAY_PLACEHOLDER,
                    'factory_name' => PaymentMethodInterface::LOAD_BALANCING_GATEWAY_PLACEHOLDER,
                    'supported_gateways' => [
                        [
                            'gateway_name' => 'mollie_payment_dokteronline',
                            'enabled' => true,
                            'daily_limit' => 200,
                            'payment_service_provider_identifier' => 'test_mollie_identifier',
                        ],
                    ],
                    'channels' => [
                        'dok_nl',
                        'dok_be',
                        'dok_gb',
                        'dok_pl',
                        'dok_ro',
                        'dok_se',
                        'dok_dk',
                        'dok_ch',
                    ],
                    'translations' => [
                        'en' => [
                            'name' => 'Test name',
                            'instructions' => 'Test translation',
                        ],
                    ],
                    'different_address_allowed' => true,
                    'reshipment_costs_allowed' => true,
                    'maximum_order_amounts' => [
                        'dok_nl' => 30000,
                        'dok_pl' => null,
                    ],
                    'enabled' => true,
                ],
            ],
        ];
    }
}
