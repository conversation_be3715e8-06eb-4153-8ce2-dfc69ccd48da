<?php

declare(strict_types=1);

namespace App\Tests\Functional\Payment\MessageHandler;

use App\Entity\Payment\GatewayConfig;
use App\Entity\Payment\PaymentMethod;
use App\Entity\Payment\PaymentMethodGatewayConfig;
use App\Payment\Message\IncrementDailyUsage;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Messenger\MessageBusInterface;
use Zenstruck\Messenger\Test\InteractsWithMessenger;

final class IncrementDailyUsageHandlerTest extends KernelTestCase
{
    use InteractsWithMessenger;

    private MessageBusInterface $messageBus;
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        $container = self::getContainer();

        $messageBus = $container->get(MessageBusInterface::class);
        self::assertInstanceOf(MessageBusInterface::class, $messageBus);
        $this->messageBus = $messageBus;

        $entityManager = $container->get(EntityManagerInterface::class);
        self::assertInstanceOf(EntityManagerInterface::class, $entityManager);
        $this->entityManager = $entityManager;
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->entityManager->close();
        unset($this->entityManager, $this->messageBus);
    }

    /**
     * @dataProvider providePaymentMethodGateWayConfigWithUsage
     */
    public function testInvokeIncrementsDailyUsage(int $expectedDailyUsage, int $dailyUsage = 0, ?DateTimeImmutable $dateTimeImmutable = null): void
    {
        // Arrange
        $paymentMethodGatewayConfig = $this->createPaymentMethodGatewayConfig($dailyUsage, $dateTimeImmutable);

        // Act
        $this->messageBus->dispatch(new IncrementDailyUsage((int) $paymentMethodGatewayConfig->getId()));
        $this->transport('async')->processOrFail(1);

        // Assert
        self::assertEquals($expectedDailyUsage, $paymentMethodGatewayConfig->getDailyUsage());
    }

    /**
     * @return iterable<array-key, array{int, int, DateTimeImmutable|null}>
     */
    public function providePaymentMethodGateWayConfigWithUsage(): iterable
    {
        yield 'Payment method gateway config without usage' => [1, 0, null];
        yield 'Payment method gateway config with usage and current date' => [5, 4, new DateTimeImmutable()];
        yield 'Payment method gateway config with usage and yesterdays date' => [1, 4, new DateTimeImmutable('-1 days')];
    }

    public function createPaymentMethodGatewayConfig(int $dailyUsage = 0, DateTimeImmutable $dailyUsageUpdatedAt = null): PaymentMethodGatewayConfig
    {
        $paymentMethod = new PaymentMethod();
        $paymentMethod->setCode('payment-method-code');
        $paymentMethod->setCurrentLocale('de-DE');

        $gatewayConfig = new GatewayConfig();
        $gatewayConfig->setGatewayName('gateway-name');
        $gatewayConfig->setFactoryName('factory-name');
        $gatewayConfig->setConfig(['factory' => 'factory-name']);

        $paymentMethodGatewayConfig = new PaymentMethodGatewayConfig(
            'payment-service-provider-identifier',
            $paymentMethod,
            $gatewayConfig
        );
        $paymentMethodGatewayConfig->setDailyUsage($dailyUsage);
        $paymentMethodGatewayConfig->setDailyUsageUpdatedAt($dailyUsageUpdatedAt);

        $this->entityManager->persist($paymentMethod);
        $this->entityManager->persist($gatewayConfig);
        $this->entityManager->persist($paymentMethodGatewayConfig);
        $this->entityManager->flush();

        return $paymentMethodGatewayConfig;
    }
}
