<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Order\OrderSequence;
use App\Factory\Order\OrderSequenceFactory;
use App\Repository\OrderSequenceRepositoryInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class OrderSequenceRepositoryTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private OrderSequenceRepositoryInterface $orderSequenceRepository;

    public function setUp(): void
    {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        /** @var OrderSequenceRepositoryInterface $orderSequenceRepository */
        $orderSequenceRepository = $this->entityManager->getRepository(OrderSequence::class);
        $this->orderSequenceRepository = $orderSequenceRepository;
    }

    public function testCanFindSequence(): void
    {
        $businessUnitRepository = $this->entityManager
            ->getRepository(BusinessUnit::class);

        $businessUnits = $businessUnitRepository
            ->createQueryBuilder('businessUnit', 'businessUnit.code')
            ->groupBy('businessUnit.orderNumberPrefix')
            ->getQuery()
            ->getResult();

        $orderSequenceFactory = new OrderSequenceFactory();
        foreach ($businessUnits as $businessUnit) {
            $orderSequence = $orderSequenceFactory->createNewForBusinessUnit($businessUnit);

            $this->entityManager->persist($orderSequence);
        }

        $this->entityManager->flush();

        $orderSequences = $this->entityManager
            ->getRepository(OrderSequence::class)
            ->createQueryBuilder('orderSequence', 'orderSequence.prefix')
            ->getQuery()
            ->getResult();

        $businessUnit = $businessUnitRepository->findOneBy(['code' => 'doctoronline']);

        self::assertInstanceOf(BusinessUnit::class, $businessUnit);
        self::assertSame($orderSequences['DO'], $this->orderSequenceRepository->findSequence($businessUnit));
        self::assertSame($orderSequences['DO'], $this->orderSequenceRepository->findSequence($businessUnits['dokteronline']));
        self::assertSame($orderSequences['BC'], $this->orderSequenceRepository->findSequence($businessUnits['blueclinic']));
    }
}
