<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Entity\Addressing\Address;
use App\Entity\Channel\Channel;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\Order\Order;
use App\Repository\AddressRepository;
use App\Tests\Util\PersistedFactory\AddressFactory;
use App\Tests\Util\PersistedFactory\CustomerFactory;
use App\Tests\Util\PersistedFactory\OrderFactory;
use DateTime;

final class AddressRepositoryTest extends AbstractRepositoryTestCase
{
    private AddressRepository $addressRepository;
    private CustomerFactory $customerFactory;
    private AddressFactory $addressFactory;
    private OrderFactory $orderFactory;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var AddressRepository $addressRepository */
        $addressRepository = $this->entityManager->getRepository(Address::class);
        self::assertInstanceOf(AddressRepository::class, $addressRepository);
        $this->addressRepository = $addressRepository;

        $this->customerFactory = CustomerFactory::build($this->entityManager);
        $this->addressFactory = AddressFactory::build($this->entityManager);
        $this->orderFactory = new OrderFactory($this->entityManager);
    }

    public function testShippingAddressIsUsedByOtherCustomer(): void
    {
        // Arrange: Create customers, addresses, and orders
        $channel = $this->entityManager->getRepository(Channel::class)->findOneBy(['code' => 'dok_nl']);

        $customerOne = $this->customerFactory->create($this->getCustomerPool(), email: '<EMAIL>');
        $customerTwo = $this->customerFactory->create($this->getCustomerPool(), email: '<EMAIL>');
        $addressOne = $this->addressFactory->create(customer: $customerOne);
        $addressTwo = $this->addressFactory->create(customer: $customerTwo);

        $orderOne = $this->orderFactory->create(state: Order::STATE_NEW, channel: $channel);
        $orderOne->setCustomer($customerOne);
        $orderOne->setShippingAddress($addressOne);
        $orderOne->setBillingAddress($addressOne);

        $orderTwo = $this->orderFactory->create(state: Order::STATE_NEW, channel: $channel);
        $orderTwo->setCustomer($customerTwo);
        $orderTwo->setShippingAddress($addressTwo);
        $orderTwo->setBillingAddress($addressTwo);

        $this->entityManager->flush();

        // Act: Call the method under test
        $result = $this->addressRepository->shippingAddressIsUsedByOtherCustomer($orderOne, new DateTime('-1 year'));

        // Assert: Verify the result is true
        $this->assertTrue($result);
    }

    public function testShippingAddressIsUsedByOtherCustomerOutsideTheTimeframe(): void
    {
        // Arrange: Create customers, addresses, and orders
        $channel = $this->entityManager->getRepository(Channel::class)->findOneBy(['code' => 'dok_nl']);

        $customerOne = $this->customerFactory->create($this->getCustomerPool(), email: '<EMAIL>');
        $customerTwo = $this->customerFactory->create($this->getCustomerPool(), email: '<EMAIL>');
        $addressOne = $this->addressFactory->create(customer: $customerOne);
        $addressTwo = $this->addressFactory->create(customer: $customerTwo);
        $addressThree = $this->addressFactory->create(customer: $customerTwo);
        $addressFour = $this->addressFactory->create(customer: $customerOne);

        $orderOne = $this->orderFactory->create(state: Order::STATE_NEW, channel: $channel);
        $orderOne->setCustomer($customerOne);
        $orderOne->setShippingAddress($addressOne);
        $orderOne->setBillingAddress($addressOne);

        // This order is not used to check if the address is used by another customer because it's outside the timeframe
        $orderTwo = $this->orderFactory->create(state: Order::STATE_NEW, createdAt: new DateTime('-1 year -1 day'), channel: $channel);
        $orderTwo->setCustomer($customerTwo);
        $orderTwo->setShippingAddress($addressTwo);
        $orderTwo->setBillingAddress($addressTwo);

        // This order is not used to check if the address is used by another customer because it's in the cart state
        $orderThree = $this->orderFactory->create(state: Order::STATE_CART, channel: $channel);
        $orderThree->setCustomer($customerTwo);
        $orderThree->setShippingAddress($addressThree);
        $orderThree->setBillingAddress($addressThree);

        // This order is not used to check if the address is used by another customer because it's the same customer
        $orderThree = $this->orderFactory->create(state: Order::STATE_NEW, channel: $channel);
        $orderThree->setCustomer($customerOne);
        $orderThree->setShippingAddress($addressFour);
        $orderThree->setBillingAddress($addressFour);

        $this->entityManager->flush();

        // Act: Call the method under test
        $result = $this->addressRepository->shippingAddressIsUsedByOtherCustomer($orderOne, new DateTime('-1 year'));

        // Assert: Verify the result is true
        $this->assertFalse($result);
    }

    public function testShippingAddressIsUsedByFollowUpOrder(): void
    {
        // Arrange channels & Customers
        $bc = $this->entityManager->getRepository(Channel::class)->findOneBy(['code' => 'blueclinic_nl']);
        $dokNl = $this->entityManager->getRepository(Channel::class)->findOneBy(['code' => 'dok_nl']);

        $customerDok = $this->customerFactory->create($this->getCustomerPool(), email: '<EMAIL>');
        $customerBc = $this->customerFactory->create($this->getCustomerPool(code: 'blueclinic'), email: '<EMAIL>');

        // Arrange orders
        $addressOne = $this->addressFactory->create(customer: $customerDok);
        $addressTwo = $this->addressFactory->create(customer: $customerBc);
        $addressThree = $this->addressFactory->create(customer: $customerDok);

        // Arrange existing orders
        $orderOne = $this->orderFactory->create(state: Order::STATE_FULFILLED, tokenValue: 'dok-og-order', channel: $dokNl);
        $orderOne->setCustomer($customerDok);
        $orderOne->setShippingAddress($addressOne);
        $orderOne->setBillingAddress($addressOne);

        $orderTwo = $this->orderFactory->create(state: Order::STATE_FULFILLED, tokenValue: 'bc-og-order', channel: $bc);
        $orderTwo->setCustomer($customerBc);
        $orderTwo->setParentOrder($orderOne);
        $orderTwo->setShippingAddress($addressTwo);
        $orderTwo->setBillingAddress($addressTwo);

        // Arrange new order
        $orderThree = $this->orderFactory->create(state: Order::STATE_NEW, tokenValue: 'dok-new-order', channel: $dokNl);
        $orderThree->setCustomer($customerDok);
        $orderThree->setShippingAddress($addressThree);
        $orderThree->setBillingAddress($addressThree);

        $this->entityManager->flush();

        // Act
        $result = $this->addressRepository->shippingAddressIsUsedByOtherCustomer($orderThree, new DateTime('-1 year'));

        // Assert: Verify the result is true
        $this->assertFalse($result);
    }

    public function testFindByCustomerAndCountryCode(): void
    {
        // Arrange
        $customerOne = $this->customerFactory->create(customerPool: $this->getCustomerPool());
        $addressOne = $this->addressFactory->create(customer: $customerOne);
        $addressTwo = $this->addressFactory->create(customer: $customerOne, pickupPointName: 'DHL ServicePoint');

        $customerTwo = $this->customerFactory->create(customerPool: $this->getCustomerPool(), email: '<EMAIL>');
        $this->addressFactory->create(customer: $customerTwo);

        // Act
        $addresses = $this->addressRepository->findByCustomerAndCountryCode($customerOne);

        // Assert
        self::assertCount(2, $addresses);
        self::assertSame($addressOne, $addresses[0]);
        self::assertSame($addressTwo, $addresses[1]);
    }

    public function testFindByCustomerAndCountryCodeAndFilterByPickupPointFalse(): void
    {
        // Arrange
        $customerOne = $this->customerFactory->create(customerPool: $this->getCustomerPool());
        $address = $this->addressFactory->create(customer: $customerOne);
        $this->addressFactory->create(customer: $customerOne, pickupPointName: 'DHL ServicePoint');

        $customerTwo = $this->customerFactory->create(customerPool: $this->getCustomerPool(), email: '<EMAIL>');
        $this->addressFactory->create(customer: $customerTwo);
        $this->addressFactory->create(customer: $customerTwo, pickupPointName: 'DHL ServicePoint');

        // Act
        $addresses = $this->addressRepository->findByCustomerAndCountryCode(
            customer: $customerOne,
            filterByPickupPoint: false,
        );

        // Assert
        self::assertCount(1, $addresses);
        self::assertSame($address, $addresses[0]);
    }

    private function getCustomerPool(string $code = 'dokteronline'): CustomerPool
    {
        $customerPool = $this->entityManager->getRepository(CustomerPool::class)->findOneBy([
            'code' => 'dokteronline',
        ]);
        self::assertInstanceOf(CustomerPool::class, $customerPool);

        return $customerPool;
    }
}
