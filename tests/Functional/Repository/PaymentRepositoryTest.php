<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Entity\Payment\GatewayConfig;
use App\Entity\Payment\Payment;
use App\Entity\Payment\PaymentMethod;
use App\Entity\Payment\PaymentMethodGatewayConfig;
use App\Repository\PaymentRepository;
use App\Tests\Util\PersistedFactory\OrderFactory;
use App\Tests\Util\PersistedFactory\PaymentFactory;

final class PaymentRepositoryTest extends AbstractRepositoryTestCase
{
    private readonly PaymentFactory $paymentFactory;
    private readonly OrderFactory $orderFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $container = self::getContainer();

        $paymentFactory = $container->get(PaymentFactory::class);
        self::assertInstanceOf(PaymentFactory::class, $paymentFactory);
        $this->paymentFactory = $paymentFactory;

        $orderFactory = $container->get(OrderFactory::class);
        self::assertInstanceOf(OrderFactory::class, $orderFactory);
        $this->orderFactory = $orderFactory;
    }

    public function testFindPaymentByDetailReference(): void
    {
        // Arrange
        $reference = 'test-reference-value';
        $gatewayName = 'gateway-name';
        $factoryName = 'test-factory-name';
        $referenceKey = 'test-reference-key';

        $paymentMethod = new PaymentMethod();
        $paymentMethod->setCode('payment-method-code');
        $paymentMethod->setCurrentLocale('de-DE');

        $gatewayConfig = new GatewayConfig();
        $gatewayConfig->setGatewayName($gatewayName);
        $gatewayConfig->setFactoryName($factoryName);
        $gatewayConfig->setConfig(['factory' => $factoryName]);

        $paymentMethodGatewayConfig = new PaymentMethodGatewayConfig(
            'payment-service-provider-identifier',
            $paymentMethod,
            $gatewayConfig,
        );

        $this->entityManager->persist($paymentMethod);
        $this->entityManager->persist($gatewayConfig);
        $this->entityManager->persist($paymentMethodGatewayConfig);
        $this->entityManager->flush();

        $expectedPayment = $this->paymentFactory->create(
            amount: 1000,
            method: $paymentMethod,
            order: $this->orderFactory->create(),
            state: 'new',
            details: [
                $factoryName => [
                    $referenceKey => $reference,
                ],
            ],
            paymentMethodGatewayConfig: $paymentMethodGatewayConfig,
        );

        // Act
        /** @var PaymentRepository $paymentRepository */
        $paymentRepository = $this->entityManager->getRepository(Payment::class);
        $actualPayment = $paymentRepository->findPaymentByDetailReference(
            $reference,
            $gatewayName,
            $factoryName,
            $referenceKey,
        );

        // Assert
        self::assertSame($expectedPayment, $actualPayment);
    }
}
