<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Entity\Payment\PaymentMethodGatewayConfig;
use App\Entity\Payment\PaymentMethodGatewayConfigInterface;
use App\Entity\Payment\PaymentMethodInterface;
use App\Repository\PaymentMethodGatewayConfigRepositoryInterface;
use App\Tests\Util\Builder\PaymentMethodBuilder;
use App\Tests\Util\Factory\GatewayConfigFactory;
use DateInterval;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class PaymentMethodGatewayConfigRepositoryTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private PaymentMethodGatewayConfigRepositoryInterface $paymentMethodGatewayConfigRepository;

    public function setUp(): void
    {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        /** @var PaymentMethodGatewayConfigRepositoryInterface $paymentMethodGatewayConfigRepository */
        $paymentMethodGatewayConfigRepository = $this->entityManager->getRepository(PaymentMethodGatewayConfig::class);
        $this->paymentMethodGatewayConfigRepository = $paymentMethodGatewayConfigRepository;
    }

    public function testCanFindOneByLoadBalancingGatewayPaymentMethod(): void
    {
        $gatewayConfig = GatewayConfigFactory::create(['gatewayName' => 'test_gateway']);

        $this->entityManager->persist($gatewayConfig);

        $paymentMethods['test_ideal'] = PaymentMethodBuilder::create($this->entityManager)
            ->setPaymentMethod(code: 'test_ideal', icon: 'test_ideal')
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: $gatewayConfig,
            )
            ->getPaymentMethod();

        $paymentMethods['test_visa'] = PaymentMethodBuilder::create($this->entityManager)
            ->setPaymentMethod(code: 'test_visa', icon: 'test_visa')
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: $gatewayConfig,
            )
            ->getPaymentMethod();

        $this->entityManager->flush();

        foreach (['test_ideal', 'test_visa'] as $paymentMethodCode) {
            $actualPaymentMethodGatewayConfig = $this->paymentMethodGatewayConfigRepository
                ->findOneByFallbackGatewayPaymentMethod($paymentMethods[$paymentMethodCode]);

            self::assertInstanceOf(PaymentMethodGatewayConfigInterface::class, $actualPaymentMethodGatewayConfig);
            self::assertSame($paymentMethodCode, $actualPaymentMethodGatewayConfig->getPaymentMethod()->getCode());
        }
    }

    public function testCanFindOneByFallbackGatewayPaymentMethodWithDisabledSupportedGateway(): void
    {
        $paymentMethod = PaymentMethodBuilder::create($this->entityManager)
            ->setPaymentMethod(code: 'test_ideal', icon: 'test_ideal')
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'disabled_gateway'],
                enabled: false,
            )
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'enabled_gateway'],
            )
            ->getPaymentMethod();

        $this->entityManager->flush();

        $actualPaymentMethodGatewayConfig = $this->paymentMethodGatewayConfigRepository
            ->findOneByFallbackGatewayPaymentMethod($paymentMethod);

        self::assertInstanceOf(PaymentMethodGatewayConfigInterface::class, $actualPaymentMethodGatewayConfig);
        self::assertSame('enabled_gateway', $actualPaymentMethodGatewayConfig->getGatewayConfig()->getGatewayName());
    }

    public function testCanFindOneByFallbackPaymentMethodDoesNotReturnDailyLimitedSupportedGateways(): void
    {
        $paymentMethod = PaymentMethodBuilder::create($this->entityManager)
            ->setPaymentMethod(code: 'test_gateway', icon: 'test_gateway')
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'test_limit_reached_daily_usage_today_gateway'],
                dailyLimit: 5,
                dailyUsage: 5,
                dailyUsageUpdatedAt: new DateTimeImmutable('today'),
            )
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'test_limit_exceeded_daily_usage_today_gateway'],
                dailyLimit: 5,
                dailyUsage: 6, // exceeded limit, should never occur
                dailyUsageUpdatedAt: new DateTimeImmutable('today'),
            )
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'test_limit_reached_daily_usage_yesterday_gateway'],
                dailyLimit: 5,
                dailyUsage: 5,
                dailyUsageUpdatedAt: new DateTimeImmutable('yesterday'),
            )
            ->getPaymentMethod();

        $this->entityManager->flush();

        $actualPaymentMethodGatewayConfig = $this->paymentMethodGatewayConfigRepository
            ->findOneByFallbackGatewayPaymentMethod($paymentMethod);

        self::assertInstanceOf(PaymentMethodGatewayConfigInterface::class, $actualPaymentMethodGatewayConfig);
        self::assertSame(
            'test_limit_reached_daily_usage_yesterday_gateway',
            $actualPaymentMethodGatewayConfig->getGatewayConfig()->getGatewayName()
        );
    }

    public function testCanFindOneByFallbackGatewayRotatesReturnedPaymentMethodGatewayConfig(): void
    {
        $paymentMethod = PaymentMethodBuilder::create($this->entityManager)
            ->setPaymentMethod(code: 'test_gateway', icon: 'test_gateway')
            ->addPaymentMethodGatewayConfig(gatewayConfig: ['gatewayName' => 'test_one_gateway'])
            ->addPaymentMethodGatewayConfig(gatewayConfig: ['gatewayName' => 'test_two_gateway'])
            ->addPaymentMethodGatewayConfig(gatewayConfig: ['gatewayName' => 'test_three_gateway'])
            ->getPaymentMethod();

        $this->entityManager->flush();

        $expectedSupportedGatewayNames = [
            // iteration 1
            'test_one_gateway',
            'test_two_gateway',
            'test_three_gateway',
            // iteration 2
            'test_one_gateway',
            'test_two_gateway',
            'test_three_gateway',
            // iteration 3
            'test_one_gateway',
            'test_two_gateway',
            'test_three_gateway',
        ];

        foreach ($expectedSupportedGatewayNames as $expectedSupportedGatewayName) {
            $actualPaymentMethodGatewayConfig = $this->paymentMethodGatewayConfigRepository
                ->findOneByFallbackGatewayPaymentMethod($paymentMethod);

            self::assertInstanceOf(PaymentMethodGatewayConfigInterface::class, $actualPaymentMethodGatewayConfig);
            self::assertSame(
                $expectedSupportedGatewayName,
                $actualPaymentMethodGatewayConfig->getGatewayConfig()->getGatewayName()
            );

            $actualPaymentMethodGatewayConfig->incrementDailyUsage();

            $this->entityManager->flush();
        }
    }

    public function testCanFindOneByFallbackGatewayRotatesReturnedPaymentMethodGatewayConfigWithDailyLimits(): void
    {
        $paymentMethod = PaymentMethodBuilder::create($this->entityManager)
            ->setPaymentMethod(code: 'test_gateway', icon: 'test_gateway')
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'test_one_gateway'],
                dailyLimit: 1
            )
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'test_two_gateway'],
                dailyLimit: 2
            )
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'test_three_gateway'],
                dailyLimit: 3
            )
            ->getPaymentMethod();

        $this->entityManager->flush();

        $expectedSupportedGatewayNames = [
            // iteration 1
            'test_one_gateway',
            'test_two_gateway',
            'test_three_gateway',
            // iteration 2
            'test_two_gateway',
            'test_three_gateway',
            // iteration 3
            'test_three_gateway',
        ];

        foreach ($expectedSupportedGatewayNames as $expectedSupportedGatewayName) {
            $actualPaymentMethodGatewayConfig = $this->paymentMethodGatewayConfigRepository
                ->findOneByFallbackGatewayPaymentMethod($paymentMethod);

            self::assertInstanceOf(PaymentMethodGatewayConfigInterface::class, $actualPaymentMethodGatewayConfig);
            self::assertSame(
                $expectedSupportedGatewayName,
                $actualPaymentMethodGatewayConfig->getGatewayConfig()->getGatewayName()
            );

            $actualPaymentMethodGatewayConfig->incrementDailyUsage();

            $this->entityManager->flush();
        }
    }

    public function testCanFindOneByFallbackGatewayRotatesReturnedSupportedGatewayByDailyUsageUpdates(): void
    {
        $paymentMethod = PaymentMethodBuilder::create($this->entityManager)
            ->setPaymentMethod(code: 'test_gateway', icon: 'test_gateway')
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'test_no_limit_no_daily_usage_gateway'],
            )
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'test_no_limit_daily_usage_today_gateway'],
                dailyUsage: 1,
                dailyUsageUpdatedAt: new DateTimeImmutable('today'),
            )
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'test_no_limit_daily_usage_yesterday_gateway'],
                dailyUsage: 1,
                dailyUsageUpdatedAt: new DateTimeImmutable('yesterday'),
            )
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'test_limit_no_daily_usage_gateway'],
                dailyLimit: 5
            )
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'test_limit_reached_daily_usage_today_gateway'],
                dailyLimit: 5,
                dailyUsage: 5,
                dailyUsageUpdatedAt: (new DateTimeImmutable('today'))->add(new DateInterval('PT1H')),
            )
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'test_limit_not_reached_daily_usage_today_gateway'],
                dailyLimit: 5,
                dailyUsage: 3,
                dailyUsageUpdatedAt: (new DateTimeImmutable('today'))->add(new DateInterval('PT2H')),
            )
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'test_limit_reached_daily_usage_yesterday_gateway'],
                dailyLimit: 5,
                dailyUsage: 5,
                dailyUsageUpdatedAt: (new DateTimeImmutable('yesterday'))->add(new DateInterval('PT1H')),
            )
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'test_limit_not_reached_daily_usage_yesterday_gateway'],
                dailyLimit: 5,
                dailyUsage: 3,
                dailyUsageUpdatedAt: (new DateTimeImmutable('yesterday'))->add(new DateInterval('PT2H')),
            )
            ->getPaymentMethod();

        $this->entityManager->flush();

        $expectedSupportedGatewayNames = [
            'test_no_limit_no_daily_usage_gateway',
            'test_limit_no_daily_usage_gateway',
            'test_no_limit_daily_usage_yesterday_gateway',
            'test_limit_reached_daily_usage_yesterday_gateway',
            'test_limit_not_reached_daily_usage_yesterday_gateway',
        ];

        foreach ($expectedSupportedGatewayNames as $expectedSupportedGatewayName) {
            $actualPaymentMethodGatewayConfig = $this->paymentMethodGatewayConfigRepository
                ->findOneByFallbackGatewayPaymentMethod($paymentMethod);

            self::assertInstanceOf(PaymentMethodGatewayConfigInterface::class, $actualPaymentMethodGatewayConfig);
            self::assertSame(
                $expectedSupportedGatewayName,
                $actualPaymentMethodGatewayConfig->getGatewayConfig()->getGatewayName()
            );

            $actualPaymentMethodGatewayConfig->incrementDailyUsage();

            $this->entityManager->flush();
        }
    }

    /**
     * @return iterable<array-key, array{0: PaymentMethodInterface, 1: string}>
     */
    public function providePaymentMethodAndGateway(): iterable
    {
        $paymentMethod = PaymentMethodBuilder::create()
            ->setPaymentMethod(code: 'test_ideal', icon: 'ideal_dokteronline')
            ->addPaymentMethodGatewayConfig(gatewayConfig: ['gatewayName' => 'test_payment_service_provider'])
            ->addPaymentMethodGatewayConfig(gatewayConfig: ['gatewayName' => 'test_mollie_payment'])
            ->getPaymentMethod();

        yield [
            $paymentMethod,
            'test_mollie_payment',
        ];

        yield [
            $paymentMethod,
            'test_payment_service_provider',
        ];
    }

    /**
     * @dataProvider providePaymentMethodAndGateway
     */
    public function testFindOneByPaymentMethodAndGatewayName(
        PaymentMethodInterface $paymentMethod,
        string $gatewayName,
    ): void {
        $this->entityManager->persist($paymentMethod);
        foreach ($paymentMethod->getPaymentMethodGatewayConfigs() as $paymentMethodGatewayConfig) {
            $this->entityManager->persist($paymentMethodGatewayConfig);
            $this->entityManager->persist($paymentMethodGatewayConfig->getGatewayConfig());
        }

        $this->entityManager->flush();

        $actualPaymentMethodGatewayConfig = $this->paymentMethodGatewayConfigRepository
            ->findOneByPaymentMethodAndGatewayName($paymentMethod, $gatewayName);

        self::assertInstanceOf(PaymentMethodGatewayConfigInterface::class, $actualPaymentMethodGatewayConfig);
        self::assertSame($gatewayName, $actualPaymentMethodGatewayConfig->getGatewayConfig()->getGatewayName());
    }

    public function testCanFindOneByLoadBalancingGatewayPaymentMethodWithSingleDailyLimit(): void
    {
        $gatewayConfig = GatewayConfigFactory::create(['gatewayName' => 'test_gateway']);

        $this->entityManager->persist($gatewayConfig);

        $paymentMethod = PaymentMethodBuilder::create($this->entityManager)
            ->setPaymentMethod(code: 'test_ideal', icon: 'test_ideal')
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: $gatewayConfig,
                dailyLimit: 1
            )
            ->getPaymentMethod();

        $this->entityManager->flush();

        $actualPaymentMethodGatewayConfig = $this->paymentMethodGatewayConfigRepository
            ->findOneByFallbackGatewayPaymentMethod($paymentMethod);

        self::assertInstanceOf(PaymentMethodGatewayConfigInterface::class, $actualPaymentMethodGatewayConfig);
        self::assertSame(
            'test_gateway',
            $actualPaymentMethodGatewayConfig->getGatewayConfig()->getGatewayName()
        );

        $actualPaymentMethodGatewayConfig->incrementDailyUsage();

        $this->entityManager->flush();

        $actualPaymentMethodGatewayConfig = $this->paymentMethodGatewayConfigRepository
            ->findOneByFallbackGatewayPaymentMethod($paymentMethod);

        self::assertNull($actualPaymentMethodGatewayConfig);
    }
}
