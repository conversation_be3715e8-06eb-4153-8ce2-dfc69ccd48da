<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\DependencyInjection\ContainerInterface;

abstract class AbstractRepositoryTestCase extends KernelTestCase
{
    protected readonly EntityManagerInterface $entityManager;
    protected readonly ContainerInterface $container;

    protected function setUp(): void
    {
        parent::setUp();

        $container = self::getContainer();
        self::assertInstanceOf(ContainerInterface::class, $container);
        $this->container = $container;

        $entityManager = $container->get(EntityManagerInterface::class);
        self::assertInstanceOf(EntityManagerInterface::class, $entityManager);
        $this->entityManager = $entityManager;
    }
}
