<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Entity\Channel\Channel;
use App\Entity\Product\Product;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariant;
use App\Entity\Supplier\Supplier;
use App\Repository\ProductVariantRepositoryInterface;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Util\PersistedFactory\ChannelPricingFactory;
use App\Tests\Util\PersistedFactory\ProductFactory;
use App\Tests\Util\PersistedFactory\ProductVariantFactory;
use DateTime;
use DateTimeImmutable;
use DateTimeInterface;

final class ProductVariantRepositoryTest extends AbstractWebTestCase
{
    private ProductVariantRepositoryInterface $productVariantRepository;
    private ProductFactory $productFactory;
    private ProductVariantFactory $productVariantFactory;
    private ChannelPricingFactory $channelPricingFactory;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var ProductVariantRepositoryInterface $productVariantRepository */
        $productVariantRepository = $this->entityManager?->getRepository(ProductVariant::class);
        self::assertInstanceOf(ProductVariantRepositoryInterface::class, $productVariantRepository);
        $this->productVariantRepository = $productVariantRepository;

        $productFactory = self::getContainer()->get(ProductFactory::class);
        self::assertInstanceOf(ProductFactory::class, $productFactory);
        $this->productFactory = $productFactory;

        $productVariantFactory = self::getContainer()->get(ProductVariantFactory::class);
        self::assertInstanceOf(ProductVariantFactory::class, $productVariantFactory);
        $this->productVariantFactory = $productVariantFactory;

        $channelPricingFactory = self::getContainer()->get(ChannelPricingFactory::class);
        self::assertInstanceOf(ChannelPricingFactory::class, $channelPricingFactory);
        $this->channelPricingFactory = $channelPricingFactory;
    }

    /**
     * Regression test for https://mv-jira-1.atlassian.net/browse/DV-9105.
     */
    public function testFindOneByCodePrefixAndSupplierRanksCountryOverWorldwideVariant(): void
    {
        // Arrange
        $product = $this->productFactory->create(
            code: '3635',
            name: 'Vardenafil',
            type: ProductType::MEDICATION,
        );

        $productVariantOne = $this->productVariantFactory->create(
            product: $product,
            code: '3635_33403_ndsm_apotheek_worldwide',
            name: 'Vardenafil 20 mg 8 tabl.',
            supplierIdentifier: 'ndsm-apotheek',
        );

        $productVariantTwo = $this->productVariantFactory->create(
            product: $product,
            code: '3635_33403_ndsm_apotheek_de',
            name: 'Vardenafil 20 mg 8 tabl.',
            supplierIdentifier: 'ndsm-apotheek',
            country: 'DE',
        );

        $this->channelPricingFactory->create(
            productVariant: $productVariantOne,
            channelCode: 'dok_de',
            price: 12120,
        );

        $this->channelPricingFactory->create(
            productVariant: $productVariantTwo,
            channelCode: 'dok_de',
            price: 12120,
        );

        $channel = $this->entityManager?->getRepository(Channel::class)->findOneBy(['code' => 'dok_de']);
        self::assertInstanceOf(Channel::class, $channel, "Channel with code 'dok_de' does not exist.");

        $supplier = $this->entityManager?->getRepository(Supplier::class)->findOneBy(['identifier' => 'ndsm-apotheek']);
        self::assertInstanceOf(Supplier::class, $supplier, "Supplier with identifier 'ndsm-apotheek' does not exist.");

        // Act
        $productVariant = $this->productVariantRepository->findOneByCodePrefixAndSupplier(
            $channel,
            '3635_33403',
            $supplier,
        );

        // Assert
        self::assertInstanceOf(ProductVariant::class, $productVariant);
        self::assertSame('3635_33403_ndsm_apotheek_de', $productVariant->getCode());
    }

    /**
     * @return iterable<string, array{
     *     0: DateTimeInterface,
     *     1: int,
     *     2: int,
     *     3: int,
     * }>
     */
    public function provideDataForTestIncrementAmountOrderedToday(): iterable
    {
        yield 'updatedAt is today, currentOrderedAmount is 0 so a quantity of 46 should result in currentOrderedAmount 46.' => [
            new DateTimeImmutable(),
            0,
            46,
            46,
        ];

        yield 'updatedAt is today, currentOrderedAmount is 46 so a quantity of 46 should result in currentOrderedAmount 92.' => [
            new DateTimeImmutable(),
            46,
            46,
            92,
        ];

        yield 'updatedAt is yesterday, currentOrderedAmount is 46 so a quantity of 46 should result in currentOrderedAmount 46.' => [
            (new DateTime())->modify('-1 day, -1 second'),
            46,
            46,
            46,
        ];
    }

    /**
     * @dataProvider provideDataForTestIncrementAmountOrderedToday
     */
    public function testIncrementAmountOrderedToday(
        DateTimeInterface $currentAmountOrderedTodayUpdatedAt,
        int $currentAmountOrderedToday,
        int $orderItemQuantity,
        int $expectedAmountOrderedToday,
    ): void {
        // Arrange
        $this->prepareDatabaseWithProducts(['dok_de'], 'de');

        $this->entityManager?->getConnection()->executeQuery(
            '
                UPDATE sylius_product_variant
                SET amount_ordered_today = :amountOrderedToday,
                    amount_ordered_today_updated_at = :amountOrderedTodayUpdatedAt;
            ',
            [
                'amountOrderedTodayUpdatedAt' => $currentAmountOrderedTodayUpdatedAt->format('Y-m-d H:i:s'),
                'amountOrderedToday' => $currentAmountOrderedToday,
            ],
        );

        $productVariant = $this->productVariantRepository->findOneByCode(self::PRODUCT_VARIANT_CODE_OTC);
        self::assertInstanceOf(ProductVariant::class, $productVariant);

        // Act
        $this->productVariantRepository->incrementAmountOrderedToday($productVariant, $orderItemQuantity);

        // Assert
        $this->entityManager?->refresh($productVariant);
        self::assertSame($expectedAmountOrderedToday, $productVariant->getAmountOrderedToday());
    }

    public function testCanDisableAllProductVariantsBySupplier(): void
    {
        $supplier = $suppliers[] = new Supplier();
        $supplier->setName('Prima Pharmacy');
        $supplier->setIdentifier('prima-pharmacy');

        $supplier = $suppliers[] = new Supplier();
        $supplier->setName('NSDM apotheek');
        $supplier->setIdentifier('nsdm-apotheek');

        $product = new Product();
        $product->setCode('viagra');

        $productVariant = $productVariants[] = new ProductVariant();
        $productVariant->setSupplier($suppliers[0]);
        $productVariant->setCode('viagra_25mg');
        $productVariant->setProduct($product);
        $productVariant->enable();

        $productVariant = $productVariants[] = new ProductVariant();
        $productVariant->setSupplier($suppliers[1]);
        $productVariant->setCode('viagra_50mg');
        $productVariant->setProduct($product);
        $productVariant->enable();

        foreach ([...$suppliers, ...$productVariants, $product] as $entity) {
            $this->entityManager?->persist($entity);
        }

        $this->entityManager?->flush();
        $this->entityManager?->clear();

        $this->productVariantRepository->disableAllProductVariantsBySupplier($suppliers[0]);

        $enabledVariants = $this->productVariantRepository->findBy(['supplier' => $suppliers[1]]);
        self::assertContainsOnlyInstancesOf(ProductVariant::class, $enabledVariants);
        self::assertNotEmpty($enabledVariants);
        foreach ($enabledVariants as $enabledVariant) {
            self::assertTrue($enabledVariant->isEnabled());
        }

        $disabledVariants = $this->productVariantRepository->findBy(['supplier' => $suppliers[0]]);
        self::assertContainsOnlyInstancesOf(ProductVariant::class, $disabledVariants);
        self::assertNotEmpty($disabledVariants);
        foreach ($disabledVariants as $disabledVariant) {
            self::assertFalse($disabledVariant->isEnabled());
        }
    }
}
