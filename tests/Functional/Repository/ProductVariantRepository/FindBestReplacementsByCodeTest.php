<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository\ProductVariantRepository;

use App\Entity\Addressing\Country;
use App\Entity\Channel\Channel;
use App\Entity\Product\Product;
use App\Entity\Product\ProductAttribute;
use App\Entity\Product\ProductOptionValue;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariant;
use App\Entity\Product\ProductVariantTranslation;
use App\Entity\Supplier\Supplier;
use App\Entity\Supplier\SupplierCountryShipping;
use App\Repository\ProductRepository;
use App\Repository\ProductVariantRepository;
use App\Repository\SupplierRepository;
use App\Tests\Util\Factory\ChannelPricingFactory;
use App\Tests\Util\Factory\ProductFactory;
use App\Tests\Util\Factory\ProductVariantFactory;
use App\Tests\Util\Factory\SupplierFactory;
use App\Tests\Util\Factory\TranslationFactory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

/**
 * Tests for @see https://mv-jira-1.atlassian.net/browse/DV-9897.
 *
 * @phpstan-type SupplierData array{
 *      supplier: Supplier,
 *      price: int,
 *      preferredSupplier?: bool,
 *      isPreferredVariantForMinimumDailyOrders?: bool,
 *      isWorldwide?: bool,
 *  }
 */
final class FindBestReplacementsByCodeTest extends KernelTestCase
{
    private const string COUNTRY_CODE = 'DE';
    private const string LOCALE_CODE = 'de';
    private const string CHANNEL_CODE = 'dok_de';

    private const string NDSM_APOTHEEK = 'ndsm-apotheek';
    private const string PRIME_PHARMACY = 'prime-pharmacy';
    private const string DISFARCON = 'disfarcon';
    private const string APOTHEEK_BAD_NIEUWESCHANS = 'apotheek-bad-nieuweschans';

    private EntityManagerInterface $entityManager;
    private ProductVariantRepository $productVariantRepository;
    private ProductRepository $productRepository;
    private SupplierRepository $supplierRepository;
    private Channel $channel;
    private Country $country;

    protected function setUp(): void
    {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        /** @var ProductVariantRepository $productVariantRepository */
        $productVariantRepository = $this->entityManager->getRepository(ProductVariant::class);
        $this->productVariantRepository = $productVariantRepository;

        /** @var SupplierRepository $supplierRepository */
        $supplierRepository = $this->entityManager->getRepository(Supplier::class);
        $this->supplierRepository = $supplierRepository;

        /** @var ProductRepository $productRepository */
        $productRepository = $this->entityManager->getRepository(Product::class);
        $this->productRepository = $productRepository;

        /** @var Channel $channel */
        $channel = $this->entityManager->getRepository(Channel::class)->findOneBy(['code' => self::CHANNEL_CODE]);
        $this->channel = $channel;

        // Make sure the channel supports multiple shipments
        if (!$channel->isMultipleShipmentsAllowed()) {
            $channel->setMultipleShipmentsAllowed(true);
            $this->entityManager->flush();
        }

        /** @var Country $country */
        $country = $this->entityManager->getRepository(Country::class)->findOneBy(['code' => self::COUNTRY_CODE]);
        $this->country = $country;
    }

    /**
     * One supplier as the best option.
     */
    public function testSingleSupplierScenario1(): void
    {
        // Arrange
        $variants = $this->prepareSingleSupplierScenario1();

        $option1 = [$variants['7_18_test_ndsm_apotheek_de'], $variants['11_12_test_ndsm_apotheek_de']];
        $option2 = [$variants['7_18_test_prime_pharmacy_de'], $variants['11_12_test_prime_pharmacy_de']];
        $option3 = [$variants['7_18_test_my_own_chemist_de'], $variants['11_12_test_my_own_chemist_de']];
        $option4 = [$variants['7_18_test_my_own_chemist_de'], $variants['11_12_test_prime_pharmacy_de']];
        $option5 = [$variants['7_18_test_ndsm_apotheek_de'], $variants['11_12_test_prime_pharmacy_de']];

        // Act
        $resultOption1 = $this->productVariantRepository->findCheapestReplacementsByCode($this->channel, self::LOCALE_CODE, $option1);
        $resultOption2 = $this->productVariantRepository->findCheapestReplacementsByCode($this->channel, self::LOCALE_CODE, $option2);
        $resultOption3 = $this->productVariantRepository->findCheapestReplacementsByCode($this->channel, self::LOCALE_CODE, $option3);
        $resultOption4 = $this->productVariantRepository->findCheapestReplacementsByCode($this->channel, self::LOCALE_CODE, $option4);
        $resultOption5 = $this->productVariantRepository->findCheapestReplacementsByCode($this->channel, self::LOCALE_CODE, $option5);

        // Assert: Option 1 (ndsm-apotheek/ndsm-apotheek) is always the best option.
        $this->assertEquals($variants['7_18_test_ndsm_apotheek_de'], $resultOption1[0]);
        $this->assertEquals($variants['11_12_test_ndsm_apotheek_de'], $resultOption1[1]);

        $this->assertEquals($variants['7_18_test_ndsm_apotheek_de'], $resultOption2[0]);
        $this->assertEquals($variants['11_12_test_ndsm_apotheek_de'], $resultOption2[1]);

        $this->assertEquals($variants['7_18_test_ndsm_apotheek_de'], $resultOption3[0]);
        $this->assertEquals($variants['11_12_test_ndsm_apotheek_de'], $resultOption3[1]);

        $this->assertEquals($variants['7_18_test_ndsm_apotheek_de'], $resultOption4[0]);
        $this->assertEquals($variants['11_12_test_ndsm_apotheek_de'], $resultOption4[1]);

        $this->assertEquals($variants['7_18_test_ndsm_apotheek_de'], $resultOption5[0]);
        $this->assertEquals($variants['11_12_test_ndsm_apotheek_de'], $resultOption5[1]);
    }

    /**
     * One supplier is the best option taking tax rates into account.
     */
    public function testSingleSupplierScenario2(): void
    {
        // Arrange
        $variants = $this->prepareSingleSupplierScenario2();

        $option1 = [$variants['7_18_test_ndsm_apotheek_de']];
        $option2 = [$variants['7_18_test_prime_pharmacy_de']];
        $option3 = [$variants['7_18_test_my_own_chemist_de']];

        // Act
        $resultOption1 = $this->productVariantRepository->findCheapestReplacementsByCode($this->channel, self::LOCALE_CODE, $option1);
        $resultOption2 = $this->productVariantRepository->findCheapestReplacementsByCode($this->channel, self::LOCALE_CODE, $option2);
        $resultOption3 = $this->productVariantRepository->findCheapestReplacementsByCode($this->channel, self::LOCALE_CODE, $option3);

        // Assert: Option 1 (my-own-chemist) is always the best option.
        $this->assertEquals($variants['7_18_test_my_own_chemist_de'], $resultOption1[0]);

        $this->assertEquals($variants['7_18_test_my_own_chemist_de'], $resultOption2[0]);

        $this->assertEquals($variants['7_18_test_my_own_chemist_de'], $resultOption3[0]);
    }

    public function testScenario1(): void
    {
        // Arrange
        $variants = $this->prepareScenario1();

        // Act
        $result = $this->productVariantRepository->findCheapestReplacementsByCode(
            $this->channel,
            self::LOCALE_CODE,
            [
                $variants['2742_27917_ndsm_apotheek_worldwide'],
                $variants['2925_28641_apotheek_bad_nieuweschans_de'],
                $variants['3277_31063_apotheek_bad_nieuweschans_de'],
            ],
        );

        // Assert
        self::assertEquals($variants['2742_27917_apotheek_bad_nieuweschans_de'], $result[0]);
        self::assertEquals($variants['2925_28641_apotheek_bad_nieuweschans_de'], $result[1]);
        self::assertEquals($variants['3277_31063_apotheek_bad_nieuweschans_de'], $result[2]);
    }

    public function testScenario2(): void
    {
        // Arrange
        $variants = $this->prepareScenario2();

        // Act
        $result = $this->productVariantRepository->findCheapestReplacementsByCode(
            $this->channel,
            self::LOCALE_CODE,
            [
                $variants['2613_35102_apotheek_bad_nieuweschans_de'],
                $variants['2613_34413_apotheek_bad_nieuweschans_de'],
                $variants['2580_27435_ndsm_apotheek_worldwide'],
            ],
        );

        // Assert
        self::assertEquals($variants['2613_35102_apotheek_bad_nieuweschans_de'], $result[0]);
        self::assertEquals($variants['2613_34413_apotheek_bad_nieuweschans_de'], $result[1]);
        self::assertEquals($variants['2580_27435_ndsm_apotheek_worldwide'], $result[2]);
    }

    public function testScenario3(): void
    {
        // Arrange
        $variants = $this->prepareScenario3();

        // Act
        $result = $this->productVariantRepository->findCheapestReplacementsByCode(
            $this->channel,
            self::LOCALE_CODE,
            [
                $variants['3277_33552_apotheek_bad_nieuweschans_worldwide'],
                $variants['7_26258_disfarcon_worldwide'],
            ],
        );

        // Assert
        self::assertEquals($variants['7_26258_ndsm_apotheek_worldwide'], $result[0]);
        self::assertEquals($variants['3277_33552_ndsm_apotheek_worldwide'], $result[1]);
    }

    public function testScenario4(): void
    {
        // Arrange
        $variants = $this->prepareScenario4();

        // Act
        $result = $this->productVariantRepository->findCheapestReplacementsByCode(
            $this->channel,
            self::LOCALE_CODE,
            [
                $variants['3277_33401_prime_pharmacy_de'],
                $variants['3635_33452_prime_pharmacy_de'],
                $variants['2572_27734_prime_pharmacy_de'],
                $variants['11_13_ndsm_apotheek_worldwide'],
            ],
        );

        // Assert
        self::assertEquals($variants['11_13_prime_pharmacy_de'], $result[0]);
        self::assertEquals($variants['2572_27734_prime_pharmacy_de'], $result[1]);
        self::assertEquals($variants['3277_33401_prime_pharmacy_de'], $result[2]);
        self::assertEquals($variants['3635_33452_prime_pharmacy_de'], $result[3]);
    }

    /**
     * @return array<array-key, ProductVariant>
     */
    private function prepareSingleSupplierScenario1(): array
    {
        $ndsmApotheek = $this->createSupplier(identifier: 'ndsm-apotheek', shippingCost: 200, handlingFee: 100);
        $primePharmacy = $this->createSupplier(identifier: 'prime-pharmacy', shippingCost: 400, handlingFee: 100);
        $myOwnChemist = $this->createSupplier(identifier: 'my-own-chemist', shippingCost: 300, handlingFee: 200, dailyOrderLimitReached: true);

        // Viagra
        $viagraVariants = $this->createProductVariants('7', '18', [
            [
                'supplier' => $ndsmApotheek,
                'price' => 200,
                'preferredSupplier' => true,
                'isPreferredVariantForMinimumDailyOrders' => true,
            ],
            [
                'supplier' => $primePharmacy,
                'price' => 300,
            ],
            [
                'supplier' => $myOwnChemist,
                'price' => 150,
                'isPreferredVariantForMinimumDailyOrders' => true,
            ],
        ]);

        // Cialis
        $cialisVariants = $this->createProductVariants('11', '12', [
            [
                'supplier' => $ndsmApotheek,
                'price' => 800,
                'preferredSupplier' => true,
                'isPreferredVariantForMinimumDailyOrders' => true,
            ],
            [
                'supplier' => $primePharmacy,
                'price' => 400,
            ],
            [
                'supplier' => $myOwnChemist,
                'price' => 600,
                'isPreferredVariantForMinimumDailyOrders' => true,
            ],
        ]);

        $this->entityManager->flush();

        return array_merge_recursive($viagraVariants, $cialisVariants);
    }

    /**
     * @return array<array-key, ProductVariant>
     */
    private function prepareSingleSupplierScenario2(): array
    {
        $ndsmApotheek = $this->createSupplier(identifier: 'ndsm-apotheek', shippingCost: 200, handlingFee: 100, taxRateInPercentage: 20.00);
        $primePharmacy = $this->createSupplier(identifier: 'prime-pharmacy', shippingCost: 200, handlingFee: 100, taxRateInPercentage: 15.00);
        $myOwnChemist = $this->createSupplier(identifier: 'my-own-chemist', shippingCost: 200, handlingFee: 100, taxRateInPercentage: 0.00);

        // Viagra
        $viagraVariants = $this->createProductVariants('7', '18', [
            [
                'supplier' => $ndsmApotheek,
                'price' => 200,
            ],
            [
                'supplier' => $primePharmacy,
                'price' => 200,
            ],
            [
                'supplier' => $myOwnChemist,
                'price' => 200,
            ],
        ]);

        $this->entityManager->flush();

        return $viagraVariants;
    }

    /** @return array<array-key, ProductVariant> */
    private function prepareScenario1(): array
    {
        $suppliers = $this->prepareSuppliers();

        $variants2742 = $this->createProductVariants('2742', '27917', [
            [
                'supplier' => $suppliers[self::APOTHEEK_BAD_NIEUWESCHANS],
                'price' => 1664,
                'preferredSupplier' => true,
            ],
            [
                'supplier' => $suppliers[self::DISFARCON],
                'price' => 681,
                'isWorldwide' => true,
            ],
            [
                'supplier' => $suppliers[self::NDSM_APOTHEEK],
                'price' => 1100,
                'isWorldwide' => true,
            ],
        ]);

        $variants2925 = $this->createProductVariants('2925', '28641', [
            [
                'supplier' => $suppliers[self::APOTHEEK_BAD_NIEUWESCHANS],
                'price' => 2446,
                'preferredSupplier' => true,
            ],
            [
                'supplier' => $suppliers[self::DISFARCON],
                'price' => 1186,
                'isWorldwide' => true,
            ],
            [
                'supplier' => $suppliers[self::NDSM_APOTHEEK],
                'price' => 2700,
                'isWorldwide' => true,
            ],
        ]);

        $variants3277 = $this->createProductVariants('3277', '31063', [
            [
                'supplier' => $suppliers[self::APOTHEEK_BAD_NIEUWESCHANS],
                'price' => 3958,
                'preferredSupplier' => true,
            ],
            [
                'supplier' => $suppliers[self::DISFARCON],
                'price' => 3000,
                'preferredSupplier' => true,
                'isWorldwide' => true,
            ],
            [
                'supplier' => $suppliers[self::NDSM_APOTHEEK],
                'price' => 6810,
                'isWorldwide' => true,
            ],
        ]);

        $this->entityManager->flush();

        return array_merge_recursive($variants2742, $variants2925, $variants3277);
    }

    /**
     * @return array<array-key, ProductVariant>
     */
    private function prepareScenario2(): array
    {
        $suppliers = $this->prepareSuppliers();

        $variants261335102 = $this->createProductVariants('2613', '35102', [
            [
                'supplier' => $suppliers[self::APOTHEEK_BAD_NIEUWESCHANS],
                'price' => 877,
            ],
        ]);

        $variants261334413 = $this->createProductVariants('2613', '34413', [
            [
                'supplier' => $suppliers[self::APOTHEEK_BAD_NIEUWESCHANS],
                'price' => 495,
            ],
            [
                'supplier' => $suppliers[self::NDSM_APOTHEEK],
                'price' => 1050,
                'isWorldwide' => true,
            ],
        ]);

        $variants2580 = $this->createProductVariants('2580', '27435', [
            [
                'supplier' => $suppliers[self::NDSM_APOTHEEK],
                'price' => 3700,
                'isWorldwide' => true,
            ],
        ]);

        $this->entityManager->flush();

        return array_merge_recursive($variants261335102, $variants261334413, $variants2580);
    }

    /**
     * @return array<array-key, ProductVariant>
     */
    private function prepareScenario3(): array
    {
        $suppliers = $this->prepareSuppliers();

        $variants7 = $this->createProductVariants('7', '26258', [
            [
                'supplier' => $suppliers[self::APOTHEEK_BAD_NIEUWESCHANS],
                'price' => 5930,
                'preferredSupplier' => true,
                'isWorldwide' => true,
            ],
            [
                'supplier' => $suppliers[self::NDSM_APOTHEEK],
                'price' => 5930,
                'preferredSupplier' => true,
                'isWorldwide' => true,
            ],
            [
                'supplier' => $suppliers[self::DISFARCON],
                'price' => 6512,
                'preferredSupplier' => true,
                'isWorldwide' => true,
            ],
        ]);

        $variants3277 = $this->createProductVariants('3277', '33552', [
            [
                'supplier' => $suppliers[self::APOTHEEK_BAD_NIEUWESCHANS],
                'price' => 6860,
                'isWorldwide' => true,
            ],
            [
                'supplier' => $suppliers[self::NDSM_APOTHEEK],
                'price' => 6860,
                'isWorldwide' => true,
            ],
        ]);

        $this->entityManager->flush();

        return array_merge_recursive($variants7, $variants3277);
    }

    /**
     * @return array<array-key, ProductVariant>
     */
    private function prepareScenario4(): array
    {
        $suppliers = $this->prepareSuppliers();

        $variants11 = $this->createProductVariants('11', '13', [
            [
                'supplier' => $suppliers[self::NDSM_APOTHEEK],
                'price' => 3735,
                'isWorldwide' => true,
            ],
            [
                'supplier' => $suppliers[self::PRIME_PHARMACY],
                'price' => 4015,
            ],
        ]);

        $variants2572 = $this->createProductVariants('2572', '27734', [
            [
                'supplier' => $suppliers[self::APOTHEEK_BAD_NIEUWESCHANS],
                'price' => 820,
                'preferredSupplier' => true,
            ],
            [
                'supplier' => $suppliers[self::DISFARCON],
                'price' => 975,
                'preferredSupplier' => true,
                'isWorldwide' => true,
            ],
            [
                'supplier' => $suppliers[self::NDSM_APOTHEEK],
                'price' => 2970,
                'isWorldwide' => true,
            ],
            [
                'supplier' => $suppliers[self::PRIME_PHARMACY],
                'price' => 498,
                'preferredSupplier' => true,
            ],
        ]);

        $variants3277 = $this->createProductVariants('3277', '33401', [
            [
                'supplier' => $suppliers[self::APOTHEEK_BAD_NIEUWESCHANS],
                'price' => 789,
            ],
            [
                'supplier' => $suppliers[self::NDSM_APOTHEEK],
                'price' => 2940,
                'isWorldwide' => true,
            ],
            [
                'supplier' => $suppliers[self::PRIME_PHARMACY],
                'price' => 789,
                'preferredSupplier' => true,
            ],
        ]);

        $variants3635 = $this->createProductVariants('3635', '33452', [
            [
                'supplier' => $suppliers[self::APOTHEEK_BAD_NIEUWESCHANS],
                'price' => 1308,
                'preferredSupplier' => true,
            ],
            [
                'supplier' => $suppliers[self::PRIME_PHARMACY],
                'price' => 1095,
                'preferredSupplier' => true,
            ],
        ]);

        $this->entityManager->flush();

        return array_merge_recursive($variants11, $variants2572, $variants3277, $variants3635);
    }

    /**
     * @return array<string, Supplier>
     */
    private function prepareSuppliers(): array
    {
        return [
            self::NDSM_APOTHEEK => $this->updateSupplier(identifier: self::NDSM_APOTHEEK, shippingCost: 650, handlingFee: 0),
            self::APOTHEEK_BAD_NIEUWESCHANS => $this->updateSupplier(identifier: self::APOTHEEK_BAD_NIEUWESCHANS, shippingCost: 436, handlingFee: 300),
            self::DISFARCON => $this->updateSupplier(identifier: self::DISFARCON, shippingCost: 1589, handlingFee: 0),
            self::PRIME_PHARMACY => $this->updateSupplier(identifier: self::PRIME_PHARMACY, shippingCost: 458, handlingFee: 330),
        ];
    }

    private function updateSupplier(
        string $identifier,
        int $shippingCost,
        int $handlingFee,
        float $taxRateInPercentage = 0.00000,
    ): Supplier {
        $supplier = $this->supplierRepository->findOneBy(['identifier' => $identifier]);
        if (!$supplier instanceof Supplier) {
            $supplier = $this->createSupplier($identifier, $shippingCost, $handlingFee);
        }

        $supplierCountryShipping = $supplier->getSupplierCountryShippingByCountryCode($this->country->getCode());
        self::assertInstanceOf(SupplierCountryShipping::class, $supplierCountryShipping);
        $supplierCountryShipping->setShippingCost($shippingCost);
        $supplierCountryShipping->setTaxRateInPercentage($taxRateInPercentage);

        $supplier->setHandlingFee($handlingFee);

        $this->entityManager->flush();

        return $supplier;
    }

    private function createSupplier(
        string $identifier,
        int $shippingCost,
        int $handlingFee,
        bool $dailyOrderLimitReached = false,
        float $taxRateInPercentage = 0.00000,
    ): Supplier {
        $supplierCountryShipping = new SupplierCountryShipping();
        $supplierCountryShipping->setEnabled(true);
        $supplierCountryShipping->setCountry($this->country);
        $supplierCountryShipping->setShippingCost($shippingCost);
        $supplierCountryShipping->setTaxRateInPercentage($taxRateInPercentage);

        $supplier = SupplierFactory::create([
            'name' => ucfirst($identifier),
            'identifier' => 'test-'.$identifier,
            'handlingFee' => $handlingFee,
            'supplierCountriesShippings' => [
                $supplierCountryShipping,
            ],
            'dailyOrderLimit' => $dailyOrderLimitReached ? 10 : 5,
            'amountOrderedToday' => 10,
        ]);

        $supplierCountryShipping->setSupplier($supplier);

        $this->entityManager->persist($supplierCountryShipping);
        $this->entityManager->persist($supplier);

        return $supplier;
    }

    /**
     * @param array<SupplierData> $suppliers
     * @return array<string, ProductVariant>
     */
    private function createProductVariants(
        string $productCode,
        string $variantCode,
        array $suppliers,
    ): array {
        $product = $this->productRepository->findOneByCode($productCode);
        if (!$product instanceof Product) {
            $product = ProductFactory::create([
                'code' => $productCode,
                'enabled' => true,
                'currentLocale' => self::LOCALE_CODE,
            ]);
        }

        $productAttribute = $this->entityManager->getRepository(ProductAttribute::class)->findOneBy(['code' => 'type']);
        $productAttributeValue = ProductFactory::createProductAttributeValue(
            [
                'product' => $product,
                'value' => [ProductType::MEDICATION->value],
                'attribute' => $productAttribute,
            ]
        );
        $product->addAttribute($productAttributeValue);

        $this->entityManager->persist($productAttributeValue);
        $this->entityManager->persist($product);

        $this->entityManager->flush();

        $productVariants = [];

        foreach ($suppliers as $supplierData) {
            $productVariant = $this->createProductVariant($productCode, $variantCode, $supplierData, $product);
            $productVariants[$productVariant->getCode()] = $productVariant;
        }

        return $productVariants;
    }

    /**
     * @param SupplierData $supplierData
     */
    private function createProductVariant(
        string $productCode,
        string $variantCode,
        mixed $supplierData,
        Product $product,
    ): ProductVariant {
        $productVariantCode = $this->getVariantCode(
            $productCode,
            $variantCode,
            $supplierData['supplier'],
            $supplierData['isWorldwide'] ?? false,
        );

        /**
         * isPreferredVariantForMinimumDailyOrders is true when:
         * amountOrderedToday < preferredVariantForMinimumDailyOrders
         * {@see \App\Repository\Query\ProductVariantQuery::OptimizeVariantCostPriceForMultipleSuppliers}
         * WHEN pv.amount_ordered_today < pv.preferred_variant_for_minimum_daily_orders THEN 1
         */
        $amountOrderedToday = $supplierData['isPreferredVariantForMinimumDailyOrders'] ?? false ? 0 : 10;

        $productVariant = ProductVariantFactory::create([
            'product' => $product,
            'code' => $productVariantCode,
            'supplier' => $supplierData['supplier'],
            'preferredSupplier' => $supplierData['preferredSupplier'] ?? false,
            'preferredVariantForMinimumDailyOrders' => 5,
            'amountOrderedToday' => $amountOrderedToday,
            'costPrice' => $supplierData['price'],
        ]);

        $channelPricing = ChannelPricingFactory::create([
            'price' => $supplierData['price'],
            'productVariant' => $productVariant,
            'channelCode' => $this->channel->getCode(),
            'enabled' => true,
        ]);

        $productVariant->addChannelPricing($channelPricing);

        TranslationFactory::addTranslation(
            $productVariant,
            ProductVariantTranslation::class,
            self::LOCALE_CODE,
            ['name' => "Translation for {$productVariantCode}"],
        );

        // Options: Form
        /** @var ProductOptionValue $formOptionValue */
        $formOptionValue = $this->entityManager->getRepository(ProductOptionValue::class)->findOneBy(
            ['code' => 'tablet']
        );
        $productVariant->addOptionValue($formOptionValue);

        $this->entityManager->persist($channelPricing);
        $this->entityManager->persist($productVariant);

        return $productVariant;
    }

    private function getVariantCode(string $productCode, string $variantCode, Supplier $supplier, bool $isWorldwide = false): string
    {
        return implode(
            '_',
            [
                $productCode,
                $variantCode,
                str_replace('-', '_', (string) $supplier->getIdentifier()),
                $isWorldwide ? 'worldwide' : strtolower((string) $this->country->getCode()),
            ]
        );
    }
}
