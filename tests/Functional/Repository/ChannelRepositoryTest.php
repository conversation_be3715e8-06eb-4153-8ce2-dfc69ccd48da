<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Entity\Addressing\Country;
use App\Entity\Channel\Channel;
use App\Repository\ChannelRepositoryInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class ChannelRepositoryTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private ChannelRepositoryInterface $channelRepository;

    public function setUp(): void
    {
        $this->entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->channelRepository = $this->entityManager->getRepository(Channel::class);
    }

    public function testCanFindByCountries(): void
    {
        $countries = $this->entityManager
            ->getRepository(Country::class)
            ->findBy(['code' => ['DE', 'NL']]);

        self::assertCount(
            3,
            $this->channelRepository->findByCountries(...$countries)
        );
    }

    public function testCanFindOneByCountryCodeForPreprLink(): void
    {
        $channel = $this->entityManager
            ->getRepository(Channel::class)
            ->findOneBy(['code' => 'dok_nl']);

        self::assertSame(
            $channel,
            $this->channelRepository->findOneByCountryCodeForPreprLink('nl')
        );
    }
}
