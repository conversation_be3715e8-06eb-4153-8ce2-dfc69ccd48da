<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository\Query;

use App\Repository\Query\OptimizeVariantsQueryGenerator;
use PHPUnit\Framework\TestCase;

/**
 * @phpstan-type VariantCodes list<string>
 */
final class OptimizeVariantsQueryGeneratorTest extends TestCase
{
    private OptimizeVariantsQueryGenerator $optimizeVariantsQueryGenerator;

    protected function setUp(): void
    {
        $this->optimizeVariantsQueryGenerator = OptimizeVariantsQueryGenerator::create();
    }

    /**
     * @return iterable<string, array{0: VariantCodes, 1: string}>
     */
    public static function provideData(): iterable
    {
        yield 'with 1 variant code' => [
            ['2742_27917'],
            "
                WITH CountryByChannelCode AS (
                    SELECT cc.country_id
                    FROM sylius_channel_countries cc
                    INNER JOIN sylius_channel ch ON cc.channel_id = ch.id
                    WHERE ch.code = :channelCode
                ), ranking AS (
                    WITH ProductVariants AS (
                        SELECT
                            pv.id,
                            pv.preferred_supplier,
                            SUBSTRING_INDEX(pv.code, '_', 2) AS variant_code,
                            pv.code AS supplier_variant_code,
                            pv.cost_price,
                            pv.supplier_id,
                            CASE
                                WHEN pv.preferred_variant_for_minimum_daily_orders < 1 THEN 0
                                WHEN pv.amount_ordered_today < pv.preferred_variant_for_minimum_daily_orders THEN 1
                                WHEN pv.amount_ordered_today_updated_at < CURDATE() THEN 1
                                ELSE 0
                                END AS is_preferred_variant_for_minimum_daily_orders,
                            IF(
                                    (s.amount_orders_today >= s.daily_order_limit AND s.amount_orders_today_updated_at >= CURDATE()),
                                    1,
                                    0
                            ) AS is_supplier_daily_order_limit_reached
                        FROM sylius_product_variant pv
                        INNER JOIN sylius_product_variant_option_value pvov ON pv.id = pvov.variant_id
                        INNER JOIN sylius_channel_pricing cp ON pv.id = cp.product_variant_id AND cp.channel_code = :channelCode AND cp.enabled = 1
                        INNER JOIN supplier_country_shipping cs ON pv.supplier_id = cs.supplier_id
                            AND cs.shipping_cost IS NOT NULL
                            AND cs.enabled = 1
                            AND cs.country_id = (SELECT country_id FROM CountryByChannelCode)
                        INNER JOIN sylius_product p ON p.id = pv.product_id AND p.enabled = 1
                        INNER JOIN sylius_product_variant_translation pvt ON pv.id = pvt.translatable_id
                        INNER JOIN supplier s ON pv.supplier_id = s.id AND s.enabled = 1
                        WHERE SUBSTRING_INDEX(pv.code, '_', 2) IN (:variantCodes)
                            AND pv.enabled = 1
                        GROUP BY pv.id
                    )
                    SELECT
                        pv0.id AS pv0_id,
                        RANK() OVER (
                            ORDER BY
                                -- Supplier daily order limit reached
                                (pv0.is_supplier_daily_order_limit_reached),

                                -- Preferred variant for minimum daily orders
                                (pv0.is_preferred_variant_for_minimum_daily_orders) DESC,

                                -- Then preferred supplier
                                (pv0.preferred_supplier) DESC,

                                -- Then the total cost price
                                (pv0.cost_price * (1 + (cs0.tax_rate_in_percentage / 100))) + (
                                    SELECT sum(country_shipping.shipping_cost) -- Calculate the total shipping cost
                                    FROM supplier_country_shipping country_shipping
                                    WHERE country_shipping.supplier_id in (s0.id)
                                    AND country_shipping.country_id = (SELECT country_id FROM CountryByChannelCode)
                                ) + (
                                    SELECT sum(supplier.handling_fee) -- Calculate the total handling fee
                                    FROM supplier as supplier
                                    WHERE supplier.id in (s0.id)
                                )
                            ) AS `rank`
                    FROM ProductVariants pv0
                    INNER JOIN supplier_country_shipping cs0 ON pv0.supplier_id = cs0.supplier_id AND cs0.country_id = (SELECT country_id FROM CountryByChannelCode) INNER JOIN supplier s0 ON pv0.supplier_id = s0.id
                    WHERE pv0.variant_code = '2742_27917'
                    GROUP BY pv0.id
                )
                SELECT CONCAT(pv0_id) AS variant_ids
                FROM ranking
                WHERE `rank` = 1
                LIMIT 1;
            ",
        ];

        yield 'with 2 variant codes (and weird indexes)' => [
            [1 => '2742_27917', 3 => '2925_28641'],
            "
                WITH CountryByChannelCode AS (
                    SELECT cc.country_id
                    FROM sylius_channel_countries cc
                    INNER JOIN sylius_channel ch ON cc.channel_id = ch.id
                    WHERE ch.code = :channelCode
                ), ranking AS (
                    WITH ProductVariants AS (
                        SELECT
                            pv.id,
                            pv.preferred_supplier,
                            SUBSTRING_INDEX(pv.code, '_', 2) AS variant_code,
                            pv.code AS supplier_variant_code,
                            pv.cost_price,
                            pv.supplier_id,
                            CASE
                                WHEN pv.preferred_variant_for_minimum_daily_orders < 1 THEN 0
                                WHEN pv.amount_ordered_today < pv.preferred_variant_for_minimum_daily_orders THEN 1
                                WHEN pv.amount_ordered_today_updated_at < CURDATE() THEN 1
                                ELSE 0
                                END AS is_preferred_variant_for_minimum_daily_orders,
                            IF(
                                    (s.amount_orders_today >= s.daily_order_limit AND s.amount_orders_today_updated_at >= CURDATE()),
                                    1,
                                    0
                            ) AS is_supplier_daily_order_limit_reached
                        FROM sylius_product_variant pv
                        INNER JOIN sylius_product_variant_option_value pvov ON pv.id = pvov.variant_id
                        INNER JOIN sylius_channel_pricing cp ON pv.id = cp.product_variant_id AND cp.channel_code = :channelCode AND cp.enabled = 1
                        INNER JOIN supplier_country_shipping cs ON pv.supplier_id = cs.supplier_id
                            AND cs.shipping_cost IS NOT NULL
                            AND cs.enabled = 1
                            AND cs.country_id = (SELECT country_id FROM CountryByChannelCode)
                        INNER JOIN sylius_product p ON p.id = pv.product_id AND p.enabled = 1
                        INNER JOIN sylius_product_variant_translation pvt ON pv.id = pvt.translatable_id
                        INNER JOIN supplier s ON pv.supplier_id = s.id AND s.enabled = 1
                        WHERE SUBSTRING_INDEX(pv.code, '_', 2) IN (:variantCodes)
                            AND pv.enabled = 1
                        GROUP BY pv.id
                    )
                    SELECT
                        pv1.id AS pv1_id, pv3.id AS pv3_id,
                        RANK() OVER (
                            ORDER BY
                                -- Supplier daily order limit reached
                                (pv1.is_supplier_daily_order_limit_reached + pv3.is_supplier_daily_order_limit_reached),

                                -- Preferred variant for minimum daily orders
                                (pv1.is_preferred_variant_for_minimum_daily_orders + pv3.is_preferred_variant_for_minimum_daily_orders) DESC,

                                -- Then preferred supplier
                                (pv1.preferred_supplier + pv3.preferred_supplier) DESC,

                                -- Then the total cost price
                                (pv1.cost_price * (1 + (cs1.tax_rate_in_percentage / 100))) + (pv3.cost_price * (1 + (cs3.tax_rate_in_percentage / 100))) + (
                                    SELECT sum(country_shipping.shipping_cost) -- Calculate the total shipping cost
                                    FROM supplier_country_shipping country_shipping
                                    WHERE country_shipping.supplier_id in (s1.id, s3.id)
                                    AND country_shipping.country_id = (SELECT country_id FROM CountryByChannelCode)
                                ) + (
                                    SELECT sum(supplier.handling_fee) -- Calculate the total handling fee
                                    FROM supplier as supplier
                                    WHERE supplier.id in (s1.id, s3.id)
                                )
                            ) AS `rank`
                    FROM ProductVariants pv1 CROSS JOIN ProductVariants pv3
                    INNER JOIN supplier_country_shipping cs1 ON pv1.supplier_id = cs1.supplier_id AND cs1.country_id = (SELECT country_id FROM CountryByChannelCode) INNER JOIN supplier s1 ON pv1.supplier_id = s1.id INNER JOIN supplier_country_shipping cs3 ON pv3.supplier_id = cs3.supplier_id AND cs3.country_id = (SELECT country_id FROM CountryByChannelCode) INNER JOIN supplier s3 ON pv3.supplier_id = s3.id
                    WHERE pv1.variant_code = '2742_27917' AND pv3.variant_code = '2925_28641'
                    GROUP BY pv1.id, pv3.id
                )
                SELECT CONCAT(pv1_id, ',', pv3_id) AS variant_ids
                FROM ranking
                WHERE `rank` = 1
                LIMIT 1;
            ",
        ];

        yield 'with 3 variant codes' => [
            ['2742_27917', '2925_28641', '3277_31063'],
            "
                WITH CountryByChannelCode AS (
                    SELECT cc.country_id
                    FROM sylius_channel_countries cc
                    INNER JOIN sylius_channel ch ON cc.channel_id = ch.id
                    WHERE ch.code = :channelCode
                ), ranking AS (
                    WITH ProductVariants AS (
                        SELECT
                            pv.id,
                            pv.preferred_supplier,
                            SUBSTRING_INDEX(pv.code, '_', 2) AS variant_code,
                            pv.code AS supplier_variant_code,
                            pv.cost_price,
                            pv.supplier_id,
                            CASE
                                WHEN pv.preferred_variant_for_minimum_daily_orders < 1 THEN 0
                                WHEN pv.amount_ordered_today < pv.preferred_variant_for_minimum_daily_orders THEN 1
                                WHEN pv.amount_ordered_today_updated_at < CURDATE() THEN 1
                                ELSE 0
                                END AS is_preferred_variant_for_minimum_daily_orders,
                            IF(
                                    (s.amount_orders_today >= s.daily_order_limit AND s.amount_orders_today_updated_at >= CURDATE()),
                                    1,
                                    0
                            ) AS is_supplier_daily_order_limit_reached
                        FROM sylius_product_variant pv
                        INNER JOIN sylius_product_variant_option_value pvov ON pv.id = pvov.variant_id
                        INNER JOIN sylius_channel_pricing cp ON pv.id = cp.product_variant_id AND cp.channel_code = :channelCode AND cp.enabled = 1
                        INNER JOIN supplier_country_shipping cs ON pv.supplier_id = cs.supplier_id
                            AND cs.shipping_cost IS NOT NULL
                            AND cs.enabled = 1
                            AND cs.country_id = (SELECT country_id FROM CountryByChannelCode)
                        INNER JOIN sylius_product p ON p.id = pv.product_id AND p.enabled = 1
                        INNER JOIN sylius_product_variant_translation pvt ON pv.id = pvt.translatable_id
                        INNER JOIN supplier s ON pv.supplier_id = s.id AND s.enabled = 1
                        WHERE SUBSTRING_INDEX(pv.code, '_', 2) IN (:variantCodes)
                            AND pv.enabled = 1
                        GROUP BY pv.id
                    )
                    SELECT
                        pv0.id AS pv0_id, pv1.id AS pv1_id, pv2.id AS pv2_id,
                        RANK() OVER (
                            ORDER BY
                                -- Supplier daily order limit reached
                                (pv0.is_supplier_daily_order_limit_reached + pv1.is_supplier_daily_order_limit_reached + pv2.is_supplier_daily_order_limit_reached),

                                -- Preferred variant for minimum daily orders
                                (pv0.is_preferred_variant_for_minimum_daily_orders + pv1.is_preferred_variant_for_minimum_daily_orders + pv2.is_preferred_variant_for_minimum_daily_orders) DESC,

                                -- Then preferred supplier
                                (pv0.preferred_supplier + pv1.preferred_supplier + pv2.preferred_supplier) DESC,

                                -- Then the total cost price
                                (pv0.cost_price * (1 + (cs0.tax_rate_in_percentage / 100))) + (pv1.cost_price * (1 + (cs1.tax_rate_in_percentage / 100))) + (pv2.cost_price * (1 + (cs2.tax_rate_in_percentage / 100))) + (
                                    SELECT sum(country_shipping.shipping_cost) -- Calculate the total shipping cost
                                    FROM supplier_country_shipping country_shipping
                                    WHERE country_shipping.supplier_id in (s0.id, s1.id, s2.id)
                                    AND country_shipping.country_id = (SELECT country_id FROM CountryByChannelCode)
                                ) + (
                                    SELECT sum(supplier.handling_fee) -- Calculate the total handling fee
                                    FROM supplier as supplier
                                    WHERE supplier.id in (s0.id, s1.id, s2.id)
                                )
                            ) AS `rank`
                    FROM ProductVariants pv0 CROSS JOIN ProductVariants pv1 CROSS JOIN ProductVariants pv2
                    INNER JOIN supplier_country_shipping cs0 ON pv0.supplier_id = cs0.supplier_id AND cs0.country_id = (SELECT country_id FROM CountryByChannelCode) INNER JOIN supplier s0 ON pv0.supplier_id = s0.id INNER JOIN supplier_country_shipping cs1 ON pv1.supplier_id = cs1.supplier_id AND cs1.country_id = (SELECT country_id FROM CountryByChannelCode) INNER JOIN supplier s1 ON pv1.supplier_id = s1.id INNER JOIN supplier_country_shipping cs2 ON pv2.supplier_id = cs2.supplier_id AND cs2.country_id = (SELECT country_id FROM CountryByChannelCode) INNER JOIN supplier s2 ON pv2.supplier_id = s2.id
                    WHERE pv0.variant_code = '2742_27917' AND pv1.variant_code = '2925_28641' AND pv2.variant_code = '3277_31063'
                    GROUP BY pv0.id, pv1.id, pv2.id
                )
                SELECT CONCAT(pv0_id, ',', pv1_id, ',', pv2_id) AS variant_ids
                FROM ranking
                WHERE `rank` = 1
                LIMIT 1;
            ",
        ];
    }

    /**
     * @param VariantCodes $variantCodes
     *
     * @dataProvider provideData
     */
    public function testGetSQL(array $variantCodes, string $expectedSQL): void
    {
        // Act
        $sql = $this->optimizeVariantsQueryGenerator->getSQL($variantCodes);

        // Assert
        $this->assertEquals($this->trimLeadingSpaces($expectedSQL), $this->trimLeadingSpaces($sql));
    }

    private function trimLeadingSpaces(string $sql): string
    {
        return (string) preg_replace('/^\s+/m', '', $sql);
    }
}
