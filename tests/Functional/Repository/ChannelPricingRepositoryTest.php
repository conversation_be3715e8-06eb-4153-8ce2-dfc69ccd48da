<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Entity\Channel\Channel;
use App\Entity\Channel\ChannelPricing;
use App\Entity\Product\Product;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariant;
use App\Entity\Supplier\Supplier;
use App\Repository\ChannelPricingRepository;
use App\Repository\ChannelRepository;
use App\Tests\Util\PersistedFactory\ChannelPricingFactory;
use App\Tests\Util\PersistedFactory\ProductFactory;
use App\Tests\Util\PersistedFactory\ProductVariantFactory;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Sylius\Component\Product\Generator\SlugGeneratorInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class ChannelPricingRepositoryTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private ChannelPricingRepository $channelPricingRepository;
    private ChannelRepository $channelRepository;
    private ChannelPricingFactory $channelPricingFactory;
    private ProductVariantFactory $productVariantFactory;
    private ProductFactory $productFactory;

    public function setUp(): void
    {
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        self::assertInstanceOf(EntityManagerInterface::class, $entityManager);
        $this->entityManager = $entityManager;

        $channelPricingRepository = $this->entityManager->getRepository(ChannelPricing::class);
        self::assertInstanceOf(ChannelPricingRepository::class, $channelPricingRepository);
        $this->channelPricingRepository = $channelPricingRepository;

        $channelRepository = $this->entityManager->getRepository(Channel::class);
        self::assertInstanceOf(ChannelRepository::class, $channelRepository);
        $this->channelRepository = $channelRepository;

        $slugGenerator = self::getContainer()->get(SlugGeneratorInterface::class);
        self::assertInstanceOf(SlugGeneratorInterface::class, $slugGenerator);

        $this->channelPricingFactory = new ChannelPricingFactory($this->entityManager);
        $this->productVariantFactory = new ProductVariantFactory($this->entityManager);
        $this->productFactory = new ProductFactory($this->entityManager, $this->productVariantFactory, $slugGenerator);
    }

    public function testGetEnabledChannelPricingByProductAndChannel(): void
    {
        // Arrange
        $channelCodeOne = 'dok_nl';
        $channelCodeTwo = 'blueclinic_nl';

        $channelOne = $this->channelRepository->findOneByCode($channelCodeOne);
        self::assertInstanceOf(Channel::class, $channelOne);

        $channelTwo = $this->channelRepository->findOneByCode($channelCodeTwo);
        self::assertInstanceOf(Channel::class, $channelTwo);

        $product = $this->productFactory->create('7', 'Viagra', ProductType::MEDICATION);
        $productVariant = $this->productVariantFactory->create($product, '7_5', 'Viagra 25mg 4 tabl.');

        $this->channelPricingFactory->create($productVariant, $channelCodeOne, 100, false);
        $this->channelPricingFactory->create($productVariant, $channelCodeTwo, 100);
        $channelPricing = $this->channelPricingFactory->create($productVariant, $channelCodeOne, 100);

        // Act
        $enabledChannelPricings = $this->channelPricingRepository->getEnabledChannelPricingByProductAndChannel(
            $product,
            $channelCodeOne,
        );

        // Assert
        self::assertCount(1, $enabledChannelPricings);
        self::assertContains($channelPricing, $enabledChannelPricings);
    }

    public function testGetBusinessUnitsByChannelPricings(): void
    {
        // Arrange
        $this->channelRepository->findOneByCode('dok_de');
        $this->channelRepository->findOneByCode('dok_nl');
        $this->channelRepository->findOneByCode('blueclinic_nl');

        $product = $this->productFactory->create('7', 'Viagra', ProductType::MEDICATION);
        $productVariant = $this->productVariantFactory->create($product, '7_5', 'Viagra 25mg 4 tabl.');

        // Act
        $businessUnits = $this->channelPricingRepository->getBusinessUnitsByChannelPricings([
            $this->channelPricingFactory->create($productVariant, 'dok_nl', 100),
            $this->channelPricingFactory->create($productVariant, 'dok_de', 100),
            $this->channelPricingFactory->create($productVariant, 'blueclinic_nl', 100),
        ]);

        $businessUnitCodes = array_map(static fn ($businessUnit) => $businessUnit->getCode(), $businessUnits);

        // Assert
        self::assertCount(2, $businessUnits);
        self::assertContains('dokteronline', $businessUnitCodes);
        self::assertContains('blueclinic', $businessUnitCodes);

        // Act (2)
        $businessUnits = $this->channelPricingRepository->getBusinessUnitsByChannelPricings([
            $this->channelPricingFactory->create($productVariant, 'dok_nl', 100),
            $this->channelPricingFactory->create($productVariant, 'dok_de', 100),
        ]);

        $businessUnitCodes = array_map(static fn ($businessUnit) => $businessUnit->getCode(), $businessUnits);

        // Assert (2)
        self::assertCount(1, $businessUnits);
        self::assertContains('dokteronline', $businessUnitCodes);
    }

    public function testCanDisableChannelPricingBySupplierAndChannels(): void
    {
        $supplier = $suppliers[] = new Supplier();
        $supplier->setName('Prima Pharmacy');
        $supplier->setIdentifier('prima-pharmacy');

        $supplier = $suppliers[] = new Supplier();
        $supplier->setName('NSDM apotheek');
        $supplier->setIdentifier('nsdm-apotheek');

        $product = new Product();
        $product->setCode('viagra');

        foreach ($suppliers as $supplier) {
            $productVariant = $productVariants[] = new ProductVariant();
            $productVariant->setSupplier($supplier);
            $productVariant->setCode(sprintf('viagra_25mg_%s', $supplier->getIdentifier()));
            $productVariant->setProduct($product);
            $productVariant->setUpdatedAt(new DateTime('2022-01-01'));
            $productVariant->enable();

            $productVariant = $productVariants[] = new ProductVariant();
            $productVariant->setSupplier($supplier);
            $productVariant->setCode(sprintf('viagra_50mg_%s', $supplier->getIdentifier()));
            $productVariant->setProduct($product);
            $productVariant->setUpdatedAt(new DateTime('2022-01-01'));
            $productVariant->enable();
        }

        $channelPricings = [];

        foreach ($productVariants as $productVariant) {
            $channelPricing = $channelPricings[] = new ChannelPricing();
            $channelPricing->setChannelCode('dok_de');
            $channelPricing->setProductVariant($productVariant);
            $channelPricing->enable();

            $channelPricing = $channelPricings[] = new ChannelPricing();
            $channelPricing->setProductVariant($productVariant);
            $channelPricing->setChannelCode('dok_gb');
            $channelPricing->enable();

            $channelPricing = $channelPricings[] = new ChannelPricing();
            $channelPricing->setProductVariant($productVariant);
            $channelPricing->setChannelCode('dok_fr');
            $channelPricing->enable();
        }

        $this->persistObjects(
            $product,
            ...$suppliers,
            ...$productVariants,
            ...$channelPricings
        );

        foreach ($productVariants as $productVariant) {
            /** @var ProductVariant $variant */
            $variant = $this->entityManager->getRepository(ProductVariant::class)->findOneBy(['code' => $productVariant->getCode()]);
            self::assertEquals('2022', $variant->getUpdatedAt()?->format('Y'));
        }

        $disableForChannelCodes = ['dok_gb', 'dok_de'];
        $disableForChannels = $this->entityManager->getRepository(Channel::class)
            ->findBy(['code' => $disableForChannelCodes]);

        $this->channelPricingRepository->disableChannelPricingBySupplierAndChannels(
            $suppliers[0],
            ...$disableForChannels
        );

        $this->entityManager->flush();

        /** @var ChannelPricing[] $allChannelPricing */
        $allChannelPricing = $this->channelPricingRepository->findAll();
        self::assertNotEmpty($allChannelPricing);

        foreach ($allChannelPricing as $channelPricing) {
            /** @var ProductVariant $productVariant */
            $productVariant = $channelPricing->getProductVariant();
            $this->entityManager->refresh($productVariant);
            self::assertInstanceOf(ProductVariant::class, $productVariant);

            $supplier = $productVariant->getSupplier();
            self::assertInstanceOf(Supplier::class, $supplier);

            if (
                in_array($channelPricing->getChannelCode(), $disableForChannelCodes, true)
                && $supplier->getIdentifier() === $suppliers[0]->getIdentifier()
            ) {
                self::assertFalse($channelPricing->isEnabled());

                // Make sure updated_at is modified
                self::assertEquals(date('Y'), $productVariant->getUpdatedAt()?->format('Y'));

                continue;
            }

            self::assertTrue($channelPricing->isEnabled());
        }
    }

    private function persistObjects(object ...$objects): void
    {
        foreach ($objects as $object) {
            $this->entityManager->persist($object);
        }

        $this->entityManager->flush();
        $this->entityManager->clear();
    }
}
