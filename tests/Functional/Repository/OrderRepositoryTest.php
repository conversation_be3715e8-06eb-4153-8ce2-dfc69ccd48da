<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Channel\Channel;
use App\Entity\Order\Order;
use App\Repository\OrderRepositoryInterface;
use DateTimeImmutable;
use DateTimeInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class OrderRepositoryTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private OrderRepositoryInterface $orderRepository;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        /** @var OrderRepositoryInterface $orderRepository */
        $orderRepository = $this->entityManager->getRepository(Order::class);
        $this->orderRepository = $orderRepository;
    }

    public function testFindByTimeAgo(): void
    {
        $createOrder = function (DateTimeInterface $checkoutCompletedAt): Order {
            $order = new Order();
            $order->setCurrencyCode('EUR');
            $order->setLocaleCode('NL');
            $order->setCheckoutCompletedAt($checkoutCompletedAt);

            $this->entityManager->persist($order);

            return $order;
        };

        // Arrange
        $orderToday = $createOrder(new DateTimeImmutable());
        $orderYesterday = $createOrder(new DateTimeImmutable('-1 day'));
        $orderThreeWeeksAgo = $createOrder(new DateTimeImmutable('-3 weeks'));
        $orderOneMonthAnd1HourAgo = $createOrder(new DateTimeImmutable('-1 month, -1 hour'));
        $orderTwoMonthsAgo = $createOrder(new DateTimeImmutable('-2 months'));

        $this->entityManager->flush();

        // Act
        $orders = $this->orderRepository->findByTimeAgo(new DateTimeImmutable('-1 month'));

        // Assert
        self::assertContains($orderToday, $orders);
        self::assertContains($orderYesterday, $orders);
        self::assertContains($orderThreeWeeksAgo, $orders);
        self::assertNotContains($orderOneMonthAnd1HourAgo, $orders);
        self::assertNotContains($orderTwoMonthsAgo, $orders);
    }

    /**
     * @dataProvider provideGetHighestOrderNumberForAllBusinessUnits
     */
    public function testCanGetHighestOrderNumberForAllBusinessUnits(
        string $businessUnitCode,
        ?string $expectedOrderNumber,
    ): void {
        $createOrder = function (string $number, Channel $channel): Order {
            $order = new Order();
            $order->setCurrencyCode('EUR');
            $order->setLocaleCode('NL');
            $order->setNumber($number);
            $order->setChannel($channel);

            $this->entityManager->persist($order);

            return $order;
        };

        $channels = $this->entityManager
            ->getRepository(Channel::class)
            ->createQueryBuilder('channel', 'channel.code')
            ->getQuery()
            ->getResult();

        $createOrder('DO00001336', $channels['dok_de']);
        $createOrder('DO00001337', $channels['dok_de']);
        $createOrder('DO00001339', $channels['dok_fr']);
        $createOrder('BC00000001', $channels['blueclinic_nl']);
        $createOrder('DO00001338', $channels['dok_gb']);
        $createOrder('BC00000002', $channels['blueclinic_nl']);
        $createOrder('DO00001340', $channels['dok_nl']);

        $this->entityManager->flush();

        $businessUnit = $this->entityManager
            ->getRepository(BusinessUnit::class)
            ->findOneBy(['code' => $businessUnitCode]);

        self::assertInstanceOf(BusinessUnit::class, $businessUnit);

        $actualNumber = $this->orderRepository->getHighestOrderNumber($businessUnit);

        self::assertSame($expectedOrderNumber, $actualNumber);
    }

    /**
     * @return iterable<string, array{0: string, 1: string}>
     */
    public function provideGetHighestOrderNumberForAllBusinessUnits(): iterable
    {
        yield 'with business unit dokteronline' => ['dokteronline', 'DO00001340'];
        yield 'with business unit doctoronline' => ['doctoronline', 'DO00001340'];
        yield 'with business unit blueclinic' => ['blueclinic', 'BC00000002'];
    }

    /**
     * @dataProvider provideGetHighestOrderNumberReturnsNull
     */
    public function testCanGetHighestOrderNumberReturnsNull(string $businessUnitCode): void
    {
        $businessUnit = $this->entityManager
            ->getRepository(BusinessUnit::class)
            ->findOneBy(['code' => $businessUnitCode]);

        self::assertInstanceOf(BusinessUnit::class, $businessUnit);

        $actualNumber = $this->orderRepository->getHighestOrderNumber($businessUnit);

        self::assertNull($actualNumber);
    }

    /**
     * @return iterable<string, array{0: string}>
     */
    public function provideGetHighestOrderNumberReturnsNull(): iterable
    {
        yield 'with business unit dokteronline' => ['dokteronline'];
        yield 'with business unit doctoronline' => ['doctoronline'];
        yield 'with business unit blueclinic' => ['blueclinic'];
    }
}
