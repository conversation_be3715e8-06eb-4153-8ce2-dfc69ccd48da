<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Entity\Channel\Channel;
use App\Entity\Product\ProductType;
use App\Entity\Supplier\Supplier;
use App\Repository\SupplierRepositoryInterface;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Util\PersistedFactory\ChannelPricingFactory;
use App\Tests\Util\PersistedFactory\ProductFactory;
use App\Tests\Util\PersistedFactory\ProductVariantFactory;

final class SupplierRepositoryTest extends AbstractWebTestCase
{
    private ProductFactory $productFactory;
    private ProductVariantFactory $productVariantFactory;
    private ChannelPricingFactory $channelPricingFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $productFactory = self::getContainer()->get(ProductFactory::class);
        self::assertInstanceOf(ProductFactory::class, $productFactory);
        $this->productFactory = $productFactory;

        $productVariantFactory = self::getContainer()->get(ProductVariantFactory::class);
        self::assertInstanceOf(ProductVariantFactory::class, $productVariantFactory);
        $this->productVariantFactory = $productVariantFactory;

        $channelPricingFactory = self::getContainer()->get(ChannelPricingFactory::class);
        self::assertInstanceOf(ChannelPricingFactory::class, $channelPricingFactory);
        $this->channelPricingFactory = $channelPricingFactory;
    }

    /**
     * Regression test for DV-9054 (and DV-8971).
     */
    public function testFindSuppliersForOrderItems(): void
    {
        // Arrange
        $productOne = $this->productFactory->create(
            code: '2551',
            name: 'Pantoprazole',
            type: ProductType::MEDICATION,
        );

        $productTwo = $this->productFactory->create(
            code: '3068',
            name: 'Clindamycin',
            type: ProductType::MEDICATION,
        );

        $productOneVariantOne = $this->productVariantFactory->create(
            product: $productOne,
            code: '2551_35720_ndsm_apotheek_worldwide',
            name: 'Pantoprazole 40 mg 90 tabl.',
            supplierIdentifier: 'ndsm-apotheek',
            costPrice: 1350,
            country: 'PL',
        );

        $this->channelPricingFactory->create(
            productVariant: $productOneVariantOne,
            channelCode: 'dok_pl',
            price: 42500,
        );

        $productOneVariantTwo = $this->productVariantFactory->create(
            product: $productOne,
            code: '2551_35720_apotheek_culemborg_worldwide',
            name: 'Pantoprazole 40 mg 90 tabl.',
            supplierIdentifier: 'apotheek-culemborg',
            costPrice: 4221,
            enabled: false,
            country: 'PL',
        );

        $this->channelPricingFactory->create(
            productVariant: $productOneVariantTwo,
            channelCode: 'dok_pl',
            price: 42500,
        );

        $productTwoVariantOne = $this->productVariantFactory->create(
            product: $productTwo,
            code: '3068_35671_ndsm_apotheek_worldwide',
            name: 'Clindamycin 300 mg 90 caps.',
            supplierIdentifier: 'ndsm-apotheek',
            costPrice: 5100,
            country: 'PL',
        );

        $this->channelPricingFactory->create(
            productVariant: $productTwoVariantOne,
            channelCode: 'dok_pl',
            price: 54900,
        );

        $productTwoVariantTwo = $this->productVariantFactory->create(
            product: $productTwo,
            code: '3068_35671_apotheek_culemborg_worldwide',
            name: 'Clindamycin 300 mg 90 caps.',
            supplierIdentifier: 'apotheek-culemborg',
            costPrice: 5985,
            country: 'PL',
        );

        $this->channelPricingFactory->create(
            productVariant: $productTwoVariantTwo,
            channelCode: 'dok_pl',
            price: 54900,
        );

        $channel = $this->entityManager?->getRepository(Channel::class)->findOneBy(['code' => 'dok_pl']);
        self::assertInstanceOf(Channel::class, $channel, "Channel with code 'dok_pl' does not exist.");

        // Act
        /** @var SupplierRepositoryInterface $supplierRepository */
        $supplierRepository = $this->entityManager?->getRepository(Supplier::class);
        $suppliers = $supplierRepository->findSuppliersForOrderItems(
            ['2551_35720', '3068_35671'],
            $channel,
        );

        // Assert
        self::assertCount(1, $suppliers);
        self::assertSame('NDSM Apotheek B.V.', $suppliers[0]['name']);
        self::assertSame(7584, $suppliers[0]['costPriceTotal']);
    }
}
