<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Entity\Channel\Channel;
use App\Entity\Order\Order;
use App\Entity\Payment\Payment;
use App\Entity\Payment\PaymentMethod;
use App\Entity\Payment\PaymentMethodGatewayConfig;
use App\Repository\PaymentMethodRepositoryInterface;
use App\Tests\Util\Builder\PaymentMethodBuilder;
use DateTimeImmutable;

final class PaymentMethodRepositoryTest extends AbstractRepositoryTestCase
{
    private PaymentMethodRepositoryInterface $paymentMethodRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $paymentMethodRepository = $this->entityManager->getRepository(PaymentMethod::class);
        self::assertInstanceOf(PaymentMethodRepositoryInterface::class, $paymentMethodRepository);
        $this->paymentMethodRepository = $paymentMethodRepository;
    }

    public function testFindAllWithExcludedCodesReturnsThePaymentMethodWithOtherCode(): void
    {
        // Arrange
        $paymentMethod = $this->createPaymentMethod('test-payment-method-1');

        // Act
        $paymentMethods = $this->paymentMethodRepository->findAllWithExcludedCodes([
            'test-payment-method-2',
        ]);

        // Assert
        self::assertContains($paymentMethod, $paymentMethods);
    }

    public function testFindAllWithExcludedCodesExcludesThePaymentMethodWithSameCode(): void
    {
        // Arrange
        $paymentMethod = $this->createPaymentMethod('test-payment-method');

        // Act
        $paymentMethods = $this->paymentMethodRepository->findAllWithExcludedCodes([
            'test-payment-method',
        ]);

        // Assert
        self::assertNotContains($paymentMethod, $paymentMethods);
    }

    /**
     * @dataProvider dailyLimitProvider
     */
    public function testDoesNotFindPaymentMethodWithGatewayConfigThatReachedTheDailyLimit(
        int $dailyLimit,
        int $usedToday,
        DateTimeImmutable $dailyUsageUpdatedAt,
        bool $expectPaymentMethodToBeAvailable,
    ): void {
        // Arrange
        $paymentMethod = PaymentMethodBuilder::create($this->entityManager)
            ->setPaymentMethod(code: 'test_payment_method', icon: 'test_payment_method')
            ->addPaymentMethodGatewayConfig(
                gatewayConfig: ['gatewayName' => 'test_gateway_name'],
                dailyLimit: $dailyLimit,
                dailyUsage: $usedToday,
                dailyUsageUpdatedAt: $dailyUsageUpdatedAt,
            )
            ->addChannels('dok_nl')
            ->getPaymentMethod();

        /** @var Channel $channel */
        $channel = $paymentMethod->getChannels()->first();

        /** @var PaymentMethodGatewayConfig $paymentMethodGatewayConfig */
        $paymentMethodGatewayConfig = $paymentMethod->getPaymentMethodGatewayConfigs()->first();

        $payment = new Payment();
        $payment->setPaymentMethodGatewayConfig($paymentMethodGatewayConfig);
        $payment->setMethod($paymentMethod);
        $payment->setCurrencyCode('EUR');

        $order = new Order();
        $order->addPayment($payment);
        $order->setCurrencyCode('EUR');
        $order->setLocaleCode('nl');
        $order->setChannel($channel);
        $payment->setOrder($order);

        $this->entityManager->persist($order);
        $this->entityManager->persist($payment);
        $this->entityManager->persist($paymentMethod);

        $this->entityManager->flush();

        // Act
        /** @var array<PaymentMethod> $paymentMethods */
        $paymentMethods = $this->paymentMethodRepository->findEnabledForOrder($order);

        // Assert
        self::assertCount(
            $expectPaymentMethodToBeAvailable ? 1 : 0,
            array_filter(
                $paymentMethods,
                static fn (PaymentMethod $paymentMethod): bool => $paymentMethod->getCode() === 'test_payment_method'
            )
        );
    }

    /**
     * @return array<string, array{int, int, DateTimeImmutable, bool}>
     */
    public function dailyLimitProvider(): array
    {
        return [
            'reached daily limit' => [10, 10, new DateTimeImmutable('today'), false],
            'below daily limit' => [10, 8, new DateTimeImmutable('today'), true],
            'reached daily limit but usage updated yesterday' => [10, 10, new DateTimeImmutable('yesterday'), true],
        ];
    }

    private function createPaymentMethod(string $code): PaymentMethod
    {
        $paymentMethod = new PaymentMethod();
        $paymentMethod->setCode($code);

        $this->entityManager->persist($paymentMethod);
        $this->entityManager->flush();

        return $paymentMethod;
    }
}
