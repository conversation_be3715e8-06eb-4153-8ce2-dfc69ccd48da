<?php

declare(strict_types=1);

namespace App\Tests\Functional\Mocks;

use Symfony\Component\HttpClient\MockHttpClient as BaseMockHttpClient;
use Symfony\Component\HttpClient\Response\MockResponse;

final class MockHttpClient extends BaseMockHttpClient
{
    public function setMockResponse(string|false $mockResponse): void
    {
        if (!$mockResponse) {
            return;
        }

        $this->setResponseFactory([
            new MockResponse($mockResponse),
        ]);
    }
}
