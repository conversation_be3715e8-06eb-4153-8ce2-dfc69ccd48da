<?php

declare(strict_types=1);

namespace App\Tests\Functional\StateMachine\Callback;

use App\Entity\Order\Order;
use App\Entity\Payment\Payment;
use App\Entity\Payment\PaymentInterface;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Api\ExchangeRateTestFactory;
use SM\Factory\FactoryInterface;
use Sylius\Component\Payment\Model\PaymentInterface as SyliusPaymentInterface;
use Sylius\Component\Payment\PaymentTransitions;

final class AddExchangeRateToPaymentTest extends AbstractWebTestCase
{
    /**
     * @dataProvider provideTransitionForTestApplyStateTriggersCallback
     */
    public function testApplyStateTriggersCallback(
        PaymentInterface $payment,
        ?float $expectedExchangeRate,
        float $actualExchangeRate,
        string $transition,
    ): void {
        // Arrange
        $exchangeRateTestFactory = new ExchangeRateTestFactory(self::getContainer());
        $exchangeRateTestFactory->createExchangeRate('SEK', $actualExchangeRate);

        /** @var FactoryInterface $stateMachineFactory */
        $stateMachineFactory = self::getContainer()->get(FactoryInterface::class);
        $stateMachine = $stateMachineFactory->get($payment, PaymentTransitions::GRAPH);

        // Act
        $stateMachine->apply($transition);

        // Assert
        self::assertSame($expectedExchangeRate, $payment->getExchangeRate());
    }

    /**
     * @return iterable<string, array{0: PaymentInterface, 1: ?float, 2: float, 3: string}>
     */
    public function provideTransitionForTestApplyStateTriggersCallback(): iterable
    {
        $order = new Order();
        $order->setCurrencyCode('SEK');

        $payment = new Payment();
        $payment->setState(SyliusPaymentInterface::STATE_PROCESSING);
        $payment->setOrder($order);

        yield 'Payment from processing to completed should add an exchange rate.' => [$payment, 11.27308, 11.27308, PaymentTransitions::TRANSITION_COMPLETE];

        $payment = new Payment();
        $payment->setState(SyliusPaymentInterface::STATE_PROCESSING);
        $payment->setOrder($order);

        yield 'Payment from processing to authorized should add an exchange rate.' => [$payment, 11.38721, 11.38721, PaymentTransitions::TRANSITION_AUTHORIZE];

        $payment = new Payment();
        $payment->setState(SyliusPaymentInterface::STATE_CART);
        $payment->setOrder($order);

        yield 'Payment from cart to new should add an exchange rate.' => [$payment, 11.38721, 11.38721, PaymentTransitions::TRANSITION_CREATE];

        $payment = new Payment();
        $payment->setState(SyliusPaymentInterface::STATE_NEW);
        $payment->setOrder($order);

        yield 'Payment from new to processing should NOT apply an exchange rate.' => [$payment, null, 11.38721, PaymentTransitions::TRANSITION_PROCESS];
    }

    public function testPayOrderSavesExchangeRate(): void
    {
        // Arrange
        $channelCode = 'dok_gb';
        $localeCode = 'en';
        $countryCode = 'GB';
        $exchangeRate = 1.12;

        $this->prepareDatabaseWithProducts([$channelCode], $localeCode);

        $exchangeRateTestFactory = new ExchangeRateTestFactory(self::getContainer());
        $exchangeRateTestFactory->createExchangeRate('GBP', $exchangeRate);

        $tokenValue = $this->createCart($localeCode, $channelCode)['tokenValue'];

        $this->registerAccount($channelCode);

        $userToken = $this->loginAccount($channelCode)['token'];

        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
                'quantity' => 1,
            ],
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_MEDICATION,
                'quantity' => 1,
                'parentProductVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
            ],
        ], $userToken);

        $this->addMedicalQuestionnaire($tokenValue);

        $this->completeMedicalQuestionnaire($tokenValue);

        $paymentId = $this->setAddress($tokenValue, $countryCode, $channelCode, $userToken)['payments'][0]['id'];

        $this->setPaymentMethod($tokenValue, $paymentId, 'mastercard_doctoronline', $channelCode, $userToken);

        $this->completeOrder($tokenValue, $channelCode, $userToken, [
            'declare_information_truthfully',
            'terms_and_conditions',
            'collect_medical_information',
            'transfer_prescription',
        ]);

        // Act
        $this->forcePayOrder($tokenValue);

        // Assert
        $order = $this->getOrderEntityByTokenValue($tokenValue);
        $payment = $order->getLastPayment();
        self::assertInstanceOf(PaymentInterface::class, $payment);
        self::assertSame($exchangeRate, $payment->getExchangeRate());
    }
}
