<?php

declare(strict_types=1);

namespace App\Tests\Functional\StateMachine\TransitionLog\EventListener;

use App\Entity\Order\Order;
use App\Entity\TransitionLogEntry;
use App\Tests\Functional\Api\CartTestFactory;
use Doctrine\ORM\EntityManagerInterface;
use SM\Factory\FactoryInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class PostTransitionListenerTest extends WebTestCase
{
    private EntityManagerInterface $entityManager;
    private CartTestFactory $cartTestFactory;
    private FactoryInterface $stateMachineFactory;

    protected function setUp(): void
    {
        self::bootKernel();

        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        /** @var FactoryInterface $stateMachineFactory */
        $stateMachineFactory = self::getContainer()->get(FactoryInterface::class);
        $this->stateMachineFactory = $stateMachineFactory;

        $this->cartTestFactory = new CartTestFactory(self::getContainer());
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->entityManager->close();
        unset($this->entityManager);
    }

    public function testTransitionLogEntryIsPersisted(): void
    {
        // Arrange
        $cart = $this->cartTestFactory->createCart('dok_nl', 'nl');

        // Act
        $stateMachine = $this->stateMachineFactory->get($cart, 'order_fraud_check');
        $stateMachine->apply('create');

        // Assert
        self::assertInstanceOf(Order::class, $cart);

        $transitionLogEntries = $this->entityManager->getRepository(TransitionLogEntry::class)
            ->findBy(['order' => $cart]);

        self::assertCount(1, $transitionLogEntries);
        $transitionLogEntry = $transitionLogEntries[0];

        self::assertSame($cart, $transitionLogEntry->getOrder());
        self::assertEquals('sylius_order', $transitionLogEntry->getObjectTableName());
        self::assertEquals('create', $transitionLogEntry->getTransition());
        self::assertEquals('cart', $transitionLogEntry->getStateFrom());
        self::assertEquals('awaiting_payment', $transitionLogEntry->getStateTo());
    }
}
