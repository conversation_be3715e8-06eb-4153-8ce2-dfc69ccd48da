<?php

declare(strict_types=1);

namespace App\Tests\Functional\StateMachine\TransitionLog\Command;

use App\Entity\TransitionLogEntry;
use App\StateMachine\TransitionLog\Command\CleanUpTransitionLogCommand;
use App\Tests\Functional\Api\CartTestFactory;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Console\Tester\CommandTester;

class CleanUpTransitionLogCommandTest extends KernelTestCase
{
    private CommandTester $command;
    private EntityManagerInterface $entityManager;
    private CartTestFactory $cartTestFactory;

    protected function setUp(): void
    {
        $container = self::getContainer();

        /** @var CleanUpTransitionLogCommand $command */
        $command = $container->get(CleanUpTransitionLogCommand::class);
        $this->command = new CommandTester($command);

        /** @var EntityManagerInterface $entityManager */
        $entityManager = $container->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        $this->cartTestFactory = new CartTestFactory(self::getContainer());
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->entityManager->close();
        unset($this->entityManager);
    }

    public function testItCleansUpOldTransitionLogs(): void
    {
        $cart = $this->cartTestFactory->createCart('dok_nl', 'nl');

        // Arrange
        $transitionLogEntry1 = new TransitionLogEntry(
            $cart,
            'sylius_order',
            1337,
            'test-graph',
            'state-from',
            'state-to',
            'test-transition',
        );

        $transitionLogEntry2 = new TransitionLogEntry(
            $cart,
            'sylius_order',
            1338,
            'test-graph',
            'state-from',
            'state-to',
            'test-transition',
        );

        $this->entityManager->persist($transitionLogEntry1);
        $this->entityManager->persist($transitionLogEntry2);
        $this->entityManager->flush();

        $this->entityManager->getConnection()->executeStatement(
            '
                UPDATE transition_log_entry
                SET transitioned_at = :transitionedAt
                WHERE object_id = :objectId
            ',
            [
                'transitionedAt' => (new DateTime('-2 years'))->format('Y-m-d H:i:s'),
                'objectId' => 1337,
            ]
        );

        // Assert there are two log entries
        $transitionLogEntries = $this->entityManager->getRepository(TransitionLogEntry::class)->findAll();
        self::assertCount(2, $transitionLogEntries);

        // Act
        $this->command->execute([]);

        // Assert there is only one log entry
        $transitionLogEntries = $this->entityManager->getRepository(TransitionLogEntry::class)->findAll();
        self::assertCount(1, $transitionLogEntries);
    }
}
