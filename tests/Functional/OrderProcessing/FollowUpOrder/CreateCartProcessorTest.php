<?php

declare(strict_types=1);

namespace App\Tests\Functional\OrderProcessing\FollowUpOrder;

use App\Entity\Order\Order;
use App\OrderProcessing\FollowUpOrder\Processor\CreateCartProcessor;
use App\Tests\Functional\Api\CartTestFactory;

final class CreateCartProcessorTest extends AbstractFollowUpOrderTestCase
{
    public function testProcessCreatesFollowUpOrderInDestinationChannel(): void
    {
        $order = $this->cartTestFactory->createCart(CartTestFactory::CHANNEL_CODE, CartTestFactory::LOCALE_CODE);

        $followUpOrder = $this->processor->process(
            $order,
            new Order(),
            $this->destinationChannel
        );

        $this->assertSame($followUpOrder, $order->getFollowUpOrder());
        $this->assertNotEmpty($followUpOrder->getTokenValue());
        $this->assertSame($this->destinationChannel, $followUpOrder->getChannel());
        $this->assertSame(CartTestFactory::LOCALE_CODE, $followUpOrder->getLocaleCode());
    }

    protected function getProcessorClassName(): string
    {
        return CreateCartProcessor::class;
    }
}
