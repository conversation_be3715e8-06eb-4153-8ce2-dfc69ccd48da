<?php

declare(strict_types=1);

namespace App\Tests\Functional\OrderProcessing\FollowUpOrder;

use App\Entity\Channel\Channel;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\Order\Order;
use App\Entity\User\ShopUserInterface;
use App\OrderProcessing\FollowUpOrder\Processor\CustomerAndAddressProcessor;
use App\StateMachine\OrderCheckoutStates;
use App\Tests\Functional\Api\CartTestFactory;
use DateTimeInterface;
use Sylius\Component\Core\Model\AddressInterface;

final class CustomerAndAddressProcessorTest extends AbstractFollowUpOrderTestCase
{
    public function testProcessCreatesCustomerAndUserAndAddressesForOrder(): void
    {
        $customer = $this->createCustomer();
        $order = $this->createOrder();

        $order->setCustomer($customer);

        $followUpOrder = $this->createFollowUpOrder();

        $followUpOrder = $this->processor->process(
            $order,
            $followUpOrder,
            $this->destinationChannel
        );

        /** @var CustomerPool $destinationChannelCustomerPool */
        $destinationChannelCustomerPool = $this->destinationChannel->getCustomerPool();

        /** @var Customer $followUpOrderCustomer */
        $followUpOrderCustomer = $followUpOrder->getCustomer();
        $followUpOrderBillingAddress = $followUpOrder->getBillingAddress();
        $followUpOrderShippingAddress = $followUpOrder->getShippingAddress();
        /** @var ShopUserInterface $followUpOrderShopUser */
        $followUpOrderShopUser = $followUpOrder->getUser();

        $this->assertNotSame($order->getCustomer(), $followUpOrderCustomer);
        $this->assertInstanceOf(Customer::class, $followUpOrderCustomer);
        $this->assertInstanceOf(ShopUserInterface::class, $followUpOrderShopUser);
        $this->assertInstanceOf(AddressInterface::class, $followUpOrderBillingAddress);
        $this->assertSame('Billing', $followUpOrderBillingAddress->getCity());
        $this->assertInstanceOf(AddressInterface::class, $followUpOrderShippingAddress);
        $this->assertSame('Shipping', $followUpOrderShippingAddress->getCity());
        $this->assertInstanceOf(Customer::class, $followUpOrderBillingAddress->getCustomer());
        $this->assertInstanceOf(Customer::class, $followUpOrderShippingAddress->getCustomer());
        $this->assertSame($followUpOrderCustomer, $followUpOrderBillingAddress->getCustomer());
        $this->assertSame($followUpOrderCustomer, $followUpOrderShippingAddress->getCustomer());
        $this->assertSame($destinationChannelCustomerPool, $followUpOrderCustomer->getCustomerPool());
        $this->assertIsString($followUpOrderShopUser->getPasswordResetToken());
        $this->assertInstanceOf(
            DateTimeInterface::class,
            $followUpOrderShopUser->getAccountCompletionTokenRequestedAt()
        );
    }

    public function testProcessDoesntCreateResetTokenForExistingCustomer(): void
    {
        $customer = $this->createCustomer();
        $blueclinicCustomer = $this->createCustomer('blueclinic_nl');
        $order = $this->createOrder();
        $order->setCustomer($customer);

        $followUpOrder = $this->createFollowUpOrder();

        $followUpOrder = $this->processor->process(
            $order,
            $followUpOrder,
            $this->destinationChannel
        );

        /** @var CustomerPool $destinationChannelCustomerPool */
        $destinationChannelCustomerPool = $this->destinationChannel->getCustomerPool();

        /** @var Customer $followUpOrderCustomer */
        $followUpOrderCustomer = $followUpOrder->getCustomer();
        $followUpOrderBillingAddress = $followUpOrder->getBillingAddress();
        $followUpOrderShippingAddress = $followUpOrder->getShippingAddress();
        /** @var ShopUserInterface $followUpOrderShopUser */
        $followUpOrderShopUser = $followUpOrder->getUser();

        $this->assertNotSame($order->getCustomer(), $followUpOrderCustomer);
        $this->assertSame($blueclinicCustomer, $followUpOrderCustomer);
        $this->assertInstanceOf(Customer::class, $followUpOrderCustomer);
        $this->assertInstanceOf(ShopUserInterface::class, $followUpOrderShopUser);
        $this->assertInstanceOf(AddressInterface::class, $followUpOrderBillingAddress);
        $this->assertInstanceOf(AddressInterface::class, $followUpOrderShippingAddress);
        $this->assertInstanceOf(Customer::class, $followUpOrderBillingAddress->getCustomer());
        $this->assertInstanceOf(Customer::class, $followUpOrderShippingAddress->getCustomer());
        $this->assertSame($followUpOrderCustomer, $followUpOrderBillingAddress->getCustomer());
        $this->assertSame($followUpOrderCustomer, $followUpOrderShippingAddress->getCustomer());
        $this->assertSame($destinationChannelCustomerPool, $followUpOrderCustomer->getCustomerPool());
        $this->assertEmpty($followUpOrderShopUser->getPasswordResetToken());
        $this->assertEmpty($followUpOrderShopUser->getAccountCompletionTokenRequestedAt());
    }

    protected function getProcessorClassName(): string
    {
        return CustomerAndAddressProcessor::class;
    }

    private function createCustomer(string $channelCode = 'dok_nl'): Customer
    {
        /** @var Channel $medicationChannel */
        $medicationChannel = $this->channelRepository->findOneBy([
            'code' => $channelCode,
        ]);
        /** @var CustomerPool $customerPool */
        $customerPool = $medicationChannel->getCustomerPool();

        /** @var Customer $customer */
        $customer = $this->cartTestFactory->createCustomer();
        $customer->setCustomerPool($customerPool);
        $customer->addAddress($this->cartTestFactory->createAddress());
        $customer->setLastName('Test');
        $customer->setGender('m');

        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $shopUser = $this->cartTestFactory->createUserForCustomer($customer);

        $this->entityManager->persist($shopUser);
        $this->entityManager->flush();

        return $customer;
    }

    private function createOrder(): Order
    {
        $order = $this->cartTestFactory->createCart(CartTestFactory::CHANNEL_CODE, CartTestFactory::LOCALE_CODE);

        $billingAddress = $this->cartTestFactory->createAddress();
        $billingAddress->setCity('Billing');
        $order->setBillingAddress($billingAddress);

        $shippingAddress = $this->cartTestFactory->createAddress();
        $shippingAddress->setCity('Shipping');
        $order->setShippingAddress($shippingAddress);

        $this->entityManager->persist($order);
        $this->entityManager->flush();

        return $order;
    }

    private function createFollowUpOrder(): Order
    {
        $followUpOrder = $this->cartTestFactory->createCart(
            CartTestFactory::BLUECLINIC_CHANNEL_CODE,
            CartTestFactory::LOCALE_CODE
        );
        $followUpOrder->setTokenValue('tokenValue');
        $followUpOrder->setCheckoutState(OrderCheckoutStates::STATE_MEDICAL_QUESTIONNAIRE_COMPLETED);

        $this->entityManager->persist($followUpOrder);
        $this->entityManager->flush();

        return $followUpOrder;
    }
}
