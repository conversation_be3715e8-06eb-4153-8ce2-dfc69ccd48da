<?php

declare(strict_types=1);

namespace App\Tests\Functional\OrderProcessing\FollowUpOrder;

use App\Entity\Channel\ChannelPricing;
use App\Entity\Order\OrderItem;
use App\Entity\Order\PreferredOrderItem;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariantInterface;
use App\OrderProcessing\FollowUpOrder\Processor\AddOrderItemsProcessor;
use App\Tests\Functional\Api\CartTestFactory;
use Sylius\Component\Core\Repository\ProductVariantRepositoryInterface;

final class AddOrderItemsProcessorTest extends AbstractFollowUpOrderTestCase
{
    private const string PRODUCT_CODE_CIALIS = 'test-cialis';
    private const string PRODUCT_VARIANT_CODE_CIALIS = 'test-cialis-10mg-4tabl';
    private const string PRODUCT_CODE_VIAGRA = 'test-viagra';
    private const string PRODUCT_VARIANT_CODE_VIAGRA = 'test-viagra-10mg-4tabl';
    private const string PRODUCT_CONSULT_CODE = 'consult-for-test';
    private const int QUANTITY = 2;

    /** @var ProductVariantRepositoryInterface<ProductVariantInterface> */
    private readonly ProductVariantRepositoryInterface $productVariantRepository;

    public function setUp(): void
    {
        parent::setUp();

        /** @var ProductVariantRepositoryInterface<ProductVariantInterface> $productVariantRepository */
        $productVariantRepository = $this->entityManager->getRepository(ProductVariantInterface::class);
        $this->productVariantRepository = $productVariantRepository;
    }

    public function testProcessAddsOrderItemsToFollowUpOrder(): void
    {
        $this->createProductVariantsForChannels(
            self::PRODUCT_CODE_CIALIS,
            self::PRODUCT_VARIANT_CODE_CIALIS,
        );

        $this->createProductVariantsForChannels(
            self::PRODUCT_CODE_VIAGRA,
            self::PRODUCT_VARIANT_CODE_VIAGRA,
        );

        $order = $this->cartTestFactory->createCartWithProductVariants(
            CartTestFactory::CHANNEL_CODE,
            CartTestFactory::LOCALE_CODE,
            [
                self::PRODUCT_VARIANT_CODE_CIALIS,
                self::PRODUCT_VARIANT_CODE_VIAGRA,
            ],
            self::QUANTITY,
        );

        $followUpOrder = $this->cartTestFactory->createCart(self::DESTINATION_CHANNEL_CODE, CartTestFactory::LOCALE_CODE);

        self::assertEmpty($followUpOrder->getItems());

        $this->processor->process($order, $followUpOrder, $this->destinationChannel);

        $orderItems = $followUpOrder->getItems();
        self::assertCount(2, $orderItems);
        self::assertContainsOnlyInstancesOf(OrderItem::class, $orderItems);

        /** @var OrderItem $orderItemCialis */
        $orderItemCialis = $orderItems->first();
        self::assertSame(self::PRODUCT_CODE_CIALIS, $orderItemCialis->getProduct()?->getCode());
        self::assertSame(self::PRODUCT_VARIANT_CODE_CIALIS, $orderItemCialis->getVariant()?->getCode());
        self::assertSame(self::QUANTITY, $orderItemCialis->getQuantity());

        /** @var OrderItem $orderItemViagra */
        $orderItemViagra = $orderItems->get(1);
        self::assertSame(self::PRODUCT_CODE_VIAGRA, $orderItemViagra->getProduct()?->getCode());
        self::assertSame(self::PRODUCT_VARIANT_CODE_VIAGRA, $orderItemViagra->getVariant()?->getCode());
        self::assertSame(self::QUANTITY, $orderItemViagra->getQuantity());
    }

    public function testProcessAddsPreferredOrderItemsToFollowUpOrder(): void
    {
        $this->createProductVariantsForChannels(
            self::PRODUCT_CODE_CIALIS,
            self::PRODUCT_VARIANT_CODE_CIALIS,
            true,
        );

        $this->createProductVariantsForChannels(
            self::PRODUCT_CONSULT_CODE,
            self::PRODUCT_CONSULT_CODE,
            true,
            ProductType::CONSULT
        );

        $order = $this->cartTestFactory->createCartWithProductVariants(
            CartTestFactory::CHANNEL_CODE,
            CartTestFactory::LOCALE_CODE,
            [
                self::PRODUCT_CONSULT_CODE,
            ],
            self::QUANTITY,
        );

        /** @var ProductVariantInterface $productVariant */
        $productVariant = $this->productVariantRepository->findOneBy([
            'code' => self::PRODUCT_VARIANT_CODE_CIALIS,
        ]);

        $orderItemConsult = $order->getItems()->first();

        $preferredOrderItem = new PreferredOrderItem($orderItemConsult, $productVariant, self::QUANTITY);
        $orderItemConsult->addPreferredItem($preferredOrderItem);

        $followUpOrder = $this->cartTestFactory->createCart(self::DESTINATION_CHANNEL_CODE, CartTestFactory::LOCALE_CODE);

        self::assertEmpty($followUpOrder->getItems());
        $order->setFollowUpOrder($followUpOrder);
        $this->processor->process($order, $followUpOrder, $this->destinationChannel);

        $orderItems = $followUpOrder->getItems();
        self::assertCount(1, $orderItems);
        self::assertContainsOnlyInstancesOf(OrderItem::class, $orderItems);

        /** @var OrderItem $orderItem */
        $orderItem = $orderItems->first();
        self::assertSame(self::PRODUCT_CODE_CIALIS, $orderItem->getProduct()?->getCode());
        self::assertSame(self::PRODUCT_VARIANT_CODE_CIALIS, $orderItem->getVariant()?->getCode());
        self::assertSame(self::QUANTITY, $orderItem->getQuantity());
    }

    protected function getProcessorClassName(): string
    {
        return AddOrderItemsProcessor::class;
    }

    private function createProductVariantsForChannels(
        string $productCode,
        string $productVariantCode,
        bool $isPrescriptionRequired = false,
        ProductType $productType = ProductType::MEDICATION,
    ): void {
        $this->cartTestFactory->createProductAndProductVariants(
            CartTestFactory::CHANNEL_CODE,
            $productCode,
            [
                $productVariantCode,
            ],
            'dokteronline',
            false,
            [
                $productVariantCode => 500,
            ],
            1000,
            $productType
        );

        /** @var ProductVariantInterface $productVariant */
        $productVariant = $this->productVariantRepository->findOneBy([
            'code' => $productVariantCode,
        ]);

        $productVariantChannelPricing = new ChannelPricing();
        $productVariantChannelPricing->setChannelCode(self::DESTINATION_CHANNEL_CODE);
        $productVariantChannelPricing->setPrice(1000);

        $productVariant->addChannelPricing($productVariantChannelPricing);
        $productVariant->setPrescriptionRequired($isPrescriptionRequired);

        $this->entityManager->persist($productVariantChannelPricing);
        $this->entityManager->flush();
    }
}
