<?php

declare(strict_types=1);

namespace App\Tests\Functional\OrderProcessing\FollowUpOrder;

use App\OrderProcessing\FollowUpOrder\Processor\CompleteOrderAndApproveMedicationProcessor;
use App\StateMachine\OrderPrescriptionStates;
use App\Tests\Mocks\AnamnesisServiceClient;
use Ramsey\Uuid\Uuid;
use Sylius\Component\Core\OrderCheckoutStates;

final class CompleteOrderAndApproveMedicationProcessorTest extends AbstractFollowUpOrderTestCase
{
    public function testProcessCompletesAndApprovesOrder(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(prescriptionRequired: true);

        $order = $this->cartTestFactory->createCartWithProductVariants();
        $order->setMedicalQuestionnaire(Uuid::fromString(AnamnesisServiceClient::VALID_UUID));
        $order->setPrescriptionFilename('test.pdf');
        $order->setDoctorName('Test');
        $order->setDoctorRegistrationNumber('001');
        $this->entityManager->persist($order);

        $customer = $this->cartTestFactory->createCustomer();
        $this->cartTestFactory->createUserForCustomer($customer);

        $followUpOrder = $this->cartTestFactory->createCartWithProductVariants();
        $followUpOrder->setCheckoutState(OrderCheckoutStates::STATE_SHIPPING_SELECTED);
        $followUpOrder->setPrescriptionState(OrderPrescriptionStates::STATE_CART);
        $followUpOrder->setCustomer($customer);
        $followUpOrder->setShippingAddress($this->cartTestFactory->createAddress());
        $followUpOrder->setBillingAddress($this->cartTestFactory->createAddress());
        $followUpOrder->setParentOrder($order);

        // Normally, previous processors should set the usageAdvice. But in this unit test, we need to set it manually.
        foreach ($followUpOrder->getItems() as $orderItem) {
            $orderItem->setUsageAdvice('usageAdvice');
        }

        $this->entityManager->persist($followUpOrder);
        $this->entityManager->flush();
        $this->entityManager->beginTransaction();

        $followUpOrder = $this->processor->process(
            $order,
            $followUpOrder,
            $this->destinationChannel
        );

        $this->entityManager->commit();

        $this->assertSame('new', $followUpOrder->getState());
        $this->assertSame('completed', $followUpOrder->getCheckoutState());
        $this->assertSame('approved', $followUpOrder->getPrescriptionState());
        $this->assertSame('awaiting_payment', $followUpOrder->getPaymentState());
        $this->assertSame('awaiting_payment', $followUpOrder->getShippingState());
        $this->assertSame('account', $followUpOrder->getLastPayment()?->getDetails()['payment_started_from']);
    }

    protected function getProcessorClassName(): string
    {
        return CompleteOrderAndApproveMedicationProcessor::class;
    }
}
