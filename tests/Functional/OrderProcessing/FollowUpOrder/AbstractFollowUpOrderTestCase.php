<?php

declare(strict_types=1);

namespace App\Tests\Functional\OrderProcessing\FollowUpOrder;

use App\Entity\Channel\Channel;
use App\OrderProcessing\FollowUpOrder\Processor\ProcessorInterface;
use App\Repository\ChannelRepositoryInterface;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ExchangeRateTestFactory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Webmozart\Assert\Assert;

abstract class AbstractFollowUpOrderTestCase extends WebTestCase
{
    protected const string DESTINATION_CHANNEL_CODE = 'blueclinic_nl';

    protected readonly EntityManagerInterface $entityManager;
    protected readonly CartTestFactory $cartTestFactory;
    protected readonly ProcessorInterface $processor;
    protected readonly ChannelRepositoryInterface $channelRepository;
    protected readonly Channel $destinationChannel;

    public function setUp(): void
    {
        parent::setUp();

        $this->cartTestFactory = new CartTestFactory(self::getContainer());

        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        /** @var ProcessorInterface $processor */
        $processor = self::getContainer()->get($this->getProcessorClassName());
        Assert::isInstanceOf($processor, ProcessorInterface::class);
        $this->processor = $processor;

        /** @var ChannelRepositoryInterface $channelRepository */
        $channelRepository = $entityManager->getRepository(Channel::class);
        $this->channelRepository = $channelRepository;

        /** @var Channel $destinationChannel */
        $destinationChannel = $this->channelRepository->findOneByCode(static::DESTINATION_CHANNEL_CODE);
        Assert::isInstanceOf($destinationChannel, Channel::class);
        $this->destinationChannel = $destinationChannel;

        $exchangeRateTestFactory = new ExchangeRateTestFactory(self::getContainer());
        $exchangeRateTestFactory->createExchangeRate();
    }

    /**
     * @return string a className that implements {@see ProcessorInterface}
     */
    abstract protected function getProcessorClassName(): string;
}
