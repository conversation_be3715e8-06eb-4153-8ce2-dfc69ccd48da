<?php

declare(strict_types=1);

namespace App\Tests\Functional\Doctrine;

use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShippingMethod;
use App\Entity\Supplier\Supplier;
use App\Tests\Functional\Api\CartTestFactory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class OrderCountForSupplierListenerTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private CartTestFactory $cartTestFactory;

    public function setUp(): void
    {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;
        $this->cartTestFactory = new CartTestFactory(self::getContainer());
    }

    public function testItUpdatesOrderCountForSupplier(): void
    {
        // Arrange
        $supplier = $this->entityManager
            ->getRepository(Supplier::class)
            ->findOneBy(['identifier' => 'apotheek-bad-nieuweschans']);

        $shipment = $this->createShipment();

        // Act
        $shipment->setSupplier($supplier);
        $shipment->setSupplierShipmentReference('mock-reference');

        $this->entityManager->flush();

        $this->entityManager->refresh($supplier);

        // Assert
        $this->assertSame(1, $supplier->getAmountOrdersToday());
    }

    private function createShipment(): Shipment
    {
        $method = $this->entityManager
            ->getRepository(ShippingMethod::class)
            ->findOneBy(['code' => 'dhl']);

        $shipment = new Shipment();
        $shipment->setMethod($method);

        $order = $this->cartTestFactory->createCart('dok_de', 'de');
        $shipment->setOrder($order);

        $this->entityManager->persist($shipment);
        $this->entityManager->flush();

        return $shipment;
    }
}
