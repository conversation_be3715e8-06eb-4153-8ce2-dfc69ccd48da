<?php

declare(strict_types=1);

namespace App\Tests\Functional\Command;

use App\Command\AddPaymentMethodGatewayConfigToPaymentsCommand;
use App\Entity\Payment\PaymentMethod;
use App\Entity\Payment\PaymentMethodGatewayConfigInterface;
use App\Tests\Util\PersistedFactory\OrderFactory;
use App\Tests\Util\PersistedFactory\PaymentFactory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Console\Tester\CommandTester;

final class AddPaymentMethodGatewayConfigToPaymentsCommandTest extends KernelTestCase
{
    private CommandTester $command;
    private EntityManagerInterface $entityManager;
    private PaymentFactory $paymentFactory;
    private OrderFactory $orderFactory;

    protected function setUp(): void
    {
        $container = self::getContainer();

        $command = $container->get(AddPaymentMethodGatewayConfigToPaymentsCommand::class);
        self::assertInstanceOf(AddPaymentMethodGatewayConfigToPaymentsCommand::class, $command);
        $this->command = new CommandTester($command);

        $entityManager = $container->get(EntityManagerInterface::class);
        self::assertInstanceOf(EntityManagerInterface::class, $entityManager);
        $this->entityManager = $entityManager;

        $paymentFactory = $container->get(PaymentFactory::class);
        self::assertInstanceOf(PaymentFactory::class, $paymentFactory);
        $this->paymentFactory = $paymentFactory;

        $orderFactory = $container->get(OrderFactory::class);
        self::assertInstanceOf(OrderFactory::class, $orderFactory);
        $this->orderFactory = $orderFactory;
    }

    public function testItUpdatesPaymentsWithPaymentMethodGatewayConfig(): void
    {
        // Arrange
        $paymentMethod = $this->entityManager->getRepository(PaymentMethod::class)->findOneBy(['enabled' => true]);
        self::assertInstanceOf(PaymentMethod::class, $paymentMethod);

        $payment = $this->paymentFactory->create(
            amount: 1000,
            method: $paymentMethod,
            order: $this->orderFactory->create(),
        );

        // Assert
        self::assertNull($payment->getPaymentMethodGatewayConfig());

        // Act
        $this->command->execute([]);

        // Assert (2)
        $this->entityManager->refresh($payment);
        $paymentMethodGatewayConfig = $payment->getPaymentMethodGatewayConfig();
        self::assertInstanceOf(PaymentMethodGatewayConfigInterface::class, $paymentMethodGatewayConfig);
        self::assertSame($paymentMethod, $paymentMethodGatewayConfig->getPaymentMethod());
        self::assertSame($paymentMethod->getGatewayConfig(), $paymentMethodGatewayConfig->getGatewayConfig());
    }
}
