<?php

declare(strict_types=1);

namespace App\Tests\Functional\Payum\Klarna;

use App\Payum\Klarna\Client;
use App\Payum\Klarna\Model\Request\Factory\CreateSessionFactory;
use App\Payum\Klarna\Model\Response\CreateOrder;
use App\Payum\Klarna\Model\Response\CreateSession as CreateSessionResponse;
use App\Payum\Klarna\Model\Response\OrderDetails;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\PaymentFactory;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\ParameterBag;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

final class ClientTest extends KernelTestCase
{
    private const string API_KEY = 'tets-api-key';
    private const string BASE_URI = 'https://base/uri';

    private HttpClientInterface&MockObject $httpClient;

    private Client $api;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var SerializerInterface $serializer */
        $serializer = self::getContainer()->get('app.payum.klarna.serializer');

        $this->httpClient = $this->createMock(HttpClientInterface::class);
        $this->httpClient->method('withOptions')->willReturnCallback(function (array $options) {
            $this->assertEquals([
                'base_uri' => self::BASE_URI,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Basic '.self::API_KEY,
                ],
            ], $options);

            return $this->httpClient;
        });

        $this->api = Client::create($this->httpClient, $serializer);
    }

    public function testCreateSession(): void
    {
        // Arrange
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getContent')->willReturn('{"client_token": "token", "session_id": "session_id"}');

        // Assert request to Klarna has correct body
        $this->httpClient->method('request')
            ->with(
                'POST',
                '/payments/v1/sessions',
                [
                    'body' => $this->getExpectedCreateSessionRequestBody(),
                ]
            )
            ->willReturn($response);

        $order = OrderFactory::createPrefilled();
        $payment = PaymentFactory::create();
        $order->addPayment($payment);

        // Act
        $response = $this->api->createSession(CreateSessionFactory::create($payment, 'https://notify-me'));

        // Assert
        $this->assertInstanceOf(CreateSessionResponse::class, $response);
        $this->assertEquals('token', $response->clientToken);
        $this->assertEquals('session_id', $response->sessionId);
    }

    public function testCreateOrder(): void
    {
        // Arrange
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getContent')->willReturn($this->getCreateOrderResponseBody());

        // Assert request to Klarna has correct body
        $this->httpClient->method('request')->with(
            'POST',
            '/payments/v1/authorizations/mocked_token/order',
            [
                'body' => $this->getCreateOrderExpectedRequestBody(),
            ]
        )->willReturn($response);

        $order = OrderFactory::createPrefilled();
        $payment = PaymentFactory::create();
        $order->addPayment($payment);

        $query = $this->createMock(ParameterBag::class);
        $query->method('getString')->with('authorization_token')->willReturn('mocked_token');

        $request = $this->createMock(Request::class);
        /** @phpstan-ignore-next-line InputBag is final */
        $request->query = $query;

        // Act
        $response = $this->api->createOrder($payment, $request);

        // Assert
        $this->assertInstanceOf(CreateOrder::class, $response);
        $this->assertEquals('klarna-order-id-1337', $response->orderId);
    }

    public function testGetOrderDetails(): void
    {
        // Arrange
        $response = $this->createMock(ResponseInterface::class);
        $response->method('getContent')->willReturn(sprintf('{"status": "%s", "foo": "bar"}', 'CAPTURED'));

        // Assert request to Klarna has correct body
        $this->httpClient->method('request')->with(
            'GET',
            '/ordermanagement/v1/orders/1337',
        )->willReturn($response);

        // Act
        $response = $this->api->getOrderDetails('1337');

        // Assert
        $this->assertInstanceOf(OrderDetails::class, $response);
        $this->assertEquals('CAPTURED', $response->status);
    }

    /**
     * @see https://docs.klarna.com/api/payments/#operation/createCreditSession request sample
     */
    private function getExpectedCreateSessionRequestBody(): string
    {
        return (string) json_encode([
            'billing_address' => [
                'city' => 'Test City',
                'country' => 'NL',
                'email' => '<EMAIL>',
                'family_name' => 'Doe',
                'given_name' => 'John',
                'postal_code' => '1234AZ',
                'street_address' => 'Test Street',
            ],
            'customer' => [
                'date_of_birth' => '1982-03-15',
            ],
            'locale' => 'nl',
            'merchant_reference1' => '1337',
            'merchant_urls' => [
                'notification' => 'https://notify-me',
            ],
            'order_amount' => 1337,
            'order_lines' => [
                [
                    'name' => 'Payment for order #1337',
                    'quantity' => 1,
                    'total_amount' => 1337,
                    'unit_price' => 1337,
                ],
            ],
            'purchase_country' => 'NL',
            'purchase_currency' => 'EUR',
            'order_tax_amount' => 0,
            'intent' => 'buy',
        ], JSON_THROW_ON_ERROR);
    }

    /**
     * @see https://docs.klarna.com/api/payments/#operation/createOrder response sample
     */
    private function getCreateOrderResponseBody(): string
    {
        return (string) json_encode([
            'authorized_payment_method' => [
                'number_of_days' => 0,
                'number_of_installments' => 0,
                'type' => 'invoice',
            ],
            'fraud_status' => 'ACCEPTED',
            'order_id' => 'klarna-order-id-1337',
            'redirect_url' => 'https://some-redirect-url',
        ], JSON_THROW_ON_ERROR);
    }

    /**
     * @see https://docs.klarna.com/api/payments/#operation/createOrder request sample
     */
    private function getCreateOrderExpectedRequestBody(): string
    {
        return (string) json_encode([
            'billing_address' => [
                'city' => 'Test City',
                'country' => 'NL',
                'email' => '<EMAIL>',
                'family_name' => 'Doe',
                'given_name' => 'John',
                'postal_code' => '1234AZ',
                'street_address' => 'Test Street',
            ],
            'customer' => [
                'date_of_birth' => '1982-03-15',
            ],
            'locale' => 'nl',
            'merchant_reference1' => '1337',
            'order_amount' => 1337,
            'order_lines' => [
                [
                    'name' => 'Payment for order #1337',
                    'quantity' => 1,
                    'total_amount' => 1337,
                    'unit_price' => 1337,
                ],
            ],
            'purchase_country' => 'NL',
            'purchase_currency' => 'EUR',
            'order_tax_amount' => 0,
        ], JSON_THROW_ON_ERROR);
    }
}
