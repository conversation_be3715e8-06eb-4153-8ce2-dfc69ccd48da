<?php

declare(strict_types=1);

namespace App\Tests\Functional\Payum\CM;

use App\Factory\Payment\PaymentMethodGatewayConfigFactory;
use App\Payum\CM\Client;
use App\Payum\CM\ClientInterface;
use App\Tests\Util\Factory\AddressFactory;
use App\Tests\Util\Factory\CustomerFactory;
use App\Tests\Util\Factory\GatewayConfigFactory;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\PaymentFactory;
use DateTime;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

final class ClientTest extends KernelTestCase
{
    private const string CM_MERCHANT_KEY = '4ef08825-993a-424d-a769-3ee97116a1b6';

    private HttpClientInterface&MockObject $httpClientMock;
    private ClientInterface $client;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var SerializerInterface $serializer */
        $serializer = self::getContainer()->get('app.payum.cm.serializer');

        $this->httpClientMock = $this->createMock(HttpClientInterface::class);
        $this->httpClientMock->method('withOptions')->willReturnCallback(function (array $options) {
            $this->assertEquals([
                'base_uri' => 'https://base/uri',
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Basic '.base64_encode('cm-username:cm-password'),
                ],
            ], $options);

            return $this->httpClientMock;
        });

        $this->client = Client::create($this->httpClientMock, $serializer, self::CM_MERCHANT_KEY);
    }

    public function testCreateOrder(): void
    {
        // Arrange
        $orderNumberWithoutPrefix = '000108192';
        $amount = 1337;
        $currencyCode = 'EUR';
        $email = '<EMAIL>';

        $orderKey = 'F1499C097FFA533D46FB05D52680AB9A';
        $expiresOn = '2020-01-31T19:00:12Z';
        $localeCode = 'nl';
        $countryCode = 'NL';

        $targetUrl = 'http://localhost/return/payment';

        $responseMock = $this->createMock(ResponseInterface::class);
        $responseMock->method('getContent')->willReturn(json_encode([
            'order_key' => $orderKey,
            'expires_on' => $expiresOn,
            'url' => 'http://the-page-to-redirect-to.localhost',
        ], JSON_THROW_ON_ERROR));

        $gatewayConfig = GatewayConfigFactory::create();

        $paymentMethod = PaymentFactory::createPaymentMethod([
            'gatewayConfig' => $gatewayConfig,
        ]);

        $paymentServiceProviderPaymentMethodIdentifier = 'cm-payment-method-name';

        $paymentMethodGatewayConfig = (new PaymentMethodGatewayConfigFactory())->createForPaymentMethodAndGatewayConfig(
            $paymentMethod,
            $gatewayConfig,
            $paymentServiceProviderPaymentMethodIdentifier,
        );

        $paymentId = 46;

        $payment = PaymentFactory::create([
            'id' => $paymentId,
            'order' => OrderFactory::createPrefilled([
                'number' => "BC$orderNumberWithoutPrefix",
                'customer' => CustomerFactory::create([
                    'email' => $email,
                ]),
                'localeCode' => $localeCode,
                'billingAddress' => AddressFactory::create([
                    'countryCode' => $countryCode,
                ]),
            ]),
            'amount' => $amount,
            'currencyCode' => $currencyCode,
            'paymentMethod' => $paymentMethod,
            'paymentMethodGatewayConfig' => $paymentMethodGatewayConfig,
        ]);

        // Assert
        $this->httpClientMock->expects(self::once())
            ->method('request')
            ->with(
                'POST',
                strtr(
                    '/ps/api/public/v1/merchants/{merchant-key}/orders',
                    [
                        '{merchant-key}' => self::CM_MERCHANT_KEY,
                    ],
                ),
                [
                    'body' => json_encode([
                        'return_urls' => [
                            'success' => $targetUrl,
                            'pending' => $targetUrl,
                            'canceled' => $targetUrl,
                            'error' => $targetUrl,
                        ],
                        'order_reference' => $paymentId.'-'.$orderNumberWithoutPrefix,
                        'description' => 'Payment for order '.$orderNumberWithoutPrefix,
                        'amount' => $amount,
                        'currency' => $currencyCode,
                        'email' => $email,
                        'language' => $localeCode,
                        'country' => $countryCode,
                        'profile' => $paymentServiceProviderPaymentMethodIdentifier,
                    ], JSON_THROW_ON_ERROR),
                ],
            )
            ->willReturn($responseMock);

        // Act
        $createOrderResponse = $this->client->createOrder($payment, $targetUrl);

        // Assert (2)
        self::assertSame($orderKey, $createOrderResponse->orderKey->getValue());
        self::assertEquals(new DateTime($expiresOn), $createOrderResponse->expiresOn);
    }

    public function testGetOrder(): void
    {
        // Arrange
        $orderKey = 'F1499C097FFA533D46FB05D52680AB9A';
        $paymentId = 'pid1607033373t';

        $responseMock = $this->createMock(ResponseInterface::class);
        $responseMock->method('getContent')->willReturn(json_encode([
            'order_reference' => $orderKey,
            'payments' => [
                [
                    'id' => $paymentId,
                    'created_date' => '2024-11-29T08:59:10Z',
                    'authorization' => [
                        'amount' => 1337,
                        'currency' => 'EUR',
                        'state' => 'AUTHORIZED',
                    ],
                ],
            ],
        ], JSON_THROW_ON_ERROR));

        // Assert
        $this->httpClientMock->expects(self::once())
            ->method('request')
            ->with(
                'GET',
                strtr(
                    '/ps/api/public/v1/merchants/{merchant-key}/orders/{order-key}',
                    [
                        '{merchant-key}' => self::CM_MERCHANT_KEY,
                        '{order-key}' => $orderKey,
                    ],
                ),
            )
            ->willReturn($responseMock);

        // Act
        $response = $this->client->getOrder($orderKey);

        // Assert (2)
        self::assertSame($orderKey, $response->orderReference->getValue());
        self::assertCount(1, $response->payments);
        self::assertArrayHasKey(0, $response->payments);
        self::assertSame($paymentId, $response->payments[0]->id->getValue());
    }

    public function testCapturePayment(): void
    {
        // Arrange
        $orderKey = 'F1499C097FFA533D46FB05D52680AB9A';
        $paymentId = 'pid1607033373t';
        $paymentAmount = 1337;
        $currencyCode = 'EUR';

        $payment = PaymentFactory::create([
            'amount' => $paymentAmount,
            'currencyCode' => $currencyCode,
        ]);

        $responseMock = $this->createMock(ResponseInterface::class);
        $responseMock->method('getStatusCode')
            ->willReturn(201);

        // Assert
        $this->httpClientMock->expects(self::once())
            ->method('request')
            ->with(
                'POST',
                strtr(
                    '/ps/api/public/v1/merchants/{merchant-key}/orders/{order-key}/payments/{payment-id}/captures',
                    [
                        '{merchant-key}' => self::CM_MERCHANT_KEY,
                        '{order-key}' => $orderKey,
                        '{payment-id}' => $paymentId,
                    ],
                ),
                [
                    'body' => json_encode([
                        'amount' => $paymentAmount,
                        'currency' => $currencyCode,
                    ], JSON_THROW_ON_ERROR),
                ],
            )
            ->willReturn($responseMock);

        // Act
        $response = $this->client->capturePayment($payment, $orderKey, $paymentId);

        // Assert (2)
        self::assertTrue($response);
    }
}
