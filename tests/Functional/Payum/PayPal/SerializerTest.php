<?php

declare(strict_types=1);

namespace App\Tests\Functional\Payum\PayPal;

use App\Entity\Order\Order;
use App\Entity\Payment\Payment;
use App\Payum\PayPal\Model\Request\CreateOrderRequest;
use App\Payum\PayPal\Model\Response\AccessTokenResponse;
use App\Payum\PayPal\Model\Response\CreateOrderResponse;
use App\Payum\PayPal\Model\Response\Link;
use App\Payum\PayPal\Model\Response\ShowOrderDetailsResponse;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\SerializerInterface;

final class SerializerTest extends KernelTestCase
{
    private readonly SerializerInterface $serializer;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var SerializerInterface $serializer */
        $serializer = $this->getContainer()->get('test.payum.paypal.serializer');
        $this->serializer = $serializer;
    }

    public function testSerializeCreateOrderRequest(): void
    {
        // Arrange
        $order = $this->createMock(Order::class);
        $order->method('getNumberWithoutPrefix')->willReturn('1337');
        $order->method('getNumber')->willReturn('1337');

        $payment = $this->createMock(Payment::class);
        $payment->method('getAmount')->willReturn(1337);
        $payment->method('getCurrencyCode')->willReturn('EUR');
        $payment->method('getOrder')->willReturn($order);

        $createOrderRequest = CreateOrderRequest::createFromPayment($payment, 'https://return-url', 'https://cancel-url');

        $expectedJson = '{
            "intent": "CAPTURE",
            "purchase_units": [
                {
                    "invoice_id": "1337",
                    "amount": {
                        "currency_code": "EUR",
                        "value": "13.37"
                    },
                    "description": "Payment for order 1337"
                }
            ],
            "payment_source": {
                "paypal": {
                    "experience_context": {
                        "return_url": "https://return-url",
                        "cancel_url": "https://cancel-url",
                        "user_action": "PAY_NOW"
                    }
                }
            }
        }';

        // Act
        $json = $this->serializer->serialize($createOrderRequest, 'json');

        // Assert
        $this->assertEquals(json_encode(json_decode($expectedJson)), $json);
    }

    public function testDeserializeCreateOrderResponse(): void
    {
        // Arrange
        $json = '{
            "id": "53V15126W8790052P",
            "status": "CREATED",
            "links": [
                {
                  "href": "https://test-url/v2/checkout/orders/53V15126W8790052P",
                  "rel": "self",
                  "method": "GET"
                },
                {
                  "href": "https://test-url/checkoutnow?token=53V15126W8790052P",
                  "rel": "approve",
                  "method": "GET"
                }
            ]
        }';

        // Act
        $createOrderResponse = $this->serializer->deserialize($json, CreateOrderResponse::class, 'json');

        // Assert
        self::assertInstanceOf(CreateOrderResponse::class, $createOrderResponse);
        self::assertEquals('53V15126W8790052P', $createOrderResponse->id->getValue());
        self::assertEquals('CREATED', $createOrderResponse->status->value);
        self::assertCount(2, $createOrderResponse->links);

        $link1 = $createOrderResponse->links->getIterator()->offsetGet(0);
        self::assertInstanceOf(Link::class, $link1);
        self::assertEquals('https://test-url/v2/checkout/orders/53V15126W8790052P', $link1->href->getValue());
        self::assertEquals('self', $link1->rel->getValue());
        self::assertEquals('GET', $link1->method->getValue());

        $link2 = $createOrderResponse->links->getIterator()->offsetGet(1);
        self::assertInstanceOf(Link::class, $link2);
        self::assertEquals('https://test-url/checkoutnow?token=53V15126W8790052P', $link2->href->getValue());
        self::assertEquals('approve', $link2->rel->getValue());
        self::assertEquals('GET', $link2->method->getValue());
    }

    public function testDeserializeShowOrderDetailsResponse(): void
    {
        // Arrange
        $json = '{
            "status": "APPROVED"
        }';

        // Act
        $showOrderDetailsResponse = $this->serializer->deserialize($json, ShowOrderDetailsResponse::class, 'json');

        // Assert
        self::assertEquals('APPROVED', $showOrderDetailsResponse->status->value);
    }

    public function testDeserializeAccessTokenResponse(): void
    {
        // Arrange
        $json = '{
            "access_token": "some-access-token",
            "expires_in": 1337
        }';

        // Act
        $accessTokenResponse = $this->serializer->deserialize($json, AccessTokenResponse::class, 'json');

        // Assert
        self::assertEquals('some-access-token', $accessTokenResponse->accessToken->getValue());
        self::assertEquals(1337, $accessTokenResponse->expiresIn->getValue());
    }
}
