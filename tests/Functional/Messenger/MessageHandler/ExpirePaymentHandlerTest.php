<?php

declare(strict_types=1);

namespace App\Tests\Functional\Messenger\MessageHandler;

use App\Entity\Order\Order;
use App\Entity\Payment\Payment;
use App\Entity\Payment\PaymentInterface;
use App\Messenger\Message\ExpirePayment;
use App\Messenger\MessageHandler\ExpirePaymentHandler;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class ExpirePaymentHandlerTest extends KernelTestCase
{
    private ExpirePaymentHandler $expirePaymentHandler;
    protected EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        $container = self::getContainer();

        $expirePaymentHandler = $container->get(ExpirePaymentHandler::class);
        self::assertInstanceOf(ExpirePaymentHandler::class, $expirePaymentHandler);
        $this->expirePaymentHandler = $expirePaymentHandler;

        $entityManager = $container->get(EntityManagerInterface::class);
        self::assertInstanceOf(EntityManagerInterface::class, $entityManager);
        $this->entityManager = $entityManager;
    }

    public function testInvokeAppliesExpireTransition(): void
    {
        // Arrange
        $payment = $this->createPayment();

        // Act
        ($this->expirePaymentHandler)(new ExpirePayment($payment->getId()));

        // Assert
        self::assertEquals('expired', $payment->getState());
    }

    public function testInvokeDoesNotApplyTransition(): void
    {
        // Arrange
        $payment = $this->createPayment('completed');

        // Act
        ($this->expirePaymentHandler)(new ExpirePayment($payment->getId()));

        // Assert
        self::assertEquals('completed', $payment->getState());
    }

    private function createPayment(string $state = 'new'): PaymentInterface
    {
        $order = new Order();
        $order->setCurrencyCode('EUR');
        $order->setLocaleCode('nl');

        $payment = new Payment();
        $payment->setCurrencyCode('EUR');
        $payment->setOrder($order);
        $payment->setState($state);

        $this->entityManager->persist($order);
        $this->entityManager->persist($payment);
        $this->entityManager->flush();

        return $payment;
    }
}
