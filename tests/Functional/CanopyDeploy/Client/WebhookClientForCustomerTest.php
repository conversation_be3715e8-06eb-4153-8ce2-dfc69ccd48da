<?php

declare(strict_types=1);

namespace App\Tests\Functional\CanopyDeploy\Client;

use App\CanopyDeploy\Message\CreateOrUpdateCustomerWithMarketingSubscription;
use App\CanopyDeploy\Message\WebhookMessageInterface;
use App\CanopyDeploy\Payload\CustomerAsWebhookPayload;
use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Channel\Channel;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\User\ShopUser;
use App\Event\Enum\CustomerEventName;
use App\Serializer\Webhook\OpenApiWebhookPayloadSerializer;
use App\Serializer\Webhook\WebhookPayloadInterface;
use App\Tests\Mocks\Entity\TestCustomer;
use App\Tests\Util\Factory\BusinessUnitFactory;
use DateTime;

class WebhookClientForCustomerTest extends AbstractWebhookClientTest
{
    protected function getWebhookPayload(): WebhookPayloadInterface
    {
        return new CustomerAsWebhookPayload($this->getEventName(), $this->createTestCustomer());
    }

    /**
     * @param CustomerAsWebhookPayload $webhookPayload
     */
    protected function getMessage(
        WebhookPayloadInterface $webhookPayload,
        OpenApiWebhookPayloadSerializer $webhookPayloadSerializer,
    ): WebhookMessageInterface {
        return new CreateOrUpdateCustomerWithMarketingSubscription(
            BusinessUnitFactory::createFromArguments(),
            $webhookPayloadSerializer->serialize($webhookPayload),
        );
    }

    /**
     * @return array{eventName: string, customer: array<string, scalar|null>}
     */
    protected function expectedData(): array
    {
        return [
            'eventName' => $this->getEventName(),
            'customer' => [
                'id' => 144,
                'email' => '<EMAIL>',
                'firstName' => 'John',
                'lastName' => 'Doe',
                'birthday' => '1968-10-25',
                'gender' => 'f',
                'subscribedToNewsletter' => false,
                'createdAt' => '2022-11-14T12:08:27+00:00',
                'fullName' => 'John Doe',
                'accountCompletionToken' => null,
                'totalOrderValueCurrencyCode' => null,
                'totalOrderCount' => 0,
                'totalOrderValue' => 0,
            ],
        ];
    }

    protected function getWebhookUrl(): string
    {
        return 'endpointForCustomerWebhook';
    }

    protected function getBusinessUnitCode(): string
    {
        return 'dokteronline';
    }

    protected function getBearerToken(): string
    {
        return 'dokteronlineCustomerToken';
    }

    protected function getEventName(): string
    {
        return CustomerEventName::CustomerWasCreated->name;
    }

    private function createTestCustomer(): Customer
    {
        $businessUnit = new BusinessUnit();
        $businessUnit->setCanopyDeployCustomerWebhookUrl($this->getWebhookUrl());
        $businessUnit->setCode('dokteronline');

        $channel = new Channel();
        $channel->setCode('dok_nl');
        $channel->setBusinessUnit($businessUnit);

        $customerPool = new CustomerPool();
        $customerPool->setCode('dokteronline');
        $customerPool->addChannel($channel);

        $shopUser = new ShopUser();

        $customer = new TestCustomer();
        $customer->setId(144);
        $customer->setEmail('<EMAIL>');
        $customer->setBirthday(new DateTime('1968-10-25 00:00:00'));
        $customer->setCreatedAt(new DateTime('2022-11-14 12:08:27'));
        $customer->setFirstName('John');
        $customer->setLastName('Doe');
        $customer->setGender('f');
        $customer->setSubscribedToNewsletter(false);
        $customer->setCustomerPool($customerPool);
        $customer->setUser($shopUser);

        return $customer;
    }
}
