<?php

declare(strict_types=1);

namespace App\Tests\Functional\CanopyDeploy\Client;

use App\CanopyDeploy\Message\CreateOrUpdateCustomerWithMarketingSubscription;
use App\CanopyDeploy\Message\WebhookMessageInterface;
use App\CanopyDeploy\Payload\CustomerAsWebhookPayload;
use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\MarketingSubscription\MarketingSubscription;
use App\Event\Enum\MarketingSubscriptionEventName;
use App\Serializer\Webhook\OpenApiWebhookPayloadSerializer;
use App\Serializer\Webhook\WebhookPayloadInterface;
use App\Tests\Mocks\Entity\TestMarketingSubscription;
use App\Tests\Util\Factory\BusinessUnitFactory;
use DateTime;
use DateTimeImmutable;

class WebhookClientForMarketingSubscriptionTest extends AbstractWebhookClientTest
{
    protected function getWebhookPayload(): WebhookPayloadInterface
    {
        return new CustomerAsWebhookPayload($this->getEventName(), $this->createTestMarketingSubscription());
    }

    /**
     * @param CustomerAsWebhookPayload $webhookPayload
     */
    protected function getMessage(
        WebhookPayloadInterface $webhookPayload,
        OpenApiWebhookPayloadSerializer $webhookPayloadSerializer,
    ): WebhookMessageInterface {
        return new CreateOrUpdateCustomerWithMarketingSubscription(
            BusinessUnitFactory::createFromArguments(),
            $webhookPayloadSerializer->serialize($webhookPayload),
        );
    }

    /**
     * @return array{
     *     eventName: string,
     *     customer: array<mixed>
     * }
     */
    protected function expectedData(): array
    {
        return [
            'eventName' => $this->getEventName(),
            'customer' => [
                'firstName' => 'John',
                'lastName' => 'Doe',
                'email' => '<EMAIL>',
                'countryCode' => 'NL',
                'optInSmsSubscription' => [
                    'state' => 'none',
                    'subscriptionTypes' => [
                        'service' => 'No',
                    ],
                ],
                'optInDirectMail' => 'No',
                'optInEmail' => 'No',
                'optInEmailSubscription' => [
                    'state' => 'none',
                    'subscriptionTypes' => [
                        'service' => 'No',
                        'productInformation' => 'No',
                        'promotions' => 'No',
                    ],
                    'subscriptionTimeoutUntil' => '2022-11-14T12:08:30+00:00',
                    'subscribedAt' => '2022-11-14T12:08:28+00:00',
                    'unsubscribedAt' => '2022-11-14T12:08:29+00:00',
                    'source' => 'test',
                ],
                'optInShareProductData' => [
                    'subscribed' => 'No',
                ],
                'createdAt' => '2022-11-14T12:08:27+00:00',
                'localeCode' => 'nl',
                'marketingSubscriptionUuid' => 'test-uuid',
                'optInDirectMailSubscription' => [
                    'state' => 'none',
                    'subscriptionTypes' => [
                        'promotions' => 'No',
                    ],
                ],
                'optInSms' => 'No',
            ],
        ];
    }

    protected function getWebhookUrl(): string
    {
        return 'endpointForCustomerWebhook';
    }

    protected function getBusinessUnitCode(): string
    {
        return 'dokteronline';
    }

    protected function getBearerToken(): string
    {
        return 'dokteronlineCustomerToken';
    }

    protected function getEventName(): string
    {
        return MarketingSubscriptionEventName::MarketingSubscriptionWasCreated->name;
    }

    private function createTestMarketingSubscription(): MarketingSubscription
    {
        $businessUnit = new BusinessUnit();
        $businessUnit->setCode('dokteronline');
        $businessUnit->setCanopyDeployCustomerWebhookUrl($this->getWebhookUrl());

        $marketingSubscription = new TestMarketingSubscription('<EMAIL>', 'nl', 'NL', $businessUnit);
        $marketingSubscription->setCreatedAt(new DateTime('2022-11-14 12:08:27'));
        $marketingSubscription->setFirstName('John');
        $marketingSubscription->setLastName('Doe');

        $optInEmail = $marketingSubscription->getOptInEmail();
        $optInEmail->setSource('test');
        $optInEmail->setSubscribedAt(new DateTimeImmutable('2022-11-14 12:08:28'));
        $optInEmail->setUnsubscribedAt(new DateTimeImmutable('2022-11-14 12:08:29'));
        $optInEmail->setSubscriptionTimeoutUntil(new DateTimeImmutable('2022-11-14 12:08:30'));

        return $marketingSubscription;
    }
}
