<?php

declare(strict_types=1);

namespace App\Tests\Functional\CanopyDeploy\Client;

use App\CanopyDeploy\Client\Webhook\CanopyDeployWebhookClient;
use App\CanopyDeploy\Message\WebhookMessageInterface;
use App\Entity\BusinessUnit\BusinessUnit;
use App\Serializer\Webhook\OpenApiWebhookPayloadSerializer;
use App\Serializer\Webhook\WebhookPayloadInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Nijens\OpenapiBundle\Serialization\SerializationContextBuilderInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpClient\MockHttpClient;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Serializer\SerializerInterface;

abstract class AbstractWebhookClientTest extends WebTestCase
{
    public function testItSendsCorrectData(): void
    {
        $serializer = self::getContainer()->get(SerializerInterface::class);
        self::assertInstanceOf(SerializerInterface::class, $serializer);

        $businessUnit = $this->createMock(BusinessUnit::class);
        $businessUnit
            ->method('getCanopyDeployWebhookUrl')
            ->willReturn($this->getWebhookUrl());

        $businessUnit
            ->method('getCode')
            ->willReturn($this->getBusinessUnitCode());

        $businessUnitRepository = $this->createMock(EntityRepository::class);
        $businessUnitRepository
            ->method('findOneBy')
            ->willReturn($businessUnit);

        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager
            ->method('getRepository')
            ->willReturn($businessUnitRepository);

        $serializationContextBuilder = self::getContainer()->get(SerializationContextBuilderInterface::class);
        self::assertInstanceOf(SerializationContextBuilderInterface::class, $serializationContextBuilder);

        $webhookPayloadSerializer = new OpenApiWebhookPayloadSerializer(
            $serializer,
            $serializationContextBuilder,
            'config/openapi.yaml'
        );

        $mockResponse = new MockResponse();
        $httpClient = new MockHttpClient($mockResponse, 'https://clang.test/api/');

        $webhookClient = new CanopyDeployWebhookClient(
            $httpClient,
            $entityManager,
            [
                'blueclinic' => [
                    'order' => 'blueclinicOrderToken',
                    'customer' => 'blueclinicCustomerToken',
                    'password_reset' => 'blueclinicPasswordResetToken',
                    'remove_order_item' => 'blueclinicRemoveOrderItemToken',
                ],
                'dokteronline' => [
                    'order' => 'dokteronlineOrderToken',
                    'customer' => 'dokteronlineCustomerToken',
                    'password_reset' => 'dokteronlinePasswordResetToken',
                    'remove_order_item' => 'dokteronlineRemoveOrderItemToken',
                    'product_back_in_stock' => 'dokteronlineProductBackInStockToken',
                ],
            ],
            $this->createStub(LoggerInterface::class),
            false,
        );

        $webhookPayload = $this->getWebhookPayload();
        $message = $this->getMessage($webhookPayload, $webhookPayloadSerializer);

        $webhookClient->send($message);

        $this->assertSame(1, $httpClient->getRequestsCount());
        $this->assertSame(Request::METHOD_POST, $mockResponse->getRequestMethod());
        $this->assertSame('https://clang.test/api/'.$this->getWebhookUrl(), $mockResponse->getRequestUrl());
        $authorizationHeader = $mockResponse->getRequestOptions()['headers'][0];
        $this->assertSame('Authorization: Bearer '.$this->getBearerToken(), $authorizationHeader);

        $decodedResponse = json_decode($mockResponse->getRequestOptions()['body'], true, 512, JSON_THROW_ON_ERROR);

        // Workaround for auto generated marketing subscription uuid.
        $marketingSubscriptionUuid = $decodedResponse['customer']['marketingSubscriptionUuid'] ?? null;
        if (!is_null($marketingSubscriptionUuid)) {
            $decodedResponse['customer']['marketingSubscriptionUuid'] = 'test-uuid';
        }

        $this->assertEquals(
            $this->expectedData(),
            $decodedResponse
        );
    }

    abstract protected function getWebhookPayload(): WebhookPayloadInterface;

    abstract protected function getMessage(
        WebhookPayloadInterface $webhookPayload,
        OpenApiWebhookPayloadSerializer $webhookPayloadSerializer,
    ): WebhookMessageInterface;

    abstract protected function expectedData(): array;

    abstract protected function getWebhookUrl(): string;

    abstract protected function getBusinessUnitCode(): string;

    abstract protected function getBearerToken(): string;

    abstract protected function getEventName(): string;
}
