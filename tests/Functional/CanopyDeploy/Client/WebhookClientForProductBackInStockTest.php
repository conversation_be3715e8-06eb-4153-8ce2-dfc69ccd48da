<?php

declare(strict_types=1);

namespace App\Tests\Functional\CanopyDeploy\Client;

use App\CanopyDeploy\Message\ProductBackInStock;
use App\CanopyDeploy\Message\WebhookMessageInterface;
use App\CanopyDeploy\Payload\ProductAsWebhookPayload;
use App\Entity\Channel\Channel;
use App\Entity\Product\Product;
use App\Entity\Product\ProductType;
use App\Event\Enum\ProductUpdateEventName;
use App\Repository\ChannelRepositoryInterface;
use App\Serializer\Webhook\OpenApiWebhookPayloadSerializer;
use App\Serializer\Webhook\WebhookPayloadInterface;
use App\Tests\Util\PersistedFactory\ChannelPricingFactory;
use App\Tests\Util\PersistedFactory\ProductFactory;
use App\Tests\Util\PersistedFactory\ProductVariantFactory;
use Doctrine\ORM\EntityManagerInterface;
use Sylius\Component\Product\Generator\SlugGeneratorInterface;

final class WebhookClientForProductBackInStockTest extends AbstractWebhookClientTest
{
    private EntityManagerInterface $entityManager;
    private ProductFactory $productFactory;
    private ChannelPricingFactory $channelPricingFactory;
    private ProductVariantFactory $productVariantFactory;
    private ChannelRepositoryInterface $channelRepository;

    protected function setUp(): void
    {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        /** @var ChannelRepositoryInterface $channelRepository */
        $channelRepository = $this->entityManager->getRepository(Channel::class);
        $this->channelRepository = $channelRepository;

        $slugGenerator = self::getContainer()->get(SlugGeneratorInterface::class);
        self::assertInstanceOf(SlugGeneratorInterface::class, $slugGenerator);

        $this->channelPricingFactory = new ChannelPricingFactory($this->entityManager);
        $this->productVariantFactory = new ProductVariantFactory($this->entityManager);
        $this->productFactory = new ProductFactory($this->entityManager, $this->productVariantFactory, $slugGenerator);
    }

    protected function getWebhookPayload(): WebhookPayloadInterface
    {
        return new ProductAsWebhookPayload(
            $this->getEventName(),
            $this->createProduct(),
        );
    }

    protected function getMessage(
        WebhookPayloadInterface $webhookPayload,
        OpenApiWebhookPayloadSerializer $webhookPayloadSerializer,
    ): WebhookMessageInterface {
        return new ProductBackInStock(
            $this->getBusinessUnitCode(),
            $webhookPayloadSerializer->serialize($webhookPayload),
        );
    }

    /**
     * @return array<string, mixed>
     */
    protected function expectedData(): array
    {
        return [
            'eventName' => 'ProductBackInStock',
            'product' => [
                'code' => '7',
                'nameByLocale' => [
                    'en' => 'Viagra',
                ],
                'inStockByChannel' => [
                    'dok_nl' => false,
                    'dok_gb' => true,
                    'dok_at' => false,
                    'dok_be' => false,
                    'dok_ch' => false,
                    'dok_de' => false,
                    'dok_dk' => false,
                    'dok_fi' => false,
                    'dok_fr' => false,
                    'dok_lt' => false,
                    'dok_lu' => false,
                    'dok_pl' => false,
                    'dok_pt' => false,
                    'dok_se' => false,
                    'dok_ro' => false,
                    'blueclinic_nl' => false,
                ],
            ],
        ];
    }

    protected function getWebhookUrl(): string
    {
        return 'endpoint_for_product_back_in_stock_webhook';
    }

    protected function getBusinessUnitCode(): string
    {
        return 'dokteronline';
    }

    protected function getBearerToken(): string
    {
        return 'dokteronlineProductBackInStockToken';
    }

    protected function getEventName(): string
    {
        return ProductUpdateEventName::ProductBackInStock->name;
    }

    private function createProduct(): Product
    {
        // Arrange
        /** @var Channel $channelGb */
        $channelGb = $this->channelRepository->findOneByCode('dok_gb');
        /** @var Channel $channelNl */
        $channelNl = $this->channelRepository->findOneByCode('dok_nl');

        $product = $this->productFactory->create('7', 'Viagra', ProductType::MEDICATION);
        $product->addChannel($channelGb);
        $product->addChannel($channelNl);

        $productVariant = $this->productVariantFactory->create($product, '7_5', 'Viagra 25mg 4 tabl.');

        $this->channelPricingFactory->create($productVariant, $channelGb->getCode(), 100);
        $this->channelPricingFactory->create($productVariant, $channelNl->getCode(), 100, false);

        $this->entityManager->flush();

        return $product;
    }
}
