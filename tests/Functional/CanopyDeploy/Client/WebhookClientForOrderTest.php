<?php

declare(strict_types=1);

namespace App\Tests\Functional\CanopyDeploy\Client;

use App\CanopyDeploy\Message\CreateOrUpdateCartOrOrder;
use App\CanopyDeploy\Message\WebhookMessageInterface;
use App\CanopyDeploy\Payload\OrderAsWebhookPayload;
use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Channel\Channel;
use App\Entity\Order\Cancellation;
use App\Entity\Order\Enum\CancellationBy;
use App\Entity\Payment\Payment;
use App\Entity\Payment\PaymentMethod;
use App\Entity\Product\Product;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariant;
use App\Entity\Supplier\Supplier;
use App\Event\Enum\OrderEventName;
use App\Serializer\Webhook\OpenApiWebhookPayloadSerializer;
use App\Serializer\Webhook\WebhookPayloadInterface;
use App\Tests\Mocks\Entity\TestAddress;
use App\Tests\Mocks\Entity\TestOrder;
use App\Tests\Mocks\Entity\TestOrderItem;
use App\Tests\Util\Factory\ProductFactory;
use DateTime;

class WebhookClientForOrderTest extends AbstractWebhookClientTest
{
    protected function getWebhookPayload(): WebhookPayloadInterface
    {
        return new OrderAsWebhookPayload($this->getEventName(), $this->createTestOrder());
    }

    /**
     * @param OrderAsWebhookPayload $webhookPayload
     */
    protected function getMessage(
        WebhookPayloadInterface $webhookPayload,
        OpenApiWebhookPayloadSerializer $webhookPayloadSerializer,
    ): WebhookMessageInterface {
        return new CreateOrUpdateCartOrOrder(
            $webhookPayload->order,
            $webhookPayloadSerializer->serialize($webhookPayload)
        );
    }

    protected function expectedData(): array
    {
        return [
            'eventName' => $this->getEventName(),
            'order' => [
                'id' => 8,
                'number' => '0000001',
                'channel' => [
                    'code' => 'dok_nl',
                    'addPrescriptionMedicationDirectlyToCart' => true,
                    'allowMultipleConsultsInCart' => true,
                    'pickupPointsAllowed' => false,
                ],
                'localeCode' => 'en',
                'state' => 'new',
                'checkoutState' => 'completed',
                'paymentState' => 'awaiting_payment',
                'prescriptionState' => 'awaiting_payment',
                'shippingState' => 'awaiting_payment',
                'items' => [
                    [
                        'id' => 2,
                        'variant' => [
                            'code' => 'variant-code',
                            'supplier' => [
                                'name' => 'Apotheek Bad Nieuweschans',
                            ],
                            'maximumQuantityPerOrder' => 1,
                            'prescriptionRequired' => false,
                            'product' => [
                                'code' => 'product-code',
                                'attributes' => [
                                    'type' => 'medication',
                                ],
                            ],
                            'productAttributes' => [
                                'type' => 'medication',
                            ],
                        ],
                        'productName' => 'Paracetamol / Codeine',
                        'variantName' => 'Paracetamol / Codeine 500/20 mg 30 tabletten',
                        'quantity' => 0,
                        'total' => 0,
                        'subtotal' => 0,
                        'unitPrice' => 0,
                    ],
                ],
                'shippingAddress' => [
                    'firstName' => 'Sjaak',
                    'lastName' => 'Afhaak',
                    'company' => '',
                    'street' => 'Krinkelwinkel 6',
                    'postcode' => '4202 LM',
                    'city' => 'Gorinchem',
                    'countryCode' => 'NL',
                    'id' => 5,
                    'fullName' => 'Sjaak Afhaak',
                    'createdAt' => '2022-11-11T11:11:11+00:00',
                ],
                'paymentMethodName' => 'iDeal',
                'cancelledBy' => 'doctor',
                'doctorName' => 'Dr. Test',
                'billingAddress' => [
                    'firstName' => 'Sjaak',
                    'lastName' => 'Afhaak',
                    'company' => '',
                    'street' => 'Krinkelwinkel 6',
                    'postcode' => '4202 LM',
                    'city' => 'Gorinchem',
                    'countryCode' => 'NL',
                    'id' => 5,
                    'fullName' => 'Sjaak Afhaak',
                    'createdAt' => '2022-11-11T11:11:11+00:00',
                ],
                'shipments' => [],
                'currencyCode' => 'EUR',
                'itemsTotal' => 0,
                'total' => 0,
                'createdAt' => '2022-11-11T11:11:11+00:00',
                'taxTotal' => 0,
                'shippingTotal' => 0,
                'subtotal' => 0,
                'orderPromotionTotal' => 0,
                'aftercareState' => 'cart',
                'paymentMethod' => [
                    'name' => 'iDeal',
                    'code' => 'ideal_dokteronline',
                ],
            ],
        ];
    }

    protected function getWebhookUrl(): string
    {
        return 'endpointForOrderWebhook';
    }

    protected function getBusinessUnitCode(): string
    {
        return 'dokteronline';
    }

    protected function getBearerToken(): string
    {
        return 'dokteronlineOrderToken';
    }

    protected function getEventName(): string
    {
        return OrderEventName::OrderWasCancelled->name;
    }

    private function createTestOrder(): TestOrder
    {
        $businessUnit = new BusinessUnit();
        $businessUnit->setCode('dokteronline');
        $businessUnit->setCanopyDeployOrderWebhookUrl('endpointForOrderWebhook');

        $channel = new Channel();
        $channel->setCode('dok_nl');
        $channel->setAddPrescriptionMedicationDirectlyToCart(true);
        $channel->setBusinessUnit($businessUnit);

        $paymentMethod = new PaymentMethod();
        $paymentMethod->setFallbackLocale('en');
        $paymentMethod->setCurrentLocale('en');
        $paymentMethod->setName('iDeal');
        $paymentMethod->setCode('ideal_dokteronline');

        $payment = new Payment();
        $payment->setState('completed');
        $payment->setMethod($paymentMethod);

        $order = new TestOrder();
        $order->setId(8);
        $order->setNumber('DO0000001');
        $order->setChannel($channel);
        $order->setLocaleCode('en');
        $order->setState('new');
        $order->setCheckoutState('new');
        $order->setCheckoutState('completed');
        $order->setPaymentState('awaiting_payment');
        $order->setPrescriptionState('awaiting_payment');
        $order->setShippingState('awaiting_payment');
        $order->addPayment($payment);
        $order->setDoctorName('Dr. Test');
        $order->setCancellation(new Cancellation(CancellationBy::DOCTOR, 'Wrong consult.'));
        $order->setCreatedAt(new DateTime('2022-11-11 11:11:11'));
        $order->setCurrencyCode('EUR');

        $supplier = new Supplier();
        $supplier->setName('Apotheek Bad Nieuweschans');

        $product = new Product();
        $product->setCode('product-code');

        ProductFactory::setType($product, ProductType::MEDICATION->value);

        $variant = new ProductVariant();
        $variant->setCode('variant-code');
        $variant->setSupplier($supplier);
        $variant->setProduct($product);
        $variant->setCurrentLocale('en');

        $item = new TestOrderItem();
        $item->setId(2);
        $item->setProductName('Paracetamol / Codeine');
        $item->setVariantName('Paracetamol / Codeine 500/20 mg 30 tabletten');
        $item->setVariant($variant);

        $order->addItem($item);

        $address = new TestAddress();
        $address->setId(5);
        $address->setFirstName('Sjaak');
        $address->setLastName('Afhaak');
        $address->setCompany('');
        $address->setStreet('Krinkelwinkel 6');
        $address->setPostcode('4202 LM');
        $address->setCity('Gorinchem');
        $address->setCountryCode('NL');
        $address->setCreatedAt(new DateTime('2022-11-11 11:11:11'));

        $order->setShippingAddress($address);
        $order->setBillingAddress($address);

        return $order;
    }
}
