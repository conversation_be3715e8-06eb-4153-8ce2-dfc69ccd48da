<?php

declare(strict_types=1);

namespace App\Tests\Functional\CanopyDeploy\Client;

use App\CanopyDeploy\Message\CustomerPasswordResetRequested;
use App\CanopyDeploy\Message\WebhookMessageInterface;
use App\CanopyDeploy\Payload\CustomerAsPasswordResetPayload;
use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Channel\Channel;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\MarketingSubscription\MarketingSubscription;
use App\Serializer\Webhook\OpenApiWebhookPayloadSerializer;
use App\Serializer\Webhook\WebhookPayloadInterface;
use App\Tests\Mocks\Entity\TestCustomer;
use App\Tests\Mocks\Entity\TestShopUser;

class WebhookClientForPasswordResetDataTest extends AbstractWebhookClientTest
{
    protected function getWebhookPayload(): WebhookPayloadInterface
    {
        return new CustomerAsPasswordResetPayload($this->getEventName(), $this->createTestCustomer(), $this->getChannel());
    }

    /**
     * @param CustomerAsPasswordResetPayload $webhookPayload
     */
    protected function getMessage(
        WebhookPayloadInterface $webhookPayload,
        OpenApiWebhookPayloadSerializer $webhookPayloadSerializer,
    ): WebhookMessageInterface {
        return new CustomerPasswordResetRequested(
            $webhookPayload->channel,
            $webhookPayloadSerializer->serialize($webhookPayload),
        );
    }

    protected function expectedData(): array
    {
        return [
            'eventName' => $this->getEventName(),
            'customer' => [
                'firstName' => 'Hacker',
                'lastName' => 'Man',
                'email' => '<EMAIL>',
                'passwordResetToken' => 'test-password-reset-token',
            ],
            'localization' => [
                'localeCode' => 'nl',
                'countryCode' => 'NL',
            ],
        ];
    }

    protected function getWebhookUrl(): string
    {
        return 'endpointForPasswordResetWebhook';
    }

    protected function getBusinessUnitCode(): string
    {
        return 'blueclinic';
    }

    protected function getBearerToken(): string
    {
        return 'blueclinicPasswordResetToken';
    }

    protected function getEventName(): string
    {
        return CustomerPasswordResetRequested::EVENT_CUSTOMER_PASSWORD_REQUESTED;
    }

    private function createTestCustomer(): Customer
    {
        $email = '<EMAIL>';

        $channel = $this->getChannel();
        $customerPool = new CustomerPool();
        $customerPool->setCode('blueclinic');
        $customerPool->addChannel($channel);

        $customer = new TestCustomer();

        $user = new TestShopUser();
        $user->setCustomer($customer);
        $user->setPasswordResetToken('test-password-reset-token');
        $user->setEmail($email);

        $customer->setId(1);
        $customer->setFirstName('Hacker');
        $customer->setLastName('Man');
        $customer->setEmail($email);
        $customer->setUser($user);
        $customer->setCustomerPool($customerPool);

        /** @var BusinessUnit $businessUnit */
        $businessUnit = $channel->getBusinessUnit();
        $marketingSubscription = new MarketingSubscription(
            $email,
            'nl',
            'NL',
            $businessUnit
        );
        $customer->setMarketingSubscription($marketingSubscription);

        return $customer;
    }

    private function getChannel(): Channel
    {
        $businessUnit = new BusinessUnit();
        $businessUnit->setCanopyDeployPasswordResetWebhookUrl($this->getWebhookUrl());
        $businessUnit->setCode('blueclinic');

        $channel = new Channel();
        $channel->setCode('blueclinic_nl');
        $channel->setBusinessUnit($businessUnit);

        return $channel;
    }
}
