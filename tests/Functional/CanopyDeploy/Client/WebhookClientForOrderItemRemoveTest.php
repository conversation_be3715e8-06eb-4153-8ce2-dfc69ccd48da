<?php

declare(strict_types=1);

namespace App\Tests\Functional\CanopyDeploy\Client;

use App\CanopyDeploy\Message\RemoveItemFromOrder;
use App\CanopyDeploy\Message\WebhookMessageInterface;
use App\CanopyDeploy\Payload\OrderItemAsRemoveWebhookPayload;
use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Channel\Channel;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Serializer\Webhook\OpenApiWebhookPayloadSerializer;
use App\Serializer\Webhook\WebhookPayloadInterface;
use App\Tests\Mocks\Entity\TestOrderItem;

class WebhookClientForOrderItemRemoveTest extends AbstractWebhookClientTest
{
    protected function getWebhookPayload(): WebhookPayloadInterface
    {
        return new OrderItemAsRemoveWebhookPayload($this->getEventName(), $this->createTestOrderItem());
    }

    /**
     * @param OrderItemAsRemoveWebhookPayload $webhookPayload
     */
    protected function getMessage(
        WebhookPayloadInterface $webhookPayload,
        OpenApiWebhookPayloadSerializer $webhookPayloadSerializer,
    ): WebhookMessageInterface {
        /** @var Order $order */
        $order = $webhookPayload->orderItem->getOrder();

        return new RemoveItemFromOrder(
            $order,
            $webhookPayloadSerializer->serialize($webhookPayload),
        );
    }

    protected function expectedData(): array
    {
        return [
            'eventName' => $this->getEventName(),
            'orderItem' => [
                'id' => '15',
            ],
        ];
    }

    protected function getWebhookUrl(): string
    {
        return 'endpointForOrderItemRemoveWebhook';
    }

    protected function getBusinessUnitCode(): string
    {
        return 'blueclinic';
    }

    protected function getBearerToken(): string
    {
        return 'blueclinicRemoveOrderItemToken';
    }

    protected function getEventName(): string
    {
        return RemoveItemFromOrder::EVENT_ORDER;
    }

    private function createTestOrderItem(): OrderItem
    {
        $businessUnit = new BusinessUnit();
        $businessUnit->setCanopyDeployRemoveOrderItemWebhookUrl($this->getWebhookUrl());
        $businessUnit->setCode('blueclinic');

        $channel = new Channel();
        $channel->setBusinessUnit($businessUnit);

        $customerPool = new CustomerPool();
        $customerPool->setCode('blueclinic');
        $customerPool->addChannel($channel);

        $customer = new Customer();
        $customer->setCustomerPool($customerPool);

        $order = new Order();
        $order->setCustomer($customer);
        $order->setChannel($channel);

        $orderItem = new TestOrderItem();
        $orderItem->setId(15);

        $order->addItem($orderItem);

        return $orderItem;
    }
}
