<?php

declare(strict_types=1);

namespace App\Tests\Functional\Entity\Order;

use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Order\OrderItemUnit;
use App\Entity\Order\OriginalOrderItem;
use App\Entity\Order\OriginalPreferredOrderItem;
use App\Entity\Order\PreferredOrderItem;
use App\Entity\Product\Product;
use App\Entity\Product\ProductInterface;
use App\Entity\Product\ProductVariant;
use App\Entity\Product\ProductVariantInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

final class OriginalOrderItemTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;

    public function setUp(): void
    {
        /** @phpstan-ignore-next-line */
        $this->entityManager = self::getContainer()->get(EntityManagerInterface::class);
    }

    public function testCanPersistFromOrderItemWithoutParent(): void
    {
        $product = $this->createMedicationProduct();
        $productVariant = $this->createMedicationProductVariant($product);
        $orderItem = $this->createChildOrderItem($productVariant);
        $order = $this->createOrder($orderItem);

        $originalOrderItem = OriginalOrderItem::fromOrderItem($orderItem);

        $this->persist(
            $order,
            $orderItem,
            $originalOrderItem,
            $product,
            $productVariant,
        );

        $originalOrderItem = $this->entityManager
            ->getRepository(OriginalOrderItem::class)
            ->findOneBy(['order' => $order]);

        self::assertInstanceOf(OriginalOrderItem::class, $originalOrderItem);
        self::assertIsInt($originalOrderItem->getId());
        self::assertSame($order, $originalOrderItem->getOrder());
        self::assertSame($product, $originalOrderItem->getProduct());
        self::assertSame($productVariant, $originalOrderItem->getVariant());
        self::assertSame('viagra', $originalOrderItem->getProductName());
        self::assertSame('Viagra 100mg 10 caps.', $originalOrderItem->getVariantName());
        self::assertEmpty($originalOrderItem->getOriginalPreferredItems());
        self::assertSame(1, $originalOrderItem->getQuantity());
        self::assertSame(0, $originalOrderItem->getUnitPrice());
        self::assertSame(0, $originalOrderItem->getUnitsTotal());
        self::assertSame(0, $originalOrderItem->getAdjustmentsTotal());
        self::assertSame(0, $originalOrderItem->getTotal());
        self::assertSame($orderItem, $originalOrderItem->getOrderItem());
        self::assertNull($originalOrderItem->getParent());
        self::assertNull($originalOrderItem->getParentOrderItem());
    }

    public function testCanPersistFromOrderItemWithParent(): void
    {
        $consultProduct = $this->createConsultProduct();
        $consultVariant = $this->createConsultVariant($consultProduct);
        $product = $this->createMedicationProduct();
        $productVariant = $this->createMedicationProductVariant($product);
        $parentItem = $this->createParentItem($consultVariant);

        $parentItem->addPreferredItem(new PreferredOrderItem($parentItem, $productVariant));

        $childItem = $this->createChildItem($productVariant, $parentItem);

        $order = new Order();
        $order->addItem($childItem);
        $order->addItem($parentItem);
        $order->setCurrencyCode('EUR');
        $order->setLocaleCode('en');
        $order->setTokenValue('1337');

        $originalParentItem = OriginalOrderItem::fromOrderItem($parentItem);
        $originalChildItem = OriginalOrderItem::fromOrderItem($childItem, $originalParentItem);

        $this->persist(
            $childItem,
            $consultProduct,
            $consultVariant,
            $order,
            $originalChildItem,
            $originalParentItem,
            $parentItem,
            $product,
            $productVariant,
        );

        $originalChildItem = $this->entityManager
            ->getRepository(OriginalOrderItem::class)
            ->findOneBy(['order' => $order, 'variant' => $productVariant]);

        self::assertInstanceOf(OriginalOrderItem::class, $originalChildItem);
        self::assertIsInt($originalChildItem->getId());
        self::assertSame($order, $originalChildItem->getOrder());
        self::assertSame($product, $originalChildItem->getProduct());
        self::assertSame($productVariant, $originalChildItem->getVariant());
        self::assertSame('Viagra', $originalChildItem->getProductName());
        self::assertSame('Viagra 100mg 10 caps.', $originalChildItem->getVariantName());
        self::assertCount(1, $originalParentItem->getOriginalPreferredItems());
        self::assertContainsOnlyInstancesOf(OriginalPreferredOrderItem::class, $originalParentItem->getOriginalPreferredItems());
        self::assertSame(1, $originalChildItem->getQuantity());
        self::assertSame(0, $originalChildItem->getUnitPrice());
        self::assertSame(0, $originalChildItem->getUnitsTotal());
        self::assertSame(0, $originalChildItem->getAdjustmentsTotal());
        self::assertSame(0, $originalChildItem->getTotal());
        self::assertSame($childItem, $originalChildItem->getOrderItem());
        self::assertSame($originalParentItem, $originalChildItem->getParent());
        self::assertSame($parentItem, $originalChildItem->getParentOrderItem());
    }

    public function testWhenOrderItemsAreRemovedFromOrderOriginalOrderItemsRelationIsRemoved(): void
    {
        $consultProduct = $this->createConsultProduct();
        $consultVariant = $this->createConsultVariant($consultProduct);

        $product = $this->createMedicationProduct();
        $productVariant = $this->createMedicationProductVariant($product);
        $parentItem = $this->createParentItem($consultVariant);

        $childItem = $this->createChildItem($productVariant, $parentItem);

        $order = new Order();
        $order->addItem($childItem);
        $order->addItem($parentItem);
        $order->setCurrencyCode('EUR');
        $order->setLocaleCode('en');
        $order->setTokenValue('1337');

        $originalParentItem = OriginalOrderItem::fromOrderItem($parentItem);
        $originalChildItem = OriginalOrderItem::fromOrderItem($childItem, $originalParentItem);

        $this->persist(
            $childItem,
            $consultProduct,
            $consultVariant,
            $order,
            $originalChildItem,
            $originalParentItem,
            $parentItem,
            $product,
            $productVariant,
        );

        /** @var OriginalOrderItem[] $originalOrderItemsBeforeOrderItemRemoval */
        $originalOrderItemsBeforeOrderItemRemoval = $this->entityManager
            ->getRepository(OriginalOrderItem::class)
            ->findBy(['order' => $order]);

        foreach ($order->getItems() as $item) {
            $order->removeItem($item);
        }

        $this->entityManager->flush();
        $this->entityManager->clear();

        /** @var OriginalOrderItem[] $originalOrderItemsAfterOrderItemRemoval */
        $originalOrderItemsAfterOrderItemRemoval = $this->entityManager
            ->getRepository(OriginalOrderItem::class)
            ->findBy(['order' => $order]);

        self::assertCount(2, $originalOrderItemsBeforeOrderItemRemoval);
        self::assertCount(2, $originalOrderItemsAfterOrderItemRemoval);

        foreach ($originalOrderItemsBeforeOrderItemRemoval as $originalOrderItemBeforeOrderItemRemoval) {
            self::assertInstanceOf(OrderItem::class, $originalOrderItemBeforeOrderItemRemoval->getOrderItem());
        }

        foreach ($originalOrderItemsAfterOrderItemRemoval as $originalOrderItemAfterOrderItemRemoval) {
            self::assertNull($originalOrderItemAfterOrderItemRemoval->getOrderItem());
            self::assertNull($originalOrderItemAfterOrderItemRemoval->getParentOrderItem());
        }
    }

    public function testWhenPreferredOrderItemsAreRemovedFromOrderOriginalPreferredOrderItemsRelationIsRemoved(): void
    {
        $consultProduct = $this->createConsultProduct();
        $consultVariant = $this->createConsultVariant($consultProduct);

        $product = $this->createMedicationProduct();
        $productVariant = $this->createMedicationProductVariant($product);
        $parentItem = $this->createParentItem($consultVariant);

        $preferredItem = new PreferredOrderItem($parentItem, $productVariant);
        $parentItem->addPreferredItem($preferredItem);

        $order = new Order();
        $order->addItem($parentItem);
        $order->setCurrencyCode('EUR');
        $order->setLocaleCode('en');
        $order->setTokenValue('1337');

        $originalParentItem = OriginalOrderItem::fromOrderItem($parentItem);

        $this->persist(
            $consultProduct,
            $consultVariant,
            $order,
            $originalParentItem,
            $parentItem,
            $preferredItem,
            $product,
            $productVariant,
        );

        /** @var Order $order */
        $order = $this->entityManager
            ->getRepository(Order::class)
            ->find($order->getId());

        foreach ($order->getItems() as $item) {
            $item->getPreferredItems()->clear();
        }

        $this->entityManager->flush();
        $this->entityManager->clear();

        /** @var Order $order */
        $order = $this->entityManager
            ->getRepository(Order::class)
            ->find($order->getId());

        self::assertCount(1, $order->getItems());
        foreach ($order->getItems() as $item) {
            self::assertEmpty($item->getPreferredItems());
        }

        foreach ($order->getOriginalItems() as $originalItem) {
            self::assertCount(1, $originalItem->getOriginalPreferredItems());
            foreach ($originalItem->getOriginalPreferredItems() as $originalPreferredItem) {
                self::assertInstanceOf(OrderItem::class, $originalPreferredItem->getOrderItem());
            }
        }
    }

    private function persist(object ...$objects): void
    {
        foreach ($objects as $object) {
            $this->entityManager->persist($object);
        }

        $this->entityManager->flush();
    }

    private function createMedicationProduct(): ProductInterface
    {
        $product = new Product();
        $product->setCode('viagra');
        $product->setCurrentLocale('en');
        $product->setName('Viagra');
        $product->setSlug('viagra');

        return $product;
    }

    private function createMedicationProductVariant(ProductInterface $product): ProductVariantInterface
    {
        $productVariant = new ProductVariant();
        $productVariant->setCode('1_1_viagra_supplier_worldwide');
        $productVariant->setProduct($product);

        return $productVariant;
    }

    private function createChildOrderItem(ProductVariantInterface $productVariant): OrderItem
    {
        $orderItem = new OrderItem();
        $orderItem->addUnit(new OrderItemUnit($orderItem));
        $orderItem->setProductName('viagra');
        $orderItem->setUsageAdvice('Max of one per day');
        $orderItem->setVariant($productVariant);
        $orderItem->setVariantName('Viagra 100mg 10 caps.');

        return $orderItem;
    }

    private function createOrder(OrderItem $orderItem): Order
    {
        $order = new Order();
        $order->addItem($orderItem);
        $order->setCurrencyCode('EUR');
        $order->setLocaleCode('en');

        return $order;
    }

    private function createConsultProduct(): ProductInterface
    {
        $consultProduct = new Product();
        $consultProduct->setCode('consult_erectile_dysfunction');
        $consultProduct->setCurrentLocale('en');
        $consultProduct->setName('Consult for Erectile Dysfunction');
        $consultProduct->setSlug('consult-for-erectile-dysfunction');

        return $consultProduct;
    }

    private function createConsultVariant(ProductInterface $consultProduct): ProductVariantInterface
    {
        $consultVariant = new ProductVariant();
        $consultVariant->setCode('consult_erectile_dysfunction');
        $consultVariant->setProduct($consultProduct);

        return $consultVariant;
    }

    private function createParentItem(ProductVariantInterface $consultVariant): OrderItem
    {
        $parentItem = new OrderItem();
        $parentItem->addUnit(new OrderItemUnit($parentItem));
        $parentItem->setProductName('consult_erectile_dysfunction');
        $parentItem->setVariant($consultVariant);
        $parentItem->setVariantName('Consult for Erectile Dysfunction');

        return $parentItem;
    }

    private function createChildItem(
        ProductVariantInterface $productVariant,
        OrderItem $parentItem,
    ): OrderItem {
        $childItem = new OrderItem();
        $childItem->addUnit(new OrderItemUnit($childItem));
        $childItem->setProductName('Viagra');
        $childItem->setUsageAdvice('Max one per day.');
        $childItem->setVariant($productVariant);
        $childItem->setVariantName('Viagra 100mg 10 caps.');
        $childItem->setParentOrderItem($parentItem);

        return $childItem;
    }
}
