<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api;

use App\Api\Command\Admin\Order\TransitionPrescriptionState\Approve;
use App\Entity\Order\Order;
use App\Entity\Payment\Payment;
use App\StateMachine\OrderPrescriptionTransitions;
use App\Tests\Mocks\AnamnesisServiceClient;
use Doctrine\ORM\EntityManagerInterface;
use Ramsey\Uuid\Uuid;
use SM\Factory\FactoryInterface;
use Sylius\Component\Payment\PaymentTransitions;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Zenstruck\Messenger\Test\InteractsWithMessenger;

abstract class AbstractWebTestCase extends WebTestCase
{
    use InteractsWithMessenger;

    protected const string PRODUCT_VARIANT_CODE_OTC = '2345_26657_ndsm_apotheek_worldwide';
    protected const string PRODUCT_VARIANT_OTC_SUPPLIER_NAME = 'NDSM apotheek';
    protected const string PRODUCT_VARIANT_OTC_SUPPLIER_CODE = 'ndsm-apotheek';
    protected const string PRODUCT_VARIANT_CODE_MEDICATION = '2351_34176_prime_pharmacy_worldwide';
    protected const string PRODUCT_VARIANT_MEDICATION_SUPPLIER_NAME = 'Prime-Pharmacy';
    protected const string PRODUCT_VARIANT_MEDICATION_SUPPLIER_CODE = 'prime-pharmacy';
    protected const string PRODUCT_VARIANT_CODE_CONSULT = 'consult_high_blood_pressure';
    protected const string PRODUCT_VARIANT_CODE_SERVICE_BLUECLINIC = 'service_blueclinic';
    protected const string PRODUCT_VARIANT_CODE_SERVICE_PRESCRIPTION = 'service_prescription';
    protected const string PRODUCT_VARIANT_CODE_SERVICE_RESHIP = 'service_reship';
    protected const string ACCOUNT_EMAIL = '<EMAIL>';
    protected const string ACCOUNT_PASSWORD = '@superComplexPassword123#';

    protected ?KernelBrowser $client;
    protected ?EntityManagerInterface $entityManager;
    protected ?MessageBusInterface $messageBus;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();
        $this->client = self::createClient();

        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        /** @var MessageBusInterface $messageBus */
        $messageBus = self::getContainer()->get(MessageBusInterface::class);
        $this->messageBus = $messageBus;
    }

    protected function tearDown(): void
    {
        parent::tearDown();

        $this->client = null;
        $this->entityManager->close();
        $this->entityManager = null;
    }

    protected function getResponseBody(): array
    {
        $responseBody = $this->client->getResponse()->getContent();
        self::assertIsString($responseBody);
        self::assertJson($responseBody);

        return json_decode($responseBody, true, 512, JSON_THROW_ON_ERROR);
    }

    protected function getOrderEntityByTokenValue(string $tokenValue): Order
    {
        $order = $this->entityManager->getRepository(Order::class)->findOneBy(['tokenValue' => $tokenValue]);
        self::assertInstanceOf(Order::class, $order);
        $this->entityManager->refresh($order);

        return $order;
    }

    protected function prepareDatabaseWithProducts(array $channelCodes, string $localeCode): void
    {
        $productTestFactory = new ProductTestFactory(
            self::getContainer(),
            $channelCodes,
        );

        $productTestFactory->prepareDatabaseWithRegularAndConsultProducts(
            [
                [
                    'name' => 'Furosemide',
                    'code' => '2351',
                    'variantCode' => self::PRODUCT_VARIANT_CODE_MEDICATION,
                    'variantName' => 'Furosemide 40 mg 90 tabl.',
                    'prescriptionRequired' => true,
                    'supplierName' => self::PRODUCT_VARIANT_MEDICATION_SUPPLIER_NAME,
                    'supplierCode' => self::PRODUCT_VARIANT_MEDICATION_SUPPLIER_CODE,
                ],
                [
                    'name' => 'Candida test',
                    'code' => '2345',
                    'variantCode' => self::PRODUCT_VARIANT_CODE_OTC,
                    'variantName' => 'Test-Point Candida-Test 1 St. (für Frauen)',
                    'prescriptionRequired' => false,
                    'maxQuantity' => 5,
                    'supplierName' => self::PRODUCT_VARIANT_OTC_SUPPLIER_NAME,
                    'supplierCode' => self::PRODUCT_VARIANT_OTC_SUPPLIER_CODE,
                ],
            ],
            [
                [
                    'name' => 'Consult voor Hoge Bloeddruk',
                    'code' => self::PRODUCT_VARIANT_CODE_CONSULT,
                    'prescriptionRequired' => true,
                ],
            ],
            $localeCode,
        );

        $productTestFactory->createConsultProductAssociations('2351',
            [
                self::PRODUCT_VARIANT_CODE_CONSULT,
            ]);

        $productTestFactory->prepareDatabaseWithServiceProducts([
            self::PRODUCT_VARIANT_CODE_SERVICE_BLUECLINIC,
            self::PRODUCT_VARIANT_CODE_SERVICE_PRESCRIPTION,
            self::PRODUCT_VARIANT_CODE_SERVICE_RESHIP,
        ]);
    }

    protected function createCart(string $localeCode, string $channelCode): array
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/carts',
            [
                'localeCode' => $localeCode,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => $channelCode,
            ],
        );

        self::assertResponseIsSuccessful();

        return $this->getResponseBody();
    }

    /**
     * @param array{array{
     *     productVariantCode: string,
     *     quantity: int,
     *     parentProductVariantCode?: string,
     * }} $requestBody
     */
    protected function addItemsToCart(
        string $tokenValue,
        string $channelCode,
        array $requestBody,
        ?string $userToken = null,
    ): array {
        $requestHeaders = [
            'HTTP_SYLIUS_CHANNEL_CODE' => $channelCode,
        ];

        if (!empty($userToken)) {
            $requestHeaders['HTTP_AUTHORIZATION'] = sprintf('Bearer %s', $userToken);
        }

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $tokenValue),
            $requestBody,
            $requestHeaders,
        );

        self::assertResponseIsSuccessful();

        return $this->getResponseBody();
    }

    /**
     * @param array{array{
     *     parentProductVariantCode: string,
     * }} $requestBody
     */
    protected function clearChildItemsFromCart(string $tokenValue, string $channelCode, array $requestBody): array
    {
        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/shop/carts/%s/child-items', $tokenValue),
            $requestBody,
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => $channelCode,
            ],
        );

        self::assertResponseIsSuccessful();

        return $this->getResponseBody();
    }

    protected function deleteItemFromCart(string $tokenValue, string $channelCode, int $itemId): array
    {
        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/shop/carts/%s/items/%d', $tokenValue, $itemId),
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => $channelCode,
            ],
        );

        self::assertResponseIsSuccessful();

        return $this->getResponseBody();
    }

    protected function registerAccount(
        string $channelCode,
        string $email = self::ACCOUNT_EMAIL,
        string $password = self::ACCOUNT_PASSWORD,
    ): void {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/account/register',
            [
                'email' => $email,
                'firstName' => 'Test firstname',
                'lastName' => 'Test lastname',
                'gender' => 'm',
                'birthday' => '1980-01-01',
                'phoneNumber' => '+31 123 456 789',
                'subscribedToNewsletter' => false,
                'password' => $password,
                'confirmNewPassword' => $password,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => $channelCode,
            ],
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
    }

    protected function loginAccount(
        string $channelCode,
        string $email = self::ACCOUNT_EMAIL,
        string $password = self::ACCOUNT_PASSWORD,
    ): array {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/account/authenticate',
            [
                'email' => $email,
                'password' => $password,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => $channelCode,
            ],
        );

        self::assertResponseIsSuccessful();

        return $this->getResponseBody();
    }

    protected function addMedicalQuestionnaire(string $tokenValue, ?string $uuid = null): array
    {
        if ($uuid === null) {
            $uuid = AnamnesisServiceClient::EXISTING_UUID;
        }

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/checkout/%s/questionnaire', $tokenValue),
            [
                'uuid' => $uuid,
            ],
        );

        self::assertResponseIsSuccessful();

        return $this->getResponseBody();
    }

    protected function completeMedicalQuestionnaire(string $tokenValue, ?string $uuid = null): array
    {
        if ($uuid === null) {
            $uuid = AnamnesisServiceClient::VALID_UUID;
        }

        $order = $this->entityManager->getRepository(Order::class)->findOneBy(['tokenValue' => $tokenValue]);
        self::assertInstanceOf(Order::class, $order);

        $order->setMedicalQuestionnaire(Uuid::fromString($uuid));

        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/checkout/%s/questionnaire/complete', $tokenValue),
        );

        self::assertResponseIsSuccessful();

        return $this->getResponseBody();
    }

    protected function setAddress(
        string $tokenValue,
        string $countryCode,
        string $channelCode,
        string $userToken,
    ): array {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/checkout/%s/address', $tokenValue),
            [
                'billingAddress' => [
                    'firstName' => 'Test firstname',
                    'lastName' => 'Test lastname',
                    'phoneNumber' => '**********',
                    'street' => 'Diemerkade 1',
                    'postcode' => '1111 AA',
                    'city' => 'Diemen',
                    'provinceName' => 'Noord-Holland',
                    'countryCode' => $countryCode,
                ],
                'shippingAddress' => [
                    'firstName' => 'Test firstname',
                    'lastName' => 'Test lastname',
                    'phoneNumber' => '**********',
                    'street' => Uuid::uuid4()->toString().' 1', // randomize street name, so we skip the fraud check.
                    'postcode' => '1111 AA',
                    'city' => 'Diemen',
                    'provinceName' => 'Noord-Holland',
                    'countryCode' => $countryCode,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => $channelCode,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $userToken),
            ],
        );

        self::assertResponseIsSuccessful();

        return $this->getResponseBody();
    }

    protected function setPaymentMethod(
        string $tokenValue,
        int|string $paymentId,
        string $paymentMethod,
        string $channelCode,
        string $userToken,
    ): array {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/checkout/%s/payments/%s', $tokenValue, $paymentId),
            [
                'paymentMethod' => $paymentMethod,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => $channelCode,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $userToken),
            ],
        );

        self::assertResponseIsSuccessful();

        return $this->getResponseBody();
    }

    /**
     * @param array<string> $termsAnswers
     */
    protected function completeOrder(
        string $tokenValue,
        string $channelCode,
        string $userToken,
        array $termsAnswers,
    ): array {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/checkout/%s/complete', $tokenValue),
            [
                'termsAnswers' => $termsAnswers,
                'notes' => 'Test order!',
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => $channelCode,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $userToken),
            ],
        );

        self::assertResponseIsSuccessful();

        return $this->getResponseBody();
    }

    protected function cancelOrder(string $tokenValue, string $reason, string $userToken): array
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/orders/%s/cancel', $tokenValue),
            [
                'reason' => $reason,
            ],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $userToken),
            ],
        );

        self::assertResponseIsSuccessful();

        return $this->getResponseBody();
    }

    /**
     * Applies a paid state to the payment of an order just like the Sylius admin does with
     * {@see \Sylius\Bundle\ResourceBundle\Controller\ResourceController::applyStateMachineTransitionAction} &
     * {@see \Sylius\Bundle\ResourceBundle\Controller\ResourceUpdateHandler::handle}.
     */
    protected function forcePayOrder(string $tokenValue): void
    {
        $order = $this->entityManager->getRepository(Order::class)->findOneBy(['tokenValue' => $tokenValue]);
        self::assertInstanceOf(Order::class, $order);

        // Since we are not using the API, refresh the order from the database.
        $this->entityManager->refresh($order);

        $stateMachineFactory = self::getContainer()->get(FactoryInterface::class);
        self::assertInstanceOf(FactoryInterface::class, $stateMachineFactory);

        $payment = $order->getLastPayment();
        self::assertInstanceOf(Payment::class, $payment);

        $stateMachine = $stateMachineFactory->get($payment, PaymentTransitions::GRAPH);
        self::assertTrue($stateMachine->can(PaymentTransitions::TRANSITION_COMPLETE));
        $stateMachine->apply(PaymentTransitions::TRANSITION_COMPLETE);

        $this->entityManager->flush();
    }

    protected function markOrderAwaitingMedicationReview(string $tokenValue): void
    {
        $order = $this->entityManager->getRepository(Order::class)->findOneByTokenValue($tokenValue);

        $stateMachineFactory = self::getContainer()->get(FactoryInterface::class);
        $stateMachine = $stateMachineFactory->get($order, OrderPrescriptionTransitions::GRAPH);
        $stateMachine->apply(OrderPrescriptionTransitions::TRANSITION_CHECK);

        $this->entityManager->flush();
    }

    protected function approveMedication(string $tokenValue): void
    {
        $approveCommand = new Approve(
            prescriptionFilename: 'test_prescription.pdf',
            doctorName: 'Test Doctor',
            doctorRegistrationNumber: 'DOC_001',
            doctorUuid: Uuid::fromString('8d7537c9-2c2a-42ec-a38b-228f9b1df9b8'),
        );
        $approveCommand->setOrder($this->getOrderEntityByTokenValue($tokenValue));

        $this->messageBus?->dispatch($approveCommand);
    }

    protected function cancelPayment(
        string $tokenValue,
        int|string $paymentId,
        string $userToken,
    ): array {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/orders/%s/payments/%s/cancel', $tokenValue, $paymentId),
            [],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $userToken),
            ],
        );

        self::assertResponseIsSuccessful();

        return $this->getResponseBody();
    }
}
