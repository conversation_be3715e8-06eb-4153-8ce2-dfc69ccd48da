<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Prepr\Taxon;

use App\Tests\Util\Builder\TaxonBuilder;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;

final class ListControllerTest extends WebTestCase
{
    private const string API_URI = '/api/shop/prepr-integration/taxons';

    private const string DATETIME_YEAR_2022 = '2022-01-01T00:00:00+00:00';
    private const string DATETIME_YEAR_2023 = '2023-01-01T00:00:00+00:00';

    /**
     * @var array<array-key, array{
     *     code: string,
     *     createdAt: string,
     *     updatedAt: string,
     *     translations: array<array-key, array{
     *         name: string,
     *         slug: string,
     *         locale: string,
     *     }>
     * }>
     */
    private const array TAXON_DATA = [
        [
            'code' => 'acne',
            'createdAt' => self::DATETIME_YEAR_2022,
            'updatedAt' => self::DATETIME_YEAR_2022,
            'translations' => [
                [
                    'name' => 'Acne treatment',
                    'slug' => 'acne',
                    'locale' => 'en',
                ],
                [
                    'name' => 'Akne',
                    'slug' => 'acne',
                    'locale' => 'de',
                ],
                [
                    'name' => 'L’acné',
                    'slug' => 'acne',
                    'locale' => 'fr',
                ],
            ],
        ],
        [
            'code' => 'alcohol_addiction',
            'createdAt' => self::DATETIME_YEAR_2022,
            'updatedAt' => self::DATETIME_YEAR_2022,
            'translations' => [
                [
                    'name' => 'Alcohol addiction',
                    'slug' => 'alcohol-addiction',
                    'locale' => 'en',
                ],
                [
                    'name' => 'Alkoholsucht',
                    'slug' => 'alcohol-addiction',
                    'locale' => 'de',
                ],
                [
                    'name' => 'Alcoolisme',
                    'slug' => 'alcohol-addiction',
                    'locale' => 'fr',
                ],
            ],
        ],
        [
            'code' => 'allergy',
            'createdAt' => self::DATETIME_YEAR_2023,
            'updatedAt' => self::DATETIME_YEAR_2023,
            'translations' => [
                [
                    'name' => 'Allergy',
                    'slug' => 'allergy',
                    'locale' => 'en',
                ],
                [
                    'name' => 'Allergie',
                    'slug' => 'allergy',
                    'locale' => 'de',
                ],
                [
                    'name' => 'Allergie',
                    'slug' => 'allergy',
                    'locale' => 'fr',
                ],
            ],
        ],
    ];

    private KernelBrowser $client;

    protected function setUp(): void
    {
        $this->client = self::createClient();

        $this->createTaxon();
    }

    /**
     * @dataProvider provideRequestQueryParameters
     */
    public function testCanList(
        array $query,
        string $acceptLanguage,
        string $expectedResponseJson,
    ): void {
        $requestUri = sprintf('%s?%s', self::API_URI, http_build_query($query));

        $this->client->jsonRequest(
            Request::METHOD_GET,
            $requestUri,
            [],
            ['HTTP_ACCEPT_LANGUAGE' => $acceptLanguage]
        );

        self::assertJsonStringEqualsJsonString(
            $expectedResponseJson,
            (string) $this->client->getResponse()->getContent(),
        );
    }

    /**
     * @return iterable<string, array{
     *     0: array{
     *         "q"?: string,
     *         "skip"?: int,
     *         "limit"?: int,
     *         "changed_since"?: int
     *     },
     *     1: string,
     *     2: string,
     * }>
     */
    public function provideRequestQueryParameters(): iterable
    {
        yield 'no request query parameters' => [
            [],
            'en',
            (string) json_encode([
                'items' => [
                    [
                        'body' => 'Acne treatment',
                        'changed_on' => self::DATETIME_YEAR_2022,
                        'created_on' => self::DATETIME_YEAR_2022,
                        'id' => 'acne',
                    ],
                    [
                        'body' => 'Alcohol addiction',
                        'changed_on' => self::DATETIME_YEAR_2022,
                        'created_on' => self::DATETIME_YEAR_2022,
                        'id' => 'alcohol_addiction',
                    ],
                    [
                        'body' => 'Allergy',
                        'changed_on' => self::DATETIME_YEAR_2023,
                        'created_on' => self::DATETIME_YEAR_2023,
                        'id' => 'allergy',
                    ],
                ],
                'total' => 3,
            ]),
        ];

        yield 'search query parameters' => [
            [
                'q' => 'Acne',
            ],
            'en',
            (string) json_encode([
                'items' => [
                    [
                        'body' => 'Acne treatment',
                        'changed_on' => self::DATETIME_YEAR_2022,
                        'created_on' => self::DATETIME_YEAR_2022,
                        'id' => 'acne',
                    ],
                ],
                'total' => 1,
            ]),
        ];

        yield 'limit query parameters' => [
            [
                'limit' => 1,
            ],
            'en',
            (string) json_encode([
                'items' => [
                    [
                        'body' => 'Acne treatment',
                        'changed_on' => self::DATETIME_YEAR_2022,
                        'created_on' => self::DATETIME_YEAR_2022,
                        'id' => 'acne',
                    ],
                ],
                'total' => 3,
            ]),
        ];

        yield 'skip and limit query parameters' => [
            [
                'skip' => 2,
                'limit' => 1,
            ],
            'en',
            (string) json_encode([
                'items' => [
                    [
                        'body' => 'Allergy',
                        'changed_on' => self::DATETIME_YEAR_2023,
                        'created_on' => self::DATETIME_YEAR_2023,
                        'id' => 'allergy',
                    ],
                ],
                'total' => 3,
            ]),
        ];

        yield 'skip request query parameters' => [
            [
                'skip' => 1,
            ],
            'en',
            (string) json_encode([
                'items' => [
                    [
                        'body' => 'Alcohol addiction',
                        'changed_on' => self::DATETIME_YEAR_2022,
                        'created_on' => self::DATETIME_YEAR_2022,
                        'id' => 'alcohol_addiction',
                    ],
                    [
                        'body' => 'Allergy',
                        'changed_on' => self::DATETIME_YEAR_2023,
                        'created_on' => self::DATETIME_YEAR_2023,
                        'id' => 'allergy',
                    ],
                ],
                'total' => 3,
            ]),
        ];

        yield 'fr accept language' => [
            [],
            'fr',
            (string) json_encode([
                'items' => [
                    [
                        'body' => 'L’acné',
                        'changed_on' => self::DATETIME_YEAR_2022,
                        'created_on' => self::DATETIME_YEAR_2022,
                        'id' => 'acne',
                    ],
                    [
                        'body' => 'Alcoolisme',
                        'changed_on' => self::DATETIME_YEAR_2022,
                        'created_on' => self::DATETIME_YEAR_2022,
                        'id' => 'alcohol_addiction',
                    ],
                    [
                        'body' => 'Allergie',
                        'changed_on' => self::DATETIME_YEAR_2023,
                        'created_on' => self::DATETIME_YEAR_2023,
                        'id' => 'allergy',
                    ],
                ],
                'total' => 3,
            ]),
        ];

        yield 'changed since timestamp from year 2023' => [
            [
                'changed_since' => (int) (new DateTime(self::DATETIME_YEAR_2023))->format('U'),
            ],
            'en',
            (string) json_encode([
                'items' => [
                    [
                        'body' => 'Allergy',
                        'changed_on' => self::DATETIME_YEAR_2023,
                        'created_on' => self::DATETIME_YEAR_2023,
                        'id' => 'allergy',
                    ],
                ],
                'total' => 1,
            ]),
        ];

        yield 'all query parameters' => [
            [
                'q' => 'Allergy',
                'skip' => 0,
                'limit' => 1,
                'changed_since' => (int) (new DateTime(self::DATETIME_YEAR_2023))->format('U'),
            ],
            'en',
            (string) json_encode([
                'items' => [
                    [
                        'body' => 'Allergy',
                        'changed_on' => self::DATETIME_YEAR_2023,
                        'created_on' => self::DATETIME_YEAR_2023,
                        'id' => 'allergy',
                    ],
                ],
                'total' => 1,
            ]),
        ];
    }

    private function createTaxon(): void
    {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);

        foreach (self::TAXON_DATA as $taxonData) {
            $builder = TaxonBuilder::create(
                code: $taxonData['code'],
                createdAt: new DateTime($taxonData['createdAt']),
                updatedAt: new DateTime($taxonData['updatedAt']),
            );

            foreach ($taxonData['translations'] as $translationData) {
                $builder->withTranslation(
                    name: $translationData['name'],
                    slug: $translationData['slug'],
                    locale: $translationData['locale'],
                );
            }

            $entityManager->persist($builder->getTaxon());
        }

        $entityManager->flush();
    }
}
