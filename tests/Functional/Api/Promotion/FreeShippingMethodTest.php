<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Promotion;

use App\Admin\Form\ProductVariantEligiblePromotion;
use App\Entity\Order\Adjustment;
use App\Entity\Order\Order;
use App\Entity\Promotion\PromotionAction;
use App\Entity\Promotion\PromotionRule;
use App\Promotion\Checker\Rule\CartQuantityItemTypeRuleChecker;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ShipmentTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use Doctrine\ORM\EntityManagerInterface;
use Sylius\Component\Core\Model\AdjustmentInterface;
use Sylius\Component\Core\Promotion\Action\ShippingPercentageDiscountPromotionActionCommand;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class FreeShippingMethodTest extends WebTestCase
{
    public const string PRODUCT_CODE_RX = 'viagra_test_special';
    public const string PRODUCT_VARIANT_CODE_RX = 'viagra_25mg_4_apotheek_bad_nieuweschans_worldwide';

    public const string PRODUCT_CODE_OTC = 'libido_test_special';
    public const string PRODUCT_VARIANT_CODE_OTC = 'libido_25mg_4_apotheek_bad_nieuweschans_worldwide';

    private const string  PRODUCT_CONSULT_CODE = 'consult_erectile_dysfunction';

    private const string CHANNEL_CODE = 'dok_nl';

    private KernelBrowser $client;

    private CartTestFactory $cartTestFactory;

    private ShipmentTestFactory $shipmentTestFactory;

    private EntityManagerInterface $entityManager;
    private ShopUserAuthenticationTestHelper $authenticationHelper;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();

        $this->client = static::createClient();

        $this->cartTestFactory = new CartTestFactory(static::getContainer());

        $this->shipmentTestFactory = new ShipmentTestFactory(static::getContainer());
        $this->authenticationHelper = new ShopUserAuthenticationTestHelper($this->client);
        /** @var EntityManagerInterface $entityManagerInterface */
        $entityManagerInterface = static::getContainer()->get('doctrine.orm.entity_manager');
        $this->entityManager = $entityManagerInterface;
    }

    public function testOrderWithRxProduct(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(
            CartTestFactory::CHANNEL_CODE,
            self::PRODUCT_CODE_RX,
            [self::PRODUCT_VARIANT_CODE_RX],
            'apotheek-bad-nieuweschans',
            true,
        );

        $this->cartTestFactory->createConsultForProductVariants([self::PRODUCT_VARIANT_CODE_RX]);

        $order = $this->prepareOrder();

        $this->addProductVariantsToCart($order, self::PRODUCT_VARIANT_CODE_RX, self::PRODUCT_CONSULT_CODE);

        $refreshedOrder = $this->entityManager->getRepository(Order::class)->findOneBy(
            ['tokenValue' => $order->getTokenValue()]
        );
        self::assertSame(0, $refreshedOrder->getShippingTotal());
    }

    public function testOrderWithOTCProduct(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(
            CartTestFactory::CHANNEL_CODE,
            self::PRODUCT_CODE_OTC,
            [self::PRODUCT_VARIANT_CODE_OTC]
        );

        $order = $this->prepareOrder();

        $this->addProductVariantsToCart($order, self::PRODUCT_VARIANT_CODE_OTC);

        $adjustment = $this->findAdjustment($order);
        $refreshedOrder = $this->entityManager->getRepository(Order::class)->findOneBy(
            ['tokenValue' => $order->getTokenValue()]
        );
        self::assertNull($adjustment, 'With no OTC products, no adjustment to shipping costs will be made');
        self::assertSame(ShipmentTestFactory::SHIPPING_COST, $refreshedOrder->getShippingTotal());
    }

    private function createPromotion(): void
    {
        $promotion = $this->cartTestFactory->createPromotion(CartTestFactory::CHANNEL_CODE, 'Shipping cost promotion');
        $action = new PromotionAction();
        $action->setType(ShippingPercentageDiscountPromotionActionCommand::TYPE);
        $action->setConfiguration(['percentage' => 1]);
        $rule = new PromotionRule();
        $rule->setType(CartQuantityItemTypeRuleChecker::TYPE);
        $rule->setConfiguration(['eligibleType' => ProductVariantEligiblePromotion::RX->value, 'count' => 1]);
        $rule->setPromotion($promotion);

        $promotion->addAction($action);
        $promotion->addRule($rule);
    }

    private function findAdjustment(Order $order): ?Adjustment
    {
        return $this->entityManager
            ->getRepository(Adjustment::class)
            ->findOneBy(
                [
                    'order' => $order,
                    'type' => AdjustmentInterface::ORDER_SHIPPING_PROMOTION_ADJUSTMENT,
                ]
            );
    }

    private function prepareOrder(): Order
    {
        $customer = $this->cartTestFactory->createCustomer();
        $this->cartTestFactory->createUserForCustomer($customer);
        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $this->shipmentTestFactory->updateShippingMethod();
        $order = $this->createEmptyOrder();

        $order->setCustomer($customer);
        $order->setBillingAddress($this->cartTestFactory->createAddress());
        $order->setShippingAddress($this->cartTestFactory->createAddress());
        $this->entityManager->persist($order);

        $this->createPromotion();

        $this->entityManager->flush();

        return $order;
    }

    private function createEmptyOrder(): Order
    {
        return $this->cartTestFactory->createCartWithProductVariants(
            CartTestFactory::CHANNEL_CODE,
            CartTestFactory::LOCALE_CODE,
            []
        );
    }

    private function addProductVariantsToCart(
        Order $order,
        string $variantCode,
        ?string $consultCode = null,
    ): void {
        $token = $this->authenticationHelper->authenticate();
        self::assertResponseIsSuccessful();

        $items[] = [
            'productVariantCode' => $variantCode,
            'quantity' => 1,
        ];
        if (is_string($consultCode)) {
            $items = [
                [
                    'productVariantCode' => $consultCode,
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $variantCode,
                    'quantity' => 1,
                    'parentProductVariantCode' => $consultCode,
                ],
            ];
        }

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $order->getTokenValue()),
            $items,
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
    }
}
