<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Channel;

use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class GetChannelControllerTest extends WebTestCase
{
    private KernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
    }

    public function testItCanFetchChannels(): void
    {
        $code = 'dok_gb';
        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/channels/%s', $code),
        );

        $response = json_decode((string) $this->client->getResponse()->getContent(), true);
        $this->assertResponseStatusCodeSame(Response::HTTP_OK);
        $this->assertEquals($code, $response['code']);
    }

    public function testItCannotFetchNonExistingChannels(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/channels/%s', 'metal'),
        );

        $response = json_decode((string) $this->client->getResponse()->getContent(), true);
        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        $this->assertEquals('Channel could not be found.', $response['detail']);
    }
}
