<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Order;

use App\Entity\Addressing\Address;
use App\Entity\Addressing\Zone;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\Order\Order;
use App\Entity\Payment\Payment;
use App\Entity\Payment\PaymentMethod;
use App\Entity\Shipping\ShippingMethod;
use App\Entity\User\ShopUser;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use DateTimeImmutable;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Sylius\Component\Core\Model\ShopUserInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

abstract class BaseFunctionalOrderTestCase extends WebTestCase
{
    protected const string CHANNEL_CODE = 'dok_nl';
    protected const string LOCALE_CODE = 'nl';
    protected const string PRODUCT_CODE = 'viagra_test_special';
    protected const string PRODUCT_VARIANT_CODE = 'viagra_25mg_4_test_special';
    protected const string SHIPPING_METHOD_CODE = 'carrier_code';
    protected const string USER_EMAIL = '<EMAIL>';
    protected const string USER_PASSWORD = 'test';

    protected const int PAYMENT_AMOUNT = 1000;

    private const string IDEAL_CODE_DOKTERONLINE = 'ideal_dokteronline';

    protected KernelBrowser $client;
    protected CartTestFactory $cartTestFactory;
    protected EntityManager $entityManager;
    protected ShopUserAuthenticationTestHelper $authenticationTestFactory;

    public function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
        $this->cartTestFactory = new CartTestFactory(self::getContainer());
        $this->entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->authenticationTestFactory = new ShopUserAuthenticationTestHelper($this->client);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
        $this->entityManager->close();
        unset($this->entityManager);
    }

    protected function createShippingMethod(): ShippingMethod
    {
        $shippingMethod = new ShippingMethod();
        $shippingMethod->setCode(self::SHIPPING_METHOD_CODE);
        $shippingMethod->setCalculator('flat_rate');
        $shippingMethod->setZone(
            $this->entityManager->getRepository(Zone::class)->findOneBy(['code' => 'WORLD'])
        );
        $shippingMethod->enable();
        $shippingMethod->setPosition(99);

        $this->entityManager->persist($shippingMethod);

        return $shippingMethod;
    }

    protected function createOrder(): Order
    {
        $this
            ->cartTestFactory
            ->createProductAndProductVariants(
                self::CHANNEL_CODE,
                self::PRODUCT_CODE,
                [self::PRODUCT_VARIANT_CODE]
            );

        return $this
            ->cartTestFactory
            ->createCart(
                self::CHANNEL_CODE,
                self::LOCALE_CODE
            );
    }

    protected function createOrderWithProductVariants(): Order
    {
        $this
            ->cartTestFactory
            ->createProductAndProductVariants(
                self::CHANNEL_CODE,
                self::PRODUCT_CODE,
                [self::PRODUCT_VARIANT_CODE]
            );

        return $this
            ->cartTestFactory
            ->createCartWithProductVariants(
                self::CHANNEL_CODE,
                self::LOCALE_CODE,
                [self::PRODUCT_VARIANT_CODE]
            );
    }

    protected function createCustomer(
        string $email = self::USER_EMAIL,
        string $password = self::USER_PASSWORD,
    ): Customer {
        /** @var CustomerPool $customerPool */
        $customerPool = $this->entityManager
            ->getRepository(CustomerPool::class)
            ->findOneBy(['code' => 'dokteronline']);

        $customer = new Customer();
        $customer->setBirthday(new DateTimeImmutable('1997-03-09'));
        $customer->setFirstName('Hans');
        $customer->setEmail($email);
        $customer->setCustomerPool($customerPool);
        $customer->setUser($this->createUser($customer, $email, $password));

        foreach ($this->createAddresses() as $address) {
            $customer->addAddress($address);
        }

        return $customer;
    }

    protected function authenticate(string $email, string $password): ?string
    {
        return $this->authenticationTestFactory->authenticate($email, $password);
    }

    protected function getPayment(int $amount = self::PAYMENT_AMOUNT, string $state = Payment::STATE_COMPLETED): Payment
    {
        /** @var PaymentMethod $paymentMethod */
        $paymentMethod = $this->entityManager->getRepository(PaymentMethod::class)->findOneBy(
            ['code' => self::IDEAL_CODE_DOKTERONLINE]
        );

        $payment = new Payment();
        $payment->setMethod($paymentMethod);
        $payment->setCurrencyCode('EUR');
        $payment->setAmount($amount);
        $payment->setState($state);

        return $payment;
    }

    protected function createUser(
        Customer $customer,
        string $email = self::USER_EMAIL,
        string $password = self::USER_PASSWORD,
    ): ShopUserInterface {
        $user = new ShopUser();
        $user->setUsername($email);
        $user->setPlainPassword($password);
        $user->setCustomer($customer);
        $user->enable();
        $this->entityManager->persist($user);

        return $user;
    }

    private function createAddresses(): array
    {
        $address = new Address();
        $address->setFirstName('Hans');
        $address->setLastName('Test');
        $address->setStreet('Test street');
        $address->setPostcode('9876ZY');
        $address->setCity('Breda');
        $address->setCountryCode('NL');

        $secondAddress = new Address();
        $secondAddress->setFirstName('Hans');
        $secondAddress->setLastName('Test');
        $secondAddress->setStreet('Test square');
        $secondAddress->setPostcode('1234AB');
        $secondAddress->setCity('Berlin');
        $secondAddress->setCountryCode('DE');

        return [$address, $secondAddress];
    }
}
