<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Order;

use App\Api\Controller\Order\GetPaymentMethodsController;
use App\Tests\Functional\Api\AbstractGetPaymentMethodsTest;
use Sylius\Component\Order\Model\OrderInterface;

/**
 * Functional test for {@see GetPaymentMethodsController}.
 */
class GetPaymentMethodsTest extends AbstractGetPaymentMethodsTest
{
    protected function getEndpoint(string $tokenValue): string
    {
        return sprintf('/api/shop/orders/%s/payment-methods', $tokenValue);
    }

    protected function getOrderState(): string
    {
        return OrderInterface::STATE_NEW;
    }

    protected function getOrderNotFoundMessage(): string
    {
        return 'Order not found.';
    }

    protected function disallowedOrderStateProvider(): array
    {
        return [
            [OrderInterface::STATE_CART],
            [OrderInterface::STATE_CANCELLED],
        ];
    }
}
