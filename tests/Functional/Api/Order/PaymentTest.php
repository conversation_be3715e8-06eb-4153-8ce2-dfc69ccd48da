<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Order;

use App\Entity\Customer\Customer;
use App\Entity\Payment\Payment;
use App\Entity\User\ShopUser;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Mocks\MockHttpClient;
use App\Tests\Util\GuzzleMockHandlerFactory;
use App\Tests\Util\MockHttpClientHandlerFactory;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\Psr7\Response as GuzzleResponse;
use Sylius\Component\Core\Model\ShopUserInterface;
use Sylius\Component\Core\OrderCheckoutStates;
use Sylius\Component\Order\Model\OrderInterface;
use Sylius\Component\Payment\Model\PaymentInterface;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PaymentTest extends AbstractWebTestCase
{
    private const string CHANNEL_CODE = 'dok_nl';

    private const string LOCALE_CODE = 'nl';

    private const string PRODUCT_CODE = 'viagra_test_special';

    private const string PRODUCT_VARIANT_CODE = 'viagra_25mg_4_test_special';

    private const string USER_EMAIL = '<EMAIL>';

    private const string USER_PASSWORD = 'test';

    private CartTestFactory $cartTestFactory;

    private MockHandler $mockHandler;
    private MockHttpClient $cmHttpClientMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cartTestFactory = new CartTestFactory(self::getContainer());
        $this->mockHandler = self::getContainer()->get(MockHandler::class);

        // Seed persistent mock instances for HTTP-related services.
        // This ensures that Guzzle and Symfony HttpClient mocks retain their state
        // across kernel reboots during functional tests.
        GuzzleMockHandlerFactory::seed(new MockHandler());
        MockHttpClientHandlerFactory::seed(new MockHttpClient());

        /** @var MockHttpClient $cmHttpClient */
        $cmHttpClient = self::getContainer()->get('cm.mock_http_client');
        $this->cmHttpClientMock = $cmHttpClient;
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
    }

    public function testItCanStartAPayment(): void
    {

        $mockResponse = new MockResponse(
            (string) json_encode([
                'order_key' => 'F1499C097FFA533D46FB05D52680AB9A',
                'expires_on' => '2020-01-31T19:00:12Z',
                'url' => 'https://cm.localhost/pay',
            ]),
            [
                'http_code' => 200,
            ],
        );
        $this->cmHttpClientMock->setResponseFactory($mockResponse);

        $this->cartTestFactory->createProductAndProductVariants(self::CHANNEL_CODE, self::PRODUCT_CODE, [self::PRODUCT_VARIANT_CODE]);

        $customer = $this->cartTestFactory->createCustomer();
        $customer->setUser($this->createUser($customer));

        $order = $this->cartTestFactory->createCartWithProductVariants(self::CHANNEL_CODE, self::LOCALE_CODE, [self::PRODUCT_VARIANT_CODE]);
        $order->setCustomer($customer);
        $order->setBillingAddress($this->cartTestFactory->createAddress());
        $order->setCheckoutState(OrderCheckoutStates::STATE_COMPLETED);
        $order->setState(OrderInterface::STATE_NEW);
        $order->setNumber('001');

        /** @var Payment $payment */
        $payment = $order->getLastPayment();
        $payment->setState(PaymentInterface::STATE_NEW);

        $this->entityManager->flush();

        $responseBody = json_encode([
            'uuid' => 'ab101d76-c14e-4e51-971f-c837f1fe30fa',
            'status' => 'new',
            'reference' => '1354368',
            'paymentProfile' => 'cars',
            'store' => 'dokteronline',
            'paymentMethods' => [
                'iDeal',
            ],
            'amount' => [
                'currency' => 'EUR',
                'amount' => ********,
            ],
            'amountPaid' => [
                'currency' => 'EUR',
                'amount' => ********,
            ],
            'amountDue' => [
                'currency' => 'EUR',
                'amount' => ********,
            ],
            'customerPaymentLink' => 'https://dokteronline.payments.ehvg.dev/pay',
            'createdAt' => '2021-10-06T08:25:17.600Z',
            'updatedAt' => '2021-10-06T08:25:17.600Z',
        ]);

        $this->mockHandler->append(
            new GuzzleResponse(201, [], $responseBody),
        );

        $token = $this->loginAccount(self::CHANNEL_CODE, self::USER_EMAIL, self::USER_PASSWORD)['token'];

        $this->client->disableReboot();
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(
                '/api/shop/orders/%s/payment',
                $order->getTokenValue()
            ),
            [],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();

        $responseBody = $this->client->getResponse()->getContent();
        $decodedResponseBody = json_decode($responseBody, true);

        static::assertSame(
            [
                'payment_url' => 'https://cm.localhost/pay',
                'cm_payments' => [
                    'order_key' => 'F1499C097FFA533D46FB05D52680AB9A',
                    'order_reference' => $payment->getId().'-001',
                ],
            ],
            $decodedResponseBody['payments'][0]['details']
        );
    }

    public function testItThrowsExceptionWhenOrderCannotBeFound(): void
    {
        $customer = $this->cartTestFactory->createCustomer();
        $customer->setUser($this->createUser($customer));

        $this->entityManager->flush();

        $token = $this->loginAccount(self::CHANNEL_CODE, self::USER_EMAIL, self::USER_PASSWORD)['token'];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(
                '/api/shop/orders/%s/payment',
                'notExistingToken'
            ),
            [],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $responseBody = $this->client->getResponse()->getContent();

        $errorMessage = [
            'detail' => 'The requested order could not be found.',
            'status' => Response::HTTP_NOT_FOUND,
            'title' => 'An error occurred.',
            'type' => 'about:blank',
        ];

        static::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        static::assertJsonStringEqualsJsonString(json_encode($errorMessage), $responseBody);
    }

    private function createUser(Customer $customer): ShopUserInterface
    {
        $user = new ShopUser();
        $user->setUsername(self::USER_EMAIL);
        $user->setPlainPassword(self::USER_PASSWORD);
        $user->setCustomer($customer);
        $user->enable();
        $this->entityManager->persist($user);

        return $user;
    }
}
