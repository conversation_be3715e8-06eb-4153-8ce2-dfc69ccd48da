<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Order;

use App\Entity\Order\Order;
use App\Prescription\HttpClient\LegacyPrescriptionClient;
use App\StateMachine\OrderAftercareStates;
use App\StateMachine\OrderPaymentStates;
use App\StateMachine\OrderPrescriptionStates;
use App\StateMachine\OrderShippingStates;
use App\Tests\Util\Factory\ProblemDetailsFactory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpClient\MockHttpClient;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class PrescriptionControllerTest extends BaseFunctionalOrderTestCase
{
    public function testCannotFetchPrescriptionWithUnknownTokenValue(): void
    {
        $customer = $this->createCustomer();

        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $entityManager->persist($customer);
        $entityManager->flush();

        $token = $this->authenticate('<EMAIL>', 'test');

        $this->client->jsonRequest(
            Request::METHOD_GET,
            '/api/shop/orders/unknown-token/prescription',
            server: [
                'HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl',
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        self::assertSame(
            ProblemDetailsFactory::NotFound->json(
                'The requested resource could not be found.'
            ),
            $this->client->getResponse()->getContent()
        );
    }

    public function testCannotFetchPrescriptionWhenNotAuthenticated(): void
    {
        $order = $this->createOrder();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/orders/%s/prescription', $order->getTokenValue()),
            ['HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl'],
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
        self::assertSame(
            ProblemDetailsFactory::Unauthorized->json(),
            $this->client->getResponse()->getContent()
        );
    }

    public function testCannotFetchPrescriptionWhenOrderShippingStateInInvalidState(): void
    {
        $customer = $this->createCustomer();
        $order = $this->createOrderWithProductVariants();

        $order->setCustomer($customer);
        $order->setState(Order::STATE_FULFILLED);
        $order->setShippingState(OrderShippingStates::STATE_READY);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);
        $order->setAftercareState(OrderAftercareStates::STATE_OPENED);
        $order->setNumber('007');

        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $entityManager->persist($order);
        $entityManager->persist($customer);
        $entityManager->flush();

        $token = $this->authenticate('<EMAIL>', 'test');

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/orders/%s/prescription', $order->getTokenValue()),
            server: [
                'HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl',
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        self::assertSame(
            ProblemDetailsFactory::NotFound->json(
                'The requested resource could not be found.'
            ),
            $this->client->getResponse()->getContent()
        );
    }

    public function testCannotFetchPrescriptionWhenNotOwnerOfPrescription(): void
    {
        $order = $this->createOrderWithProductVariants();
        $customer = $this->createCustomer();

        $order->setState(Order::STATE_FULFILLED);
        $order->setShippingState(OrderShippingStates::STATE_SHIPPED);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);
        $order->setAftercareState(OrderAftercareStates::STATE_OPENED);
        $order->setNumber('007');

        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $entityManager->persist($order);
        $entityManager->persist($customer);
        $entityManager->flush();

        $token = $this->authenticate('<EMAIL>', 'test');

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/orders/%s/prescription', $order->getTokenValue()),
            server: [
                'HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl',
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
        self::assertSame(
            ProblemDetailsFactory::Forbidden->json(),
            $this->client->getResponse()->getContent()
        );
    }

    public function testCannotFetchPrescriptionWithoutOrderNumber(): void
    {
        $customer = $this->createCustomer();

        $order = $this->createOrderWithProductVariants();
        $order->setCustomer($customer);
        $order->setState(Order::STATE_FULFILLED);
        $order->setShippingState(OrderShippingStates::STATE_SHIPPED);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);
        $order->setAftercareState(OrderAftercareStates::STATE_OPENED);

        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $entityManager->persist($order);
        $entityManager->persist($customer);
        $entityManager->flush();

        $token = $this->authenticate('<EMAIL>', 'test');

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/orders/%s/prescription', $order->getTokenValue()),
            server: [
                'HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl',
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        self::assertSame(
            ProblemDetailsFactory::NotFound
                ->json('The requested resource could not be found.'),
            $this->client->getResponse()->getContent()
        );
    }

    public function testCannotFetchPrescriptionWithoutPrescriptionFileName(): void
    {
        $customer = $this->createCustomer();

        $order = $this->createOrderWithProductVariants();
        $order->setCustomer($customer);
        $order->setState(Order::STATE_FULFILLED);
        $order->setShippingState(OrderShippingStates::STATE_SHIPPED);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);
        $order->setAftercareState(OrderAftercareStates::STATE_OPENED);
        $order->setNumber('007');

        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $entityManager->persist($order);
        $entityManager->persist($customer);
        $entityManager->flush();

        $token = $this->authenticate('<EMAIL>', 'test');

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/orders/%s/prescription', $order->getTokenValue()),
            server: [
                'HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl',
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        self::assertSame(
            ProblemDetailsFactory::NotFound
                ->json('The requested resource could not be found.'),
            $this->client->getResponse()->getContent()
        );
    }

    /**
     * @dataProvider validShippingStateProvider
     */
    public function testCanFetchPrescriptionInOrderShippingState(string $state): void
    {
        $customer = $this->createCustomer();

        $order = $this->createOrderWithProductVariants();
        $order->setCustomer($customer);
        $order->setState(Order::STATE_FULFILLED);
        $order->setShippingState($state);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);
        $order->setAftercareState(OrderAftercareStates::STATE_OPENED);
        $order->setNumber('007');
        $order->setPrescriptionFilename('test-prescription.pdf');

        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $entityManager->persist($order);
        $entityManager->persist($customer);
        $entityManager->flush();

        $token = $this->authenticate('<EMAIL>', 'test');

        $mockResponse = new MockResponse(
            'file stream content',
            [
                'response_headers' => [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => 'attachment; filename=test.pdf',
                ],
            ]
        );
        $mockHttpClient = new MockHttpClient($mockResponse);

        self::ensureKernelShutdown();
        $client = static::createClient();

        $client->getContainer()->set(
            LegacyPrescriptionClient::class,
            new LegacyPrescriptionClient($mockHttpClient)
        );

        $client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/orders/%s/prescription', $order->getTokenValue()),
            server: [
                'HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl',
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
    }

    public function validShippingStateProvider(): iterable
    {
        $validStates = [OrderShippingStates::STATE_SHIPPED, OrderShippingStates::STATE_RETURNED];
        foreach ($validStates as $validState) {
            yield $validState => [$validState];
        }
    }
}
