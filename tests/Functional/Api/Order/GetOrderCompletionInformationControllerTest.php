<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Order;

use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\Order\Order;
use App\Tests\Functional\Api\CartTestFactory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetOrderCompletionInformationControllerTest extends WebTestCase
{
    private const string CHANNEL_CODE = 'dok_de';

    private const string LOCALE_CODE = 'de';

    private const string PRODUCT_CODE = 'viagra_test_special';

    private const string PRODUCT_VARIANT_CODE = 'viagra_25mg_4_test_special';

    private KernelBrowser $client;

    private CartTestFactory $cartTestFactory;

    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
        $this->cartTestFactory = new CartTestFactory(static::getContainer());
        $this->entityManager = static::getContainer()->get(EntityManagerInterface::class);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
        $this->entityManager->close();
        unset($this->entityManager);
    }

    public function testCanGetOrderCompletionInformation(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(self::CHANNEL_CODE, self::PRODUCT_CODE, [self::PRODUCT_VARIANT_CODE]);

        $order = $this->cartTestFactory->createCartWithProductVariants(self::CHANNEL_CODE, self::LOCALE_CODE, [self::PRODUCT_VARIANT_CODE]);
        $order->setNumber('1919');
        $order->setState(Order::STATE_NEW);

        $order->setCustomer($this->createCustomer());

        $lastPaymentId = $order->getLastPayment()->getId();

        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/checkout/%s/completion-information?id=%s', $order->getTokenValue(), $order->getId())
        );

        static::assertResponseIsSuccessful();

        $responseBody = $this->client->getResponse()->getContent();

        static::assertJson($responseBody);

        $expectedJson =
        [
            'customer' => [
                'email' => '<EMAIL>',
            ],
            'paymentState' => 'cart',
            'payments' => [
                [
                    'id' => $lastPaymentId,
                    'state' => 'cart',
                    'method' => [
                        'code' => 'bank_transfer_eur_dokteronline',
                        'icon' => 'bank_transfer',
                        'instructions' => 'An <strong>additional fee</strong> is charged for using this payment method. In addition, the order will take <strong>two days longer</strong> to deliver.',
                        'name' => 'Bank transfer',
                    ],
                    'amount' => 1495,
                ],
            ],
            'number' => '1919',
        ];

        static::assertJsonStringEqualsJsonString(json_encode($expectedJson), $responseBody);
    }

    public function testItReturns404IfGivenIdDoesNotMatchOrderId(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(self::CHANNEL_CODE, self::PRODUCT_CODE, [self::PRODUCT_VARIANT_CODE]);

        $order = $this->cartTestFactory->createCartWithProductVariants(self::CHANNEL_CODE, self::LOCALE_CODE, [self::PRODUCT_VARIANT_CODE]);
        $order->setNumber('1919');

        $randomOrderId = 999;

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/checkout/%s/completion-information?id=%s', $order->getTokenValue(), $randomOrderId)
        );

        static::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    private function createCustomer(): Customer
    {
        /** @var CustomerPool $customerPool */
        $customerPool = $this->entityManager
            ->getRepository(CustomerPool::class)
            ->findOneBy(['code' => 'dokteronline']);

        $customer = new Customer();
        $customer->setFirstName('Hans');
        $customer->setEmail('<EMAIL>');
        $customer->setCustomerPool($customerPool);

        return $customer;
    }
}
