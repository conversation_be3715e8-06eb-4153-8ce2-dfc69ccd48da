<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Order\Communication;

use App\Entity\Addressing\Address;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\Order\Order;
use App\Entity\User\ShopUser;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use DateTime;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use Ramsey\Uuid\Uuid;
use SuperBrave\ConsultSystemClient\ClientInterface;
use SuperBrave\ConsultSystemClient\Model\CommunicationMessage;
use SuperBrave\ConsultSystemClient\Model\CommunicationMessageAuthor;
use SuperBrave\ConsultSystemClient\Model\Enum\AuthorType;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ListControllerTest extends WebTestCase
{
    private const string CHANNEL_CODE = 'dok_nl';
    private const string USER_EMAIL = '<EMAIL>';
    private const string USER_PASSWORD = 'test';

    private KernelBrowser $client;
    private CartTestFactory $cartTestFactory;
    private EntityManagerInterface $entityManager;
    private ShopUserAuthenticationTestHelper $authenticationTestFactory;
    private ClientInterface&MockObject $consultClient;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();
        $this->client = static::createClient();

        /** @var EntityManagerInterface $entityManager */
        $entityManager = static::getContainer()->get(EntityManagerInterface::class);
        /** @var ClientInterface&MockObject $consultClient */
        $consultClient = static::getContainer()->get(ClientInterface::class);
        $this->consultClient = $consultClient;

        $this->cartTestFactory = new CartTestFactory(static::getContainer());

        $this->authenticationTestFactory = new ShopUserAuthenticationTestHelper($this->client);

        $this->entityManager = $entityManager;

        parent::setUp();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
        $this->entityManager->close();
        unset($this->entityManager);
    }

    public function testCanGetMessages(): void
    {
        $mockResponseMessages[] = [
            'uuid' => Uuid::fromString('095be615-a8ad-4c33-8e9c-c7612fbf6c9f'),
            'author' => [
                'type' => 'client',
                'name' => 'Mr. Customer',
            ],
            'message' => 'Hello Dr. Doak',
            'createdAt' => '2022-12-14T14:20:04+00:00',
        ];

        $this->consultClient->method('listConsultRequestMessages')
            ->willReturn(
                array_map(
                    static function ($item) {
                        return new CommunicationMessage(
                            uuid: $item['uuid']->toString(),
                            author: new CommunicationMessageAuthor(AuthorType::from($item['author']['type']), $item['author']['name']),
                            message: $item['message'],
                            createdAt: new DateTime($item['createdAt']),
                        );
                    },
                    $mockResponseMessages
                )
            );

        $order = $this->createOrder();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/orders/%s/messages', $order->getTokenValue()),
            [],
            $this->appendAuthenticationToken(headers: [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ])
        );

        $responseBody = $this->client->getResponse()->getContent();

        $expectedResponseBody[] = [
            'id' => '095be615-a8ad-4c33-8e9c-c7612fbf6c9f',
            'author' => [
                'type' => 'client',
                'name' => 'Mr. Customer',
            ],
            'message' => 'Hello Dr. Doak',
            'createdAt' => '2022-12-14T14:20:04+00:00',
        ];

        $expectedJsonResponseBody = json_encode($expectedResponseBody);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertJsonStringEqualsJsonString((string) $expectedJsonResponseBody, (string) $responseBody);
    }

    public function testCannotListWhenShopUserIsNotOwnerOfOrder(): void
    {
        $order = $this->createOrder();

        $this->createAccount('<EMAIL>', '1234');

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/orders/%s/messages', $order->getTokenValue()),
            [],
            $this->appendAuthenticationToken(
                '<EMAIL>',
                '1234',
                [
                    'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                ]
            )
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        $expectedProblemDetailResponse = (string) json_encode([
            'detail' => 'You are not allowed to perform this action.',
            'status' => Response::HTTP_FORBIDDEN,
            'title' => 'An error occurred.',
            'type' => 'about:blank',
        ]);

        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
        self::assertJsonStringEqualsJsonString($expectedProblemDetailResponse, $responseBody);
    }

    public function testCannotListWhenOrderDoesNotExist(): void
    {
        $this->createAccount('<EMAIL>', '1234');

        $this->client->jsonRequest(
            Request::METHOD_GET,
            '/api/shop/orders/notAnOrder/messages',
            [],
            $this->appendAuthenticationToken(
                '<EMAIL>',
                '1234',
                [
                    'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                ]
            )
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        $expectedProblemDetailResponse = (string) json_encode([
            'detail' => "Order with token value 'notAnOrder' not found.",
            'status' => Response::HTTP_NOT_FOUND,
            'title' => 'An error occurred.',
            'type' => 'about:blank',
        ]);

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        self::assertJsonStringEqualsJsonString($expectedProblemDetailResponse, $responseBody);
    }

    private function createOrder(): Order
    {
        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            'viagra_test_special',
            ['viagra_25mg_4_test_special']
        );

        $address = new Address();
        $address->setFirstName('Hans');
        $address->setLastName('Test');
        $address->setStreet('Test street');
        $address->setPostcode('Test postcode');
        $address->setCity('Breda');
        $address->setCountryCode('NL');

        $customer = $this->createAccount(self::USER_EMAIL, self::USER_PASSWORD);

        $order = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            'nl',
            ['viagra_25mg_4_test_special']
        );

        $order->setState(Order::STATE_NEW);
        $order->setCustomer($customer);
        $order->setBillingAddress($address);
        $order->setConsultSystemReference(Uuid::uuid4());

        $this->entityManager->flush();

        return $order;
    }

    private function createAccount(string $username, string $password): Customer
    {
        /** @var CustomerPool $customerPool */
        $customerPool = $this->entityManager
            ->getRepository(CustomerPool::class)
            ->findOneBy(['code' => 'dokteronline']);

        $shopUser = new ShopUser();
        $shopUser->setUsername($username);
        $shopUser->setPlainPassword($password);
        $shopUser->enable();

        $customer = new Customer();
        $customer->setBirthday(new DateTimeImmutable('1997-03-09'));
        $customer->setFirstName('FirstName');
        $customer->setEmail($username);
        $customer->setUser($shopUser);
        $customer->setCustomerPool($customerPool);

        $shopUser->setCustomer($customer);

        $this->entityManager->persist($shopUser);
        $this->entityManager->persist($customer);

        $this->entityManager->flush();

        return $customer;
    }

    private function appendAuthenticationToken(
        string $username = self::USER_EMAIL,
        string $password = self::USER_PASSWORD,
        array $headers = [],
    ): array {
        $headers['HTTP_AUTHORIZATION'] = sprintf(
            'Bearer %s',
            $this->authenticationTestFactory->authenticate($username, $password)
        );

        return $headers;
    }
}
