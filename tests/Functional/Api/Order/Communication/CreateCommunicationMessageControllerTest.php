<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Order\Communication;

use App\Entity\Addressing\Address;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\Order\Order;
use App\Entity\User\ShopUser;
use App\StateMachine\OrderAftercareStates;
use App\StateMachine\OrderPrescriptionStates;
use App\StateMachine\OrderShippingStates;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use App\Tests\Util\Factory\ProblemDetailsFactory;
use DateTime;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use Ramsey\Uuid\Uuid;
use SuperBrave\ConsultSystemClient\ClientInterface;
use SuperBrave\ConsultSystemClient\Model\CommunicationMessage;
use SuperBrave\ConsultSystemClient\Model\CommunicationMessageAuthor;
use SuperBrave\ConsultSystemClient\Model\Enum\AuthorType;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class CreateCommunicationMessageControllerTest extends WebTestCase
{
    private const string CHANNEL_CODE = 'dok_nl';
    private const string USER_EMAIL = '<EMAIL>';
    private const string USER_PASSWORD = 'test';

    private const array MOCKED_CREATED_MESSAGE_RESPONSE_BODY = [
        [
            'uuid' => 'd4c3b2a1-0f9e-8d7c-6b5a-4e3f2d1c0b9a',
            'author' => [
                'type' => 'client',
                'name' => 'Mr. Test',
            ],
            'message' => 'Hello Dr. Doak',
            'createdAt' => '2022-12-14T14:20:04+00:00',
        ],
    ];

    private KernelBrowser $client;
    private CartTestFactory $cartTestFactory;
    private EntityManagerInterface $entityManager;
    private ShopUserAuthenticationTestHelper $authenticationTestFactory;
    private ClientInterface&MockObject $consultClient;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        $this->client = static::createClient();
        $this->client->disableReboot();

        /** @var EntityManagerInterface $entityManager */
        $entityManager = static::getContainer()->get(EntityManagerInterface::class);
        /** @var ClientInterface&MockObject $consultClient */
        $consultClient = static::getContainer()->get(ClientInterface::class);
        $this->consultClient = $consultClient;

        $this->cartTestFactory = new CartTestFactory(static::getContainer());

        $this->authenticationTestFactory = new ShopUserAuthenticationTestHelper($this->client);

        $this->entityManager = $entityManager;

        parent::setUp();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
        $this->entityManager->close();
        unset($this->entityManager);
    }

    /**
     * @dataProvider validOrderPrescriptionStateProvider
     */
    public function testCanSendMessage(
        string $actualPrescriptionState,
        string $actualShippingState,
        string $expectedPrescriptionState,
    ): void {
        $this->consultClient->method('listConsultRequestMessages')
            ->willReturn(
                array_map(static function ($item) {
                    return new CommunicationMessage(
                        uuid: $item['uuid'],
                        author: new CommunicationMessageAuthor(AuthorType::from($item['author']['type']), $item['author']['name']),
                        message: $item['message'],
                        createdAt: new DateTime($item['createdAt']),
                    );
                },
                    self::MOCKED_CREATED_MESSAGE_RESPONSE_BODY
                )
            );


        $order = $this->createOrder($actualPrescriptionState, $actualShippingState);

        $tokenValue = $order->getTokenValue();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/orders/%s/messages', $tokenValue),
            [
                'message' => 'Hello Dr. Doak',
            ],
            $this->appendAuthenticationToken(headers: [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ])
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        $expectedResponseBody = self::MOCKED_CREATED_MESSAGE_RESPONSE_BODY;
        $expectedResponseBody[0]['id'] = $expectedResponseBody[0]['uuid'];
        unset($expectedResponseBody[0]['uuid']);

        $expectedJsonResponseBody = (string) json_encode($expectedResponseBody);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertJsonStringEqualsJsonString($expectedJsonResponseBody, $responseBody);

        $order = $this->entityManager->getRepository(Order::class)->findOneBy([
            'tokenValue' => $tokenValue,
        ]);
        $this->entityManager->refresh($order);

        self::assertSame($expectedPrescriptionState, $order->getPrescriptionState());
    }

    /**
     * @return iterable<string, array{
     *     0: string,
     *     1: string,
     *     2: string,
     * }>
     */
    public function validOrderPrescriptionStateProvider(): iterable
    {
        yield "Can send a message when prescriptionState is 'pending' and should get state 'pending'" => [
            OrderPrescriptionStates::STATE_PENDING,
            OrderShippingStates::STATE_AWAITING_PRESCRIPTION,
            OrderPrescriptionStates::STATE_PENDING,
        ];

        yield "Can send a message when prescriptionState is 'waiting_for_response' and should get state 'pending'" => [
            OrderPrescriptionStates::STATE_WAITING_FOR_RESPONSE,
            OrderShippingStates::STATE_AWAITING_PRESCRIPTION,
            OrderPrescriptionStates::STATE_PENDING,
        ];

        yield 'Can create aftercare message' => [
            OrderPrescriptionStates::STATE_APPROVED,
            OrderShippingStates::STATE_SHIPPED,
            OrderPrescriptionStates::STATE_APPROVED,
        ];
    }

    /**
     * @dataProvider invalidOrderPrescriptionStateProvider
     */
    public function testCannotSendMessageWithInvalidOrder(
        string $actualPrescriptionState,
        string $actualShippingState,
        string $actualAftercareState,
    ): void {
        $order = $this->createOrder($actualPrescriptionState, $actualShippingState, $actualAftercareState);

        $tokenValue = $order->getTokenValue();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/orders/%s/messages', $tokenValue),
            [
                'message' => 'Hello Dr. Doak',
            ],
            $this->appendAuthenticationToken(headers: [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ])
        );

        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    /**
     * @return iterable<string, array{
     *     0: string,
     *     1: string,
     *     2: string,
     * }>
     */
    public function invalidOrderPrescriptionStateProvider(): iterable
    {
        yield 'Cannot communicate with a doctor when the order is approved but not sent.' => [
            OrderPrescriptionStates::STATE_APPROVED,
            OrderShippingStates::STATE_AWAITING_PRESCRIPTION,
            OrderAftercareStates::STATE_CART,
        ];

        yield 'Cannot communicate with a cancelled prescription.' => [
            OrderPrescriptionStates::STATE_CANCELLED,
            OrderShippingStates::STATE_AWAITING_PRESCRIPTION,
            OrderAftercareStates::STATE_CART,
        ];

        yield 'Cannot communicate with a cart state' => [
            OrderPrescriptionStates::STATE_CART,
            OrderShippingStates::STATE_SHIPPED,
            OrderAftercareStates::STATE_CART,
        ];

        yield 'Cannot communicate with an invalid shipping state' => [
            OrderPrescriptionStates::STATE_APPROVED,
            OrderShippingStates::STATE_AWAITING_PAYMENT,
            OrderAftercareStates::STATE_CART,
        ];

        yield 'Cannot communicate with aftercare state "cart"' => [
            OrderPrescriptionStates::STATE_APPROVED,
            OrderShippingStates::STATE_SHIPPED,
            OrderAftercareStates::STATE_CART,
        ];

        yield 'Cannot communicate with aftercare state "awaiting_shipment"' => [
            OrderPrescriptionStates::STATE_APPROVED,
            OrderShippingStates::STATE_PENDING,
            OrderAftercareStates::STATE_AWAITING_SHIPMENT,
        ];

        yield 'Cannot communicate with aftercare state "closed"' => [
            OrderPrescriptionStates::STATE_APPROVED,
            OrderShippingStates::STATE_SHIPPED,
            OrderAftercareStates::STATE_CLOSED,
        ];

        yield 'Cannot communicate with aftercare state "skipped"' => [
            OrderPrescriptionStates::STATE_APPROVED,
            OrderShippingStates::STATE_SHIPPED,
            OrderAftercareStates::STATE_SKIPPED,
        ];
    }

    public function testCannotSendMessageWhenOrderPrescriptionStateHasInvalidState(): void
    {
        $this->consultClient->method('listConsultRequestMessages')
            ->willReturn(
                array_map(static function ($item) {
                    return new CommunicationMessage(
                        uuid: $item['uuid'],
                        author: new CommunicationMessageAuthor(AuthorType::from($item['author']['type']), $item['author']['name']),
                        message: $item['message'],
                        createdAt: new DateTime($item['createdAt']),
                    );
                },
                    self::MOCKED_CREATED_MESSAGE_RESPONSE_BODY
                )
            );


        $order = $this->createOrder('cancelled', 'pending', 'cart');

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/orders/%s/messages', $order->getTokenValue()),
            [
                'message' => 'Hello Dr. Doak',
            ],
            $this->appendAuthenticationToken(headers: [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ])
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        $expectedProblemDetailResponse = (string) json_encode([
            'detail' => 'You\'re not allowed to send a message to the doctor (yet).',
            'status' => Response::HTTP_FORBIDDEN,
            'title' => 'Forbidden',
            'type' => 'about:blank',
        ]);

        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
        self::assertJsonStringEqualsJsonString($expectedProblemDetailResponse, $responseBody);
    }

    public function testCannotSendMessageWhenShopUserIsNotOwnerOfOrder(): void
    {
        $order = $this->createOrder();

        $this->createAccount('<EMAIL>', '1234');

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/orders/%s/messages', $order->getTokenValue()),
            [
                'message' => 'Hello Dr. Doak',
            ],
            $this->appendAuthenticationToken(
                '<EMAIL>',
                '1234',
                [
                    'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                ]
            )
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        $expectedProblemDetails = ProblemDetailsFactory::NotFound
            ->json('The requested order could not be found.');

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        self::assertJsonStringEqualsJsonString($expectedProblemDetails, $responseBody);
    }

    public function testCannotSendMessageWhenOrderDoesNotExist(): void
    {
        $this->createAccount('<EMAIL>', '1234');

        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/orders/notAnOrder/messages',
            [
                'message' => 'Hello Dr. Doak',
            ],
            $this->appendAuthenticationToken(
                '<EMAIL>',
                '1234',
                [
                    'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                ]
            )
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        $expectedProblemDetails = ProblemDetailsFactory::NotFound
            ->json('The requested order could not be found.');

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        self::assertJsonStringEqualsJsonString($expectedProblemDetails, $responseBody);
    }

    private function createOrder(
        string $orderPrescriptionState = 'pending',
        string $orderShippingState = 'pending',
        string $orderAftercareState = 'opened',
    ): Order {
        $this->cartTestFactory->createProductAndProductVariants(
            channelCode: self::CHANNEL_CODE,
            productCode: 'viagra_test_special',
            productVariantCodes: ['viagra_25mg_4_test_special'],
            prescriptionRequired: true,
        );

        $address = new Address();
        $address->setFirstName('Hans');
        $address->setLastName('Test');
        $address->setStreet('Test street');
        $address->setPostcode('Test postcode');
        $address->setCity('Breda');
        $address->setCountryCode('NL');

        $customer = $this->createAccount(self::USER_EMAIL, self::USER_PASSWORD);

        $order = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            'nl',
            ['viagra_25mg_4_test_special']
        );

        $order->setState('new');
        $order->setPrescriptionState($orderPrescriptionState);
        $order->setShippingState($orderShippingState);
        $order->setAftercareState($orderAftercareState);
        $order->setCustomer($customer);
        $order->setBillingAddress($address);
        $order->setConsultSystemReference(Uuid::fromString('d4c3b2a1-0f9e-8d7c-6b5a-4e3f2d1c0b9a'));

        $this->entityManager->flush();

        return $order;
    }

    private function createAccount(string $username, string $password): Customer
    {
        /** @var CustomerPool $customerPool */
        $customerPool = $this->entityManager
            ->getRepository(CustomerPool::class)
            ->findOneBy(['code' => 'dokteronline']);

        $shopUser = new ShopUser();
        $shopUser->setUsername($username);
        $shopUser->setPlainPassword($password);
        $shopUser->enable();

        $customer = new Customer();
        $customer->setBirthday(new DateTimeImmutable('1997-03-09'));
        $customer->setFirstName('FirstName');
        $customer->setEmail($username);
        $customer->setUser($shopUser);
        $customer->setCustomerPool($customerPool);

        $shopUser->setCustomer($customer);

        $this->entityManager->persist($shopUser);
        $this->entityManager->persist($customer);

        $this->entityManager->flush();

        return $customer;
    }

    private function appendAuthenticationToken(
        string $username = self::USER_EMAIL,
        string $password = self::USER_PASSWORD,
        array $headers = [],
    ): array {
        $headers['HTTP_AUTHORIZATION'] = sprintf(
            'Bearer %s',
            $this->authenticationTestFactory->authenticate($username, $password)
        );

        return $headers;
    }
}
