<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Order;

use App\Entity\Customer\Customer;
use App\Entity\Order\Order;
use App\Entity\Order\PreferredOrderItem;
use App\StateMachine\Callback\CreateOriginalOrderItems;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ProductTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use Doctrine\ORM\EntityManagerInterface;
use Sylius\Component\Order\Model\OrderInterface as BaseOrderInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PropertyAccess\PropertyAccess;

class GetOrderTest extends WebTestCase
{
    private const string API_URI_GET_ORDER = '/api/shop/orders/%s';
    private const string CHANNEL_CODE = 'dok_nl';
    private const string LOCALE_CODE = 'nl';
    private const string PRODUCT_CODE = 'viagra_get_order_test';
    private const string PRODUCT_VARIANT_CODE = 'viagra_get_order_test_25mg_4tabl';

    private KernelBrowser $client;
    private EntityManagerInterface $entityManager;
    private ShopUserAuthenticationTestHelper $authenticationTestHelper;
    private CartTestFactory $cartTestFactory;
    private ProductTestFactory $productTestFactory;
    private CreateOriginalOrderItems $createOriginalOrderItems;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
        $this->cartTestFactory = new CartTestFactory(static::getContainer());

        /** @var EntityManagerInterface $entityManager */
        $entityManager = static::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        $authenticationTestHelper = new ShopUserAuthenticationTestHelper($this->client);
        $this->authenticationTestHelper = $authenticationTestHelper;

        $this->productTestFactory = new ProductTestFactory(
            static::getContainer(),
            [self::CHANNEL_CODE],
        );

        $this->createOriginalOrderItems = new CreateOriginalOrderItems($entityManager);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
        $this->entityManager->close();
        unset($this->entityManager);
    }

    public function testGetOrderResponseContainsCustomProperties(): void
    {
        $cart = $this->createCartWithItemsAndCustomer();
        $cart->setState(BaseOrderInterface::STATE_NEW);

        $token = $this->authenticationTestHelper->authenticate();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf(self::API_URI_GET_ORDER, $cart->getTokenValue()),
            [],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();

        $responseBody = $this->client->getResponse()->getContent();

        static::assertIsString($responseBody);
        static::assertJson($responseBody);

        $validateData = [
            '[prescriptionState]' => 'cart',
            '[channel][code]' => self::CHANNEL_CODE,
            '[checkoutState]' => 'cart',
            '[paymentState]' => 'cart',
            '[payments][0][method][code]' => $cart->getLastPayment()?->getMethod()?->getCode(),
            '[currencyCode]' => $cart->getLastPayment()?->getCurrencyCode(),
            '[localeCode]' => self::LOCALE_CODE,
            '[shippingState]' => 'cart',
            '[tokenValue]' => $cart->getTokenValue(),
            '[items][0][variant][supplier][identifier]' => 'apotheek-bad-nieuweschans',
            '[items][0][variant][maximumQuantityPerOrder]' => 1,
            '[items][0][variant][leaflet]' => null,
            '[items][0][variant][code]' => self::PRODUCT_VARIANT_CODE,
            '[items][0][quantity]' => 1,
            '[state]' => 'new',
        ];

        self::assertResponsePropertiesSame($validateData, $responseBody);
    }

    /**
     * $token = $this->authenticationTestHelper->authenticate(); doesn't look at a {@see CustomerPool} so it provides
     * the token from the first user it finds by the identifier (email). In this testcase, we expect the token from
     * the $customerBlueclinic, but we get the token from $customerDokteronline.
     *
     * We want to test if bearer token from the blueclinic user cannot access the order from the dokteronline user
     * which have the same e-mailaddress.
     */
    public function testGetOrderGivesErrorResponseWhenOrderBelongsToUserButInDifferentCustomerPool(): void
    {
        $cartDokteronline = $this->createCartWithItemsAndCustomer();
        $cartDokteronline->setState(BaseOrderInterface::STATE_NEW);

        /** @var Customer $customerDokteronline */
        $customerDokteronline = $cartDokteronline->getCustomer();
        $customerDokteronline->setFirstName('DOK');

        $this->entityManager->persist($customerDokteronline);

        $customerBlueclinic = $this->cartTestFactory->createCustomer('blueclinic');
        $this->cartTestFactory->createUserForCustomer($customerBlueclinic);
        $customerBlueclinic->setFirstName('BLUECLINIC');

        $this->entityManager->persist($customerBlueclinic);

        $this->entityManager->flush();

        $blueclinicToken = $this->authenticationTestHelper->authenticate(
            CartTestFactory::USER_EMAIL,
            CartTestFactory::USER_PASSWORD,
            CartTestFactory::BLUECLINIC_CHANNEL_CODE
        );

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf(self::API_URI_GET_ORDER, $cartDokteronline->getTokenValue()),
            [],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $blueclinicToken),
                'HTTP_SYLIUS_CHANNEL_CODE' => 'blueclinic_nl',
            ]
        );

        $content = $this->client->getResponse()->getContent();
        $decodedResponseBody = json_decode((string) $content, true, 512, JSON_THROW_ON_ERROR);
        $expectedMessage = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => Response::HTTP_NOT_FOUND,
            'detail' => sprintf('Order with tokenValue \'%s\' not found.', $cartDokteronline->getTokenValue()),
        ];

        static::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        static::assertSame($expectedMessage, $decodedResponseBody);
    }

    public function testGetOrderGivesErrorResponseWhenOrderDoesNotBelongToUser(): void
    {
        $cart = $this->createCartWithItemsAndCustomer();
        $cart->setState(BaseOrderInterface::STATE_NEW);

        $otherCustomer = $this->cartTestFactory->createCustomer();
        $otherShopUserEmail = '<EMAIL>';
        $otherShopUserPassword = '123';
        $this->cartTestFactory->createUserForCustomer($otherCustomer);

        $token = $this->authenticationTestHelper->authenticate($otherShopUserEmail, $otherShopUserPassword);

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf(self::API_URI_GET_ORDER, $cart->getTokenValue()),
            [],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseStatusCodeSame(401);
    }

    public function testGetOrderResponseContainsAttributeProperties(): void
    {
        $cart = $this->createCartWithItemsAndCustomer($this->createProductsAndVariants());
        $cart->setState(BaseOrderInterface::STATE_NEW);

        $token = $this->authenticationTestHelper->authenticate();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf(self::API_URI_GET_ORDER, $cart->getTokenValue()),
            [],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();

        $responseBody = $this->client->getResponse()->getContent();

        static::assertIsString($responseBody);
        static::assertJson($responseBody);

        $validateData = [
            '[items][0][variant][productAttributes][type]' => 'medication',
            '[items][1][variant][productAttributes][type]' => 'consult',
            '[items][0][variant][maximumQuantityPerOrder]' => 1,
        ];

        self::assertResponsePropertiesSame($validateData, $responseBody);
    }

    /**
     * @dataProvider provideExpectedResponseProperties
     */
    public function testGetOrderResponseWithOriginalOrderItems(
        callable $additionalCartCallback,
        array $expectedResponseProperties,
    ): void {
        $cart = $this->createCartWithItemsAndCustomer($this->createProductsAndVariants());

        $additionalCartCallback($cart, $this->cartTestFactory, $this->createOriginalOrderItems);

        $this->entityManager->flush();

        $token = $this->authenticationTestHelper->authenticate();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf(self::API_URI_GET_ORDER, $cart->getTokenValue()),
            [],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $responseBody = $this->client->getResponse()->getContent();

        self::assertResponseIsSuccessful();
        self::assertIsString($responseBody);
        self::assertJson($responseBody);
        self::assertResponsePropertiesSame($expectedResponseProperties, $responseBody);
    }

    /**
     * @return iterable<string, array{
     *     callable(Order, CartTestFactory, CreateOriginalOrderItems): void,
     *     array<string, scalar|null|callable(array): void>
     * }>
     */
    public function provideExpectedResponseProperties(): iterable
    {
        yield 'Without order item modification' => [
            function (
                Order $cart,
                CartTestFactory $cartTestFactory,
                CreateOriginalOrderItems $createOriginalOrderItems,
            ): void {
                $createOriginalOrderItems($cart);

                $cart->setState(BaseOrderInterface::STATE_NEW);
            },
            [
                '[items]' => fn (array $actualItems) => self::assertCount(2, $actualItems),
            ],
        ];

        yield 'With added item' => [
            function (
                Order $cart,
                CartTestFactory $cartTestFactory,
                CreateOriginalOrderItems $createOriginalOrderItems,
            ): void {
                $createOriginalOrderItems($cart);

                $cartTestFactory->addItemToCart($cart, self::PRODUCT_VARIANT_CODE, 1);
                $cart->setState(BaseOrderInterface::STATE_NEW);
            },
            [
                '[items][2][modifiedSinceOriginalOrder]' => 'added',
                '[items]' => fn (array $actualItems) => self::assertCount(3, $actualItems),
            ],
        ];

        yield 'With remove item' => [
            function (
                Order $cart,
                CartTestFactory $cartTestFactory,
                CreateOriginalOrderItems $createOriginalOrderItems,
            ): void {
                $createOriginalOrderItems($cart);

                $firstOrderItem = $cart->getItems()->first();

                $cartTestFactory->removeItemFromCart($cart, $firstOrderItem);
                $cart->setState(BaseOrderInterface::STATE_NEW);
            },
            [
                '[items][1][modifiedSinceOriginalOrder]' => 'removed',
                '[items]' => fn (array $actualItems) => self::assertCount(2, $actualItems),
            ],
        ];

        yield 'Without preferred order item modification' => [
            function (
                Order $cart,
                CartTestFactory $cartTestFactory,
                CreateOriginalOrderItems $createOriginalOrderItems,
            ): void {
                $firstOrderItem = $cart->getItems()->first();

                $preferredItem = new PreferredOrderItem($firstOrderItem, $firstOrderItem->getVariant());
                $firstOrderItem->addPreferredItem($preferredItem);

                $createOriginalOrderItems($cart);

                $cart->setState(BaseOrderInterface::STATE_NEW);
            },
            [
                '[items][0][preferredVariants]' => fn (array $actualItems) => self::assertCount(1, $actualItems),
                '[items][1][modifiedSinceOriginalOrder]' => null,
                '[items]' => fn (array $actualItems) => self::assertCount(2, $actualItems),
            ],
        ];

        yield 'With remove preferred item' => [
            function (
                Order $cart,
                CartTestFactory $cartTestFactory,
                CreateOriginalOrderItems $createOriginalOrderItems,
            ): void {
                $firstOrderItem = $cart->getItems()->first();

                $preferredItem = new PreferredOrderItem($firstOrderItem, $firstOrderItem->getVariant());
                $firstOrderItem->addPreferredItem($preferredItem);

                $createOriginalOrderItems($cart);

                $firstOrderItem->removePreferredItem($preferredItem);

                $cart->setState(BaseOrderInterface::STATE_NEW);
            },
            [
                '[items][0][preferredVariants]' => fn (array $actualItems) => self::assertCount(1, $actualItems),
                '[items][0][preferredVariants][0][modifiedSinceOriginalOrder]' => 'removed',
                '[items]' => fn (array $actualItems) => self::assertCount(2, $actualItems),
            ],
        ];

        yield 'With added preferred item' => [
            function (
                Order $cart,
                CartTestFactory $cartTestFactory,
                CreateOriginalOrderItems $createOriginalOrderItems,
            ): void {
                $firstOrderItem = $cart->getItems()->first();

                self::assertNotNull($firstOrderItem->getVariant());
                $preferredItem = new PreferredOrderItem($firstOrderItem, $firstOrderItem->getVariant());
                $firstOrderItem->addPreferredItem($preferredItem);

                $createOriginalOrderItems($cart);

                self::assertNotNull($firstOrderItem->getVariant());
                $preferredItem = new PreferredOrderItem($firstOrderItem, $firstOrderItem->getVariant());
                $firstOrderItem->addPreferredItem($preferredItem);

                $cart->setState(BaseOrderInterface::STATE_NEW);
            },
            [
                '[items][0][preferredVariants][1][modifiedSinceOriginalOrder]' => 'added',
                '[items][0][preferredVariants]' => fn (array $actualItems) => self::assertCount(2, $actualItems),
                '[items]' => fn (array $actualItems) => self::assertCount(2, $actualItems),
            ],
        ];
    }

    /**
     * @return array<int, string>
     */
    private function createProductsAndVariants(): array
    {
        $productCode = 'viagra2_'.microtime(true);
        $productVariantCode = 'viagra2_25mg_4'.microtime(true);
        $consultProductCode = 'consult_viagra2_'.microtime(true);
        $consultProductVariantCode = 'consult_viagra2_25mg_4'.microtime(true);

        $products = [
            'regular' => [
                [
                    'code' => $productCode,
                    'name' => $productCode,
                    'variantCode' => $productVariantCode,
                    'variantName' => $productVariantCode,
                ],
            ],
            'consult' => [
                [
                    'code' => $consultProductCode,
                    'name' => $consultProductCode,
                    'variantCode' => $consultProductVariantCode,
                    'variantName' => $consultProductVariantCode,
                ],
            ],
        ];

        $this->productTestFactory->prepareDatabaseWithRegularAndConsultProducts(
            $products['regular'],
            $products['consult'],
        );

        return [
            $productVariantCode,
            $consultProductVariantCode,
        ];
    }

    /**
     * @param array<string, string|int|callable|null> $expectedValues
     */
    private static function assertResponsePropertiesSame(array $expectedValues, string $responseBody): void
    {
        $decodedResponseBody = json_decode($responseBody, true);
        $propertyAccessor = PropertyAccess::createPropertyAccessor();

        foreach ($expectedValues as $propertyPath => $expectedValue) {
            $actualValue = $propertyAccessor->getValue($decodedResponseBody, $propertyPath);

            if (is_callable($expectedValue)) {
                $expectedValue($actualValue);

                continue;
            }

            static::assertSame(
                $expectedValue,
                $actualValue,
                sprintf(
                    "Expected value '%s' in responseBody at path '%s', actual value '%s'.",
                    $expectedValue,
                    $propertyPath,
                    $actualValue
                )
            );
        }
    }

    /**
     * @param array<string> $productVariantCodes
     */
    private function createCartWithItemsAndCustomer(
        array $productVariantCodes = [self::PRODUCT_VARIANT_CODE],
    ): Order {
        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            self::PRODUCT_CODE,
            [
                self::PRODUCT_VARIANT_CODE,
            ],
        );

        $cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            self::LOCALE_CODE,
            $productVariantCodes
        );

        $customer = $this->cartTestFactory->createCustomer();
        $this->cartTestFactory->createUserForCustomer($customer);
        $cart->setCustomer($customer);

        $this->entityManager->persist($cart);
        $this->entityManager->flush();

        return $cart;
    }
}
