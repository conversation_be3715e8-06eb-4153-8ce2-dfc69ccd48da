<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Order;

use App\Entity\Order\Cancellation;
use App\Entity\Order\Enum\CancellationBy;
use App\Entity\Order\Order;
use App\Entity\Refund\RefundPayment;
use App\StateMachine\OrderAftercareStates;
use App\StateMachine\OrderPrescriptionStates;
use App\StateMachine\OrderShippingStates;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Sylius\Component\Core\OrderPaymentStates;
use Sylius\Component\Core\OrderShippingStates as SyliusOrderShippingStates;
use Sylius\Component\Order\Model\OrderInterface as SyliusOrderInterface;
use Sylius\Component\Payment\Model\PaymentInterface;
use Sylius\RefundPlugin\Entity\Refund;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class CancelOrderControllerTest extends BaseFunctionalOrderTestCase
{
    private const string PATH = '/api/shop/orders/%s/cancel';
    private const string REASON = 'To test the cancellation.';
    private const string ORDER_NUMBER = '00001';

    public function testThatTheOrderCanBeCancelledAndNoRefund(): void
    {
        $order = $this->getOrder();
        $this->entityManager->flush();

        /** @var string $token */
        $token = $this->authenticate(self::USER_EMAIL, self::USER_PASSWORD);
        $this->addItemToOrder($order, $token);

        /** @var Order $order */
        $order = $this->entityManager->find(Order::class, $order->getId());
        $order->setState(SyliusOrderInterface::STATE_NEW);
        $order->setShippingState(OrderShippingStates::STATE_AWAITING_PRESCRIPTION);
        $order->setPaymentState(OrderPaymentStates::STATE_AUTHORIZED);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_PENDING);

        foreach ($order->getPayments() as $payment) {
            $payment->setState(PaymentInterface::STATE_NEW);
        }

        foreach ($order->getShipments() as $shipment) {
            $shipment->setState(OrderShippingStates::STATE_AWAITING_PRESCRIPTION);
        }

        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getUri($order),
            [
                'reason' => self::REASON,
            ],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );
        $this->assertResponseIsSuccessful();

        $this->entityManager->refresh($order);
        $this->assertEquals(new Cancellation(CancellationBy::CUSTOMER, self::REASON), $order->getCancellation());
        $this->assertSame(SyliusOrderInterface::STATE_CANCELLED, $order->getState());
        $this->assertSame(SyliusOrderShippingStates::STATE_CANCELLED, $order->getShippingState());
        $this->assertSame(OrderPrescriptionStates::STATE_CANCELLED, $order->getPrescriptionState());
        $this->assertSame(OrderAftercareStates::STATE_CART, $order->getAftercareState());

        $refund = $this->entityManager->getRepository(Refund::class)->findOneBy(['order' => $order->getId()]);
        $this->assertNotInstanceOf(
            Refund::class,
            $refund,
            'Refund should not be created when payment has not been finished'
        );
    }

    public function testThatCancelledOrderHasRefund(): void
    {
        $order = $this->getOrder();
        $this->entityManager->flush();

        /** @var string $token */
        $token = $this->authenticate(self::USER_EMAIL, self::USER_PASSWORD);
        $this->addItemToOrder($order, $token);

        /** @var Order $order */
        $order = $this->entityManager->getRepository(Order::class)->find($order->getId());
        $order->addPayment($this->getPayment());
        $order->setState(SyliusOrderInterface::STATE_NEW);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setShippingState(OrderShippingStates::STATE_AWAITING_PAYMENT);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_DECLINED);

        foreach ($order->getShipments() as $shipment) {
            $shipment->setState(OrderShippingStates::STATE_AWAITING_PRESCRIPTION);
        }

        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getUri($order),
            [
                'reason' => self::REASON,
            ],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $this->assertResponseIsSuccessful();
        $this->entityManager->refresh($order);
        $this->assertEquals(new Cancellation(CancellationBy::CUSTOMER, self::REASON), $order->getCancellation());
        $this->assertSame(SyliusOrderInterface::STATE_CANCELLED, $order->getState());
        $this->assertSame(PaymentInterface::STATE_COMPLETED, $order->getLastPayment()?->getState());
        $this->assertSame(SyliusOrderShippingStates::STATE_CANCELLED, $order->getShippingState());
        $this->assertSame(OrderPrescriptionStates::STATE_DECLINED, $order->getPrescriptionState());

        /** @var RefundPayment $refundPayment */
        $refundPayment = $this->entityManager->getRepository(RefundPayment::class)->findOneBy(['order' => $order]);
        $this->assertInstanceOf(RefundPayment::class, $refundPayment);
        $this->assertSame(BaseFunctionalOrderTestCase::PAYMENT_AMOUNT, $refundPayment->getAmount());
    }

    public function testCancelOrderWhenOrderIsPartiallyPaid(): void
    {
        $order = $this->getOrder();
        $this->entityManager->flush();

        /** @var string $token */
        $token = $this->authenticate(self::USER_EMAIL, self::USER_PASSWORD);
        $this->addItemToOrder($order, $token);

        /** @var Order $order */
        $order = $this->entityManager->getRepository(Order::class)->find($order->getId());
        $order->addPayment($this->getPayment());
        $order->addPayment($this->getPayment(state: PaymentInterface::STATE_NEW));
        $order->setState(SyliusOrderInterface::STATE_NEW);
        $order->setPaymentState(OrderPaymentStates::STATE_PARTIALLY_PAID);
        $order->setShippingState(OrderShippingStates::STATE_AWAITING_PAYMENT);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);

        foreach ($order->getShipments() as $shipment) {
            $shipment->setState(OrderShippingStates::STATE_AWAITING_PAYMENT);
        }

        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getUri($order),
            [
                'reason' => self::REASON,
            ],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseIsSuccessful();
        $this->entityManager->refresh($order);
        $this->assertEquals(new Cancellation(CancellationBy::CUSTOMER, self::REASON), $order->getCancellation());
        $this->assertSame(SyliusOrderInterface::STATE_CANCELLED, $order->getState());
        $this->assertSame(SyliusOrderShippingStates::STATE_CANCELLED, $order->getShippingState());
        $this->assertSame(OrderPrescriptionStates::STATE_APPROVED, $order->getPrescriptionState());

        /** @var RefundPayment $refundPayment */
        $refundPayment = $this->entityManager->getRepository(RefundPayment::class)->findOneBy(['order' => $order]);
        $this->assertInstanceOf(RefundPayment::class, $refundPayment);
        $this->assertSame(BaseFunctionalOrderTestCase::PAYMENT_AMOUNT, $refundPayment->getAmount());
    }

    public function testCannotCancelForOtherCustomer(): void
    {
        $customer = $this->createCustomer('<EMAIL>');
        $this->entityManager->persist($customer);

        $loggedInCustomer = $this->createCustomer();
        $this->entityManager->persist($loggedInCustomer);
        $this->entityManager->flush();

        $token = $this->authenticate(self::USER_EMAIL, self::USER_PASSWORD);

        $order = $this->createOrderWithProductVariants();
        $order->setState(SyliusOrderInterface::STATE_NEW);
        $order->setShippingState(SyliusOrderShippingStates::STATE_READY);
        $order->setCustomer($customer);

        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getUri($order),
            [
                'reason' => self::REASON,
            ],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        /** @var string $content */
        $content = $this->client->getResponse()->getContent();
        $response = json_decode(
            $content,
            true,
            JSON_THROW_ON_ERROR
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        self::assertSame('The requested order could not be found.', $response['detail']);
    }

    public function testThatOrderCannotBeCancelledWhenShippingIsReady(): void
    {
        $order = $this->getOrder();
        $this->entityManager->flush();

        /** @var string $token */
        $token = $this->authenticate(self::USER_EMAIL, self::USER_PASSWORD);
        $this->addItemToOrder($order, $token);

        /** @var Order $order */
        $order = $this->entityManager->find(Order::class, $order->getId());
        $order->setState(SyliusOrderInterface::STATE_NEW);
        $order->setShippingState(SyliusOrderShippingStates::STATE_READY);

        foreach ($order->getShipments() as $shipment) {
            $shipment->setState(SyliusOrderShippingStates::STATE_READY);
        }

        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getUri($order),
            [
                'reason' => self::REASON,
            ],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        /** @var Order $order */
        $order = $this->entityManager->getRepository(Order::class)->find($order->getId());

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        $this->assertNull($order->getCancellation()->getBy());
        $this->assertNull($order->getCancellation()->getReason());
        $this->assertSame(SyliusOrderInterface::STATE_NEW, $order->getState());
    }

    public function testThatItShouldThrowAnErrorWhenNoOrderIsFound(): void
    {
        $customer = $this->createCustomer();
        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $token = $this->authenticate(self::USER_EMAIL, self::USER_PASSWORD);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::PATH, '123SDFasdf'),
            [
                'reason' => self::REASON,
            ],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    private function getUri(Order $order): string
    {
        return sprintf(self::PATH, $order->getTokenValue());
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    private function getOrder(): Order
    {
        $customer = $this->createCustomer();
        $this->entityManager->persist($customer);

        /** @var Order $order */
        $order = $this->createOrder();
        $order->setCustomer($customer);
        $address = $customer->getAddresses()->first() ?: null;
        $order->setShippingAddress($address);
        $order->setBillingAddress($address);
        $order->setNumber(self::ORDER_NUMBER);

        return $order;
    }

    /**
     * Patch is required to update cart with all order processors.
     */
    private function addItemToOrder(Order $order, string $token): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $order->getTokenValue()),
            [
                [
                    'productVariantCode' => self::PRODUCT_VARIANT_CODE,
                    'quantity' => 1,
                ],
            ],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ],
        );
        $this->assertResponseIsSuccessful();
    }
}
