<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\MarketingSubscription;

use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\MarketingSubscription\MarketingSubscription;
use App\Tests\Functional\Api\AbstractWebTestCase;
use <PERSON>\Uuid\Uuid;
use <PERSON>\Uuid\UuidInterface;
use Symfony\Component\HttpFoundation\Request;

final class GetMarketingSubscriptionControllerTest extends AbstractWebTestCase
{
    public const string SUBSCRIBER_EMAIL = '<EMAIL>';
    public const string SUBSCRIBER_LOCALE_CODE = 'nl';
    public const string SUBSCRIBER_COUNTRY_CODE = 'NL';

    public function testCanGetMarketingSubscription(): void
    {
        /** @var BusinessUnit $businessUnit */
        $businessUnit = $this->entityManager->getRepository(BusinessUnit::class)->findOneBy(['code' => 'dokteronline']);
        $marketingSubscription = new MarketingSubscription(
            self::SUBSCRIBER_EMAIL,
            self::SUBSCRIBER_LOCALE_CODE,
            self::SUBSCRIBER_COUNTRY_CODE,
            $businessUnit
        );

        $this->entityManager->persist($marketingSubscription);
        $this->entityManager->flush();

        $this->client->request(
            Request::METHOD_GET,
            $this->getApiUri($marketingSubscription->getUuid()),
        );

        $this->assertResponseIsSuccessful();

        $response = $this->getResponseBody();

        self::assertSame(self::SUBSCRIBER_EMAIL, $response['email']);
        self::assertSame(self::SUBSCRIBER_LOCALE_CODE, $response['localeCode']);
        self::assertSame(self::SUBSCRIBER_COUNTRY_CODE, $response['countryCode']);
        self::assertIsArray($response['optInEmail']);
    }

    public function testThrows404ErrorWhenMarketingSubscriptionDoesntExist(): void
    {
        $uuid = Uuid::uuid4();

        $this->client->request(
            Request::METHOD_GET,
            $this->getApiUri($uuid),
        );

        $this->assertResponseStatusCodeSame(404);

        $response = $this->getResponseBody();

        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => 404,
            'detail' => 'The requested resource could not be found.',
        ];

        $this->assertSame($expectedResponse, $response);
    }

    private function getApiUri(UuidInterface $uuid): string
    {
        return sprintf('/api/shop/marketing-subscriptions/%s', $uuid->toString());
    }
}
