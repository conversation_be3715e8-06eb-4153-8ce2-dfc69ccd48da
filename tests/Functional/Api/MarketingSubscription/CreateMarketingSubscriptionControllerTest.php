<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\MarketingSubscription;

use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\MarketingSubscription\MarketingSubscription;
use App\Entity\MarketingSubscription\Subscription;
use App\Tests\Functional\Api\AbstractWebTestCase;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Ramsey\Uuid\UuidInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class CreateMarketingSubscriptionControllerTest extends AbstractWebTestCase
{
    private const string API_URI = '/api/shop/marketing-subscriptions';

    /**
     * @dataProvider provideCreateRequestBody
     */
    public function testCanCreateSubscription(
        array $requestBody,
        MarketingSubscription $expectedMarketingSubscription,
    ): void {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::API_URI,
            $requestBody,
        );

        /** @var EntityManagerInterface $entityManager */
        $entityManager = $this->client->getContainer()->get(EntityManagerInterface::class);

        $actualMarketingSubscription = $entityManager->getRepository(MarketingSubscription::class)
            ->findOneBy(['emailAddress' => '<EMAIL>']);
        self::assertInstanceOf(MarketingSubscription::class, $actualMarketingSubscription);

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        self::assertEmpty($this->client->getResponse()->getContent());
        self::assertMarketingSubscriptionSame($expectedMarketingSubscription, $actualMarketingSubscription);
    }

    public function provideCreateRequestBody(): iterable
    {
        $expectedMarketingSubscription = new MarketingSubscription('<EMAIL>', 'nl', 'NL', new BusinessUnit());
        $optInEmail = $expectedMarketingSubscription->getOptInEmail();
        $optInEmail->setSubscription(Subscription::SINGLE);
        $optInEmail->setSubscriptionService(true);
        $optInEmail->setSubscriptionProductInformation(true);
        $optInEmail->setSubscriptionPromotions(true);

        yield 'Without optional parameters' => [
            [
                'email' => '<EMAIL>',
                'localeCode' => 'nl',
                'countryCode' => 'NL',
            ],
            $expectedMarketingSubscription,
        ];

        $expectedMarketingSubscription = new MarketingSubscription('<EMAIL>', 'nl', 'NL', new BusinessUnit());
        $optInEmail = $expectedMarketingSubscription->getOptInEmail();
        $optInEmail->setSubscription(Subscription::SINGLE);
        $optInEmail->setSource('website');
        $optInEmail->setSubscriptionService(true);
        $optInEmail->setSubscriptionProductInformation(true);
        $optInEmail->setSubscriptionPromotions(true);

        yield 'With all available parameters' => [
            [
                'email' => '<EMAIL>',
                'localeCode' => 'nl',
                'countryCode' => 'NL',
                'optInEmailSource' => 'website',
            ],
            $expectedMarketingSubscription,
        ];
    }

    /**
     * @dataProvider provideInvalidCreateRequestBody
     */
    public function testCannotCreateSubscriptionWithInvalidRequestBody(
        array $requestBody,
        array $expectedErrorResponse,
    ): void {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::API_URI,
            $requestBody
        );

        $this->assertSame(
            $expectedErrorResponse,
            $this->getResponseBody()
        );
    }

    public function provideInvalidCreateRequestBody(): iterable
    {
        $createRequestBody = static fn (
            string $email = '<EMAIL>',
            string $localeCode = 'nl',
            string $countryCode = 'NL',
        ) => [
            'email' => $email,
            'localeCode' => $localeCode,
            'countryCode' => $countryCode,
        ];

        $createExpectedRequestBody = static fn (
            string $constraint,
            string $message,
            string $property,
        ) => [
            'type' => 'about:blank',
            'title' => 'The request body contains errors.',
            'status' => 400,
            'detail' => 'Validation of JSON request body failed.',
            'violations' => [
                [
                    'constraint' => $constraint,
                    'message' => $message,
                    'property' => $property,
                ],
            ],
        ];

        $expectedValidationFailedRequestBody = $createExpectedRequestBody(
            'format',
            'Invalid email',
            'email',
        );
        $expectedValidationFailedRequestBody['violations'][] = [
            'constraint' => 'allOf',
            'message' => 'Failed to match all schemas',
            'property' => '',
        ];

        yield 'With invalid email address' => [
            $createRequestBody(email: 'invalid@<EMAIL>'),
            $expectedValidationFailedRequestBody,
        ];

        yield 'With invalid locale code' => [
            $createRequestBody(localeCode: 'xx'),
            $createExpectedRequestBody(
                'locale_code_exists',
                "The localeCode 'xx' does not exist.",
                'localeCode',
            ),
        ];

        yield 'With invalid country code' => [
            $createRequestBody(countryCode: 'XX'),
            $createExpectedRequestBody(
                'country_code_exists',
                "The countryCode 'XX' does not exist.",
                'countryCode',
            ),
        ];
    }

    /**
     * @dataProvider provideOptInEmailSubscription
     */
    public function testCanCreateSubscriptionWithExistingSubscription(
        Subscription $existingOptInEmailSubscription,
        Subscription $expectedOptInEmailSubscription,
    ): void {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = $this->client->getContainer()->get(EntityManagerInterface::class);

        /** @var BusinessUnit $businessUnit */
        $businessUnit = $this->entityManager->getRepository(BusinessUnit::class)->findOneBy(['code' => 'dokteronline']);
        $marketingSubscription = new MarketingSubscription('<EMAIL>', 'nl', 'NL', $businessUnit);
        $optInEmail = $marketingSubscription->getOptInEmail();
        $optInEmail->setSubscription($existingOptInEmailSubscription);
        $optInEmail->setSubscriptionService(true);
        $optInEmail->setSubscriptionProductInformation(true);
        $optInEmail->setSubscriptionPromotions(true);

        if (in_array($existingOptInEmailSubscription, [Subscription::SINGLE, Subscription::DOUBLE], true)) {
            $optInEmail->setSubscribedAt(new DateTimeImmutable());
        }

        $entityManager->persist($marketingSubscription);
        $entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::API_URI,
            [
                'email' => '<EMAIL>',
                'localeCode' => 'nl',
                'countryCode' => 'NL',
            ],
        );

        /** @var EntityManagerInterface $entityManager */
        $entityManager = $this->client->getContainer()->get(EntityManagerInterface::class);

        $actualMarketingSubscription = $entityManager->getRepository(MarketingSubscription::class)
            ->findOneBy(['emailAddress' => '<EMAIL>']);
        self::assertInstanceOf(MarketingSubscription::class, $actualMarketingSubscription);

        $expectedMarketingSubscription = new MarketingSubscription('<EMAIL>', 'nl', 'NL', new BusinessUnit());
        $optInEmail = $expectedMarketingSubscription->getOptInEmail();
        $optInEmail->setSubscription($expectedOptInEmailSubscription);
        $optInEmail->setSubscriptionService(true);
        $optInEmail->setSubscriptionProductInformation(true);
        $optInEmail->setSubscriptionPromotions(true);

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        self::assertEmpty($this->client->getResponse()->getContent());
        self::assertMarketingSubscriptionSame($expectedMarketingSubscription, $actualMarketingSubscription);
    }

    public function provideOptInEmailSubscription(): iterable
    {
        yield sprintf('Existing %s is updated to %s', Subscription::NONE->name, Subscription::SINGLE->name) => [
            Subscription::NONE,
            Subscription::SINGLE,
        ];

        yield sprintf('Existing %1$s stays %1$s', Subscription::SINGLE->name) => [
            Subscription::DOUBLE,
            Subscription::DOUBLE,
        ];

        yield sprintf('Existing %1$s is updated to %1$s', Subscription::DOUBLE->name) => [
            Subscription::SINGLE,
            Subscription::SINGLE,
        ];
    }

    private static function assertMarketingSubscriptionSame(
        MarketingSubscription $expected,
        MarketingSubscription $actual,
    ): void {
        $expectedOptInEmail = $expected->getOptInEmail();
        $actualOptInEmail = $actual->getOptInEmail();

        self::assertSame($expected->getCountryCode(), $actual->getCountryCode());
        self::assertSame($expected->getEmail(), $actual->getEmail());
        self::assertSame($expected->getFirstName(), $actual->getFirstName());
        self::assertSame($expected->getLastName(), $actual->getLastName());
        self::assertSame($expected->getLocaleCode(), $actual->getLocaleCode());
        self::assertSame($expected->getOptInDirect()->getMailAt(), $actual->getOptInDirect()->getMailAt());
        self::assertInstanceOf(DateTimeImmutable::class, $actualOptInEmail->getSubscribedAt());
        self::assertSame($expectedOptInEmail->getSource(), $actualOptInEmail->getSource());
        self::assertSame($expectedOptInEmail->getSubscriptionTimeoutUntil(), $actualOptInEmail->getSubscriptionTimeoutUntil());
        self::assertSame($expectedOptInEmail->getUnsubscribedAt(), $actualOptInEmail->getUnsubscribedAt());
        self::assertSame($expected->getOptInSmsAt(), $actual->getOptInSmsAt());
        self::assertSame($expected->getOptInSmsSubscription(), $actual->getOptInSmsSubscription());
        self::assertSame($expected->getOptInDirect()->isMail(), $actual->getOptInDirect()->isMail());
        self::assertSame($expectedOptInEmail->isSubscriptionPromotions(), $actualOptInEmail->isSubscriptionPromotions());
        self::assertSame($expectedOptInEmail->isSubscriptionProductInformation(), $actualOptInEmail->isSubscriptionProductInformation());
        self::assertSame($expectedOptInEmail->isSubscriptionService(), $actualOptInEmail->isSubscriptionService());

        self::assertEquals($expectedOptInEmail->getSubscription(), $actualOptInEmail->getSubscription());

        self::assertInstanceOf(DateTimeImmutable::class, $actual->getCreatedAt());
        self::assertInstanceOf(DateTimeImmutable::class, $actual->getUpdatedAt());
        self::assertInstanceOf(UuidInterface::class, $actual->getUuid());
    }
}
