<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\MarketingSubscription;

use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\MarketingSubscription\MarketingSubscription;
use App\Entity\MarketingSubscription\Subscription;
use App\Tests\Functional\Api\AbstractWebTestCase;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;
use Symfony\Component\HttpFoundation\Request;

final class VerifyMarketingSubscriptionControllerTest extends AbstractWebTestCase
{
    public function testCanVerifyMarketingSubscription(): void
    {
        /** @var BusinessUnit $businessUnit */
        $businessUnit = $this->entityManager->getRepository(BusinessUnit::class)->findOneBy(['code' => 'dokteronline']);
        $marketingSubscription = new MarketingSubscription(
            sprintf('<EMAIL>', microtime()),
            'nl',
            'NL',
            $businessUnit
        );
        $marketingSubscription->getOptInEmail()->setSubscription(Subscription::SINGLE);

        $this->entityManager->persist($marketingSubscription);
        $this->entityManager->flush();

        self::assertSame(Subscription::SINGLE, $marketingSubscription->getOptInEmail()->getSubscription());

        $this->client->request(
            Request::METHOD_POST,
            $this->getApiUri($marketingSubscription->getUuid()),
        );

        $this->assertResponseIsSuccessful();

        $this->entityManager->refresh($marketingSubscription);

        self::assertSame(Subscription::DOUBLE, $marketingSubscription->getOptInEmail()->getSubscription());
    }

    public function testGivesErrorWhenUuidDoesNotExist(): void
    {
        $this->client->request(
            Request::METHOD_POST,
            $this->getApiUri(Uuid::uuid4()),
        );

        $this->assertResponseStatusCodeSame(404);

        $response = $this->getResponseBody();

        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => 404,
        ];

        $this->assertSame($expectedResponse, $response);
    }

    private function getApiUri(UuidInterface $uuid): string
    {
        return sprintf('/api/shop/marketing-subscriptions/%s/verify', $uuid->toString());
    }
}
