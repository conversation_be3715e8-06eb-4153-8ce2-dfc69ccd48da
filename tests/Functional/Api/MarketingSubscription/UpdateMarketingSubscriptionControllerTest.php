<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\MarketingSubscription;

use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\MarketingSubscription\MarketingSubscription;
use App\Entity\MarketingSubscription\Subscription;
use App\Entity\MarketingSubscription\SubscriptionTimeout;
use App\Tests\Functional\Api\AbstractWebTestCase;
use DateTimeImmutable;
use DateTimeInterface;
use Ramsey\Uuid\UuidInterface;
use Symfony\Component\HttpFoundation\Request;

final class UpdateMarketingSubscriptionControllerTest extends AbstractWebTestCase
{
    public function testCanUpdateMarketingSubscription(): void
    {
        $marketingSubscription = $this->createMarketingSubscription();

        self::assertSame(Subscription::NONE, $marketingSubscription->getOptInEmail()->getSubscription());
        self::assertFalse($marketingSubscription->getOptInEmail()->isSubscriptionService());

        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getApiUri($marketingSubscription->getUuid()),
            [
                'optInEmail' => [
                    'subscriptionTypes' => [
                        'service' => true,
                        'productInformation' => true,
                        'promotions' => true,
                    ],
                    'subscriptionTimeout' => SubscriptionTimeout::THREE_MONTHS->value,
                ],
            ],
            []
        );

        $this->assertResponseIsSuccessful();

        $response = $this->getResponseBody();

        self::assertTrue($response['optInEmail']['subscriptionTypes']['service']);
        self::assertTrue($response['optInEmail']['subscriptionTypes']['productInformation']);
        self::assertTrue($response['optInEmail']['subscriptionTypes']['promotions']);
    }

    public function testCanUpdateMarketingSubscriptionTimeoutToForever(): void
    {
        $marketingSubscription = $this->createMarketingSubscription();
        $marketingSubscription->getOptInEmail()->setSubscriptionTimeoutUntil(new DateTimeImmutable());

        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getApiUri($marketingSubscription->getUuid()),
            [
                'optInEmail' => [
                    'subscriptionTypes' => [
                        'service' => true,
                        'productInformation' => true,
                        'promotions' => true,
                    ],
                    'subscriptionTimeout' => SubscriptionTimeout::FOREVER->value,
                ],
            ],
            []
        );

        $this->assertResponseIsSuccessful();

        $response = $this->getResponseBody();

        $this->assertArrayNotHasKey('subscriptionTimeoutUntil', $response['optInEmail']);
    }

    public function testUpdateWithNoTimeoutDoesntResetTimeout(): void
    {
        $marketingSubscription = $this->createMarketingSubscription();
        $marketingSubscription->getOptInEmail()->setSubscriptionTimeoutUntil((new DateTimeImmutable())->setDate(1980, 4, 3));

        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getApiUri($marketingSubscription->getUuid()),
            [
                'optInEmail' => [
                    'subscriptionTypes' => [
                        'service' => true,
                        'productInformation' => true,
                        'promotions' => true,
                    ],
                ],
                'localeCode' => 'en',
                'countryCode' => 'GB',
            ],
            []
        );

        $this->assertResponseIsSuccessful();

        $response = $this->getResponseBody();

        /** @var DateTimeInterface $subscriptionTimeout */
        $subscriptionTimeout = new DateTimeImmutable($response['optInEmail']['subscriptionTimeoutUntil']);
        self::assertSame('1980', $subscriptionTimeout->format('Y'));
        self::assertSame('04', $subscriptionTimeout->format('m'));
        self::assertSame('03', $subscriptionTimeout->format('d'));
    }

    public function testCanUpdateMarketingSubscriptionWithLocaleAndCountryCode(): void
    {
        $marketingSubscription = $this->createMarketingSubscription();

        self::assertSame(Subscription::NONE, $marketingSubscription->getOptInEmail()->getSubscription());
        self::assertFalse($marketingSubscription->getOptInEmail()->isSubscriptionService());

        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getApiUri($marketingSubscription->getUuid()),
            [
                'optInEmail' => [
                    'subscriptionTypes' => [
                        'service' => true,
                        'productInformation' => true,
                        'promotions' => true,
                    ],
                    'subscriptionTimeout' => '+1 month',
                ],
                'localeCode' => 'en',
                'countryCode' => 'GB',
            ],
            []
        );

        $this->assertResponseIsSuccessful();

        $response = $this->getResponseBody();

        self::assertTrue($response['optInEmail']['subscriptionTypes']['service']);
        self::assertTrue($response['optInEmail']['subscriptionTypes']['productInformation']);
        self::assertTrue($response['optInEmail']['subscriptionTypes']['promotions']);
        self::assertSame('en', $response['localeCode']);
        self::assertSame('GB', $response['countryCode']);
    }

    private function getApiUri(UuidInterface $uuid): string
    {
        return sprintf('/api/shop/marketing-subscriptions/%s', $uuid->toString());
    }

    private function createMarketingSubscription(): MarketingSubscription
    {
        /** @var BusinessUnit $businessUnit */
        $businessUnit = $this->entityManager->getRepository(BusinessUnit::class)->findOneBy(['code' => 'dokteronline']);
        $marketingSubscription = new MarketingSubscription(
            sprintf('<EMAIL>', microtime()),
            'nl',
            'NL',
            $businessUnit
        );

        $this->entityManager->persist($marketingSubscription);
        $this->entityManager->flush();

        return $marketingSubscription;
    }
}
