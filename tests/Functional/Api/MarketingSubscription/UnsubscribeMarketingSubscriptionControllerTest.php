<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\MarketingSubscription;

use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\MarketingSubscription\MarketingSubscription;
use App\Entity\MarketingSubscription\Subscription;
use App\Tests\Functional\Api\AbstractWebTestCase;
use DateTimeImmutable;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class UnsubscribeMarketingSubscriptionControllerTest extends AbstractWebTestCase
{
    public const string SUBSCRIBER_EMAIL = '<EMAIL>';
    public const string SUBSCRIBER_LOCALE_CODE = 'nl';
    public const string SUBSCRIBER_COUNTRY_CODE = 'NL';

    public function testCanGetMarketingSubscription(): void
    {
        /** @var BusinessUnit $businessUnit */
        $businessUnit = $this->entityManager->getRepository(BusinessUnit::class)->findOneBy(['code' => 'dokteronline']);
        $marketingSubscription = new MarketingSubscription(
            self::SUBSCRIBER_EMAIL,
            self::SUBSCRIBER_LOCALE_CODE,
            self::SUBSCRIBER_COUNTRY_CODE,
            $businessUnit
        );

        $marketingSubscription->getOptInEmail()->setSubscription(Subscription::DOUBLE);

        $this->entityManager->persist($marketingSubscription);
        $this->entityManager->flush();

        $this->client->request(
            Request::METHOD_POST,
            $this->getApiUri($marketingSubscription->getUuid()),
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        $marketingSubscription = $this->entityManager->find(MarketingSubscription::class, $marketingSubscription->getUuid()->toString());
        $this->assertInstanceOf(MarketingSubscription::class, $marketingSubscription);
        $this->assertEquals(Subscription::NONE, $marketingSubscription->getOptInEmail()->getSubscription());
        $this->assertInstanceOf(DateTimeImmutable::class, $marketingSubscription->getOptInEmail()->getUnsubscribedAt());
    }

    public function testThrowsNotFoundExceptionWhenMarketingSubscriptionDoesntExist(): void
    {
        $uuid = Uuid::uuid4();
        $this->client->request(
            Request::METHOD_POST,
            $this->getApiUri($uuid),
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    private function getApiUri(UuidInterface $uuid): string
    {
        return sprintf('/api/shop/marketing-subscriptions/%s/unsubscribe', $uuid->toString());
    }
}
