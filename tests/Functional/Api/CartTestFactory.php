<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api;

use App\Api\Command\Cart\AddItem;
use App\Api\Command\Cart\AddItems;
use App\Api\Command\Cart\PickupCart;
use App\Api\Command\Cart\RemoveItem;
use App\Api\CommandHandler\Cart\AddItemsHandler;
use App\Api\CommandHandler\Cart\PickupCartHandler;
use App\Api\CommandHandler\Cart\RemoveItemHandler;
use App\Entity\Addressing\Address;
use App\Entity\Channel\Channel;
use App\Entity\Channel\ChannelPricing;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Product\Product;
use App\Entity\Product\ProductAssociation;
use App\Entity\Product\ProductAssociationType;
use App\Entity\Product\ProductAttribute;
use App\Entity\Product\ProductAttributeValue;
use App\Entity\Product\ProductImage;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariant;
use App\Entity\Shipping\ShippingCategory;
use App\Entity\Supplier\Supplier;
use App\Entity\User\ShopUser;
use App\Repository\ProductRepository;
use App\Tests\Util\Factory\PromotionFactory;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use RuntimeException;
use Sylius\Bundle\ApiBundle\Command\Cart\AddItemToCart;
use Sylius\Bundle\ApiBundle\CommandHandler\Cart\AddItemToCartHandler;
use Sylius\Component\Core\Model\ProductInterface;
use Sylius\Component\Core\Model\PromotionCouponInterface;
use Sylius\Component\Core\Model\PromotionInterface;
use Sylius\Component\Product\Factory\ProductFactoryInterface;
use Sylius\Component\Product\Factory\ProductVariantFactoryInterface;
use Sylius\Component\Product\Model\ProductAttributeInterface;
use Sylius\Resource\Factory\FactoryInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Creates carts with the required product and product variants for functional tests.
 *
 * <AUTHOR> Nijens <<EMAIL>>
 */
class CartTestFactory
{
    public const string CHANNEL_CODE = 'dok_nl';
    public const string BLUECLINIC_CHANNEL_CODE = 'blueclinic_nl';
    public const string LOCALE_CODE = 'nl';
    public const string PRODUCT_CODE = 'viagra_test_special';
    public const string PRODUCT_VARIANT_CODE = 'viagra_25mg_4_test_special';
    public const string USER_EMAIL = '<EMAIL>';
    public const string USER_PASSWORD = '123';
    public const string CUSTOMER_POOL_CODE = 'dokteronline';
    public const string CONSULT_PRODUCT_CODE = 'consult_erectile_dysfunction';
    private const string CONSULT_PRODUCT_VARIANT_CODE = 'consult_erectile_dysfunction';

    private ContainerInterface $container;

    private EntityManagerInterface $entityManager;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
        /** @var EntityManagerInterface $entityManager */
        $entityManager = $container->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;
    }

    public function teardown(): void
    {
        $this->entityManager->close();
        unset($this->container, $this->entityManager);
    }

    public function createCart(string $channelCode, string $localeCode): Order
    {
        $pickupCart = new PickupCart($localeCode);
        $channel = $this->entityManager->getRepository(Channel::class)->findOneBy(['code' => $channelCode]);
        $pickupCart->setChannel($channel);

        /** @var PickupCartHandler $pickupCartHandler */
        $pickupCartHandler = $this->container->get(PickupCartHandler::class);
        $cart = $pickupCartHandler($pickupCart);

        $this->entityManager->flush();

        return $cart;
    }

    public function createCartWithProductVariants(
        string $channelCode = self::CHANNEL_CODE,
        string $localeCode = self::LOCALE_CODE,
        array $productVariantCodes = [self::PRODUCT_VARIANT_CODE],
        int $quantity = 1,
    ): Order {
        $cart = $this->createCart($channelCode, $localeCode);

        foreach ($productVariantCodes as $productVariantCode) {
            $isConsult = str_starts_with($productVariantCode, 'consult');
            $this->addItemToCart($cart, $productVariantCode, $isConsult ? 1 : $quantity);
        }

        return $cart;
    }

    public function addItemToCart(
        Order $cart,
        string $productVariantCode,
        int $quantity,
    ): void {
        $addItemToCart = new AddItemToCart($productVariantCode, $quantity);
        $addItemToCart->setOrderTokenValue($cart->getTokenValue());

        /** @var AddItemToCartHandler $addItemToCartHandler */
        $addItemToCartHandler = $this->container->get(AddItemToCartHandler::class);
        $addItemToCartHandler($addItemToCart);

        $this->entityManager->flush();
    }

    public function removeItemFromCart(
        Order $cart,
        OrderItem $item,
    ): void {
        $removeItemFromCart = new RemoveItem();
        $removeItemFromCart->setOrderItem($item);
        $removeItemFromCart->setOrder($cart);

        /** @var RemoveItemHandler $removeItemFromCartHandler */
        $removeItemFromCartHandler = $this->container->get(RemoveItemHandler::class);
        $removeItemFromCartHandler($removeItemFromCart);

        $this->entityManager->flush();
    }

    public function addRelatedItemToCart(
        Order $cart,
        string $consultProductVariantCode,
        int $consultProductVariantQuantity,
        string $productVariantCode,
        int $productVariantQuantity,
        string $parentProductVariantCode,
    ): void {
        $addItems = [
            new AddItem($consultProductVariantCode, $consultProductVariantQuantity),
            new AddItem($productVariantCode, $productVariantQuantity, $parentProductVariantCode),
        ];

        /** @var AddItemsHandler $addItemsHandler */
        $addItemsHandler = $this->container->get(AddItemsHandler::class);
        $command = new AddItems($addItems);
        $command->setOrder($cart);

        $addItemsHandler($command);

        $this->entityManager->flush();
    }

    public function createConsultForProductVariants(
        array $productVariants = [],
        string $channelCode = self::CHANNEL_CODE,
        string $productCode = self::CONSULT_PRODUCT_CODE,
        string $productVariantCode = self::CONSULT_PRODUCT_VARIANT_CODE,
        string $supplierIdentifier = 'blueclinic',
        array $maxQuantitiesPerOrder = [],
        int $price = 1000,
    ): void {
        /** @var ProductFactoryInterface $productFactory */
        $productFactory = $this->container->get('sylius.factory.product');
        /** @var ProductInterface $consultProduct */
        $consultProduct = $productFactory->createNew();
        $consultProduct->setCode($productCode);
        $consultProduct->setName($productCode);
        $consultProduct->setSlug($productCode);
        $consultProduct->addChannel(
            $this->entityManager->getRepository(Channel::class)->findOneByCode($channelCode)
        );

        $productAttributeRepository = $this->entityManager->getRepository(ProductAttribute::class);
        $productTypeAttribute = $productAttributeRepository->findOneBy(['code' => ProductAttribute::ATTRIBUTE_CODE_TYPE]);
        if (!$productTypeAttribute instanceof ProductAttributeInterface) {
            throw new RuntimeException(sprintf('Configuration error! Missing product attribute with code "%s"', ProductAttribute::ATTRIBUTE_CODE_TYPE));
        }

        $typeAttributeValue = new ProductAttributeValue();
        $typeAttributeValue->setAttribute($productTypeAttribute);
        $typeAttributeValue->setValue([ProductType::CONSULT->value]);
        $consultProduct->addAttribute($typeAttributeValue);

        $supplier = $this->entityManager->getRepository(Supplier::class)->findOneBy(
            ['identifier' => $supplierIdentifier]
        );
        $this->entityManager->persist($consultProduct);

        $this->createProductVariant($productVariantCode, $supplier, true, $maxQuantitiesPerOrder, $channelCode, $price, $consultProduct);
        $this->entityManager->flush();

        $associationType = $this->entityManager->getRepository(ProductAssociationType::class)->findOneBy(['code' => 'consult_products']);
        $productAssociation = new ProductAssociation();
        $productAssociation->setType($associationType);
        $productAssociation->addAssociatedProduct($consultProduct);

        foreach ($productVariants as $productVariant) {
            /** @var ProductVariant $variant */
            $variant = $this->entityManager->getRepository(ProductVariant::class)->findOneBy(['code' => $productVariant]);
            /** @var ProductInterface $product */
            $product = $variant->getProduct();
            $product->addAssociation($productAssociation);
        }
        $this->entityManager->persist($productAssociation);
        $this->entityManager->flush();
    }

    public function createProductAndProductVariants(
        string $channelCode = self::CHANNEL_CODE,
        string $productCode = self::PRODUCT_CODE,
        array $productVariantCodes = [self::PRODUCT_VARIANT_CODE],
        string $supplierIdentifier = 'apotheek-bad-nieuweschans',
        bool $prescriptionRequired = false,
        array $maxQuantitiesPerOrder = [],
        int $price = 1000,
        ProductType $productType = ProductType::MEDICATION,
    ): void {
        /** @var FactoryInterface $productTranslationFactory */
        $productImageFactory = $this->container->get('sylius.factory.product_image');
        /** @var ProductImage $productImage */
        $productImage = $productImageFactory->createNew();
        $productImage->setPath('path/to');

        /** @var ProductRepository $productRepository */
        $productRepository = $this->entityManager->getRepository(Product::class);
        $product = $productRepository->findOneByCode($productCode);
        if (!$product instanceof ProductInterface) {
            /** @var ProductFactoryInterface $productFactory */
            $productFactory = $this->container->get('sylius.factory.product');
            /** @var ProductInterface $product */
            $product = $productFactory->createNew();
            $product->setCode($productCode);
            $this->entityManager->persist($product);
        }

        $product->setName($productCode);
        $product->setSlug($productCode);
        $product->addImage($productImage);
        $product->addChannel(
            $this->entityManager->getRepository(Channel::class)->findOneByCode($channelCode)
        );

        if ($productType instanceof ProductType) {
            $productAttributeRepository = $this->entityManager->getRepository(ProductAttribute::class);
            $productTypeAttribute = $productAttributeRepository->findOneBy(
                ['code' => ProductAttribute::ATTRIBUTE_CODE_TYPE]
            );
            if (!$productTypeAttribute instanceof ProductAttributeInterface) {
                throw new RuntimeException(sprintf('Configuration error! Missing product attribute with code "%s"', ProductAttribute::ATTRIBUTE_CODE_TYPE));
            }

            $typeAttributeValue = new ProductAttributeValue();
            $typeAttributeValue->setAttribute($productTypeAttribute);
            $typeAttributeValue->setValue([$productType->value]);
            $product->addAttribute($typeAttributeValue);
        }

        $supplier = null;
        if (!empty($supplierIdentifier)) {
            $supplier = $this->entityManager->getRepository(Supplier::class)->findOneBy([
                'identifier' => $supplierIdentifier,
            ]);
        }

        foreach ($productVariantCodes as $productVariantCode) {
            $this->createProductVariant(
                $productVariantCode,
                $supplier,
                $prescriptionRequired,
                $maxQuantitiesPerOrder,
                $channelCode,
                $price,
                $product
            );
        }

        $this->entityManager->flush();
    }

    public function addAttributeToProduct($attributeCode = ProductAttribute::ATTRIBUTE_CODE_ADDICTIVE, $productCode = self::PRODUCT_CODE): void
    {
        $product = $this->entityManager->getRepository(Product::class)->findOneBy([
            'code' => $productCode,
        ]);

        $tagAttribute = $this->entityManager->getRepository(ProductAttribute::class)->findOneBy([
            'code' => $attributeCode,
        ]);

        if (!$tagAttribute instanceof ProductAttribute) {
            return;
        }

        $productAttributeValue = new ProductAttributeValue();
        $productAttributeValue->setAttribute($tagAttribute);
        $productAttributeValue->setValue(true);

        $product->addAttribute($productAttributeValue);

        $this->entityManager->flush();
    }

    public function createCouponPromotion(string $channelCode, string $couponCode = 'TEST'): PromotionCouponInterface
    {
        $promotion = PromotionFactory::create([
            'name' => 'Test',
            'code' => 'Test',
            'couponBased' => true,
            'channels' => [$this->entityManager->getRepository(Channel::class)->findOneByCode($channelCode)],
        ]);

        $promotionCouponFactory = $this->container->get('sylius.custom_factory.promotion_coupon');
        $promotionCoupon = $promotionCouponFactory->createForPromotion($promotion);
        $promotionCoupon->setCode($couponCode);

        $this->entityManager->persist($promotion);
        $this->entityManager->persist($promotionCoupon);
        $this->entityManager->flush();

        return $promotionCoupon;
    }

    public function createPromotion(string $channelCode, string $name = 'Test promotion'): PromotionInterface
    {
        $promotion = PromotionFactory::create([
            'name' => $name,
            'code' => $name,
            'channels' => [$this->entityManager->getRepository(Channel::class)->findOneByCode($channelCode)],
        ]);

        $this->entityManager->persist($promotion);
        $this->entityManager->flush();

        return $promotion;
    }

    public function createCustomer(string $customerPool = self::CUSTOMER_POOL_CODE): Customer
    {
        /** @var CustomerPool $customerPool */
        $customerPool = $this->entityManager
            ->getRepository(CustomerPool::class)
            ->findOneBy(['code' => $customerPool]);

        $customer = new Customer();
        $customer->setBirthday(new DateTimeImmutable('1997-03-09'));
        $customer->setFirstName('Hans');
        $customer->setLastName('Hans');
        $customer->setEmail(self::USER_EMAIL);
        $customer->setCustomerPool($customerPool);
        $customer->setGender(Customer::MALE_GENDER);

        return $customer;
    }

    public function createAddress(): Address
    {
        $address = new Address();
        $address->setFirstName('Hans');
        $address->setLastName('Test');
        $address->setStreet('Test street 4');
        $address->setPostcode('Test postcode');
        $address->setCity('Breda');
        $address->setCountryCode('NL');
        $address->setPhoneNumber('+3112345678');

        return $address;
    }

    public function createUserForCustomer(Customer $customer, string $password = self::USER_PASSWORD): ShopUser
    {
        $user = new ShopUser();
        $user->setUsername($customer->getEmail());
        $user->setUsernameCanonical($customer->getEmail());
        $user->setPlainPassword($password);
        $user->enable();

        $customer->setUser($user);

        return $user;
    }

    private function createProductVariant(mixed $productVariantCode, ?Supplier $supplier, bool $prescriptionRequired, array $maxQuantitiesPerOrder, string $channelCode, int $price, ProductInterface $product): void
    {
        /** @var ProductVariantFactoryInterface<ProductVariant> $productVariantFactory */
        $productVariantFactory = $this->container->get('sylius.factory.product_variant');

        $productVariant = $productVariantFactory->createNew();
        $productVariant->setCode($productVariantCode);
        $productVariant->setName('Viagra 25mg 4 tablets');
        $productVariant->setCaption('Including doctor consult and service fees');
        $productVariant->setCostPrice(100);
        $productVariant->setSupplier($supplier);
        $productVariant->setPrescriptionRequired($prescriptionRequired);

        $shippingCategory = $this->entityManager->getRepository(ShippingCategory::class)->findOneBy([
            'code' => $prescriptionRequired ? ShippingCategory::SHIPPING_CATEGORY_RX_MEDICATION : ShippingCategory::SHIPPING_CATEGORY_OTC_MEDICATION,
        ]);
        $productVariant->setShippingCategory($shippingCategory);

        if (array_key_exists($productVariantCode, $maxQuantitiesPerOrder)) {
            $productVariant->setMaximumQuantityPerOrder($maxQuantitiesPerOrder[$productVariantCode]);
        }

        $productVariantChannelPricing = new ChannelPricing();
        $productVariantChannelPricing->setChannelCode($channelCode);
        $productVariantChannelPricing->setPrice($price);

        $productVariant->addChannelPricing($productVariantChannelPricing);
        $product->addVariant($productVariant);

        $this->entityManager->persist($productVariant);
    }
}
