<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\StateMachine;

use App\Entity\Order\Order;
use App\Entity\Shipping\Shipment;
use App\StateMachine\OrderAftercareStates;
use App\StateMachine\OrderShippingStates;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ExchangeRateTestFactory;
use App\Tests\Mocks\AnamnesisServiceClient;
use Ramsey\Uuid\Uuid;
use SM\Factory\FactoryInterface;
use Sylius\Component\Core\OrderCheckoutStates;
use Sylius\Component\Core\OrderCheckoutTransitions;
use Sylius\Component\Core\OrderPaymentStates;
use Sylius\Component\Core\OrderShippingTransitions;
use Sylius\Component\Order\Model\OrderInterface as SyliusOrderInterface;
use Sylius\Component\Order\OrderTransitions;
use Sylius\Component\Shipping\Model\ShipmentInterface;

class AftercareStateMachineTest extends AbstractWebTestCase
{
    private const string CHANNEL_CODE = 'dok_nl';

    private const string LOCALE_CODE = 'nl';

    private CartTestFactory $cartTestFactory;

    private FactoryInterface $stateMachineFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cartTestFactory = new CartTestFactory(static::getContainer());
        $this->stateMachineFactory = static::getContainer()->get(FactoryInterface::class);
    }

    public function testNewOrdersHaveAftercareStateCart(): void
    {
        $this->cartTestFactory->createProductAndProductVariants();
        $cart = $this->cartTestFactory->createCartWithProductVariants();

        static::assertSame(
            OrderAftercareStates::STATE_CART,
            $cart->getAftercareState()
        );
    }

    /**
     * @dataProvider provideCompletedOrders
     */
    public function testCompletedOrderHasCorrectState(bool $prescriptionRequired, bool $isFollowUpOrder, string $expectedAftercareState): void
    {
        $exchangeRateTestFactory = new ExchangeRateTestFactory(self::getContainer());
        $exchangeRateTestFactory->createExchangeRate();

        $order = $this->createOrder($prescriptionRequired);
        $order->setCheckoutState(OrderCheckoutStates::STATE_PAYMENT_SKIPPED);
        $order->setCheckoutTermsAnswers([
            'declare_information_truthfully',
            'terms_and_conditions',
            'collect_medical_information',
            'transfer_prescription',
            'pharmacy_blueclinic',
        ]);

        if ($isFollowUpOrder) {
            $followUpOrder = $this->cartTestFactory->createCart(self::CHANNEL_CODE, self::LOCALE_CODE);
            $order->setFollowUpOrder($followUpOrder);
        }

        $this->entityManager->flush();

        $stateMachine = $this->stateMachineFactory->get($order, OrderCheckoutTransitions::GRAPH);
        $stateMachine->apply(OrderCheckoutTransitions::TRANSITION_COMPLETE);

        static::assertSame($expectedAftercareState, $order->getAftercareState());
    }

    public function provideCompletedOrders(): iterable
    {
        yield 'otc order applies skip transition' => [false, false, OrderAftercareStates::STATE_SKIPPED];
        yield 'follow-up order applies skip transition' => [false, true, OrderAftercareStates::STATE_SKIPPED];
        yield 'follow-up order with prescription required applies skip transition' => [false, true, OrderAftercareStates::STATE_SKIPPED];
        yield 'prescription required order applies create transition' => [true, false, OrderAftercareStates::STATE_AWAITING_SHIPMENT];
    }

    public function testShippedOrderHasCorrectState(): void
    {
        $order = $this->createOrder(true);
        $order->setState(SyliusOrderInterface::STATE_NEW);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setShippingState(OrderShippingStates::STATE_PENDING);
        $order->setAftercareState(OrderAftercareStates::STATE_AWAITING_SHIPMENT);

        /** @var Shipment $shipment */
        $shipment = $order->getShipments()->first();
        $shipment->setState(ShipmentInterface::STATE_SHIPPED);

        $this->entityManager->flush();

        $stateMachine = $this->stateMachineFactory->get($order, OrderShippingTransitions::GRAPH);
        $stateMachine->apply(OrderShippingTransitions::TRANSITION_SHIP);

        static::assertSame(OrderAftercareStates::STATE_OPENED, $order->getAftercareState());
    }

    /**
     * @dataProvider providePlacedOrders
     */
    public function testCancelledOrderHasCorrectState($aftercareState, $expectedAftercareState): void
    {
        $order = $this->createOrder(true);
        $order->setState(SyliusOrderInterface::STATE_NEW);
        $order->setAftercareState($aftercareState);

        $this->entityManager->flush();

        $stateMachine = $this->stateMachineFactory->get($order, OrderTransitions::GRAPH);
        $stateMachine->apply(OrderTransitions::TRANSITION_CANCEL);

        static::assertSame($expectedAftercareState, $order->getAftercareState());
    }

    public function providePlacedOrders(): iterable
    {
        yield 'prescription required placed order' => [OrderAftercareStates::STATE_AWAITING_SHIPMENT, OrderAftercareStates::STATE_CLOSED];
        yield 'otc placed order' => [OrderAftercareStates::STATE_SKIPPED, OrderAftercareStates::STATE_SKIPPED];
    }

    private function createOrder($prescriptionRequired = false): Order
    {
        $this->cartTestFactory->createProductAndProductVariants(prescriptionRequired: $prescriptionRequired);
        $order = $this->cartTestFactory->createCartWithProductVariants();
        $order->setMedicalQuestionnaire(Uuid::fromString(AnamnesisServiceClient::VALID_UUID));
        $order->setCustomer($this->cartTestFactory->createCustomer());
        $order->setBillingAddress($this->cartTestFactory->createAddress());
        $order->setNumber('001');

        return $order;
    }
}
