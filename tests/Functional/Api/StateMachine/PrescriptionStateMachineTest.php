<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\StateMachine;

use App\Entity\Addressing\Zone;
use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShippingMethod;
use App\StateMachine\OrderFraudCheckStates;
use App\StateMachine\OrderPrescriptionStates;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ExchangeRateTestFactory;
use SM\Factory\FactoryInterface;
use Sylius\Component\Core\OrderCheckoutStates;
use Sylius\Component\Core\OrderPaymentStates;
use Sylius\Component\Core\OrderPaymentTransitions;
use Sylius\Component\Order\Model\OrderInterface as BaseOrderInterface;
use Sylius\Component\Order\OrderTransitions;
use Sylius\Component\Payment\Model\PaymentInterface;

class PrescriptionStateMachineTest extends AbstractWebTestCase
{
    private const string CHANNEL_CODE = 'dok_nl';

    private const string LOCALE_CODE = 'nl';

    private const string PRODUCT_CODE = 'viagra_test_special';

    private const string PRODUCT_VARIANT_CODE = 'viagra_25mg_4_test_special';

    private CartTestFactory $cartTestFactory;

    private FactoryInterface $stateMachineFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cartTestFactory = new CartTestFactory(static::getContainer());
        $this->stateMachineFactory = static::getContainer()->get(FactoryInterface::class);
    }

    public function testNewOrdersHavePrescriptionStateCart(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        $cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            self::LOCALE_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        static::assertSame(
            OrderPrescriptionStates::STATE_CART,
            $cart->getPrescriptionState()
        );
    }

    public function testPrescriptionStateIsMarkedAsNewWhenOrderCreated(): void
    {
        $exchangeRateTestFactory = new ExchangeRateTestFactory(self::getContainer());
        $exchangeRateTestFactory->createExchangeRate();

        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE],
            'blueclinic',
            true
        );

        $cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            self::LOCALE_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );
        $cart->setCustomer($this->cartTestFactory->createCustomer());
        $cart->setBillingAddress($this->cartTestFactory->createAddress());
        $cart->setCheckoutState(OrderCheckoutStates::STATE_COMPLETED);
        $cart->setNumber('001');

        $this->entityManager->flush();

        $stateMachine = $this->stateMachineFactory->get($cart, OrderTransitions::GRAPH);

        $stateMachine->apply(OrderTransitions::TRANSITION_CREATE);

        static::assertSame(
            OrderPrescriptionStates::STATE_AWAITING_PAYMENT,
            $cart->getPrescriptionState()
        );
    }

    public function testPrescriptionStateIsMarkedAsSkippedWhenOrderIsPaidAndPrescriptionIsNotRequired(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        $cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            self::LOCALE_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );
        $cart->setCustomer($this->cartTestFactory->createCustomer());
        $cart->setBillingAddress($this->cartTestFactory->createAddress());
        $cart->setPaymentState(OrderPaymentStates::STATE_CART);
        $cart->setPrescriptionState(OrderPrescriptionStates::STATE_CART);
        $cart->setPaymentState(OrderPaymentStates::STATE_AWAITING_PAYMENT);
        $cart->getLastPayment()?->setState(PaymentInterface::STATE_COMPLETED);
        $cart->setFraudCheckState(OrderFraudCheckStates::STATE_SKIPPED);

        $zone = new Zone();
        $zone->setCode('test-zone');
        $zone->setName('Test zone');
        $zone->setType('country');

        $shippingMethod = new ShippingMethod();
        $shippingMethod->setCode('test-shipping-method');
        $shippingMethod->setCurrentLocale(self::LOCALE_CODE);
        $shippingMethod->setFallbackLocale('en');
        $shippingMethod->setName('Test shipping method');
        $shippingMethod->setCalculator('test calculator');
        $shippingMethod->setZone($zone);

        $shipment = new Shipment();
        $shipment->setMethod($shippingMethod);
        $shipment->setState('ready');

        $cart->addShipment($shipment);

        $cart->setBillingAddress($this->cartTestFactory->createAddress());
        $cart->setShippingAddress($this->cartTestFactory->createAddress());

        $this->entityManager->persist($zone);
        $this->entityManager->persist($shippingMethod);
        $this->entityManager->persist($shipment);
        $this->entityManager->flush();

        $stateMachine = $this->stateMachineFactory->get($cart, OrderPaymentTransitions::GRAPH);

        $stateMachine->apply(OrderPaymentTransitions::TRANSITION_PAY);

        static::assertSame(
            OrderPrescriptionStates::STATE_SKIPPED,
            $cart->getPrescriptionState()
        );
    }

    public function testPrescriptionStateIsMarkedAsCancelledWhenOrderIsCancelled(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        $cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            self::LOCALE_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );
        $cart->setCustomer($this->cartTestFactory->createCustomer());
        $cart->setBillingAddress($this->cartTestFactory->createAddress());
        $cart->setState(BaseOrderInterface::STATE_NEW);
        $cart->setPrescriptionState(OrderPrescriptionStates::STATE_AWAITING_PAYMENT);
        $cart->getLastPayment()?->setState(PaymentInterface::STATE_COMPLETED);

        $this->entityManager->flush();

        $stateMachine = $this->stateMachineFactory->get($cart, OrderTransitions::GRAPH);

        $stateMachine->apply(OrderTransitions::TRANSITION_CANCEL);

        static::assertSame(
            OrderPrescriptionStates::STATE_CANCELLED,
            $cart->getPrescriptionState()
        );
    }

    public function testPrescriptionStateIsNotMarkedAsCancelledWhenSkipped(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        $cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            self::LOCALE_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );
        $cart->setCustomer($this->cartTestFactory->createCustomer());
        $cart->setBillingAddress($this->cartTestFactory->createAddress());
        $cart->setState(BaseOrderInterface::STATE_NEW);
        $cart->setPrescriptionState(OrderPrescriptionStates::STATE_SKIPPED);

        $this->entityManager->flush();

        $stateMachine = $this->stateMachineFactory->get($cart, OrderTransitions::GRAPH);

        $stateMachine->apply(OrderTransitions::TRANSITION_CANCEL);

        static::assertSame(
            OrderPrescriptionStates::STATE_SKIPPED,
            $cart->getPrescriptionState()
        );
    }
}
