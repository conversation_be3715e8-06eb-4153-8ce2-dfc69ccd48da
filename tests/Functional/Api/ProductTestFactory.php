<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api;

use App\Entity\Addressing\Country;
use App\Entity\Channel\Channel;
use App\Entity\Channel\ChannelPricing;
use App\Entity\Product\ProductAssociation;
use App\Entity\Product\ProductAttribute;
use App\Entity\Product\ProductAttributeValue;
use App\Entity\Product\ProductInterface;
use App\Entity\Product\ProductOption;
use App\Entity\Product\ProductOptionTranslation;
use App\Entity\Product\ProductOptionValue;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariant;
use App\Entity\Shipping\ShippingCategory;
use App\Entity\Supplier\Supplier;
use App\Entity\Supplier\SupplierCountryShipping;
use App\Entity\Supplier\SupplierInterface;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use RuntimeException;
use Sylius\Bundle\CoreBundle\Doctrine\ORM\ProductOptionRepository;
use Sylius\Bundle\CoreBundle\Fixture\Factory\ExampleFactoryInterface;
use Sylius\Bundle\CoreBundle\Fixture\Factory\ProductAssociationExampleFactory;
use Sylius\Component\Attribute\Model\AttributeValueInterface;
use Sylius\Component\Channel\Repository\ChannelRepositoryInterface;
use Sylius\Component\Core\Model\Product;
use Sylius\Component\Core\Model\ProductVariantInterface;
use Sylius\Component\Product\Factory\ProductFactoryInterface;
use Sylius\Component\Product\Factory\ProductVariantFactoryInterface;
use Sylius\Component\Product\Model\ProductAttributeInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\String\Slugger\SluggerInterface;
use Webmozart\Assert\Assert;

class ProductTestFactory
{
    public const int DEFAULT_SHIPPING_COST = 25;
    public const int DEFAULT_HANDLING_FEE = 50;
    public const int DEFAULT_PRODUCT_COST = 100;
    public const int DEFAULT_PRODUCT_VARIANT_UNIT_PRICE = 1000;

    private const string SLUG_SEPARATOR = '-';

    private const array DEFAULT_VARIANT_OPTIONS = [
        'name' => 'Viagra 25mg 4 tablets',
        'caption' => 'Including doctor consult and service fees',
    ];

    private EntityManagerInterface $objectManager;
    private ExampleFactoryInterface $productAssociationExampleFactory;
    private SluggerInterface $slugger;

    /**
     * @var array<string, SupplierCountryShipping>
     */
    private array $supplierCountryShipping = [];

    /**
     * @param array<string> $channelCodes
     */
    public function __construct(
        private readonly ContainerInterface $container,
        private readonly array $channelCodes,
    ) {
        /** @var SluggerInterface $slugger */
        $slugger = $this->container->get(SluggerInterface::class);
        $this->slugger = $slugger;
        $this->objectManager = $container->get(EntityManagerInterface::class);
        $this->productAssociationExampleFactory = new ProductAssociationExampleFactory(
            $container->get('sylius.factory.product_association'),
            $container->get('sylius.repository.product_association_type'),
            $container->get('sylius.repository.product'),
        );
    }

    /**
     * @param array<int, string> $serviceProductCodes
     *
     * @return array<int, ProductVariant>
     */
    public function prepareDatabaseWithServiceProducts(
        array $serviceProductCodes = [],
        string $locale = 'en',
    ): array {
        $createdVariants = [];

        foreach ($serviceProductCodes as $serviceProductCode) {
            $product = $this->createProduct(
                $serviceProductCode,
                [],
                $serviceProductCode,
                $locale,
                null,
                ProductType::SERVICE
            );

            $product->setName($serviceProductCode);
            $product->setSlug($serviceProductCode);

            $variant = $this->createProductVariant(
                $product,
                $serviceProductCode,
                []
            );
            $variant->setName($serviceProductCode);
            $variant->setMaximumQuantityPerOrder(1);
            $variant->setPrescriptionRequired(false);

            // Product Variant defaults to having a 'en' translation, we need an 'nl' translation as well.
            $this->createProductVariantTranslations($variant, [
                'nl' => [
                    'name' => $serviceProductCode,
                ],
            ]);

            if ($serviceProductCode === ProductInterface::SERVICE_BLUECLINIC_CODE) {
                $shippingCategoryCode = 'blueclinic';
            } else {
                $shippingCategoryCode = 'prescription_mailer';
            }

            $shippingCategory = $this->objectManager->getRepository(ShippingCategory::class)->findOneBy([
                'code' => $shippingCategoryCode,
            ]);
            $variant->setShippingCategory($shippingCategory);

            $this->objectManager->persist($product);
            $this->objectManager->persist($variant);

            $createdVariants[] = $variant;
        }

        return $createdVariants;
    }

    /**
     * @param array{array{
     *     code: string,
     *     name: string,
     *     variantCode?: string,
     *     variantName?: string,
     *     prescriptionRequired?: bool,
     *     variantEnabled?: bool,
     *     maxQuantity?: int,
     *     supplierName?: string,
     *     supplierCode?: string,
     * }} $regularProducts
     * @param ?array{array{
     *     code: string,
     *     name: string,
     *     variantCode?: string,
     *     variantName?: string,
     *     prescriptionRequired?: bool,
     *     variantEnabled?: bool,
     * }} $consultProducts
     *
     * @return array<int, ProductVariant>
     */
    public function prepareDatabaseWithRegularAndConsultProducts(
        array $regularProducts,
        array $consultProducts = null,
        string $locale = 'en',
        ?string $supplierCode = null,
    ): array {
        $createdProductVariants = [];

        // Make sure we have a type attribute for storing product types.
        $typeAttribute = $this->objectManager->getRepository(ProductAttribute::class)->findOneBy(
            ['code' => ProductAttribute::ATTRIBUTE_CODE_TYPE]
        );
        if (!$typeAttribute instanceof ProductAttributeInterface) {
            $typeAttribute = new ProductAttribute();
            $typeAttribute->setCode(ProductAttribute::ATTRIBUTE_CODE_TYPE);
            $typeAttribute->setStorageType(AttributeValueInterface::STORAGE_JSON);
            $this->objectManager->persist($typeAttribute);
        }

        $countries = [];
        foreach ($this->channelCodes as $channelCode) {
            $channel = $this->objectManager->getRepository(Channel::class)->findOneBy(['code' => $channelCode]);
            $countries = array_merge($channel->getCountries()->toArray(), $countries);
        }

        $supplier = $this->createOrGetSupplier($supplierCode ?? 'sup-'.random_int(10, 99), countries: $countries);

        foreach ($regularProducts as $productData) {
            $product = $this->createProduct(
                $productData['code'],
                [],
                $productData['name'],
                $locale
            );

            if (!empty($productData['supplierCode']) && !empty($productData['supplierName'])) {
                $supplier = $this->createOrGetSupplier(
                    $productData['supplierCode'],
                    $productData['supplierName'],
                    $countries,
                );
            }

            $variant = $this->createProductVariant(
                $product,
                $productData['variantCode'] ?? $productData['code'],
                [],
                supplier: $supplier,
                prescriptionRequired: $productData['prescriptionRequired'] ?? false
            );
            $variant->setCostPrice(self::DEFAULT_PRODUCT_COST);
            $variantName = $productData['variantName'] ?? $productData['name'];
            $variant->setName($variantName);
            $variant->setMaximumQuantityPerOrder($productData['maxQuantity'] ?? 1);
            $variant->setPrescriptionRequired($productData['prescriptionRequired'] ?? false);
            $variant->setEnabled($productData['variantEnabled'] ?? true);

            // Product Variant defaults to having an 'en' translation, we need a 'nl' translation as well.
            $this->createProductVariantTranslations($variant, [
                'nl' => [
                    'name' => $variantName,
                ],
            ]);

            $shippingCategory = $this->objectManager->getRepository(ShippingCategory::class)->findOneBy([
                'code' => $variant->isPrescriptionRequired() ? ShippingCategory::SHIPPING_CATEGORY_RX_MEDICATION : ShippingCategory::SHIPPING_CATEGORY_OTC_MEDICATION,
            ]);
            $variant->setShippingCategory($shippingCategory);

            $this->objectManager->persist($product);
            $this->objectManager->persist($variant);

            $createdProductVariants[] = $variant;
        }

        if (is_array($consultProducts)) {
            foreach ($consultProducts as $productData) {
                $product = $this->createProduct(
                    $productData['code'],
                    [],
                    $productData['name'],
                    $locale,
                    null,
                    ProductType::CONSULT
                );

                $product->setName($productData['name']);
                $product->setSlug($productData['code']);

                $variant = $this->createProductVariant(
                    $product,
                    $productData['variantCode'] ?? $productData['code'],
                    [],
                    prescriptionRequired: $productData['prescriptionRequired'] ?? false
                );
                $variant->setCostPrice(100);
                $variantName = $productData['variantName'] ?? $productData['name'];
                $variant->setName($variantName);
                $variant->setMaximumQuantityPerOrder(1);
                $variant->setPrescriptionRequired(true);
                $variant->setEnabled($productData['variantEnabled'] ?? true);

                // Product Variant defaults to having an 'en' translation, we need a 'nl' translation as well.
                $this->createProductVariantTranslations($variant, [
                    'nl' => [
                        'name' => $variantName,
                    ],
                ]);

                $shippingCategory = $this->objectManager->getRepository(ShippingCategory::class)->findOneBy([
                    'code' => ShippingCategory::SHIPPING_CATEGORY_RX_MEDICATION,
                ]);
                $variant->setShippingCategory($shippingCategory);

                $this->objectManager->persist($product);
                $this->objectManager->persist($variant);
                $createdProductVariants[] = $variant;
            }
        }

        $this->objectManager->flush();

        return $createdProductVariants;
    }

    public function createProduct(
        string $productCode,
        array $productOptions,
        string $productName = 'Viagra test',
        string $locale = 'en',
        ?string $channelCode = null,
        ProductType $productType = ProductType::MEDICATION,
    ): ProductInterface {
        $productFactory = $this->container->get(ProductFactoryInterface::class);

        /** @var ProductInterface $product */
        $product = $productFactory->createNew();
        $product->setVariantSelectionMethod(Product::VARIANT_SELECTION_MATCH);

        $slug = $this->slugger->slug($productName, self::SLUG_SEPARATOR, $locale)->toString();

        $productTranslation = $product->getTranslation($locale);
        $productTranslation->setName($productName);
        $productTranslation->setSlug(strtolower($slug));

        // Product should always have an English translation. Enforced by the PIM system.
        if ($locale !== 'en') {
            $slug = $this->slugger->slug($productName, self::SLUG_SEPARATOR, 'en')->toString();

            $productTranslation = $product->getTranslation('en');
            $productTranslation->setName($productName);
            $productTranslation->setSlug(strtolower($slug));
        }

        // Product should always have a Dutch translation. Enforced by the PIM system.
        if ($locale !== 'nl') {
            $slug = $this->slugger->slug($productName, self::SLUG_SEPARATOR, 'nl')->toString();

            $productTranslation = $product->getTranslation('nl');
            $productTranslation->setName($productName);
            $productTranslation->setSlug(strtolower($slug));
        }

        /** @var ChannelRepositoryInterface<Channel> $channelRepository */
        $channelRepository = $this->objectManager->getRepository(Channel::class);
        if ($channelCode !== null) {
            $channel = $channelRepository->findOneByCode($channelCode);
            Assert::isInstanceOf($channel, Channel::class);

            $product->addChannel($channel);
        } else {
            foreach ($this->channelCodes as $channelCode) {
                $channel = $channelRepository->findOneByCode($channelCode);
                Assert::isInstanceOf($channel, Channel::class);

                $product->addChannel($channel);
            }
        }

        $product->setCode($productCode);

        $this->createProductOptionsAndValues($product, $productOptions, $locale);
        $this->createProductAttribute($product, $productType);

        $this->objectManager->persist($product);
        $this->objectManager->flush();

        return $product;
    }

    public function createProductVariant(
        ProductInterface $product,
        string $variantCode,
        array $productOptions,
        ?SupplierInterface $supplier = null,
        ?Country $country = null,
        array $productVariantOption = [],
        array $channelPricings = [],
        bool $prescriptionRequired = false,
        bool $isEnabled = true,
        bool $shippingRequired = true,
    ): ProductVariant {
        /** @var ProductVariantFactoryInterface<ProductVariant> $productVariantFactory */
        $productVariantFactory = $this->container->get(ProductVariantFactoryInterface::class);
        /** @var ProductVariant $productVariant */
        $productVariant = $productVariantFactory->createNew();

        $productVariantName = $productVariantOption['name'] ?? self::DEFAULT_VARIANT_OPTIONS['name'];
        $productVariant->setName($productVariantName);

        $productVariantCaption = $productVariantOption['caption'] ?? self::DEFAULT_VARIANT_OPTIONS['caption'];
        $productVariant->setCaption($productVariantCaption);

        $productVariant->setCode($variantCode);
        $productVariant->setCountry($country);
        $productVariant->setSupplier($supplier);
        $productVariant->setOnHand(1);
        $productVariant->setPrescriptionRequired($prescriptionRequired);
        $productVariant->setShippingRequired($shippingRequired);
        $productVariant->setEnabled($isEnabled);
        $productVariant->setCostPrice($productVariantOption['costPrice'] ?? self::DEFAULT_PRODUCT_COST);

        $shippingCategory = $this->objectManager->getRepository(ShippingCategory::class)->findOneBy([
            'code' => $prescriptionRequired ? ShippingCategory::SHIPPING_CATEGORY_RX_MEDICATION : ShippingCategory::SHIPPING_CATEGORY_OTC_MEDICATION,
        ]);
        $productVariant->setShippingCategory($shippingCategory);

        $this->addOptionValuesToVariant($productVariant, $productOptions);

        $product->addVariant($productVariant);

        $this->setChannelPricings($productVariant, $channelPricings);

        return $productVariant;
    }

    /**
     * @param array<string, array<string>> $translationsData
     */
    public function createProductVariantTranslations(ProductVariantInterface $productVariant, array $translationsData): void
    {
        foreach ($translationsData as $locale => $translations) {
            $productVariant->setFallbackLocale($locale);
            $translation = $productVariant->getTranslation($locale);
            $translation->setLeaflet('https://downloads.dokteronline.com/leaflets/nl/test-leaflet.pdf');
            foreach ($translations as $property => $value) {
                $translation->{'set'.ucfirst($property)}($value);
            }

            $this->objectManager->persist($translation);
        }
    }

    public function createOrGetSupplier(string $supplierCode, string $name = 'Test supplier', array $countries = []): SupplierInterface
    {
        // Some suppliers are created by the shop configuration, so we fetch them instead of creating a duplicate.
        $supplierRepository = $this->objectManager->getRepository(Supplier::class);
        $supplier = $supplierRepository->findOneBy([
            'identifier' => $supplierCode,
        ]);

        if ($supplier instanceof SupplierInterface) {
            return $supplier;
        }

        $supplier = new Supplier();
        $supplier->setName($name);
        $supplier->setIdentifier($supplierCode);
        $supplier->setHandlingFee(self::DEFAULT_HANDLING_FEE);

        /** @var Country $country */
        foreach ($countries as $country) {
            $supplierCountryShipping = $this->supplierCountryShipping[$supplier->getName().'-'.$country->getId()] ?? null;
            if (!$supplierCountryShipping instanceof SupplierCountryShipping) {
                $supplierCountryShipping = new SupplierCountryShipping();
                $supplierCountryShipping->setCountry($country);
                $supplierCountryShipping->setEnabled(true);

                $this->supplierCountryShipping[$supplier->getName().'-'.$country->getId()] = $supplierCountryShipping;
            }
            $supplierCountryShipping->setShippingCost(self::DEFAULT_SHIPPING_COST);
            $supplier->addSupplierCountryShipping($supplierCountryShipping);
        }

        $this->objectManager->persist($supplier);
        $this->objectManager->flush($supplier);

        return $supplier;
    }

    /**
     * @param array<int, string> $associatedProducts
     */
    public function createConsultProductAssociations(string $owner, array $associatedProducts): void
    {
        /** @var ProductAssociation $association */
        $association = $this->productAssociationExampleFactory->create([
            'type' => 'consult_products',
            'owner' => $owner,
            'associated_products' => $associatedProducts,
        ]);

        // Hydrate product.association collection since the productAssociationExampleFactory does not do this... :(
        $association->getOwner()?->addAssociation($association);

        $this->objectManager->persist($association);
        $this->objectManager->flush();
    }

    private function addOptionValuesToVariant(ProductVariantInterface $productVariant, array $productOptions): void
    {
        foreach (array_values($productOptions) as $productOptionValues) {
            if (!is_array($productOptionValues)) {
                $productOptionValues = [$productOptionValues];
            }

            foreach ($productOptionValues as $productOptionValue) {
                $optionValue = $this->objectManager
                    ->getRepository(ProductOptionValue::class)
                    ->findOneBy(['code' => $productOptionValue]);
                if (!$optionValue instanceof ProductOptionValue) {
                    throw new Exception($productOptionValue.' not found as ProductOptionValue');
                }
                $productVariant->addOptionValue($optionValue);
            }
        }
    }

    private function createProductOptionsAndValues(
        ProductInterface $product,
        array $productOptions,
        string $locale = 'en',
    ): void {
        $productOptionFactory = $this->container->get('sylius.factory.product_option');
        $productOptionValueFactory = $this->container->get('sylius.factory.product_option_value');

        $position = 1;

        foreach ($productOptions as $productOptionCode => $productOptionValues) {
            if (!is_array($productOptionValues)) {
                $productOptionValues = [$productOptionValues];
            }

            /** @var ProductOptionRepository<ProductOption> $productOptionRepository */
            $productOptionRepository = $this->objectManager->getRepository(ProductOption::class);
            $productOption = $productOptionRepository->findOneBy(['code' => $productOptionCode]);
            if (!$productOption instanceof ProductOption) {
                $productOption = $productOptionFactory->createNew();
            }

            $productOption->setCode($productOptionCode);
            $productOption->setPosition($position);

            foreach ($productOptionValues as $productOptionValue) {
                $productOptionValueRepository = $this->objectManager->getRepository(ProductOptionValue::class);
                $optionValue = $productOptionValueRepository->findOneBy(['code' => $productOptionValue]);
                if (!$optionValue instanceof ProductOptionValue) {
                    $optionValue = $productOptionValueFactory->createNew();
                }

                $optionValue->setCode($productOptionValue);
                $optionValue->setValue($productOptionValue);

                $productOptionTranslation = new ProductOptionTranslation();
                $productOptionTranslation->setLocale($locale);
                $productOptionTranslation->setName($productOptionValue);

                $productOption->addValue($optionValue);
                $productOption->addTranslation($productOptionTranslation);
            }

            $product->addOption($productOption);

            $this->objectManager->persist($productOption);
            ++$position;
        }
    }

    private function createProductAttribute(ProductInterface $product, ProductType $productType): void
    {
        $productAttributeRepository = $this->objectManager->getRepository(ProductAttribute::class);
        $productTypeAttribute = $productAttributeRepository->findOneBy(
            ['code' => ProductAttribute::ATTRIBUTE_CODE_TYPE]
        );
        if (!$productTypeAttribute instanceof ProductAttributeInterface) {
            throw new RuntimeException(sprintf('Configuration error! Missing product attribute with code "%s"', ProductAttribute::ATTRIBUTE_CODE_TYPE));
        }

        $typeAttributeValue = new ProductAttributeValue();
        $typeAttributeValue->setAttribute($productTypeAttribute);
        $typeAttributeValue->setValue([$productType->value]);

        $product->addAttribute($typeAttributeValue);
    }

    private function setChannelPricings(ProductVariant $productVariant, array $channelPricings): void
    {
        if (count($channelPricings) === 0) {
            foreach ($this->channelCodes as $channelCode) {
                $this->createChannelPricingForProductVariant($productVariant, $channelCode, true);
            }

            return;
        }

        foreach ($channelPricings as $channelPricing) {
            $this->createChannelPricingForProductVariant(
                $productVariant,
                $channelPricing['channelCode'],
                $channelPricing['enabled'],
            );
        }
    }

    private function createChannelPricingForProductVariant(
        ProductVariantInterface $productVariant,
        string $channelCode,
        bool $enabled,
    ): void {
        $price = self::DEFAULT_PRODUCT_VARIANT_UNIT_PRICE;

        $product = $productVariant->getProduct();
        Assert::isInstanceOf($product, ProductInterface::class);

        $channelRepository = $this->objectManager->getRepository(Channel::class);
        $channel = $channelRepository->findOneBy([
            'code' => $channelCode,
        ]);
        Assert::isInstanceOf($channel, Channel::class);

        // Consults in medication channel have a price of 0, consult channels have regular pricing.
        if ($product->isOfType(ProductType::CONSULT) && $channel->isAddPrescriptionMedicationDirectlyToCart()) {
            $price = 0;
        }

        $channelPricing = new ChannelPricing();
        $channelPricing->setPrice($price);
        $channelPricing->setEnabled($enabled);
        $channelPricing->setChannelCode($channelCode);

        $productVariant->addChannelPricing($channelPricing);
    }
}
