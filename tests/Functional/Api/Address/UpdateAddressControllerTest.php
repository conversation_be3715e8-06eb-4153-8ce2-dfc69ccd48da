<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Address;

use App\Entity\Addressing\Address;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\Order\Order;
use App\Entity\User\ShopUser;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Sylius\Component\Core\Model\ShopUserInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class UpdateAddressControllerTest extends WebTestCase
{
    private const string USER_EMAIL = '<EMAIL>';
    private const string USER_PASSWORD = 'test';
    private const string UPDATE_ADDRESS_ENDPOINT = '/api/shop/account/addresses/%s';
    private const string CHANNEL_CODE = 'dok_gb';

    private KernelBrowser $client;

    private EntityManagerInterface $entityManager;

    private ShopUserAuthenticationTestHelper $authenticationTestFactory;
    private CartTestFactory $cartTestFactory;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = self::createClient();
        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;
        $this->cartTestFactory = new CartTestFactory(self::getContainer());

        $this->authenticationTestFactory = new ShopUserAuthenticationTestHelper($this->client);
    }

    public function testItCanUpdateAddress(): void
    {
        $customer = $this->createCustomer();
        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        /** @var Address $addressToUpdate */
        $addressToUpdate = $customer->getDefaultAddress();
        $updateAddressBody = $this->getUpdateAddressBody($addressToUpdate);

        $token = $this->authenticationTestFactory->authenticate(self::USER_EMAIL, self::USER_PASSWORD);

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            sprintf(self::UPDATE_ADDRESS_ENDPOINT, $addressToUpdate->getId()),
            $updateAddressBody,
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();
        $decodedResponseBody = json_decode($responseBody, true, 512, JSON_THROW_ON_ERROR);

        $createdAt = $decodedResponseBody['createdAt'];
        $updatedAt = $decodedResponseBody['updatedAt'];
        unset($decodedResponseBody['createdAt'], $decodedResponseBody['updatedAt']);

        $updateAddressBody['fullName'] = 'Grober Random';

        self::assertResponseIsSuccessful();
        self::assertJson($responseBody);
        self::assertEquals($updateAddressBody, $decodedResponseBody);
        $pattern = '/^[\d]{4}-[\d]{2}-[\d]{2}T[\d]{1,2}:[\d]{1,2}:[\d]{1,2}(.*)$/';
        self::assertMatchesRegularExpression($pattern, $createdAt);
        self::assertMatchesRegularExpression($pattern, $updatedAt);
    }

    public function testItCannotUpdateAddressThatIsLinkedToOrder(): void
    {
        $customer = $this->createCustomer();
        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        /** @var Order $order */
        $order = $customer->getOrders()->first();
        /** @var Address|null $addressToUpdate */
        $addressToUpdate = $order->getBillingAddress();

        $updateAddressBody = $this->getUpdateAddressBody($addressToUpdate);

        $token = $this->authenticationTestFactory->authenticate(self::USER_EMAIL, self::USER_PASSWORD);

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            sprintf(self::UPDATE_ADDRESS_ENDPOINT, $addressToUpdate?->getId()),
            $updateAddressBody,
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );
        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    public function testItCannotUpdateAddressFromOtherCustomer(): void
    {
        $customer = $this->createCustomer();
        $secondCustomer = $this->createCustomer('<EMAIL>');

        $this->entityManager->persist($customer);
        $this->entityManager->persist($secondCustomer);
        $this->entityManager->flush();

        /** @var Address|null $addressToUpdate */
        $addressToUpdate = $secondCustomer->getDefaultAddress();
        $updateAddressBody = $this->getUpdateAddressBody($addressToUpdate);

        $token = $this->authenticationTestFactory->authenticate(self::USER_EMAIL, self::USER_PASSWORD);

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            sprintf(self::UPDATE_ADDRESS_ENDPOINT, $addressToUpdate?->getId()),
            $updateAddressBody,
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );
        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    private function createCustomer(string $email = '<EMAIL>'): Customer
    {
        $customer = new Customer();
        $customer->setBirthday(new DateTimeImmutable('1997-03-09'));
        $customer->setFirstName('Hans');
        $customer->setEmail($email);
        /** @var CustomerPool $customerPool */
        $customerPool = $this->entityManager->getRepository(CustomerPool::class)->findOneBy(['code' => 'dokteronline']);
        $customer->setCustomerPool($customerPool);
        $customer->setUser($this->createUser($customer));
        foreach ($this->createAddresses() as $key => $address) {
            $customer->addAddress($address);

            if ($key === 1) {
                $customer->setDefaultAddress($address);
            }

            if ($key === 2) {
                $this->createOrder($address, $customer);
            }
        }

        return $customer;
    }

    private function createOrder(Address $address, Customer $customer): void
    {
        $order = $this->cartTestFactory->createCart('dok_de', 'de');
        $order->setCustomer($customer);
        $order->setBillingAddress($address);
        $order->setShippingAddress($address);

        $customer->addOrder($order);
    }

    private function createUser(Customer $customer): ShopUserInterface
    {
        $user = new ShopUser();
        $user->setUsername($customer->getEmail());
        $user->setPlainPassword(self::USER_PASSWORD);
        $user->setCustomer($customer);
        $user->enable();
        $this->entityManager->persist($user);

        return $user;
    }

    /**
     * @return array{
     *      0: Address,
     *      1: Address,
     *      2: Address,
     * }
     */
    private function createAddresses(): array
    {
        $address = new Address();
        $address->setFirstName('Hans');
        $address->setLastName('Test');
        $address->setStreet('Test street 4');
        $address->setPostcode('9876ZY');
        $address->setCity('Breda');
        $address->setCountryCode('NL');

        $secondAddress = new Address();
        $secondAddress->setFirstName('Hans');
        $secondAddress->setLastName('Test');
        $secondAddress->setStreet('Test square 4');
        $secondAddress->setPostcode('1234AB');
        $secondAddress->setCity('Berlin');
        $secondAddress->setCountryCode('DE');

        $thirdAddress = new Address();
        $thirdAddress->setFirstName('Hans');
        $thirdAddress->setLastName('Test');
        $thirdAddress->setStreet('Test square 4');
        $thirdAddress->setPostcode('1234AB');
        $thirdAddress->setCity('Berlin');
        $thirdAddress->setCountryCode('DE');

        return [$address, $secondAddress, $thirdAddress];
    }

    private function getUpdateAddressBody(?Address $addressToUpdate): array
    {
        return [
            'id' => $addressToUpdate?->getId(),
            'firstName' => 'Grober',
            'lastName' => 'Random',
            'phoneNumber' => '*********',
            'company' => 'updatedCompany',
            'street' => 'Updated street 4',
            'postcode' => '1111AB',
            'city' => 'Eindhoven',
            'provinceCode' => 'WB',
            'provinceName' => 'brabant',
            'countryCode' => 'NL',
        ];
    }
}
