<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Address;

use App\Entity\Addressing\Country;
use App\Tests\Functional\Api\AbstractWebTestCase;
use Symfony\Component\HttpFoundation\Request;

final class GetCountriesControllerTest extends AbstractWebTestCase
{
    private const string API_URI = '/api/shop/countries';

    public function testCanGetAllCountries(): void
    {
        $countryRepository = $this->entityManager->getRepository(Country::class);
        $totalCountries = count($countryRepository->findAll());

        $this->client->jsonRequest(
            Request::METHOD_GET,
            self::API_URI,
            [],
            [],
        );

        self::assertResponseIsSuccessful();

        $countries = $this->getResponseBody();

        self::assertCount($totalCountries, $countries);

        foreach ($countries as $country) {
            self::assertArrayHasKey('code', $country);
            self::assertArrayHasKey('name', $country);
        }
    }

    /**
     * @dataProvider provideChannelCodes
     */
    public function testCanGetCountriesByChannel(
        string $channelCode,
        string $expectedCountryCode,
    ): void {
        $this->client->jsonRequest(
            Request::METHOD_GET,
            self::API_URI,
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => $channelCode,
            ],
        );

        self::assertResponseIsSuccessful();

        $countries = $this->getResponseBody();

        self::assertCount(1, $countries);
        self::assertSame($expectedCountryCode, $countries[0]['code']);
    }

    /**
     * @return iterable<string, array<int, string>>
     */
    public function provideChannelCodes(): iterable
    {
        yield 'Channel dok_nl should have country NL' => ['dok_nl', 'NL'];

        yield 'Channel blueclinic_nl should have country NL' => ['blueclinic_nl', 'NL'];

        yield 'Channel dok_de should have country DE' => ['dok_de', 'DE'];
    }
}
