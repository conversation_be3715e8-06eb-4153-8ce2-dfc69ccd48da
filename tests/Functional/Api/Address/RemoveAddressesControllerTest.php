<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Address;

use App\Entity\Addressing\Address;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\User\ShopUser;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Sylius\Component\Core\Model\ShopUserInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class RemoveAddressesControllerTest extends WebTestCase
{
    private const string USER_EMAIL = '<EMAIL>';
    private const string USER_PASSWORD = 'test';

    private KernelBrowser $client;
    private EntityManagerInterface $entityManager;
    private ShopUserAuthenticationTestHelper $authenticationTestFactory;
    private CartTestFactory $cartTestFactory;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
        /** @var EntityManagerInterface $entityManager */
        $entityManager = static::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;
        $this->cartTestFactory = new CartTestFactory(static::getContainer());

        $this->authenticationTestFactory = new ShopUserAuthenticationTestHelper($this->client);
    }

    public function testMustBeAuthenticated(): void
    {
        $customer = $this->createCustomer();
        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        /** @var Address $address */
        $address = $customer->getAddresses()[0];

        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/shop/account/addresses/%d', $address->getId()),
            [],
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    /**
     * @dataProvider channelCodeProvider
     */
    public function testItCanRemoveAddress(int $addressIndex, bool $canRemove): void
    {
        $customer = $this->createCustomer();
        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $token = $this->authenticate(self::USER_EMAIL, self::USER_PASSWORD);

        $requestHeaders = ['HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token)];
        /** @var Address $address */
        $address = $customer->getAddresses()[$addressIndex];

        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/shop/account/addresses/%d', $address->getId()),
            [],
            $requestHeaders
        );

        if ($canRemove) {
            $this->assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

            return;
        }
        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    public function channelCodeProvider(): iterable
    {
        yield 'Can remove address without order' => ['address_index' => 0, 'canRemove' => true];
        yield 'Cannot remove address with order' => ['address_index' => 2, 'canRemove' => false];
    }

    private function createCustomer(): Customer
    {
        $customer = new Customer();
        $customer->setBirthday(new DateTimeImmutable('1997-03-09'));
        $customer->setFirstName('Hans');
        $customer->setEmail('<EMAIL>');
        /** @var CustomerPool $customerPool */
        $customerPool = $this->entityManager->getRepository(CustomerPool::class)->findOneBy(['code' => 'dokteronline']);
        $customer->setCustomerPool($customerPool);
        $customer->setUser($this->createUser($customer));
        foreach ($this->createAddresses() as $key => $address) {
            $customer->addAddress($address);

            if ($key === 2) {
                $this->createOrder($address, $customer);
            }
        }

        return $customer;
    }

    private function createOrder(Address $address, Customer $customer): void
    {
        $order = $this->cartTestFactory->createCart('dok_de', 'de');
        $order->setCustomer($customer);
        $order->setBillingAddress($address);
        $order->setShippingAddress($address);

        $customer->addOrder($order);
    }

    private function createUser(Customer $customer): ShopUserInterface
    {
        $user = new ShopUser();
        $user->setUsername(self::USER_EMAIL);
        $user->setPlainPassword(self::USER_PASSWORD);
        $user->setCustomer($customer);
        $user->enable();
        $this->entityManager->persist($user);

        return $user;
    }

    /**
     * @return array{
     *      0: Address,
     *      1: Address,
     *      2: Address,
     * }
     */
    private function createAddresses(): array
    {
        $address = new Address();
        $address->setFirstName('Hans');
        $address->setLastName('Test');
        $address->setStreet('Test street');
        $address->setPostcode('9876ZY');
        $address->setCity('Breda');
        $address->setCountryCode('NL');

        $secondAddress = new Address();
        $secondAddress->setFirstName('Hans');
        $secondAddress->setLastName('Test');
        $secondAddress->setStreet('Test square');
        $secondAddress->setPostcode('1234AB');
        $secondAddress->setCity('Berlin');
        $secondAddress->setCountryCode('DE');

        $thirdAddress = new Address();
        $thirdAddress->setFirstName('Hans');
        $thirdAddress->setLastName('Test');
        $thirdAddress->setStreet('Test square');
        $thirdAddress->setPostcode('1234AB');
        $thirdAddress->setCity('Berlin');
        $thirdAddress->setCountryCode('DE');

        return [$address, $secondAddress, $thirdAddress];
    }

    private function authenticate(string $email, string $password): ?string
    {
        return $this->authenticationTestFactory->authenticate($email, $password);
    }
}
