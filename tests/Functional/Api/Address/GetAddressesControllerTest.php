<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Address;

use App\Entity\Addressing\Address;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\User\ShopUser;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Sylius\Component\Core\Model\ShopUserInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetAddressesControllerTest extends WebTestCase
{
    private const string USER_EMAIL = '<EMAIL>';

    private const string USER_PASSWORD = 'test';

    private KernelBrowser $client;
    private EntityManagerInterface $entityManager;
    private ShopUserAuthenticationTestHelper $authenticationTestFactory;
    private CartTestFactory $cartTestFactory;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
        $this->entityManager = static::getContainer()->get(EntityManagerInterface::class);
        $this->cartTestFactory = new CartTestFactory(static::getContainer());

        $this->authenticationTestFactory = new ShopUserAuthenticationTestHelper($this->client);
    }

    /**
     * @dataProvider channelCodeProvider
     */
    public function testItReturnsAddresses(?string $channelCode, int $expectedAddressCount): void
    {
        $customer = $this->createCustomer();
        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $token = $this->authenticate(self::USER_EMAIL, self::USER_PASSWORD);

        $requestHeaders = ['HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token)];
        if ($channelCode !== null) {
            $requestHeaders['HTTP_SYLIUS_CHANNEL_CODE'] = $channelCode;
        }

        $this->client->jsonRequest(
            Request::METHOD_GET,
            '/api/shop/account/addresses',
            [],
            $requestHeaders
        );

        $responseBody = $this->client->getResponse()->getContent();

        $decodedResponseBody = json_decode($responseBody);

        static::assertResponseIsSuccessful();
        static::assertJson($responseBody);
        static::assertCount($expectedAddressCount, $decodedResponseBody);
    }

    public function testItThrowsExceptionWhenChannelCodeDoesNotExist(): void
    {
        $customer = $this->createCustomer();
        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $token = $this->authenticate(self::USER_EMAIL, self::USER_PASSWORD);

        $this->client->jsonRequest(
            Request::METHOD_GET,
            '/api/shop/account/addresses',
            [],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => 'fake_channel_code',
            ]
        );

        $responseBody = $this->client->getResponse()->getContent();

        static::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $errorMessage = [
            'detail' => "The requested channel 'fake_channel_code' does not exist.",
            'status' => Response::HTTP_BAD_REQUEST,
            'title' => 'An error occurred.',
            'type' => 'about:blank',
        ];

        static::assertJsonStringEqualsJsonString(json_encode($errorMessage), $responseBody);
    }

    public function channelCodeProvider(): iterable
    {
        yield 'The dok_nl channel returns 1 address' => ['dok_nl', 1];
        yield 'No channel returns 3 orders' => [null, 3];
    }

    private function createCustomer(): Customer
    {
        $customer = new Customer();
        $customer->setBirthday(new DateTimeImmutable('1997-03-09'));
        $customer->setFirstName('Hans');
        $customer->setEmail('<EMAIL>');
        /** @var CustomerPool $customerPool */
        $customerPool = $this->entityManager->getRepository(CustomerPool::class)->findOneBy(['code' => 'dokteronline']);
        $customer->setCustomerPool($customerPool);
        $customer->setUser($this->createUser($customer));
        foreach ($this->createAddresses() as $key => $address) {
            $customer->addAddress($address);

            if ($key === 2) {
                $this->createOrder($address, $customer);
            }
        }

        return $customer;
    }

    private function createOrder(Address $address, Customer $customer): void
    {
        $order = $this->cartTestFactory->createCart('dok_de', 'de');
        $order->setCustomer($customer);
        $order->setBillingAddress($address);
        $order->setShippingAddress($address);

        $customer->addOrder($order);
    }

    private function createUser(Customer $customer): ShopUserInterface
    {
        $user = new ShopUser();
        $user->setUsername(self::USER_EMAIL);
        $user->setPlainPassword(self::USER_PASSWORD);
        $user->setCustomer($customer);
        $user->enable();
        $this->entityManager->persist($user);

        return $user;
    }

    /**
     * @return array{
     *      0: Address,
     *      1: Address,
     *      2: Address,
     *      3: Address,
     * }
     */
    private function createAddresses(): array
    {
        $address = new Address();
        $address->setFirstName('Hans');
        $address->setLastName('Test');
        $address->setStreet('Test street');
        $address->setPostcode('9876ZY');
        $address->setCity('Breda');
        $address->setCountryCode('NL');

        $secondAddress = new Address();
        $secondAddress->setFirstName('Hans');
        $secondAddress->setLastName('Test');
        $secondAddress->setStreet('Test square');
        $secondAddress->setPostcode('1234AB');
        $secondAddress->setCity('Berlin');
        $secondAddress->setCountryCode('DE');

        $thirdAddress = new Address();
        $thirdAddress->setFirstName('Hans');
        $thirdAddress->setLastName('Test');
        $thirdAddress->setStreet('Test square');
        $thirdAddress->setPostcode('1234AB');
        $thirdAddress->setCity('Berlin');
        $thirdAddress->setCountryCode('DE');

        $fourthAddress = new Address();
        $fourthAddress->setFirstName('Hans');
        $fourthAddress->setLastName('Test');
        $fourthAddress->setStreet('Test square');
        $fourthAddress->setPostcode('1234AB');
        $fourthAddress->setCity('Berlin');
        $fourthAddress->setCountryCode('DE');
        $fourthAddress->setAdditionalAddressInformation('Test pickup point');
        $fourthAddress->setPickupPointAddress('testpickup', 'Test pickup point', '1234AB', 'Berlin', 'DE');

        return [$address, $secondAddress, $thirdAddress, $fourthAddress];
    }

    private function authenticate(string $email, string $password): ?string
    {
        return $this->authenticationTestFactory->authenticate($email, $password);
    }
}
