<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class AuthenticateTest extends WebTestCase
{
    private CartTestFactory $cartTestFactory;
    private KernelBrowser $client;
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
        $this->cartTestFactory = new CartTestFactory(static::getContainer());
        /** @var EntityManagerInterface $entityManager */
        $entityManager = static::getContainer()->get('doctrine.orm.entity_manager');
        $this->entityManager = $entityManager;
    }

    public function testItCanAuthenticate(): void
    {
        $customer = $this->cartTestFactory->createCustomer();
        $user = $this->cartTestFactory->createUserForCustomer($customer);
        $this->entityManager->persist($customer);
        $this->entityManager->persist($user);
        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/account/authenticate',
            [
                'email' => CartTestFactory::USER_EMAIL,
                'password' => CartTestFactory::USER_PASSWORD,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => CartTestFactory::CHANNEL_CODE,
            ]
        );

        $this->assertResponseIsSuccessful();
        /** @var string $responseBody */
        $responseBody = $this->client->getResponse()->getContent();

        /** @var array $decodedResponseBody */
        $decodedResponseBody = json_decode($responseBody, true, JSON_THROW_ON_ERROR);
        $this->assertArrayHasKey('token', $decodedResponseBody);
        $this->assertIsString($decodedResponseBody['token']);
        $this->assertArrayHasKey('customer', $decodedResponseBody);
        $this->assertEquals('/api/v2/shop/customers/'.$customer->getId(), $decodedResponseBody['customer']);
    }

    public function testItRequiresTheChannelCode(): void
    {
        $customer = $this->cartTestFactory->createCustomer();
        $user = $this->cartTestFactory->createUserForCustomer($customer);
        $this->entityManager->persist($customer);
        $this->entityManager->persist($user);
        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/account/authenticate',
            [
                'email' => CartTestFactory::USER_EMAIL,
                'password' => CartTestFactory::USER_PASSWORD,
            ]
        );

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        /** @var string $responseBody */
        $responseBody = $this->client->getResponse()->getContent();
        $this->assertSame('{"type":"about:blank","title":"The request header contains errors.","status":400,"detail":"Customer pool not found."}', $responseBody);
    }
}
