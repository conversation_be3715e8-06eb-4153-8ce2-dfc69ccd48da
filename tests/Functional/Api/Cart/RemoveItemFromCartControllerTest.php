<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Cart;

use App\Entity\Order\Order;
use App\Tests\Functional\Api\CartTestFactory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class RemoveItemFromCartControllerTest extends WebTestCase
{
    private const string CHANNEL_CODE = 'dok_nl';

    private const array PRODUCTS = [
        [
            'code' => 'regular_product_1',
            'variant' => 'regular_product_variant_1',
        ],
        [
            'code' => 'regular_product_2',
            'variant' => 'regular_product_variant_2',
        ],
        [
            'code' => 'consult_product_1',
            'variant' => 'consult_product_variant_1',
        ],
    ];

    private KernelBrowser $client;

    private CartTestFactory $cartTestFactory;

    private Order $cart;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
        $this->entityManager = static::getContainer()->get(EntityManagerInterface::class);

        $this->cartTestFactory = new CartTestFactory(static::getContainer());

        foreach (self::PRODUCTS as $product) {
            $this->cartTestFactory->createProductAndProductVariants(
                self::CHANNEL_CODE,
                $product['code'],
                [$product['variant']]
            );
        }

        $this->cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            'en',
            [self::PRODUCTS[0]['variant'], self::PRODUCTS[1]['variant'], self::PRODUCTS[2]['variant']]
        );
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
        $this->entityManager->close();
        unset($this->client, $this->entityManager);
    }

    public function testItCanRemoveItemFromCart(): void
    {
        $item = $this->cart->getItems()->first();
        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/shop/carts/%s/items/%s', $this->cart->getTokenValue(), $item->getId()),
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $responseBody = $this->client->getResponse()->getContent();

        static::assertResponseIsSuccessful();
        static::assertJson($responseBody);

        static::assertCount(count(self::PRODUCTS) - 1, $this->cart->getItems());
        static::assertNotContains($item, $this->cart->getItems());
    }

    public function testItCanRemoveItemFromCartWithoutRemovingParent(): void
    {
        $consultItem = $this->cart->getItems()->last();
        $this->cart->getItems()->first()->setParentOrderItem($consultItem);
        $this->cart->getItems()->get(1)->setParentOrderItem($consultItem);

        $item = $this->cart->getItems()->first();

        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/shop/carts/%s/items/%s', $this->cart->getTokenValue(), $item->getId()),
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $responseBody = $this->client->getResponse()->getContent();

        static::assertResponseIsSuccessful();
        static::assertJson($responseBody);

        static::assertCount(count(self::PRODUCTS) - 1, $this->cart->getItems());
        static::assertNotContains($item, $this->cart->getItems());
    }

    public function testItRemovesChildrenWhenParentIsRemoved(): void
    {
        $consultItem = $this->cart->getItems()->last();
        $this->cart->getItems()->first()->setParentOrderItem($consultItem);
        $itemId = $consultItem->getId();

        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/shop/carts/%s/items/%s', $this->cart->getTokenValue(), $itemId),
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $responseBody = $this->client->getResponse()->getContent();

        static::assertResponseIsSuccessful();
        static::assertJson($responseBody);

        static::assertCount(count(self::PRODUCTS) - 2, $this->cart->getItems());
        static::assertEquals(self::PRODUCTS[1]['variant'], $this->cart->getItems()->first()->getVariant()->getCode());
    }

    public function testItRemovesParentWhenItHasNoMoreChildren(): void
    {
        $consultItem = $this->cart->getItems()->last();
        $this->cart->getItems()->first()->setParentOrderItem($consultItem);
        $itemId = $this->cart->getItems()->first()->getId();

        $this->entityManager->persist($this->cart);
        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/shop/carts/%s/items/%s', $this->cart->getTokenValue(), $itemId),
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $responseBody = $this->client->getResponse()->getContent();

        static::assertResponseIsSuccessful();
        static::assertJson($responseBody);

        static::assertCount(count(self::PRODUCTS) - 2, $this->cart->getItems());
        static::assertEquals(self::PRODUCTS[1]['variant'], $this->cart->getItems()->first()->getVariant()->getCode());
    }

    public function testItThrowsNotFoundExceptionWhenCartIsNotFound(): void
    {
        $itemId = $this->cart->getItems()->first()->getId();
        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/shop/carts/%s/items/%s', 'nonExistingToken', $itemId)
        );

        $responseBody = $this->client->getResponse()->getContent();

        $expectedResponseBody = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => Response::HTTP_NOT_FOUND,
            'detail' => 'The requested cart session could not be found.',
        ];

        static::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        static::assertJsonStringEqualsJsonString(json_encode($expectedResponseBody), $responseBody);
    }
}
