<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Cart;

use App\Entity\Product\Product;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use App\Tests\Functional\Api\TaxonTestFactory;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetCartControllerTest extends AbstractWebTestCase
{
    private const string CHANNEL_CODE = 'dok_gb';

    private const string LOCALE_CODE = 'en';

    private const string PRODUCT_CODE = 'viagra_test_special';

    private const string PRODUCT_VARIANT_CODE = 'viagra_25mg_4_test_special';

    private CartTestFactory $cartTestFactory;
    private ShopUserAuthenticationTestHelper $authenticationHelper;
    private TaxonTestFactory $taxonTestFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->authenticationHelper = new ShopUserAuthenticationTestHelper($this->client);
        $this->cartTestFactory = new CartTestFactory(static::getContainer());
        $this->taxonTestFactory = new TaxonTestFactory(static::getContainer());
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
    }

    public function testCanGetCart(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(self::CHANNEL_CODE, self::PRODUCT_CODE, [self::PRODUCT_VARIANT_CODE]);
        $cart = $this->cartTestFactory->createCartWithProductVariants(self::CHANNEL_CODE, self::LOCALE_CODE, [self::PRODUCT_VARIANT_CODE]);

        $this->createProductTaxonomy();

        $medicalQuestionnaireUuid = Uuid::uuid4();
        $cart->setMedicalQuestionnaire($medicalQuestionnaireUuid);

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/carts/%s', $cart->getTokenValue()),
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();

        $responseBody = $this->getResponseBody();

        $expectedTaxonResponse = [
            'code' => TaxonTestFactory::TAXON_CODE,
        ];

        self::assertSame($medicalQuestionnaireUuid->toString(), $responseBody['medicalQuestionnaire']['uuid']);
        self::assertSame($expectedTaxonResponse, $responseBody['items'][0]['mainTaxon']);
    }

    public function testCanGetCartAsCustomer(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(self::CHANNEL_CODE, self::PRODUCT_CODE, [self::PRODUCT_VARIANT_CODE]);
        $cart = $this->cartTestFactory->createCartWithProductVariants(self::CHANNEL_CODE, self::LOCALE_CODE, [self::PRODUCT_VARIANT_CODE]);

        $customer = $this->cartTestFactory->createCustomer();
        $this->cartTestFactory->createUserForCustomer($customer);
        $cart->setCustomer($customer);
        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $token = $this->authenticationHelper->authenticate();
        static::assertResponseIsSuccessful();
        static::assertNotNull($token);

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/carts/%s', $cart->getTokenValue()),
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        static::assertResponseIsSuccessful();

        $responseBody = $this->client->getResponse()->getContent();

        static::assertJson((string) $responseBody);
    }

    public function testCannotGetOtherCart(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(self::CHANNEL_CODE, self::PRODUCT_CODE, [self::PRODUCT_VARIANT_CODE]);
        $cart = $this->cartTestFactory->createCartWithProductVariants(self::CHANNEL_CODE, self::LOCALE_CODE, [self::PRODUCT_VARIANT_CODE]);

        $customer = $this->cartTestFactory->createCustomer();
        $customer->setEmail('<EMAIL>');
        $this->cartTestFactory->createUserForCustomer($customer);
        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $cart->setCustomer($customer);

        $loginCustomer = $this->cartTestFactory->createCustomer();
        $this->cartTestFactory->createUserForCustomer($loginCustomer);

        $this->entityManager->persist($loginCustomer);
        $this->entityManager->flush();

        $token = $this->authenticationHelper->authenticate();
        static::assertResponseIsSuccessful();
        static::assertNotNull($token);

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/carts/%s', $cart->getTokenValue()),
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        static::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/carts/%s', $cart->getTokenValue()),
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    private function createProductTaxonomy(): void
    {
        $product = $this->entityManager->getRepository(Product::class)->findOneBy([
            'code' => self::PRODUCT_CODE,
        ]);

        $taxon = $this->taxonTestFactory->createTaxon();
        $this->taxonTestFactory->createTaxonTranslation($taxon, self::LOCALE_CODE);
        $this->taxonTestFactory->createProductTaxon($product, $taxon);
    }
}
