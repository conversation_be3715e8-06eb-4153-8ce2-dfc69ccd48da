<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Cart;

use App\Api\Validator\ProductVariantExists;
use App\Api\Validator\ProductVariantMaxQuantities;
use App\Entity\Order\Order;
use App\Entity\Product\ProductInterface;
use App\Entity\Product\ProductVariant;
use App\Entity\Shipping\Shipment;
use App\Entity\Supplier\Supplier;
use App\Entity\Supplier\SupplierInterface;
use App\StateMachine\OrderCheckoutStates;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ProductTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class AddItemsToCartControllerTest extends AbstractWebTestCase
{
    private const string LOCALE_CODE = 'en';
    private const string CHANNEL_CODE_NL = 'dok_nl';
    private const string CHANNEL_CODE_GB = 'dok_gb';

    private const int PRODUCT_COST = ProductTestFactory::DEFAULT_PRODUCT_COST;

    private const int HANDLING_FEE = ProductTestFactory::DEFAULT_HANDLING_FEE;
    private const int PRODUCT_VARIANT_COST = ProductTestFactory::DEFAULT_PRODUCT_COST;

    private const array PRODUCTS = [
        'regular' => [
            [
                'code' => 'test_product_1',
                'name' => 'Test Product 1',
                'variantCode' => 'test_product_variant_1',
                'variantName' => 'Test Product Variant 1',
                'prescriptionRequired' => true,
            ],
            [
                'code' => 'test_product_2',
                'name' => 'Test Product 2',
                'variantCode' => 'test_product_variant_2',
                'variantName' => 'Test Product Variant 2',
                'prescriptionRequired' => true,
            ],
            [
                'code' => 'test_product_3',
                'name' => 'Test Product 3',
                'variantCode' => 'test_product_variant_3',
                'variantName' => 'Test Product Variant 3',
                'prescriptionRequired' => false,
            ],
            [
                'code' => 'test_product_4',
                'name' => 'Test Product 4',
                'variantCode' => 'test_product_variant_4',
                'variantName' => 'Test Product Variant 4',
                'prescriptionRequired' => false,
            ],
            [
                'code' => 'test_product_5',
                'name' => 'Test Product 5',
                'variantCode' => 'test_product_variant_5',
                'variantName' => 'Test Product Disabled Variant 5',
                'prescriptionRequired' => true,
                'variantEnabled' => false,
            ],
        ],
        'consult' => [
            [
                'code' => 'test_consult_1',
                'name' => 'Test Consult 1',
                'variantCode' => 'test_consult_variant_1',
                'variantName' => 'Test Consult Variant 1',
                'prescriptionRequired' => true,
            ],
            [
                'code' => 'test_consult_2',
                'name' => 'Test Consult 2',
                'variantCode' => 'test_consult_variant_2',
                'variantName' => 'Test Consult Variant 2',
                'prescriptionRequired' => true,
            ],
        ],
        'associations' => [
            [
                'owner' => 'test_product_1',
                'associatedProducts' => [
                    'test_consult_1',
                    'test_consult_2',
                ],
            ],
            [
                'owner' => 'test_product_2',
                'associatedProducts' => [
                    'test_consult_2',
                ],
            ],
            [
                'owner' => 'test_product_5',
                'associatedProducts' => [
                    'test_consult_1',
                ],
            ],
        ],
    ];

    private ShopUserAuthenticationTestHelper $authenticationHelper;
    private CartTestFactory $cartTestFactory;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        $this->authenticationHelper = new ShopUserAuthenticationTestHelper($this->client);
        $this->cartTestFactory = new CartTestFactory(self::getContainer());
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
        unset($this->authenticationHelper);
    }

    public function testCanAddSingleItem(): void
    {
        $this->prepareProductsForChannel();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $this->getCartToken()),
            [
                [
                    'productVariantCode' => self::PRODUCTS['regular'][2]['variantCode'],
                    'quantity' => 1,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_NL,
            ]
        );

        self::assertResponseIsSuccessful();

        $decodedResponseBody = $this->getResponseBody();

        self::assertArrayHasKey('items', $decodedResponseBody);
        self::assertCount(1, $decodedResponseBody['items']);
        self::assertArrayHasKey('shipments', $decodedResponseBody);
        self::assertCount(1, $decodedResponseBody['shipments']);
        self::assertEquals('medical_questionnaire_skipped', $decodedResponseBody['checkoutState']);
        self::assertArrayHasKey('items', $decodedResponseBody);
        self::assertCount(1, $decodedResponseBody['items']);
    }

    /**
     * @dataProvider provideDataForAddItemGivesCorrectShippingMethodForCart
     *
     * @param array{
     *          array{
     *              productVariantCode: string,
     *              quantity: int,
     *              parentProductVariantCode?: string,
     *          }
     *     } $requestBody
     * @param array{
     *          array{
     *              productVariantCode: string,
     *              quantity: int,
     *              parentProductVariantCode?: string,
     *          }
     *     }|null $preAddProductVariantRequestBody
     */
    public function testAddItemAllocatesExpectedShippingMethodForCart(
        string $channelCode,
        array $requestBody,
        string $expectedShipmentMethodCode,
        int $expectedShipmentTotal,
        int $costPrice,
        ?array $preAddProductVariantRequestBody = null,
    ): void {
        $this->prepareProductsForChannel($channelCode);

        $tokenValue = $this->getCartToken($channelCode);

        if (!empty($preAddProductVariantRequestBody)) {
            $this->addItemsToCart($tokenValue, $channelCode, $preAddProductVariantRequestBody);
        }

        $this->addItemsToCart($tokenValue, $channelCode, $requestBody);

        self::assertResponseIsSuccessful();

        $decodedResponseBody = $this->getResponseBody();

        $shipment = $this->entityManager->find(Shipment::class, $decodedResponseBody['shipments'][0]['id']);
        self::assertInstanceOf(Shipment::class, $shipment);

        self::assertSame($expectedShipmentTotal, $decodedResponseBody['shippingTotal']);
        self::assertSame($expectedShipmentMethodCode, $shipment->getMethod()?->getCode());

        /** @var Order $cart */
        $cart = $this->entityManager->getRepository(Order::class)->findOneBy(['tokenValue' => $tokenValue]);
        $this->assertSame($costPrice, $cart->getCostPriceTotal());
    }

    /**
     * @dataProvider dataProviderCannotExceedMaximumNumberOfConsults
     */
    public function testCannotExceedMaximumNumberOfConsults(string $channelCode, bool $hasWarning): void
    {
        $this->prepareProductsForChannel($channelCode);

        $tokenValue = $this->getCartToken($channelCode);
        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => 'test_consult_variant_1',
                'quantity' => 1,
            ],
            [
                'productVariantCode' => 'test_consult_variant_2',
                'quantity' => 1,
            ],
        ]);

        self::assertResponseIsSuccessful();
        $decodedResponseBody = $this->getResponseBody();

        self::assertCount(2, $decodedResponseBody['items']);

        if ($hasWarning) {
            self::assertEquals(
                'multiple_consults_not_allowed',
                $decodedResponseBody['items'][0]['warnings'][0]
            );

            self::assertEquals(
                'multiple_consults_not_allowed',
                $decodedResponseBody['items'][1]['warnings'][0]
            );

            return;
        }

        self::assertEquals([], $decodedResponseBody['items'][0]['warnings']);
        self::assertEquals([], $decodedResponseBody['items'][1]['warnings']);
    }

    public function testCanNotAddItemsExceedingMaxQuantity(): void
    {
        $this->prepareProductsForChannel();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $this->getCartToken()),
            [
                [
                    'productVariantCode' => self::PRODUCTS['regular'][2]['variantCode'],
                    'quantity' => 2,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_NL,
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertJson($responseBody);

        $decodedResponseBody = json_decode($responseBody, true);

        self::assertArrayHasKey('violations', $decodedResponseBody);
        self::assertEquals(
            str_replace('{{ maxQuantity }}', '1', ProductVariantMaxQuantities::MESSAGE),
            $decodedResponseBody['violations'][0]['message']
        );
        self::assertEquals(
            '[0].quantity',
            $decodedResponseBody['violations'][0]['property']
        );
    }

    public function testCannotAddItemsToOtherCustomerCart(): void
    {
        $this->prepareProductsForChannel();

        $cart = $this->cartTestFactory->createCart(self::CHANNEL_CODE_NL, self::LOCALE_CODE);
        $customer = $this->cartTestFactory->createCustomer();
        $customer->setEmail('<EMAIL>');
        $this->cartTestFactory->createUserForCustomer($customer);
        $cart->setCustomer($customer);

        $loginCustomer = $this->cartTestFactory->createCustomer();
        $this->cartTestFactory->createUserForCustomer($loginCustomer);

        $this->entityManager->persist($loginCustomer);
        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $token = $this->authenticationHelper->authenticate();
        self::assertResponseIsSuccessful();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $cart->getTokenValue()),
            [
                [
                    'productVariantCode' => self::PRODUCTS['regular'][2]['variantCode'],
                    'quantity' => 1,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_NL,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );
        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    public function testCanAddForCustomer(): void
    {
        $this->prepareProductsForChannel();

        $cart = $this->cartTestFactory->createCart(self::CHANNEL_CODE_NL, self::LOCALE_CODE);
        $customer = $this->cartTestFactory->createCustomer();
        $this->cartTestFactory->createUserForCustomer($customer);
        $cart->setCustomer($customer);

        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $token = $this->authenticationHelper->authenticate();
        self::assertResponseIsSuccessful();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $cart->getTokenValue()),
            [
                [
                    'productVariantCode' => self::PRODUCTS['regular'][2]['variantCode'],
                    'quantity' => 1,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_NL,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );
        self::assertResponseIsSuccessful();
    }

    public function testCanNotAddItemsExceedingMaxQuantityWithProductVariantAlreadyInCart(): void
    {
        $this->prepareProductsForChannel();

        $cartToken = $this->getCartToken();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $cartToken),
            [
                [
                    'productVariantCode' => self::PRODUCTS['regular'][2]['variantCode'],
                    'quantity' => 1,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_NL,
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();
        self::assertJson($responseBody);

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $cartToken),
            [
                [
                    'productVariantCode' => self::PRODUCTS['regular'][2]['variantCode'],
                    'quantity' => 1,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_NL,
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertJson($responseBody);

        $decodedResponseBody = json_decode($responseBody, true);

        self::assertArrayHasKey('violations', $decodedResponseBody);
        self::assertEquals(
            str_replace('{{ maxQuantity }}', '1', ProductVariantMaxQuantities::MESSAGE),
            $decodedResponseBody['violations'][0]['message']
        );
        self::assertEquals(
            '[0].quantity',
            $decodedResponseBody['violations'][0]['property']
        );
    }

    public function testCanNotAddItemsThatDoNotExist(): void
    {
        $this->prepareProductsForChannel();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $this->getCartToken()),
            [
                [
                    'productVariantCode' => 'this_product_variant_does_not_exist',
                    'quantity' => 1,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_NL,
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertJson($responseBody);

        $decodedResponseBody = json_decode($responseBody, true);

        self::assertArrayHasKey('violations', $decodedResponseBody);

        self::assertEquals(
            ProductVariantExists::MESSAGE,
            $decodedResponseBody['violations'][0]['message']
        );
        self::assertEquals(
            '[0].productVariantCode',
            $decodedResponseBody['violations'][0]['property']
        );
    }

    public function testCanAddMultipleItems(): void
    {
        $this->prepareProductsForChannel();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $this->getCartToken()),
            [
                [
                    'productVariantCode' => self::PRODUCTS['regular'][2]['variantCode'],
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => self::PRODUCTS['regular'][3]['variantCode'],
                    'quantity' => 1,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_NL,
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertJson($responseBody);

        $decodedResponseBody = json_decode($responseBody, true);

        self::assertArrayHasKey('items', $decodedResponseBody);
        self::assertCount(2, $decodedResponseBody['items']);
    }

    public function testCanAddMultipleItemsFromOtherSuppliersResultsInOrderItemWarnings(): void
    {
        $this->prepareProductsForChannel(self::CHANNEL_CODE_GB);

        /** @var ProductVariant[] $productVariants */
        $productVariants = $this->entityManager
            ->getRepository(ProductVariant::class)
            ->findBy([
                'code' => [
                    self::PRODUCTS['regular'][0]['variantCode'],
                    self::PRODUCTS['regular'][1]['variantCode'],
                    self::PRODUCTS['regular'][2]['variantCode'],
                ],
            ]);

        /** @var SupplierInterface[] $suppliers */
        $suppliers = $this->entityManager
            ->getRepository(Supplier::class)
            ->findBy([
                'identifier' => [
                    'apotheek-culemborg',
                    'apotheek-bad-nieuweschans',
                ],
            ]);

        foreach ($productVariants as $productVariant) {
            $productVariant->setPrescriptionRequired(false);
        }

        $productVariants[0]->setSupplier($suppliers[0]);

        // product variants with other suppliers
        $productVariants[1]->setSupplier($suppliers[1]);
        $productVariants[2]->setSupplier($suppliers[1]);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $this->getCartToken(self::CHANNEL_CODE_GB)),
            [
                [
                    'productVariantCode' => $productVariants[0]->getCode(),
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $productVariants[1]->getCode(),
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $productVariants[2]->getCode(),
                    'quantity' => 1,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_GB,
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        $decodedResponseBody = json_decode($responseBody, true);

        $actualOrderItemWarnings = array_map(
            fn (array $orderItems) => $orderItems['warnings'],
            $decodedResponseBody['items']
        );

        $expectedOrderItemWarnings = [
            ['multiple_suppliers_on_order_not_allowed'],
            ['multiple_suppliers_on_order_not_allowed'],
            ['multiple_suppliers_on_order_not_allowed'],
        ];

        self::assertResponseIsSuccessful();
        self::assertSame($expectedOrderItemWarnings, $actualOrderItemWarnings);
    }

    public function testCanNotAddRelatedItemToNotAddedConsult(): void
    {
        $this->prepareProductsForChannel();

        $consultVariantCode = self::PRODUCTS['consult'][0]['variantCode'];
        $notAddedConsultVariantCode = self::PRODUCTS['consult'][1]['variantCode'];
        $productVariantCode = self::PRODUCTS['regular'][0]['variantCode'];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $this->getCartToken()),
            [
                [
                    'productVariantCode' => $consultVariantCode,
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $productVariantCode,
                    'quantity' => 1,
                    'parentProductVariantCode' => $notAddedConsultVariantCode,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_NL,
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertJson($responseBody);

        $decodedResponseBody = json_decode($responseBody, true);

        self::assertArrayHasKey('violations', $decodedResponseBody);
        self::assertEquals(
            'The parent product variant does not exist in the cart or request.',
            $decodedResponseBody['violations'][0]['message']
        );
        self::assertEquals(
            '[1].parentProductVariantCode',
            $decodedResponseBody['violations'][0]['property']
        );
    }

    public function testCannotAddDisabledProductVariantItem(): void
    {
        $this->prepareProductsForChannel();

        $consultVariantCode = self::PRODUCTS['consult'][0]['variantCode'];
        $productVariantCode = self::PRODUCTS['regular'][4]['variantCode'];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $this->getCartToken()),
            [
                [
                    'productVariantCode' => $consultVariantCode,
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $productVariantCode,
                    'quantity' => 1,
                    'parentProductVariantCode' => $consultVariantCode,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_NL,
            ]
        );

        $responseBody = $this->getResponseBody();

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertEquals('The product variant is not enabled.', $responseBody['violations'][0]['message']);
        self::assertEquals('items[1].productVariantCode', $responseBody['violations'][0]['property']);
    }

    public function testCanAddConsultItem(): void
    {
        $this->prepareProductsForChannel();

        $consultVariantCode = self::PRODUCTS['consult'][0]['variantCode'];
        $productVariantCode = self::PRODUCTS['regular'][0]['variantCode'];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $this->getCartToken()),
            [
                [
                    'productVariantCode' => $consultVariantCode,
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $productVariantCode,
                    'quantity' => 1,
                    'parentProductVariantCode' => $consultVariantCode,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_NL,
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        static::assertResponseIsSuccessful();
        static::assertJson($responseBody);

        $decodedResponseBody = json_decode($responseBody, true);
        static::assertEquals('cart', $decodedResponseBody['checkoutState']);
    }

    public function testCanResetCart(): void
    {
        $this->prepareProductsForChannel();

        $consultVariantCode = self::PRODUCTS['consult'][0]['variantCode'];
        $productVariantCode = self::PRODUCTS['regular'][0]['variantCode'];

        $cartToken = $this->getCartToken(checkoutState: OrderCheckoutStates::STATE_MEDICAL_QUESTIONNAIRE_COMPLETED);
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $cartToken),
            [
                [
                    'productVariantCode' => $consultVariantCode,
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $productVariantCode,
                    'quantity' => 1,
                    'parentProductVariantCode' => $consultVariantCode,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_NL,
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        static::assertResponseIsSuccessful();
        static::assertJson($responseBody);

        $decodedResponseBody = json_decode($responseBody, true);
        static::assertEquals('cart', $decodedResponseBody['checkoutState']);
    }

    public function testCanSeeItemsAsArrayResponse(): void
    {
        $this->prepareProductsForChannel();

        $consultVariantCode = self::PRODUCTS['consult'][0]['variantCode'];
        $notAddedConsultVariantCode = self::PRODUCTS['consult'][1]['variantCode'];
        $productVariantCode = self::PRODUCTS['regular'][0]['variantCode'];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $this->getCartToken()),
            [
                [
                    'productVariantCode' => $consultVariantCode,
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $productVariantCode,
                    'quantity' => 1,
                    'parentProductVariantCode' => $consultVariantCode,
                ],
                [
                    'productVariantCode' => $notAddedConsultVariantCode,
                    'quantity' => 1,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_NL,
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();
        $decodedResponseBody = json_decode($responseBody, true);

        self::assertResponseIsSuccessful();
        self::assertJson($responseBody);
        // We want to validate if items collection is refreshed properly, so a valid collection will be shown as array
        // without skipping an array key nor showing one as the collection is modified in the add items handler.
        self::assertStringNotContainsString('"0":{', $responseBody);
        self::assertArrayHasKey(0, $decodedResponseBody['items']);
        self::assertArrayHasKey(1, $decodedResponseBody['items']);
        self::assertArrayNotHasKey(2, $decodedResponseBody['items']);
    }

    /**
     * @return iterable<
     *     string,
     *     array{channel_code: string, hasWarning: bool}
     * >
     */
    private function dataProviderCannotExceedMaximumNumberOfConsults(): iterable
    {
        yield 'dok_nl has a limit of 1 consult per order' => [
            'channel_code' => 'dok_nl',
            'hasWarning' => true,
        ];

        yield 'dok_be may have multiple consults per order' => [
            'channel_code' => 'dok_be',
            'hasWarning' => false,
        ];
    }

    /**
     * @return iterable<string, array{
     *     0: string,
     *     1: array{
     *          array{
     *              productVariantCode: string,
     *              quantity: int,
     *              parentProductVariantCode?: string,
     *          }
     *     },
     *     2: string,
     *     3: int,
     *     4: int,
     *     5?: array{
     *          array{
     *              productVariantCode: string,
     *              quantity: int,
     *              parentProductVariantCode?: string,
     *          }
     *     }
     * }>
     */
    private function provideDataForAddItemGivesCorrectShippingMethodForCart(): iterable
    {
        $productVariantCodeOtc = self::PRODUCTS['regular'][3]['variantCode'];
        $productVariantCodeRx = self::PRODUCTS['regular'][1]['variantCode'];
        $productVariantCodeConsult = self::PRODUCTS['consult'][1]['variantCode'];

        yield "Order in channel 'dok_de' (addPrescriptionDirectly = true) with OTC variant should have OTC shipping method." => [
            'dok_de',
            [
                [
                    'productVariantCode' => $productVariantCodeOtc,
                    'quantity' => 1,
                ],
            ],
            'DHL',
            495,
            self::PRODUCT_COST + self::HANDLING_FEE,
        ];

        yield "Order in channel 'dok_nl' (addPrescriptionDirectly = false) with OTC variant should have OTC shipping method." => [
            'dok_nl',
            [
                [
                    'productVariantCode' => $productVariantCodeOtc,
                    'quantity' => 1,
                ],
            ],
            'DHL',
            495,
            self::PRODUCT_COST + self::HANDLING_FEE,
        ];

        yield "Order in channel 'dok_de' (addPrescriptionDirectly = true) with 1 consult and 1 RX variant should have RX shipping method." => [
            'dok_de',
            [
                [
                    'productVariantCode' => $productVariantCodeConsult,
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $productVariantCodeRx,
                    'quantity' => 1,
                    'parentProductVariantCode' => $productVariantCodeConsult,
                ],
            ],
            'DHL_RX',
            0,
            self::PRODUCT_COST + self::PRODUCT_COST + self::HANDLING_FEE,
        ];

        yield "Order in channel 'dok_nl' (addPrescriptionDirectly = false) with 1 consult and 1 RX variant should have RX shipping method." => [
            'dok_nl',
            [
                [
                    'productVariantCode' => $productVariantCodeConsult,
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $productVariantCodeRx,
                    'quantity' => 1,
                    'parentProductVariantCode' => $productVariantCodeConsult,
                ],
            ],
            'DHL_RX',
            0,
            self::PRODUCT_COST,
        ];

        yield "Order in channel 'dok_nl' (addPrescriptionDirectly = false) with 1 consult variant should have RX shipping method." => [
            'dok_nl',
            [
                [
                    'productVariantCode' => $productVariantCodeConsult,
                    'quantity' => 1,
                ],
            ],
            'DHL_RX',
            0,
            self::PRODUCT_COST,
        ];

        yield "Order in channel 'dok_nl' (addPrescriptionDirectly = false) with 1 consult, 1 preferred RX and 1 blueclinic service variant should have Blueclinic shipping method." => [
            'dok_nl',
            [
                [
                    'productVariantCode' => ProductInterface::SERVICE_BLUECLINIC_CODE,
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $productVariantCodeConsult,
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $productVariantCodeRx,
                    'quantity' => 1,
                    'parentProductVariantCode' => $productVariantCodeConsult,
                ],
            ],
            'BLUECLINIC',
            0,
            self::PRODUCT_COST + self::PRODUCT_VARIANT_COST,
        ];

        yield "Order in channel 'dok_nl' (addPrescriptionDirectly = false) with 1 consult, 1 preferred RX and then adding 1 blueclinic service variant should have Blueclinic shipping method." => [
            'dok_nl',
            [
                [
                    'productVariantCode' => ProductInterface::SERVICE_BLUECLINIC_CODE,
                    'quantity' => 1,
                ],
            ],
            'BLUECLINIC',
            0,
            self::PRODUCT_COST + self::PRODUCT_VARIANT_COST,
            [
                [
                    'productVariantCode' => $productVariantCodeConsult,
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $productVariantCodeRx,
                    'quantity' => 1,
                    'parentProductVariantCode' => $productVariantCodeConsult,
                ],
            ],
        ];

        yield "Order in channel 'dok_nl' (addPrescriptionDirectly = false) with 1 consult, 1 preferred RX and 1 paper prescription service variant should have Prescription shipping method." => [
            'dok_nl',
            [
                [
                    'productVariantCode' => ProductInterface::SERVICE_PRESCRIPTION_CODE,
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $productVariantCodeConsult,
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $productVariantCodeRx,
                    'quantity' => 1,
                    'parentProductVariantCode' => $productVariantCodeConsult,
                ],
            ],
            'ROYAL_MAIL_PRESCRIPTION_MAILER',
            0,
            self::PRODUCT_COST + self::PRODUCT_VARIANT_COST,
        ];

        yield "Order in channel 'dok_nl' (addPrescriptionDirectly = false) with 1 consult, 1 preferred RX and then adding 1 paper prescription service variant should have Prescription shipping method." => [
            'dok_nl',
            [
                [
                    'productVariantCode' => ProductInterface::SERVICE_PRESCRIPTION_CODE,
                    'quantity' => 1,
                ],
            ],
            'ROYAL_MAIL_PRESCRIPTION_MAILER',
            0,
            self::PRODUCT_COST + self::PRODUCT_VARIANT_COST,
            [
                [
                    'productVariantCode' => $productVariantCodeConsult,
                    'quantity' => 1,
                ],
                [
                    'productVariantCode' => $productVariantCodeRx,
                    'quantity' => 1,
                    'parentProductVariantCode' => $productVariantCodeConsult,
                ],
            ],
        ];
    }

    private function getCartToken(
        string $channelCode = self::CHANNEL_CODE_NL,
        string $checkoutState = 'cart',
    ): string {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/carts',
            [
                'empty' => null,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => $channelCode,
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        self::assertResponseStatusCodeSame(Response::HTTP_CREATED);
        self::assertJson($responseBody);

        $decodedResponseBody = json_decode($responseBody, true);

        self::assertArrayHasKey('tokenValue', $decodedResponseBody);
        self::assertIsString($decodedResponseBody['tokenValue']);

        if ($checkoutState !== 'cart') {
            /** @var Order $order */
            $order = $this->entityManager->getRepository(Order::class)->findOneBy(['tokenValue' => $decodedResponseBody['tokenValue']]);
            $order->setCheckoutState($checkoutState);
            $this->entityManager->flush();
        }

        return $decodedResponseBody['tokenValue'];
    }

    private function prepareProductsForChannel(string $channelCode = self::CHANNEL_CODE_NL): void
    {
        $productTestFactory = new ProductTestFactory(
            self::getContainer(),
            [$channelCode],
        );

        $productTestFactory->prepareDatabaseWithRegularAndConsultProducts(
            self::PRODUCTS['regular'],
            self::PRODUCTS['consult']
        );

        $productTestFactory->prepareDatabaseWithServiceProducts([
            ProductInterface::SERVICE_PRESCRIPTION_CODE,
            ProductInterface::SERVICE_BLUECLINIC_CODE,
        ]);

        foreach (self::PRODUCTS['associations'] as $association) {
            $productTestFactory->createConsultProductAssociations($association['owner'], $association['associatedProducts']);
        }
    }
}
