<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Cart;

use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShippingCategory;
use App\Entity\Shipping\ShippingMethod;
use App\StateMachine\OrderCheckoutStates;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ProductTestFactory;
use App\Tests\Mocks\AnamnesisServiceClient;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpFoundation\Request;

final class RemoveChildCartItemsControllerTest extends AbstractWebTestCase
{
    private const string CHANNEL_CODE = 'dok_nl';

    private const string LOCALE = 'nl';

    private const array PRODUCTS = [
        'regular' => [
            [
                'code' => 'test_product_1',
                'name' => 'Test Product 1',
                'variantCode' => 'test_product_variant_1',
                'variantName' => 'Test Product Variant 1',
                'prescriptionRequired' => true,
            ],
            [
                'code' => 'test_product_2',
                'name' => 'Test Product 2',
                'variantCode' => 'test_product_variant_2',
                'variantName' => 'Test Product Variant 2',
                'prescriptionRequired' => true,
            ],
        ],
        'consult' => [
            [
                'code' => 'test_consult_1',
                'name' => 'Test Consult 1',
                'variantCode' => 'test_consult_variant_1',
                'variantName' => 'Test Consult Variant 1',
                'prescriptionRequired' => true,
            ],
            [
                'code' => 'test_consult_2',
                'name' => 'Test Consult 2',
                'variantCode' => 'test_consult_variant_2',
                'variantName' => 'Test Consult Variant 2',
                'prescriptionRequired' => true,
            ],
        ],
        'associations' => [
            [
                'owner' => 'test_product_1',
                'associatedProducts' => [
                    'test_consult_1',
                    'test_consult_2',
                ],
            ],
            [
                'owner' => 'test_product_2',
                'associatedProducts' => [
                    'test_consult_1',
                ],
            ],
        ],
    ];

    private readonly CartTestFactory $cartTestFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cartTestFactory = new CartTestFactory(static::getContainer());

        $this->prepareDatabaseWithExampleProducts();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
    }

    public function testCanClearRelatedItemsFromCart(): void
    {
        $cart = $this->cartTestFactory->createCart(self::CHANNEL_CODE, self::LOCALE);
        $this->cartTestFactory->addRelatedItemToCart(
            $cart,
            self::PRODUCTS['consult'][0]['variantCode'],
            1,
            self::PRODUCTS['regular'][0]['variantCode'],
            1,
            self::PRODUCTS['consult'][0]['variantCode'],
        );

        $cart->setMedicalQuestionnaire(Uuid::fromString(AnamnesisServiceClient::VALID_UUID));
        $cart->setCheckoutState(OrderCheckoutStates::STATE_PREFERRED_PRODUCTS_SELECTED);
        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/shop/carts/%s/child-items', $cart->getTokenValue()),
            [
                ['parentProductVariantCode' => 'test_consult_variant_1'],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $this->assertCartResponseSame(
            function (array &$expectedResponse, array $actualResponse) {
                $expectedResponse['items'] = [
                    [
                        'warnings' => [],
                        'variant' => [
                            'caption' => 'Including doctor consult and service fees',
                            'maximumQuantityPerOrder' => 1,
                            'code' => 'test_consult_variant_1',
                            'name' => 'Test Consult Variant 1',
                            'prescriptionRequired' => true,
                            'price' => [
                                'amount' => 1000,
                                'currency' => 'EUR',
                            ],
                            'product' => [
                                'code' => 'test_consult_1',
                                'attributes' => [
                                    'type' => 'consult',
                                ],
                            ],
                            'productAttributes' => [
                                'type' => 'consult',
                            ],
                        ],
                        'productName' => 'Test Consult 1',
                        'variantName' => 'Test Consult Variant 1',
                        'id' => $actualResponse['items'][0]['id'] ?? 0,
                        'quantity' => 1,
                        'unitPrice' => 1000,
                        'total' => 1000,
                        'subtotal' => 1000,
                    ],
                ];

                $expectedResponse['itemsTotal'] = 1000;
                $expectedResponse['checkoutState'] = OrderCheckoutStates::STATE_MEDICAL_QUESTIONNAIRE_COMPLETED;
                $expectedResponse['subtotal'] = 1000;
                $expectedResponse['total'] = 1000;
                $expectedResponse['shippingTotal'] = 0;
                $expectedResponse['payments'][0]['amount'] = 1000;
                $expectedResponse['medicalQuestionnaire'] = [];
                $expectedResponse['medicalQuestionnaire']['uuid'] = AnamnesisServiceClient::VALID_UUID;
                $expectedResponse['shipments'][0]['items'] = [
                    ['name' => 'Test Consult Variant 1', 'type' => 'consult'],
                ];
            }
        );

        $actualResponse = $this->getResponseBody();

        $this->assertShippingCategoryForShipment($actualResponse['shipments'][0]['id'], ShippingCategory::SHIPPING_CATEGORY_RX_MEDICATION);
    }

    public function testCanCannotRemoveRelatedItemsWithPreferredVariantCode(): void
    {
        $cart = $this->cartTestFactory->createCart(self::CHANNEL_CODE, self::LOCALE);
        $this->cartTestFactory->addRelatedItemToCart(
            $cart,
            'test_consult_variant_1',
            1,
            'test_product_variant_1',
            1,
            'test_consult_variant_1',
        );

        $cart->setMedicalQuestionnaire(Uuid::fromString(AnamnesisServiceClient::VALID_UUID));
        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/shop/carts/%s/child-items', $cart->getTokenValue()),
            [
                ['parentProductVariantCode' => 'test_product_variant_1'],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $this->assertCartResponseSame(
            function (array &$expectedResponse, array $actualResponse) {
                $expectedResponse['items'] = [
                    [
                        'warnings' => [],
                        'preferredVariants' => [
                            [
                                'caption' => 'Including doctor consult and service fees',
                                'code' => 'test_product_variant_1',
                                'maximumQuantityPerOrder' => 1,
                                'name' => 'Test Product Variant 1',
                                'price' => [
                                    'amount' => 1000,
                                    'currency' => 'EUR',
                                ],
                                'product' => [
                                    'code' => 'test_product_1',
                                ],
                                'quantity' => 1,
                            ],
                        ],
                        'variant' => [
                            'caption' => 'Including doctor consult and service fees',
                            'maximumQuantityPerOrder' => 1,
                            'code' => 'test_consult_variant_1',
                            'name' => 'Test Consult Variant 1',
                            'prescriptionRequired' => true,
                            'price' => [
                                'amount' => 1000,
                                'currency' => 'EUR',
                            ],
                            'product' => [
                                'code' => 'test_consult_1',
                                'attributes' => [
                                    'type' => 'consult',
                                ],
                            ],
                            'productAttributes' => [
                                'type' => 'consult',
                            ],
                        ],
                        'productName' => 'Test Consult 1',
                        'variantName' => 'Test Consult Variant 1',
                        'id' => $actualResponse['items'][0]['id'] ?? 0,
                        'quantity' => 1,
                        'unitPrice' => 1000,
                        'total' => 1000,
                        'subtotal' => 1000,
                    ],
                ];

                $expectedResponse['checkoutState'] = 'medical_questionnaire_completed';
                $expectedResponse['itemsTotal'] = 1000;
                $expectedResponse['subtotal'] = 1000;
                $expectedResponse['total'] = 1000;
                $expectedResponse['shippingTotal'] = 0;
                $expectedResponse['payments'][0]['amount'] = 1000;
                $expectedResponse['medicalQuestionnaire'] = [];
                $expectedResponse['medicalQuestionnaire']['uuid'] = AnamnesisServiceClient::VALID_UUID;
                $expectedResponse['shipments'][0]['items'] = [
                    ['name' => 'Test Consult Variant 1', 'type' => 'consult'],
                ];
            }
        );

        $actualResponse = $this->getResponseBody();

        $this->assertShippingCategoryForShipment($actualResponse['shipments'][0]['id'], ShippingCategory::SHIPPING_CATEGORY_RX_MEDICATION);
    }

    public function testCanRemoveRelatedItemsFromCartReturnsNotFoundProblemDetailsResponse(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/shop/carts/%s/child-items', 'NonExistingTokenValue'),
            [
                ['parentProductVariantCode' => 'consult_erectile-dysfunction'],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl',
            ]
        );

        $expectedProblemDetailsResponse = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => 404,
            'detail' => 'The requested cart session could not be found.',
        ];

        self::assertJsonStringEqualsJsonString(
            (string) json_encode($expectedProblemDetailsResponse),
            (string) $this->client->getResponse()->getContent()
        );
    }

    public function testCanRemoveRelatedItemsFromCartReturnsBadRequestProblemDetailsResponse(): void
    {
        $cart = $this->cartTestFactory->createCart('dok_nl', 'nl');
        $this->cartTestFactory->addRelatedItemToCart(
            $cart,
            'test_consult_variant_1',
            1,
            'test_product_variant_1',
            1,
            'test_consult_variant_1',
        );

        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/shop/carts/%s/child-items', $cart->getTokenValue()),
            [
                ['bad' => 'body'],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl',
            ]
        );

        $expectedProblemDetailsResponse = [
            'type' => 'about:blank',
            'title' => 'The request body contains errors.',
            'status' => 400,
            'detail' => 'Validation of JSON request body failed.',
            'violations' => [
                [
                    'constraint' => 'required',
                    'message' => 'The property parentProductVariantCode is required',
                    'property' => '[0].parentProductVariantCode',
                ],
            ],
        ];

        self::assertJsonStringEqualsJsonString(
            (string) json_encode($expectedProblemDetailsResponse),
            (string) $this->client->getResponse()->getContent()
        );
    }

    private function assertShippingCategoryForShipment(int $shipmentId, string $expectedShippingCategory): void
    {
        $shipmentRepository = $this->entityManager->getRepository(Shipment::class);
        $shipment = $shipmentRepository->find($shipmentId);

        self::assertInstanceOf(Shipment::class, $shipment);

        $shippingMethod = $shipment->getMethod();
        self::assertInstanceOf(ShippingMethod::class, $shippingMethod);

        $shipmentCategory = $shippingMethod->getCategory();
        self::assertInstanceOf(ShippingCategory::class, $shipmentCategory);

        self::assertSame($expectedShippingCategory, $shipmentCategory->getCode());
    }

    private function prepareDatabaseWithExampleProducts(): void
    {
        $productTestFactory = new ProductTestFactory(
            static::getContainer(),
            [self::CHANNEL_CODE],
        );
        $productTestFactory->prepareDatabaseWithRegularAndConsultProducts(
            self::PRODUCTS['regular'],
            self::PRODUCTS['consult'],
            self::LOCALE,
        );
        foreach (self::PRODUCTS['associations'] as $association) {
            $productTestFactory->createConsultProductAssociations(
                $association['owner'],
                $association['associatedProducts'],
            );
        }
    }

    private function assertCartResponseSame(callable $additionalExpectedResponseCallback): void
    {
        $actualResponse = json_decode((string) $this->client->getResponse()->getContent(), true);

        $expectedResponse = [
            'channel' => [
                'addPrescriptionMedicationDirectlyToCart' => false,
                'allowMultipleConsultsInCart' => false,
                'pickupPointsAllowed' => false,
                'baseCurrency' => [
                    'code' => 'EUR',
                ],
                'code' => 'dok_nl',
            ],
            'payments' => [
                [
                    'id' => $actualResponse['payments'][0]['id'] ?? 0,
                    'state' => 'cart',
                    'method' => [
                        'code' => 'bank_transfer_eur_dokteronline',
                        'name' => 'Bank transfer',
                        'icon' => 'bank_transfer',
                        'instructions' => 'An <strong>additional fee</strong> is charged for using this payment method. In addition, the order will take <strong>two days longer</strong> to deliver.',
                    ],
                ],
            ],
            'shipments' => [
                [
                    'id' => $actualResponse['shipments'][0]['id'] ?? 0,
                    'items' => [
                        [
                            'name' => 'Viagra 25mg 4 tablets',
                            'type' => 'medication',
                        ],
                    ],
                    'method' => [
                        'translations' => [
                            'en' => [
                                'name' => 'DHL',
                            ],
                        ],
                    ],
                    'state' => 'cart',
                ],
            ],
            'currencyCode' => 'EUR',
            'localeCode' => 'nl',
            'medicalQuestionnaire' => AnamnesisServiceClient::VALID_UUID,
            'checkoutState' => 'medical_questionnaire_completed',
            'tokenValue' => $actualResponse['tokenValue'] ?? '',
            'items' => [],
            'itemsTotal' => 0,
            'subtotal' => 0,
            'total' => 0,
            'state' => 'cart',
            'taxTotal' => 0,
            'shippingTotal' => 0,
            'orderPromotionTotal' => 0,
        ];

        $additionalExpectedResponseCallback($expectedResponse, $actualResponse);

        self::assertJsonStringEqualsJsonString(
            (string) json_encode($expectedResponse),
            (string) $this->client->getResponse()->getContent(),
        );
    }
}
