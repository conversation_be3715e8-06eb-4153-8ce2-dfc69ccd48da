<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Cart;

use App\Api\Validator\CouponCodeAllowed;
use App\Entity\Order\Order;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\CouponTestFactory;
use DateTime;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ApplyCouponToCartControllerTest extends WebTestCase
{
    private const string CHANNEL_CODE = 'dok_gb';
    private const string PRODUCT_CODE = 'test-viagra';
    private const string PRODUCT_VARIANT_CODE = 'test-viagra-variant';

    private KernelBrowser $client;
    private CartTestFactory $cartTestFactory;
    private CouponTestFactory $couponTestFactory;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        $this->client = static::createClient();

        $this->cartTestFactory = new CartTestFactory(self::getContainer());
        $this->couponTestFactory = new CouponTestFactory(self::getContainer());
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
        unset($this->client, $this->couponTestFactory);
    }

    public function testItAppliesCouponWhenCouponIsAvailable(): void
    {
        $this->couponTestFactory->createPromotionCoupon(self::CHANNEL_CODE, 200);
        $cart = $this->prepareCart();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/coupon', $cart->getTokenValue()),
            [
                'couponCode' => CouponTestFactory::VALID_COUPON_CODE,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseIsSuccessful();

        $responseData = json_decode($this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);

        self::assertEquals(CouponTestFactory::VALID_COUPON_CODE, $cart->getPromotionCoupon()?->getCode());
        self::assertEquals(-200, $cart->getOrderPromotionTotal());
        self::assertEquals(800, $responseData['itemsTotal']); // order item cost (1000) - coupon discount (200)
        self::assertEquals(1000, $responseData['subtotal']); // order item cost
        self::assertEquals(1221, $responseData['total']); // itemsTotal (1000) - coupon discount (200) + shipping total (421)
    }

    public function testItReturnsAViolationWhenTheCouponIsNotFound(): void
    {
        $cart = $this->prepareCart();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/coupon', $cart->getTokenValue()),
            [
                'couponCode' => CouponTestFactory::VALID_COUPON_CODE,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $responseBody = json_decode($this->client->getResponse()->getContent(), true);

        self::assertEquals(
            $this->createExpectedViolations('coupon_code_allowed', CouponCodeAllowed::NOT_FOUND_MESSAGE),
            $responseBody['violations']
        );
    }

    public function testItReturnsAViolationWhenNoCouponIsProvided(): void
    {
        $cart = $this->prepareCart();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/coupon', $cart->getTokenValue()),
            [
                'coupon' => 'wrong-property',
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $responseBody = json_decode($this->client->getResponse()->getContent(), true);

        self::assertEquals(
            $this->createExpectedViolations('required', 'The property couponCode is required'),
            $responseBody['violations']
        );
    }

    public function testItReturnsAViolationWhenTheCouponCodeHasExpired(): void
    {
        $coupon = $this->couponTestFactory->createPromotionCoupon(self::CHANNEL_CODE);
        $coupon->setExpiresAt(new DateTime('01-01-1990'));
        $cart = $this->prepareCart();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/coupon', $cart->getTokenValue()),
            [
                'couponCode' => CouponTestFactory::VALID_COUPON_CODE,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $responseBody = json_decode($this->client->getResponse()->getContent(), true);

        self::assertEquals(
            $this->createExpectedViolations('coupon_code_allowed', CouponCodeAllowed::INVALID_MESSAGE),
            $responseBody['violations']
        );
    }

    private function prepareCart(): Order
    {
        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        return $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            'en',
            [self::PRODUCT_VARIANT_CODE]
        );
    }

    private function createExpectedViolations(string $constraint, string $message): array
    {
        return [
            [
                'constraint' => $constraint,
                'message' => $message,
                'property' => 'couponCode',
            ],
        ];
    }
}
