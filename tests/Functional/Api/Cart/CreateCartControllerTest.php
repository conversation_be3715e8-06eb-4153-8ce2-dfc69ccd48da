<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Cart;

use App\Entity\Order\Order;
use Doctrine\ORM\EntityManager;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class CreateCartControllerTest extends WebTestCase
{
    private const string CHANNEL_CODE = 'dok_gb';

    private KernelBrowser $client;

    private EntityManager $entityManager;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
        $this->entityManager = static::getContainer()->get('doctrine.orm.entity_manager');
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->entityManager->close();
        unset($this->client, $this->entityManager);
    }

    public function testCanCreateCart(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/carts',
            [
                'empty' => null,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();

        $responseBody = $this->client->getResponse()->getContent();

        static::assertJson($responseBody);
        static::assertJsonStringEqualsJsonString(
            $this->createExpectedJsonResponse($responseBody),
            $responseBody
        );
    }

    public function testCanPickupCartWithAffiliateId(): void
    {
        $affiliateId = 'affiliate.'.microtime();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/carts',
            [
                'affiliateId' => $affiliateId,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();

        $responseBody = $this->client->getResponse()->getContent();

        static::assertJson($responseBody);

        $order = $this->entityManager->getRepository(Order::class)->findOneBy([
            'affiliateId' => $affiliateId,
        ]);

        static::assertNotNull($order);
    }

    public function testCanCreateCartWithLocaleCode(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/carts',
            [
                'localeCode' => 'en',
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();

        $responseBody = $this->client->getResponse()->getContent();

        static::assertJson($responseBody);
        static::assertJsonStringEqualsJsonString(
            $this->createExpectedJsonResponse($responseBody),
            $responseBody
        );
    }

    public function testCannotCreateCartWithoutChannelCode(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/carts',
            [
                'localeCode' => 'en',
            ]
        );

        static::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $responseBody = $this->client->getResponse()->getContent();

        $errorMessage = [
            'detail' => 'Validation of JSON request body failed.',
            'status' => Response::HTTP_BAD_REQUEST,
            'title' => 'The request body contains errors.',
            'type' => 'about:blank',
            'violations' => [
                [
                    'constraint' => 'not_blank',
                    'message' => 'This value should not be blank.',
                    'property' => 'channel',
                ],
            ],
        ];

        static::assertJsonStringEqualsJsonString(json_encode($errorMessage), $responseBody);
    }

    public function testCannotCreateCartWithUnknownChannelCode(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/carts',
            [
                'localeCode' => 'en',
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => 'unknown',
            ]
        );

        static::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $responseBody = $this->client->getResponse()->getContent();

        $errorMessage = [
            'detail' => 'Validation of JSON request body failed.',
            'status' => Response::HTTP_BAD_REQUEST,
            'title' => 'The request body contains errors.',
            'type' => 'about:blank',
            'violations' => [
                [
                    'constraint' => 'not_blank',
                    'message' => 'This value should not be blank.',
                    'property' => 'channel',
                ],
            ],
        ];

        static::assertJsonStringEqualsJsonString(json_encode($errorMessage), $responseBody);
    }

    public function testCannotCreateCartWithInvalidChannelCodeLocaleCodeCombination(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/carts',
            [
                'localeCode' => 'nl',
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $responseBody = $this->client->getResponse()->getContent();

        $errorMessage = [
            'detail' => 'Validation of JSON request body failed.',
            'status' => Response::HTTP_BAD_REQUEST,
            'title' => 'The request body contains errors.',
            'type' => 'about:blank',
            'violations' => [
                [
                    'constraint' => 'locale_code_exists',
                    'message' => 'The localeCode \'nl\' does not exist within the channel \'dok_gb\'.',
                    'property' => 'localeCode',
                ],
            ],
        ];

        static::assertJsonStringEqualsJsonString(json_encode($errorMessage), $responseBody);
    }

    /**
     * Regression test for https://mv-jira-1.atlassian.net/browse/DV-2531.
     */
    public function testCreateCartShouldNotResultInDoubleOrder(): void
    {
        $orderCountBefore = $this->entityManager->getRepository(Order::class)->count([]);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/carts',
            [
                'empty' => null,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();

        $responseBody = $this->client->getResponse()->getContent();

        static::assertJson($responseBody);
        static::assertJsonStringEqualsJsonString(
            $this->createExpectedJsonResponse($responseBody),
            $responseBody
        );

        $orderCountAfter = $this->entityManager->getRepository(Order::class)->count([]);

        $this->assertEquals($orderCountBefore + 1, $orderCountAfter);
    }

    private function createExpectedJsonResponse(string $actualResponseBody): string
    {
        $actualResponseBody = json_decode($actualResponseBody, true);

        return json_encode([
            'tokenValue' => $actualResponseBody['tokenValue'],
            'state' => 'cart',
            'checkoutState' => 'cart',
            'channel' => [
                'code' => 'dok_gb',
                'addPrescriptionMedicationDirectlyToCart' => true,
                'allowMultipleConsultsInCart' => true,
                'pickupPointsAllowed' => false,
                'baseCurrency' => [
                    'code' => 'GBP',
                ],
            ],
            'items' => [],
            'payments' => [],
            'shipments' => [],
            'currencyCode' => 'GBP',
            'localeCode' => 'en',
            'itemsTotal' => 0,
            'shippingTotal' => 0,
            'orderPromotionTotal' => 0,
            'taxTotal' => 0,
            'subtotal' => 0,
            'total' => 0,
        ]);
    }
}
