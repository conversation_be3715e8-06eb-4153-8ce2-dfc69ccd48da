<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Cart;

use App\Api\Command\Admin\Order\AddOrderItemOperation;
use App\Api\Command\Admin\Order\CompositeOrderItemOperations;
use App\Entity\Order\Order;
use App\Tests\Functional\Api\CartTestFactory;
use Doctrine\ORM\EntityManagerInterface;
use Sylius\Component\Order\Model\OrderInterface as SyliusOrderInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

final class OrderItemOperationsHandlerTest extends KernelTestCase
{
    private MessageBusInterface $messageBus;
    private EntityManagerInterface $entityManager;
    private CartTestFactory $cartTestFactory;

    protected function setUp(): void
    {
        self::bootKernel();

        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        /** @var MessageBusInterface $messageBus */
        $messageBus = self::getContainer()->get(MessageBusInterface::class);
        $this->messageBus = $messageBus;

        $this->cartTestFactory = new CartTestFactory(self::getContainer());
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
        $this->entityManager->close();
        unset($this->entityManager, $this->messageBus);
    }

    public function testInvokeDoesntFlushDatabaseIfAnyOperationFails(): void
    {
        $order = $this->cartTestFactory->createCart(
            CartTestFactory::CHANNEL_CODE,
            CartTestFactory::LOCALE_CODE,
        );
        $order->setState(SyliusOrderInterface::STATE_NEW);

        $this->cartTestFactory->createProductAndProductVariants(
            CartTestFactory::CHANNEL_CODE,
            'cialis_test_special',
            ['cialis_25mg_4_test_special']
        );

        $this->cartTestFactory->createProductAndProductVariants(
            CartTestFactory::CHANNEL_CODE,
            CartTestFactory::PRODUCT_CODE,
            [CartTestFactory::PRODUCT_VARIANT_CODE],
        );

        $this->entityManager->flush();

        /** @var string $tokenValue */
        $tokenValue = $order->getTokenValue();
        self::assertNotEmpty($tokenValue);

        self::assertEmpty($order->getItems());

        $operation1 = new AddOrderItemOperation(
            'string',
            CartTestFactory::PRODUCT_VARIANT_CODE,
            1,
            null
        );

        $operation2 = new AddOrderItemOperation(
            'string',
            'cialis_25mg_4_test_special',
            1,
            null
        );

        $operations = [
            // This AddOrderItemOperation should succeed.
            $operation1,
            // This AddOrderItemOperation should fail, because the tokenValue does not exist.
            $operation2,
        ];

        try {
            $orderItemOperations = new CompositeOrderItemOperations($operations);
            $orderItemOperations->setOrder($order);

            // Force operation to contain an order with an invalid token value.
            $orderThatDoesNotExist = new Order();
            $orderThatDoesNotExist->setTokenValue('token-does-not-exist');
            $operation2->setOrder($orderThatDoesNotExist);

            $this->messageBus->dispatch($orderItemOperations);
        } catch (Throwable) {
            // Catch should be empty, because we expect the message handlers to throw an exception.
            // But we don't want the exception to stop the test.
        }

        $this->entityManager->refresh($order);

        self::assertEmpty($order->getItems());
    }
}
