<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Cart;

use App\Entity\Order\Order;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ValidateCartControllerTest extends WebTestCase
{
    private const string CHANNEL_CODE = 'dok_nl';
    private const array PRODUCTS = [
        [
            'code' => 'test_product_1',
            'variant' => 'test_product_variant_1',
        ],
        [
            'code' => 'test_product_2',
            'variant' => 'test_product_variant_2',
        ],
    ];

    private KernelBrowser $client;
    private CartTestFactory $cartTestFactory;
    private Order $cart;
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
        $this->authenticationHelper = new ShopUserAuthenticationTestHelper($this->client);
        /** @var EntityManagerInterface $entityManager */
        $entityManager = static::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        $this->cartTestFactory = new CartTestFactory(static::getContainer());

        foreach (self::PRODUCTS as $product) {
            $this->cartTestFactory->createProductAndProductVariants(self::CHANNEL_CODE, $product['code'], [$product['variant']]);
        }

        $this->cart = $this->cartTestFactory->createCartWithProductVariants(self::CHANNEL_CODE, 'en', [self::PRODUCTS[0]['variant'], self::PRODUCTS[1]['variant']]);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
        $this->entityManager->close();
        unset($this->entityManager, $this->cart);
    }

    public function testItCanValidateValidCart(): void
    {
        $requestBody['test'] = [];
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/validate', $this->cart->getTokenValue()),
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
    }

    public function testUserMustBeTheOwner(): void
    {
        $requestBody['test'] = [];

        $customer = $this->cartTestFactory->createCustomer();
        $this->cartTestFactory->createUserForCustomer($customer);

        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $this->cart->setCheckoutState('cart');
        $this->cart->setCustomer($customer);
        $this->entityManager->flush();

        $token = $this->authenticationHelper->authenticate($customer->getEmail());
        static::assertResponseIsSuccessful();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/validate', $this->cart->getTokenValue()),
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );
        static::assertResponseIsSuccessful();
    }

    public function testUserCannotValidateOtherCart(): void
    {
        $requestBody['test'] = [];

        $customer = $this->cartTestFactory->createCustomer();
        $this->cartTestFactory->createUserForCustomer($customer);

        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $this->cart->setCheckoutState('cart');
        $this->cart->setCustomer($customer);
        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/validate', $this->cart->getTokenValue()),
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    public function testItCanNotValidateInvalidStateCart(): void
    {
        $requestBody['test'] = [];

        $this->cart->setState(Order::STATE_NEW);
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/validate', $this->cart->getTokenValue()),
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $responseBody = $this->client->getResponse()->getContent();
        static::assertJson($responseBody);
        $responseBody = json_decode($responseBody, true);
        $expectedResponseBody = $this->getExpectedInvalidStateBody();
        static::assertContains($expectedResponseBody, $responseBody);
    }

    public function testItCanNotValidateInvalidCart(): void
    {
        $this->cart->getItems()->first()->setWarnings(['Maximum quantity per order is exceeded.']);

        $requestBody['test'] = [];
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/validate', $this->cart->getTokenValue()),
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $responseBody = $this->client->getResponse()->getContent();

        $expectedResponseBody = $this->getExpectedResponseBodyForInvalidCart($this->cart->getItems()->first()->getId());

        static::assertResponseStatusCodeSame(Response::HTTP_CONFLICT);
        static::assertSame($expectedResponseBody, $responseBody);
    }

    public function testItThrowsNotFoundExceptionWhenCartIsNotFound(): void
    {
        $requestBody['test'] = [];
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/validate', 'nonExistingToken'),
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $responseBody = $this->client->getResponse()->getContent();

        $expectedResponseBody = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => Response::HTTP_NOT_FOUND,
            'detail' => 'The requested cart session could not be found.',
        ];

        static::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        static::assertJsonStringEqualsJsonString(json_encode($expectedResponseBody), $responseBody);
    }

    private function getExpectedInvalidStateBody(): array
    {
        return [[
            'constraint' => 'valid_cart',
            'message' => 'The requested cart session has an invalid state.',
            'property' => '',
        ]];
    }

    private function getExpectedResponseBodyForInvalidCart(int $orderItemId): string
    {
        return json_encode([
            [
                'constraint' => 'maximum quantity',
                'message' => 'Maximum quantity per order is exceeded.',
                'orderItem' => $orderItemId,
            ],
        ]);
    }
}
