<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Cart;

use App\Admin\Form\ProductVariantEligiblePromotion;
use App\Api\Command\OrderItemAwareInterface;
use App\Entity\Channel\Channel;
use App\Entity\Product\ProductType;
use App\Entity\Promotion\Promotion;
use App\Entity\Promotion\PromotionAction;
use App\Entity\Promotion\PromotionRule;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Api\CartTestFactory;
use Symfony\Component\HttpFoundation\Request;

class ModifyItemQuantityControllerTest extends AbstractWebTestCase
{
    private readonly CartTestFactory $cartTestFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cartTestFactory = new CartTestFactory(static::getContainer());
        $this->cartTestFactory->createProductAndProductVariants(
            CartTestFactory::CHANNEL_CODE,
            CartTestFactory::PRODUCT_CODE,
            [
                CartTestFactory::PRODUCT_VARIANT_CODE,
            ],
            'apotheek-bad-nieuweschans',
            false,
            [
                CartTestFactory::PRODUCT_VARIANT_CODE => 8,
            ]
        );
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
    }

    public function testCanModifyItemQuantity(): void
    {
        $cart = $this->cartTestFactory->createCart(CartTestFactory::CHANNEL_CODE, CartTestFactory::LOCALE_CODE);
        $this->cartTestFactory->addItemToCart($cart, CartTestFactory::PRODUCT_VARIANT_CODE, 1);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items/%s', $cart->getTokenValue(), $cart->getItems()->first()->getId()),
            ['quantity' => 2],
            ['HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl']
        );

        $this->assertCartResponseSame(
            function (array &$expectedResponse, array $actualResponse) {
                $expectedResponse['items'] = $this->getExpectedResponseItems(
                    $actualResponse['items'][0]['id'],
                    2,
                    2000,
                    2000
                );
                $expectedResponse['itemsTotal'] = 2000;
                $expectedResponse['subtotal'] = 2000;
                $expectedResponse['total'] = 2495;
                $expectedResponse['shippingTotal'] = 495;
                $expectedResponse['payments'][0]['amount'] = 2495;
            }
        );
    }

    public function testCanModifyItemQuantityWithDiscount(): void
    {
        $this->createPromotionForChannel();
        $cart = $this->cartTestFactory->createCart(CartTestFactory::CHANNEL_CODE, CartTestFactory::LOCALE_CODE);
        $this->cartTestFactory->addItemToCart($cart, CartTestFactory::PRODUCT_VARIANT_CODE, 1);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items/%s', $cart->getTokenValue(), $cart->getItems()->first()->getId()),
            ['quantity' => 2],
            ['HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl']
        );

        $this->assertCartResponseSame(
            function (array &$expectedResponse, array $actualResponse) {
                $expectedResponse['items'] = $this->getExpectedResponseItems(
                    $actualResponse['items'][0]['id'],
                    2,
                    2000,
                    1000
                );
                $expectedResponse['itemsTotal'] = 1000;
                $expectedResponse['subtotal'] = 2000;
                $expectedResponse['total'] = 1495;
                $expectedResponse['shippingTotal'] = 495;
                $expectedResponse['payments'][0]['amount'] = 1495;
                $expectedResponse['orderPromotionTotal'] = -1000;
            }
        );
    }

    public function testCanModifyItemQuantityWithoutDiscount(): void
    {
        $this->createPromotionForChannel();
        $this->cartTestFactory->addAttributeToProduct();
        $cart = $this->cartTestFactory->createCart(CartTestFactory::CHANNEL_CODE, CartTestFactory::LOCALE_CODE);
        $this->cartTestFactory->addItemToCart($cart, CartTestFactory::PRODUCT_VARIANT_CODE, 1);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items/%s', $cart->getTokenValue(), $cart->getItems()->first()->getId()),
            ['quantity' => 2],
            ['HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl']
        );

        $this->assertCartResponseSame(
            function (array &$expectedResponse, array $actualResponse) {
                $expectedResponse['items'] = $this->getExpectedResponseItems(
                    $actualResponse['items'][0]['id'],
                    2,
                    2000,
                    2000
                );
                $expectedResponse['itemsTotal'] = 2000;
                $expectedResponse['subtotal'] = 2000;
                $expectedResponse['total'] = 2495;
                $expectedResponse['shippingTotal'] = 495;
                $expectedResponse['payments'][0]['amount'] = 2495;
            }
        );
    }

    public function testCanModifyItemQuantityWithSameQuantity(): void
    {
        $cart = $this->cartTestFactory->createCart(CartTestFactory::CHANNEL_CODE, CartTestFactory::LOCALE_CODE);
        $this->cartTestFactory->addItemToCart($cart, CartTestFactory::PRODUCT_VARIANT_CODE, 1);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items/%s', $cart->getTokenValue(), $cart->getItems()->first()->getId()),
            ['quantity' => 1],
            ['HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl']
        );

        $this->assertCartResponseSame(
            function (array &$expectedResponse, array $actualResponse) {
                $expectedResponse['items'] = $this->getExpectedResponseItems(
                    $actualResponse['items'][0]['id'],
                    1,
                    1000,
                    1000
                );
                $expectedResponse['itemsTotal'] = 1000;
                $expectedResponse['subtotal'] = 1000;
                $expectedResponse['total'] = 1495;
                $expectedResponse['shippingTotal'] = 495;
                $expectedResponse['payments'][0]['amount'] = 1495;
            }
        );
    }

    public function testCannotModifyItemQuantityExceedingMaximumQuantity(): void
    {
        $cart = $this->cartTestFactory->createCart(CartTestFactory::CHANNEL_CODE, CartTestFactory::LOCALE_CODE);
        $this->cartTestFactory->addItemToCart($cart, CartTestFactory::PRODUCT_VARIANT_CODE, 1);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items/%s', $cart->getTokenValue(), $cart->getItems()->first()->getId()),
            ['quantity' => 10],
            ['HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl']
        );

        $expectedProblemDetailsResponse = [
            'detail' => 'Validation of JSON request body failed.',
            'status' => 400,
            'title' => 'The request body contains errors.',
            'type' => 'about:blank',
            'violations' => [
                [
                    'constraint' => 'less_than_or_equal',
                    'message' => 'This value should be less than or equal to 8.',
                    'property' => 'quantity',
                ],
            ],
        ];

        self::assertJsonStringEqualsJsonString(
            json_encode($expectedProblemDetailsResponse),
            $this->client->getResponse()->getContent()
        );
    }

    public function testCannotModifyItemQuantityWithNoExistingTokenValue(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items/%s', 'hIMoM', 1),
            ['quantity' => 10],
            ['HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl']
        );

        $expectedProblemDetailsResponse = [
            'detail' => OrderItemAwareInterface::ORDER_ITEM_NOT_FOUND_STATE_CART,
            'status' => 404,
            'title' => 'An error occurred.',
            'type' => 'about:blank',
        ];

        self::assertJsonStringEqualsJsonString(
            json_encode($expectedProblemDetailsResponse),
            $this->client->getResponse()->getContent()
        );
    }

    public function testCannotModifyItemQuantityWithNoExistingCartItemId(): void
    {
        $cart = $this->cartTestFactory->createCart(CartTestFactory::CHANNEL_CODE, CartTestFactory::LOCALE_CODE);
        $this->cartTestFactory->addItemToCart($cart, CartTestFactory::PRODUCT_VARIANT_CODE, 1);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items/%s', $cart->getTokenValue(), 1337),
            ['quantity' => 1],
            ['HTTP_SYLIUS_CHANNEL_CODE' => 'dok_nl']
        );

        $expectedProblemDetailsResponse = [
            'detail' => OrderItemAwareInterface::ORDER_ITEM_NOT_FOUND_STATE_CART,
            'status' => 404,
            'title' => 'An error occurred.',
            'type' => 'about:blank',
        ];

        self::assertJsonStringEqualsJsonString(
            json_encode($expectedProblemDetailsResponse),
            $this->client->getResponse()->getContent()
        );
    }

    private function getExpectedResponseItems(
        int $id,
        int $expectedQuantity,
        int $expectedSubtotal,
        int $expectedTotal,
        array $expectedAttributes = [],
    ): array {
        if (empty($expectedAttributes) || empty($expectedAttributes['type'])) {
            $expectedAttributes['type'] = ProductType::MEDICATION->value;
        }

        return [
            [
                'id' => $id,
                'productName' => 'viagra_test_special',
                'quantity' => $expectedQuantity,
                'subtotal' => $expectedSubtotal,
                'total' => $expectedTotal,
                'unitPrice' => 1000,
                'variant' => [
                    'caption' => 'Including doctor consult and service fees',
                    'code' => 'viagra_25mg_4_test_special',
                    'maximumQuantityPerOrder' => 8,
                    'name' => 'Viagra 25mg 4 tablets',
                    'prescriptionRequired' => false,
                    'price' => [
                        'amount' => 1000,
                        'currency' => 'EUR',
                    ],
                    'product' => [
                        'attributes' => $expectedAttributes,
                        'code' => 'viagra_test_special',
                    ],
                    'productAttributes' => [
                        'type' => 'medication',
                    ],
                    'supplier' => [
                        'identifier' => 'apotheek-bad-nieuweschans',
                        'name' => 'Apotheek Bad Nieuweschans BV',
                    ],
                ],
                'variantName' => 'Viagra 25mg 4 tablets',
                'warnings' => [],
            ],
        ];
    }

    private function assertCartResponseSame(callable $additionalExpectedResponseCallback): void
    {
        $actualResponse = json_decode($this->client->getResponse()->getContent(), true);

        $expectedResponse = [
            'channel' => [
                'addPrescriptionMedicationDirectlyToCart' => false,
                'allowMultipleConsultsInCart' => false,
                'pickupPointsAllowed' => false,
                'baseCurrency' => [
                    'code' => 'EUR',
                ],
                'code' => 'dok_nl',
            ],
            'payments' => [
                [
                    'id' => $actualResponse['payments'][0]['id'] ?? 0,
                    'state' => 'cart',
                    'method' => [
                        'code' => 'bank_transfer_eur_dokteronline',
                        'icon' => 'bank_transfer',
                        'name' => 'Bank transfer',
                        'instructions' => 'An <strong>additional fee</strong> is charged for using this payment method. In addition, the order will take <strong>two days longer</strong> to deliver.',
                    ],
                ],
            ],
            'shipments' => [
                [
                    'id' => $actualResponse['shipments'][0]['id'] ?? 0,
                    'items' => [
                        [
                            'name' => 'Viagra 25mg 4 tablets',
                            'type' => 'medication',
                        ],
                    ],
                    'method' => [
                        'translations' => [
                            'en' => [
                                'name' => 'DHL',
                            ],
                        ],
                    ],
                    'state' => 'cart',
                ],
            ],
            'currencyCode' => 'EUR',
            'localeCode' => 'nl',
            'checkoutState' => 'cart',
            'tokenValue' => $actualResponse['tokenValue'] ?? '',
            'items' => [],
            'itemsTotal' => 0,
            'subtotal' => 0,
            'total' => 0,
            'state' => 'cart',
            'taxTotal' => 0,
            'shippingTotal' => 0,
            'orderPromotionTotal' => 0,
        ];

        $additionalExpectedResponseCallback($expectedResponse, $actualResponse);

        self::assertJsonStringEqualsJsonString(
            json_encode($expectedResponse),
            $this->client->getResponse()->getContent(),
        );
    }

    private function createPromotionForChannel(): void
    {
        $promotionAttributeRule = new PromotionRule();
        $promotionAttributeRule->setType('product_contains_attribute');
        $promotionAttributeRule->setConfiguration([
            'attributes' => ['addictive'],
        ]);

        $promotionGraduatedDiscount = new PromotionRule();
        $promotionGraduatedDiscount->setType('n_items_of_type_x');
        $promotionGraduatedDiscount->setConfiguration([
            'eligibleType' => ProductVariantEligiblePromotion::ANY->value,
            'count' => 2,
        ]);

        $promotionAction = new PromotionAction();
        $promotionAction->setType('order_percentage_discount');
        $promotionAction->setConfiguration([
            'percentage' => 0.5,
        ]);

        $promotion = new Promotion();
        $promotion->setName('Graduated Discount');
        $promotion->setCode('graduated');
        $promotion->setAppliesToDiscounted(true);
        $promotion->addRule($promotionAttributeRule);
        $promotion->addRule($promotionGraduatedDiscount);
        $promotion->addAction($promotionAction);

        $channel = $this->entityManager?->getRepository(Channel::class)->findOneBy([
            'code' => CartTestFactory::CHANNEL_CODE,
        ]);

        $promotion->addChannel($channel);

        $this->entityManager->persist($promotion);
        $this->entityManager->flush();
    }
}
