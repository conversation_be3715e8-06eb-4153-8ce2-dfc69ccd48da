<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Cart;

use App\Entity\Order\Order;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\CouponTestFactory;
use Sylius\Component\Order\Model\OrderInterface as OrderInterfaceAlias;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class RemoveCouponFromCartControllerTest extends AbstractWebTestCase
{
    private const string VALID_COUPON_CODE = 'test-coupon';
    private const string CHANNEL_CODE = 'dok_gb';
    private const string PRODUCT_CODE = 'test-viagra';
    private const string PRODUCT_VARIANT_CODE = 'test-viagra-variant';

    private const string COUPON_API_URI = '/api/shop/carts/%s/coupon';

    private CartTestFactory $cartTestFactory;
    private CouponTestFactory $couponTestFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cartTestFactory = new CartTestFactory(self::getContainer());
        $this->couponTestFactory = new CouponTestFactory(self::getContainer());
    }

    protected function tearDown(): void
    {
        parent::tearDown();

        $this->cartTestFactory->teardown();

        unset($this->couponTestFactory, $this->cartTestFactory);
    }

    public function testItRemovesCoupon(): void
    {
        $this->couponTestFactory->createPromotionCoupon(self::CHANNEL_CODE);
        $cart = $this->prepareCartWithCoupon();

        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf(self::COUPON_API_URI, $cart->getTokenValue()),
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseIsSuccessful();

        $decodedResponseContent = json_decode(
            (string) $this->client->getResponse()->getContent(),
            true,
            512,
            JSON_THROW_ON_ERROR
        );

        $cart = $this->entityManager->find(Order::class, $cart->getId());

        self::assertInstanceOf(Order::class, $cart);
        self::assertNull($cart->getPromotionCoupon()?->getCode());
        self::assertEquals(0, $cart->getOrderPromotionTotal());
        self::assertEquals(0, $decodedResponseContent['orderPromotionTotal']);
    }

    public function testItReturnsCartNotFoundProblemDetailsResponseWhenCartIsNotFound(): void
    {
        $this->couponTestFactory->createPromotionCoupon(self::CHANNEL_CODE);
        $this->prepareCartWithCoupon();

        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf(self::COUPON_API_URI, 'randomToken'),
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();
        $expectedResponseBody = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => Response::HTTP_NOT_FOUND,
            'detail' => 'The requested cart session could not be found.',
        ];

        static::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        static::assertJsonStringEqualsJsonString((string) json_encode($expectedResponseBody, JSON_THROW_ON_ERROR), $responseBody);
    }

    /**
     * @dataProvider provideDisallowedStates
     */
    public function testItCannotRemoveCouponWhenCartIsInOtherStateThanCart(string $disallowedState): void
    {
        $this->couponTestFactory->createPromotionCoupon(self::CHANNEL_CODE);
        $cart = $this->prepareCartWithCoupon();
        $cart->setState($disallowedState);
        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf(self::COUPON_API_URI, $cart->getTokenValue()),
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();
        $expectedResponseBody = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => Response::HTTP_NOT_FOUND,
            'detail' => 'The requested cart session could not be found.',
        ];

        static::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        static::assertJsonStringEqualsJsonString((string) json_encode($expectedResponseBody, JSON_THROW_ON_ERROR), $responseBody);
    }

    public function provideDisallowedStates(): array
    {
        return [
            [OrderInterfaceAlias::STATE_NEW],
            [OrderInterfaceAlias::STATE_CANCELLED],
            [OrderInterfaceAlias::STATE_FULFILLED],
        ];
    }

    private function prepareCartWithCoupon(): Order
    {
        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        $cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            'en',
            [self::PRODUCT_VARIANT_CODE]
        );

        $this->setCoupon($cart);

        return $cart;
    }

    private function setCoupon(Order $cart): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::COUPON_API_URI, $cart->getTokenValue()),
            [
                'couponCode' => self::VALID_COUPON_CODE,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseIsSuccessful();

        self::assertEquals(self::VALID_COUPON_CODE, $cart->getPromotionCoupon()?->getCode());
        self::assertEquals(-1000, $cart->getOrderPromotionTotal());
    }
}
