<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api;

use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Component\HttpFoundation\Request;

class ShopUserAuthenticationTestHelper
{
    private KernelBrowser $client;

    public function __construct(KernelBrowser $client)
    {
        $this->client = $client;
    }

    public function authenticate(
        string $email = CartTestFactory::USER_EMAIL,
        string $password = CartTestFactory::USER_PASSWORD,
        string $channelCode = CartTestFactory::CHANNEL_CODE,
    ): ?string {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/account/authenticate',
            [
                'email' => $email,
                'password' => $password,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => $channelCode,
            ]
        );

        $responseBody = $this->client->getResponse()->getContent();

        $decodedResponseBody = json_decode($responseBody, true);

        return $decodedResponseBody['token'] ?? null;
    }
}
