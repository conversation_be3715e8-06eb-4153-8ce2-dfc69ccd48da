<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\ShopUserToken;

use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\User\ShopUser;
use App\Tests\Util\Factory\ProblemDetailsFactory;
use Doctrine\ORM\EntityManager;
use Sylius\Resource\Doctrine\Persistence\RepositoryInterface;
use Sylius\Resource\Factory\FactoryInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\NativePasswordHasher;
use Symfony\Component\PasswordHasher\Hasher\PasswordHasherFactoryInterface;

/**
 * Functional test for authenticating a user with a migrated {@see crypt} hashed password.
 */
class GetAuthenticationTokenTest extends WebTestCase
{
    private const string USER_EMAIL_SUFFIX = '<EMAIL>';

    private const string PASSWORD_HASHER_CONFIGURED = ShopUser::class;

    private const string PASSWORD_HASHER_BCRYPT = 'bcrypt';

    private const string PASSWORD_HASHER_CRYPT = 'crypt';

    private const string CHANNEL_CODE = 'dok_gb';

    private KernelBrowser $client;
    private EntityManager $entityManager;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
        $this->entityManager = static::getContainer()->get('doctrine.orm.entity_manager');
    }

    public function testCanGetAuthenticationToken(): void
    {
        $email = hrtime(true).self::USER_EMAIL_SUFFIX;

        $password = 'test123456!';
        $this->createShopUser($email, $password);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/account/authenticate',
            [
                'email' => $email,
                'password' => $password,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();

        $responseBody = $this->client->getResponse()->getContent();

        static::assertJson($responseBody);

        $jsonResponseBody = json_decode($responseBody, true);

        static::assertIsString($jsonResponseBody['token']);
        static::assertStringContainsString('/api/v2/shop/customers/', $jsonResponseBody['customer']);
    }

    public function testCanNotGetTokenWithInvalidCredentials(): void
    {
        $email = hrtime(true).self::USER_EMAIL_SUFFIX;

        $password = 'test123456!';
        $this->createShopUser($email, $password);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/account/authenticate',
            [
                'email' => $email,
                'password' => 'wrong_password',
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
        self::assertJsonStringEqualsJsonString(
            ProblemDetailsFactory::Unauthorized->json(),
            (string) $this->client->getResponse()->getContent()
        );
    }

    /**
     * @dataProvider providePasswordMigrationHashTypes
     */
    public function testCanGetAuthenticationTokenForMigratedUserWhichRehashesThePassword(string $passwordHashType): void
    {
        $email = hrtime(true).self::USER_EMAIL_SUFFIX;

        $password = 'test123456!';
        $shopUser = $this->createShopUser(
            $email,
            null,
            $this->createPasswordHash($password, $passwordHashType, $email)
        );

        $legacyPassword = $shopUser->getPassword();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/account/authenticate',
            [
                'email' => $email,
                'password' => $password,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();

        $responseBody = $this->client->getResponse()->getContent();

        static::assertJson($responseBody);

        $jsonResponseBody = json_decode($responseBody, true);

        static::assertIsString($jsonResponseBody['token']);
        static::assertStringContainsString('/api/v2/shop/customers/', $jsonResponseBody['customer']);

        static::assertNotSame($legacyPassword, $shopUser->getPassword());
        static::assertStringStartsWith('$argon2i$', $shopUser->getPassword());

        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/account/authenticate',
            [
                'email' => $email,
                'password' => $password,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();

        $responseBody = $this->client->getResponse()->getContent();

        static::assertJson($responseBody);

        $jsonResponseBody = json_decode($responseBody, true);

        static::assertIsString($jsonResponseBody['token']);
        static::assertStringContainsString('/api/v2/shop/customers/', $jsonResponseBody['customer']);
    }

    public function providePasswordMigrationHashTypes(): array
    {
        return [
            [self::PASSWORD_HASHER_BCRYPT],
            [self::PASSWORD_HASHER_CRYPT],
        ];
    }

    private function createShopUser(string $email, ?string $plainPassword = null, ?string $passwordHash = null): ShopUser
    {
        /** @var RepositoryInterface $customerPoolRepository */
        $customerPoolRepository = static::getContainer()->get('app.repository.customer_pool');
        /** @var FactoryInterface $customerFactory */
        $customerFactory = static::getContainer()->get('sylius.factory.customer');
        /** @var FactoryInterface $shopUserFactory */
        $shopUserFactory = static::getContainer()->get('sylius.factory.shop_user');

        /** @var CustomerPool $customerPool */
        $customerPool = $customerPoolRepository->findOneBy(['code' => 'dokteronline']);

        /** @var Customer $customer */
        $customer = $customerFactory->createNew();
        $customer->setCustomerPool($customerPool);

        /** @var ShopUser $shopUser */
        $shopUser = $shopUserFactory->createNew();
        $shopUser->setCustomer($customer);
        $shopUser->setEmail($email);
        $shopUser->setUsername($email);
        $shopUser->setEnabled(true);

        if (is_string($plainPassword)) {
            $shopUser->setPlainPassword($plainPassword);
        }
        if (is_string($passwordHash)) {
            $shopUser->setPassword($passwordHash);
        }

        $this->entityManager->persist($shopUser);
        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        return $shopUser;
    }

    private function createPasswordHash(
        string $password,
        string $type = self::PASSWORD_HASHER_CONFIGURED,
        string $salt = '',
    ): string {
        if ($type === self::PASSWORD_HASHER_CONFIGURED) {
            /** @var PasswordHasherFactoryInterface $passwordHasherFactory */
            $passwordHasherFactory = static::getContainer()->get(PasswordHasherFactoryInterface::class);
            $passwordHasher = $passwordHasherFactory->getPasswordHasher($type);

            return $passwordHasher->hash($password);
        }

        if ($type === self::PASSWORD_HASHER_BCRYPT) {
            $passwordHasher = new NativePasswordHasher(null, null, null, PASSWORD_BCRYPT);

            return $passwordHasher->hash($password);
        }

        return crypt($password, $salt);
    }
}
