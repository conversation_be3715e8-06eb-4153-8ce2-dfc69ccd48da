<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api;

use App\Api\Command\Cart\ApplyCoupon;
use App\Api\CommandHandler\Cart\ApplyCouponHandler;
use App\Entity\Channel\Channel;
use App\Entity\Order\Order;
use App\Entity\Promotion\Promotion;
use App\Entity\Promotion\PromotionCoupon;
use DateTimeInterface;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\Assert;
use Sylius\Component\Core\Factory\PromotionActionFactoryInterface;
use Sylius\Component\Promotion\Model\PromotionActionInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Creates Promotion Coupon.
 */
class CouponTestFactory
{
    public const string CHANNEL_CODE = 'dok_nl';
    public const string VALID_COUPON_CODE = 'test-coupon';
    public const string VALID_PROMOTION_CODE = 'test-promotion';

    private EntityManagerInterface $entityManager;

    /** @var PromotionActionFactoryInterface<PromotionActionInterface> */
    private PromotionActionFactoryInterface $promotionActionFactory;

    private ApplyCouponHandler $applyCouponHandler;

    public function __construct(ContainerInterface $container)
    {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = $container->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;
        /** @var PromotionActionFactoryInterface<PromotionActionInterface> $promotionActionFactory */
        $promotionActionFactory = $container->get(PromotionActionFactoryInterface::class);
        $this->promotionActionFactory = $promotionActionFactory;

        /** @var ApplyCouponHandler $applyCouponHandler */
        $applyCouponHandler = $container->get(ApplyCouponHandler::class);
        $this->applyCouponHandler = $applyCouponHandler;
    }

    public function createPromotionCoupon(
        string $channelCode = self::CHANNEL_CODE,
        int $fixedDiscount = 1000,
        DateTimeInterface $startsAt = null,
        DateTimeInterface $endsAt = null,
    ): PromotionCoupon {
        /** @var Channel $channel */
        $channel = $this->entityManager->getRepository(Channel::class)
            ->findOneBy(['code' => $channelCode]);

        $promotionAction = $this->promotionActionFactory->createFixedDiscount($fixedDiscount, $channelCode);

        $promotionCoupon = new PromotionCoupon();
        $promotionCoupon->setCode(self::VALID_COUPON_CODE);
        $promotionCoupon->setExpiresAt($endsAt);

        $promotion = new Promotion();
        $promotion->addChannel($channel);
        $promotion->setCouponBased(true);
        $promotion->setCode(self::VALID_PROMOTION_CODE);
        $promotion->setName('tientje');
        $promotion->addAction($promotionAction);
        $promotion->addCoupon($promotionCoupon);
        $promotion->setStartsAt($startsAt);
        $promotion->setEndsAt($endsAt);

        $this->entityManager->persist($promotionAction);
        $this->entityManager->persist($promotionCoupon);
        $this->entityManager->persist($promotion);

        $this->entityManager->flush();

        return $promotionCoupon;
    }

    public function applyCoupon(PromotionCoupon $promotionCoupon, Order $order): void
    {
        $couponCode = $promotionCoupon->getCode();
        Assert::assertIsString($couponCode);

        $applyCoupon = new ApplyCoupon((string) $couponCode);
        $applyCoupon->setOrder($order);

        $this->applyCouponHandler->__invoke($applyCoupon);
    }
}
