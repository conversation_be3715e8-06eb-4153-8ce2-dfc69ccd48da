<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Notify;

use App\Entity\Addressing\Zone;
use App\Entity\Order\Order;
use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShipmentInterface;
use App\Entity\Shipping\ShippingMethod;
use App\StateMachine\OrderShippingStates;
use App\Tests\Functional\Api\CartTestFactory;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Superbrave\PharmacyServiceClient\Model\Response\Order as SupplierOrder;
use Sylius\Component\Core\OrderPaymentStates;
use Sylius\Component\Core\OrderShippingStates as SyliusOrderShippingStates;
use Sylius\Component\Order\Model\OrderInterface as BaseOrderInterface;
use Sylius\Component\Shipping\Model\ShipmentInterface as SyliusShipmentInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpClient\MockHttpClient;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class NotifyOrderShipmentStatusControllerTest extends WebTestCase
{
    private const string CHANNEL_CODE = 'dok_nl';
    private const string LOCALE_CODE = 'nl';
    private const string PRODUCT_CODE = 'viagra_test_special';
    private const string PRODUCT_VARIANT_CODE = 'viagra_25mg_4_test_special';
    private const string SHIPPING_CARRIER_CODE = 'carrier_code';

    private const string NOTIFY_ORDER_SHIPMENT_STATUS_ENDPOINT = '/api/notify/orders/%s/shipment/%s';
    private const string EVENT_TYPE = 'order_shipment.event.unknown';

    private const string SUPPLIER_SERVICE_ORDER_UUID = 'c3ff89b4-dcab-487c-80ab-4a5f1d4fa219';

    private const string TOKEN_VALUE_FOR_NON_EXISTING_ORDER = 'saSDfa_as';
    private const string SUPPLIER_SERVICE_ORDER_UUID_FOR_NON_EXISTING_ORDER_AND_SHIPMENT = '8705b592-9f4d-40ac-963b-65b3aa669c04';
    private const string SHIPMENT_ID_FOR_FOR_NON_EXISTING_ORDER_AND_SHIPMENT = 'd8b94aa0-225c-4ccc-acac-ce31a0cab2c8';

    private CartTestFactory $cartTestFactory;
    private EntityManager $entityManager;
    private KernelBrowser $client;
    private MockHttpClient $mockHttpClient;

    public function setUp(): void
    {
        self::ensureKernelShutdown();
        $this->client = self::createClient();

        $this->cartTestFactory = new CartTestFactory(self::getContainer());
        $this->entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->mockHttpClient = self::getContainer()->get(MockHttpClient::class);
    }

    /**
     * @dataProvider supplierOrderStatusProvider
     */
    public function testThatTheStatusIsUpdatedAfterReceivingMessage(
        array $states,
        array $expectedStates,
        string $responseState,
        ?string $expectedCarrier,
        ?string $expectedTracking,
    ): void {
        $order = $this->createOrderWithShipment(...$states);
        /** @var Shipment $firstShipment */
        $firstShipment = $order->getShipments()->first();
        $shipmentId = $firstShipment->getId();

        $mockResponse = new MockResponse(file_get_contents(
            sprintf(__DIR__.'/NotifyOrderShipmentStatusController.orderShipment.%sResponse.json', $responseState)
        ));
        $this->mockHttpClient->setResponseFactory($mockResponse);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(
                self::NOTIFY_ORDER_SHIPMENT_STATUS_ENDPOINT,
                $order->getTokenValue(),
                $shipmentId
            ),
            [
                'order' => [
                    'uuid' => self::SUPPLIER_SERVICE_ORDER_UUID,
                    'status' => SupplierOrder::STATUS_SHIPPED,
                    'eventType' => self::EVENT_TYPE,
                ],
            ]
        );

        /** @var ShipmentInterface $shipment */
        $shipment = $this->entityManager->getRepository(Shipment::class)->find($shipmentId);

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        self::assertSame($expectedStates, [
            $order->getState(),
            $order->getShippingState(),
            $shipment->getState(),
        ]);
        self::assertSame($expectedCarrier, $shipment->getMethod()->getCode());
        self::assertSame($expectedTracking, $shipment->getTracking());
    }

    public function supplierOrderStatusProvider(): iterable
    {
        $states = [
            BaseOrderInterface::STATE_NEW,
            OrderShippingStates::STATE_PENDING,
            ShipmentInterface::STATE_PENDING,
        ];

        $expectedStates = [
            BaseOrderInterface::STATE_NEW,
            OrderShippingStates::STATE_PENDING,
            ShipmentInterface::STATE_PROCESSING,
        ];

        yield 'notify processing shipment' => [
            $states,
            $expectedStates,
            'processing',
            self::SHIPPING_CARRIER_CODE,
            null,
        ];

        $expectedStates = [
            BaseOrderInterface::STATE_FULFILLED,
            SyliusOrderShippingStates::STATE_SHIPPED,
            SyliusShipmentInterface::STATE_SHIPPED,
        ];

        yield 'notify shipped shipment' => [
            $states,
            $expectedStates,
            'shipped',
            'DHL_EXPRESS',
            'track-and-trace-code',
        ];

        $states = [
            BaseOrderInterface::STATE_FULFILLED,
            SyliusOrderShippingStates::STATE_SHIPPED,
            SyliusShipmentInterface::STATE_SHIPPED,
        ];

        $expectedStates = [
            BaseOrderInterface::STATE_FULFILLED,
            OrderShippingStates::STATE_RETURNED,
            ShipmentInterface::STATE_RETURNED,
        ];

        yield 'notify returned shipment' => [
            $states,
            $expectedStates,
            'returned',
            'DHL_EXPRESS',
            'track-and-trace-code',
        ];

        $expectedStates = [
            BaseOrderInterface::STATE_NEW,
            OrderShippingStates::STATE_PENDING,
            ShipmentInterface::STATE_PENDING,
        ];

        yield 'notify recalled shipment when shipped to new' => [
            $states,
            $expectedStates,
            'new',
            self::SHIPPING_CARRIER_CODE,
            null,
        ];

        $expectedStates = [
            BaseOrderInterface::STATE_NEW,
            OrderShippingStates::STATE_PENDING,
            ShipmentInterface::STATE_PROCESSING,
        ];

        yield 'notify recalled shipment when shipped to processing' => [
            $states,
            $expectedStates,
            'processing',
            self::SHIPPING_CARRIER_CODE,
            null,
        ];

        $expectedStates = [
            BaseOrderInterface::STATE_NEW,
            OrderShippingStates::STATE_PENDING,
            ShipmentInterface::STATE_PENDING,
        ];

        yield 'notify recalled shipment when shipped to reship' => [
            $states,
            $expectedStates,
            'reship',
            self::SHIPPING_CARRIER_CODE,
            null,
        ];

        $states = [
            BaseOrderInterface::STATE_NEW,
            OrderShippingStates::STATE_PENDING,
            ShipmentInterface::STATE_PROCESSING,
        ];

        yield 'notify unmarked processing shipment when processing to new' => [
            $states,
            $expectedStates,
            'new',
            self::SHIPPING_CARRIER_CODE,
            null,
        ];
    }

    public function testThatItShouldFailWhenNoOrderHasBeenFound(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(
                self::NOTIFY_ORDER_SHIPMENT_STATUS_ENDPOINT,
                self::TOKEN_VALUE_FOR_NON_EXISTING_ORDER,
                self::SHIPMENT_ID_FOR_FOR_NON_EXISTING_ORDER_AND_SHIPMENT
            ),
            [
                'order' => [
                    'uuid' => self::SUPPLIER_SERVICE_ORDER_UUID_FOR_NON_EXISTING_ORDER_AND_SHIPMENT,
                    'status' => SupplierOrder::STATUS_SHIPPED,
                    'eventType' => self::EVENT_TYPE,
                ],
            ]
        );

        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => Response::HTTP_NOT_FOUND,
            'detail' => 'The requested order could not be found.',
        ];

        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        $this->assertSame(json_encode($expectedResponse), $this->client->getResponse()->getContent());
    }

    public function testThatItShouldFailWhenNoShipmentCanBeFound(): void
    {
        $order = $this->createOrderWithShipment(
            BaseOrderInterface::STATE_NEW,
            OrderShippingStates::STATE_PENDING,
            ShipmentInterface::STATE_PENDING,
        );

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(
                self::NOTIFY_ORDER_SHIPMENT_STATUS_ENDPOINT,
                $order->getTokenValue(),
                self::SHIPMENT_ID_FOR_FOR_NON_EXISTING_ORDER_AND_SHIPMENT
            ),
            [
                'order' => [
                    'uuid' => self::SUPPLIER_SERVICE_ORDER_UUID_FOR_NON_EXISTING_ORDER_AND_SHIPMENT,
                    'status' => SupplierOrder::STATUS_SHIPPED,
                    'eventType' => self::EVENT_TYPE,
                ],
            ]
        );

        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => Response::HTTP_NOT_FOUND,
            'detail' => 'Shipment not found.',
        ];

        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        $this->assertSame(json_encode($expectedResponse), $this->client->getResponse()->getContent());
    }

    public function testThatItShouldFailWhenTheShipmentStateChangeIsNotAllowedByTheStateMachine(): void
    {
        $order = $this->createOrderWithShipment(
            BaseOrderInterface::STATE_NEW,
            OrderShippingStates::STATE_PENDING,
            ShipmentInterface::STATE_PENDING
        );
        /** @var Shipment $firstShipment */
        $firstShipment = $order->getShipments()->first();
        $shipmentId = $firstShipment->getId();

        $mockResponse = new MockResponse(
            file_get_contents(__DIR__.'/NotifyOrderShipmentStatusController.orderShipment.returnedResponse.json')
        );
        $this->mockHttpClient->setResponseFactory($mockResponse);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(
                self::NOTIFY_ORDER_SHIPMENT_STATUS_ENDPOINT,
                $order->getTokenValue(),
                $shipmentId
            ),
            [
                'order' => [
                    'uuid' => self::SUPPLIER_SERVICE_ORDER_UUID,
                    'status' => SupplierOrder::STATUS_RETURNED,
                    'eventType' => self::EVENT_TYPE,
                ],
            ]
        );

        $stateChangeNotAllowedMessage = sprintf(
            'Order with %s token cannot make \'%s\' transition',
            $order->getTokenValue(),
            'return'
        );

        $expectedResponse = [
            'detail' => $stateChangeNotAllowedMessage,
            'status' => Response::HTTP_BAD_REQUEST,
            'title' => 'An error occurred.',
            'type' => 'about:blank',
        ];

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        $this->assertJsonStringEqualsJsonString(
            json_encode($expectedResponse),
            $this->client->getResponse()->getContent()
        );
    }

    private function createShippingMethod(): ShippingMethod
    {
        $shippingMethod = new ShippingMethod();
        $shippingMethod->setCode(self::SHIPPING_CARRIER_CODE);
        $shippingMethod->setCalculator('flat_rate');
        $shippingMethod->setZone(
            $this->entityManager->getRepository(Zone::class)->findOneBy(['code' => 'WORLD'])
        );
        $shippingMethod->enable();
        $shippingMethod->setPosition(99);

        $this->entityManager->persist($shippingMethod);

        return $shippingMethod;
    }

    private function createOrderWithShipment(
        string $orderState,
        string $orderShippingState,
        string $shipmentState,
    ): Order {
        $this
            ->cartTestFactory
            ->createProductAndProductVariants(
                self::CHANNEL_CODE,
                self::PRODUCT_CODE,
                [self::PRODUCT_VARIANT_CODE]
            );

        $order = $this
            ->cartTestFactory
            ->createCartWithProductVariants(
                self::CHANNEL_CODE,
                self::LOCALE_CODE,
                [self::PRODUCT_VARIANT_CODE]
            );

        $order->setState($orderState);
        $order->setShippingState($orderShippingState);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);

        $shippingMethod = $this->createShippingMethod();

        /** @var Shipment $shipment */
        $shipment = $order->getShipments()->first();
        $shipment->setMethod($shippingMethod);
        $shipment->setState($shipmentState);

        $this->entityManager->persist($order);
        $this->entityManager->flush();

        return $order;
    }
}
