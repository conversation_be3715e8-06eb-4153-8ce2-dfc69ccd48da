<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Product;

use App\Entity\Addressing\Country;
use App\Entity\Supplier\Supplier;
use App\Entity\Supplier\SupplierInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetProductVariantsControllerTest extends AbstractProductListControllerTest
{
    protected const string CHANNEL_CODE = 'dok_at';
    protected const string PAGINATION_COUNTRY_CODE = 'AT';
    protected const string PAGINATION_LOCALE_CODE = self::EN_LOCALE_CODE;
    protected const array PRODUCT_VARIANT_TRANSLATIONS = [
        self::DE_LOCALE_CODE => [
            'name' => 'Test-viagra',
            'caption' => 'Der Caption',
        ],
        self::EN_LOCALE_CODE => [
            'name' => 'La Testa',
            'caption' => 'Le Caption',
        ],
    ];
    private const string DE_LOCALE_CODE = 'de';
    private const string EN_LOCALE_CODE = 'en';
    private const string COUNTRY_CODE_AUSTRIA = 'AT';

    private const string SUPPLIER_CODE = 'test-supplier';

    private const string PRODUCT_CODE = 'viagra_test';

    private const string PRODUCT_VARIANT_CODE = 'viagra_25mg_at_blueclinic';
    private const string TEST_TABLET = 'test_tablet';
    private const string TEST_25_MG = 'test_25mg';
    private const string TEST_50_MG = 'test_50mg';
    private const string TEST_4_PIECES = 'test_4pieces';
    private const string TEST_8_PIECES = 'test_8pieces';
    private const array VARIANT_OPTIONS_SET_1 = [
        self::TEST_TABLET,
        self::TEST_25_MG,
        self::TEST_4_PIECES,
    ];
    private const array VARIANT_SET_2 = [
        self::TEST_TABLET,
        self::TEST_25_MG,
        self::TEST_8_PIECES,
    ];
    private const array VARIANT_SET_3 = [
        self::TEST_TABLET,
        self::TEST_50_MG,
        self::TEST_8_PIECES,
    ];
    private const array VARIANT_TRANSLATION_SET_1 = [
        'en' => [
            'name' => 'viagra en tablet 25 mg 4 pieces',
        ],
        'de' => [
            'name' => 'viagra de tablet 25 mg 4 stuck',
        ],
    ];
    private const array VARIANT_TRANSLATION_SET_2 = [
        'en' => [
            'name' => 'viagra en tablet 25 mg 8 pieces',
        ],
        'de' => [
            'name' => 'viagra de tablet 25 mg 8 stuck',
        ],
    ];
    private const array VARIANT_TRANSLATION_SET_3 = [
        'en' => [
            'name' => 'viagra en tablet 50 mg 8 pieces',
        ],
        'de' => [
            'name' => 'viagra de tablet 50 mg 8 stuck',
        ],
    ];

    public function testItThrowsAnErrorWhenChannelIsNotProvided(): void
    {
        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint(),
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => self::DE_LOCALE_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $responseBody = (string) $client->getResponse()->getContent();

        $errorMessage = [
            'detail' => 'Channel was not found for given request',
            'status' => Response::HTTP_BAD_REQUEST,
            'title' => 'An error occurred.',
            'type' => 'about:blank',
        ];

        static::assertJsonStringEqualsJsonString(json_encode($errorMessage, JSON_THROW_ON_ERROR), $responseBody);
    }

    public function testItReturnsCorrectProductVariants(): void
    {
        $country = $this->entityManager->getRepository(Country::class)
            ->findOneBy(['code' => self::COUNTRY_CODE_AUSTRIA]);

        $product = $this->productTestFactory->createProduct(
            self::PRODUCT_CODE,
            self::PRODUCT_OPTIONS[0]
        );

        $variant = $this->productTestFactory->createProductVariant(
            $product,
            self::PRODUCT_VARIANT_CODE,
            self::PRODUCT_OPTIONS[0],
            $this->productTestFactory->createOrGetSupplier(
                self::SUPPLIER_CODE,
                countries: [$country]
            ),
            country: $country,
        );

        $variant->setPrescriptionRequired(true);
        $this->productTestFactory->createProductVariantTranslations($variant, self::PRODUCT_VARIANT_TRANSLATIONS);

        $this->entityManager->persist($variant);

        $this->entityManager->flush();

        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint(),
            [
                'supplier' => 'test-supplier',
            ],
            [
                'HTTP_ACCEPT_LANGUAGE' => self::DE_LOCALE_CODE,
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseIsSuccessful();

        $responseBody = (string) $client->getResponse()->getContent();

        self::assertJson($responseBody);
        self::assertJsonStringEqualsJsonString(
            (string) json_encode($this->createExpectedResponseBody(self::DE_LOCALE_CODE)),
            $responseBody
        );
    }

    public function testItReturnsMostEligibleProductVariants(): void
    {
        $this->populateDatabaseWithProductVariants();

        $this->entityManager->flush();

        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint(),
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => self::DE_LOCALE_CODE,
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseIsSuccessful();

        $responseBody = (string) $client->getResponse()->getContent();

        self::assertJson($responseBody);
        self::assertJsonStringEqualsJsonString(
            (string) json_encode($this->createExpectedResponseBodyForMostEligibleVariant()),
            $responseBody
        );
    }

    public function testItSearchesProductVariantsInProvidedLanguage(): void
    {
        $country = $this->entityManager->getRepository(Country::class)
            ->findOneBy(['code' => self::COUNTRY_CODE_AUSTRIA]);

        $product = $this->productTestFactory->createProduct(self::PRODUCT_CODE, self::PRODUCT_OPTIONS[0]);
        $variant = $this->productTestFactory->createProductVariant(
            $product,
            self::PRODUCT_VARIANT_CODE,
            self::PRODUCT_OPTIONS[0],
            $this->productTestFactory->createOrGetSupplier(self::SUPPLIER_CODE, countries: [$country]),
            country: $country
        );
        $variant->setPrescriptionRequired(true);

        $this->productTestFactory->createProductVariantTranslations($variant, self::PRODUCT_VARIANT_TRANSLATIONS);

        $this->entityManager->persist($variant);
        $this->entityManager->flush();

        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint(),
            [
                'search' => 'la_testa',
                'supplier' => self::SUPPLIER_CODE,
            ],
            [
                'HTTP_ACCEPT_LANGUAGE' => self::EN_LOCALE_CODE,
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseIsSuccessful();

        $responseBody = (string) $client->getResponse()->getContent();

        self::assertJson($responseBody);

        $expectedResponseBody = $this->createExpectedResponseBody(self::EN_LOCALE_CODE);
        $expectedResponseBody[0]['name'] = self::PRODUCT_VARIANT_TRANSLATIONS[self::EN_LOCALE_CODE]['name'];

        self::assertJsonStringEqualsJsonString(json_encode($expectedResponseBody), $responseBody);
    }

    public function testPagination(): void
    {
        $this->populateDatabaseWithProductVariants();

        $this->entityManager->flush();

        self::ensureKernelShutdown();
        $client = static::createClient();
        foreach (range(1, 3) as $page) {
            $client->jsonRequest(
                Request::METHOD_GET,
                $this->getEndpoint([
                    'itemsPerPage' => 1,
                    'page' => $page,
                ]),
                [],
                [
                    'HTTP_ACCEPT_LANGUAGE' => 'en',
                    'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                ]
            );

            $actualProductVariantCodes[] = json_decode($client->getResponse()->getContent(), true)[0]['code'] ?? '';
        }

        $expectedProductVariantCodes = [
            '7_5_prime_pharmacy_worldwide',
            '7_5_apotheek_kleurrijk_at',
            '7_28_apotheek_kleurrijk_worldwide',
        ];

        $this->assertSame(
            sort($expectedProductVariantCodes),
            sort($actualProductVariantCodes)
        );
    }

    protected function getEndpoint(array $query = []): string
    {
        $endpoint = '/api/shop/product-variants';
        if (empty($query)) {
            return $endpoint;
        }

        return sprintf('%s?%s', $endpoint, http_build_query($query));
    }

    private function createExpectedResponseBody(string $locale): array
    {
        return [
            [
                'name' => self::PRODUCT_VARIANT_TRANSLATIONS[$locale]['name'],
                'caption' => self::PRODUCT_VARIANT_TRANSLATIONS[$locale]['caption'],
                'code' => 'viagra_25mg_at_blueclinic',
                'leaflet' => 'https://downloads.dokteronline.com/leaflets/nl/test-leaflet.pdf',
                'maximumQuantityPerOrder' => 1,
                'supplier' => [
                    'identifier' => 'test-supplier',
                    'name' => 'Test supplier',
                ],
                'prescriptionRequired' => true,
                'price' => [
                    'amount' => 1000,
                    'currency' => 'EUR',
                ],
            ],
        ];
    }

    private function createExpectedResponseBodyForMostEligibleVariant(): array
    {
        return [
            [
                'code' => '7_5_apotheek_kleurrijk_at',
                'leaflet' => 'https://downloads.dokteronline.com/leaflets/nl/test-leaflet.pdf',
                'maximumQuantityPerOrder' => 1,
                'name' => 'viagra de tablet 25 mg 4 stuck',
                'prescriptionRequired' => true,
                'price' => [
                    'amount' => 1000,
                    'currency' => 'EUR',
                ],
                'supplier' => [
                    'identifier' => 'apotheek-kleurrijk',
                    'name' => 'Apotheek Kleurrijk BV',
                ],
            ],
            [
                'code' => '7_28_apotheek_kleurrijk_worldwide',
                'maximumQuantityPerOrder' => 1,
                'name' => 'viagra de tablet 25 mg 8 stuck',
                'leaflet' => 'https://downloads.dokteronline.com/leaflets/nl/test-leaflet.pdf',
                'prescriptionRequired' => true,
                'price' => [
                    'amount' => 1000,
                    'currency' => 'EUR',
                ],
                'supplier' => [
                    'identifier' => 'apotheek-kleurrijk',
                    'name' => 'Apotheek Kleurrijk BV',
                ],
            ],
        ];
    }

    private function populateDatabaseWithProductVariants(): void
    {
        $productData = [
            'viagra' => [
                'productOptions' => [
                    self::TEST_TABLET => self::TEST_TABLET,
                    self::TEST_25_MG => self::TEST_25_MG,
                    self::TEST_50_MG => self::TEST_50_MG,
                    self::TEST_4_PIECES => self::TEST_4_PIECES,
                    self::TEST_8_PIECES => self::TEST_8_PIECES,
                ],
                'variantData' => [
                    '7_5_apotheek_bad_nieuweschans' => [
                        'translations' => self::VARIANT_TRANSLATION_SET_1,
                        'options' => self::VARIANT_OPTIONS_SET_1,
                        'costPrice' => 150,
                        'supplier' => 'apotheek-bad-nieuweschans',
                        'country' => null,
                    ],
                    '7_5_prime_pharmacy_worldwide' => [
                        'translations' => self::VARIANT_TRANSLATION_SET_1,
                        'options' => self::VARIANT_OPTIONS_SET_1,
                        'costPrice' => 225,
                        'supplier' => 'prime-pharmacy',
                        'country' => null,
                    ],
                    '7_5_prime_pharmacy_nl' => [
                        'translations' => self::VARIANT_TRANSLATION_SET_1,
                        'options' => self::VARIANT_OPTIONS_SET_1,
                        'costPrice' => 225,
                        'supplier' => 'prime-pharmacy',
                        'country' => 'NL',
                    ],
                    '7_5_prime_pharmacy_de' => [
                        'translations' => self::VARIANT_TRANSLATION_SET_1,
                        'options' => self::VARIANT_OPTIONS_SET_1,
                        'costPrice' => 225,
                        'supplier' => 'prime-pharmacy',
                        'country' => 'DE',
                    ],
                    '7_5_apotheek_kleurrijk_at' => [
                        'translations' => self::VARIANT_TRANSLATION_SET_1,
                        'options' => self::VARIANT_OPTIONS_SET_1,
                        'costPrice' => 265,
                        'supplier' => 'apotheek-kleurrijk',
                        'country' => 'AT',
                    ],
                    '7_28_prime_pharmacy_wordwide' => [
                        'translations' => self::VARIANT_TRANSLATION_SET_2,
                        'options' => self::VARIANT_SET_2,
                        'costPrice' => 150,
                        'supplier' => 'prime-pharmacy',
                        'country' => null,
                    ],
                    '7_28_apotheek_kleurrijk_worldwide' => [
                        'translations' => self::VARIANT_TRANSLATION_SET_2,
                        'options' => self::VARIANT_SET_2,
                        'costPrice' => 250,
                        'supplier' => 'apotheek-kleurrijk',
                        'country' => null,
                        'preferredSupplier' => true,
                    ],
                    '7_35_prime_pharmacy_worldwide' => [
                        'translations' => self::VARIANT_TRANSLATION_SET_3,
                        'options' => self::VARIANT_SET_3,
                        'costPrice' => 250,
                        'supplier' => 'prime-pharmacy',
                        'country' => null,
                    ],
                ],
            ],
        ];

        $countries = $this->entityManager->getRepository(Country::class)->findAll();
        foreach ($productData as $productCode => $data) {
            $product = $this->productTestFactory->createProduct(
                $productCode,
                $data['productOptions']
            );

            foreach ($data['variantData'] as $variantCode => $variantData) {
                $variantCountry = null;
                if (is_string($variantData['country'])) {
                    $countries = $this->entityManager->getRepository(Country::class)->findBy(
                        ['code' => $variantData['country']]
                    );
                    $variantCountry = $countries[0];
                }
                $supplier = $this->entityManager->getRepository(Supplier::class)->findOneBy(
                    ['identifier' => $variantData['supplier']]
                );
                if (!$supplier instanceof SupplierInterface) {
                    $supplier = $this->productTestFactory->createOrGetSupplier(
                        $variantData['supplier'],
                        countries: $countries
                    );
                }
                $variant = $this->productTestFactory->createProductVariant(
                    $product,
                    $variantCode,
                    $variantData['options'],
                    $supplier,
                    country: $variantCountry,
                );

                $variant->setPrescriptionRequired(true);

                if (isset($variantData['preferredSupplier'])) {
                    $variant->setPreferredSupplier($variantData['preferredSupplier']);
                }

                $this->productTestFactory->createProductVariantTranslations(
                    $variant,
                    $variantData['translations']
                );

                $this->entityManager->persist($variant);
            }
        }
    }
}
