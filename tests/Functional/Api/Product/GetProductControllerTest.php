<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Product;

use App\Entity\Product\ProductInterface;
use App\Entity\Supplier\Supplier;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Api\ProductTestFactory;
use App\Tests\Util\Factory\ProblemDetailsFactory;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetProductControllerTest extends AbstractWebTestCase
{
    private const string CHANNEL_CODE = 'dok_gb';
    private const string CHANNEL_NL = 'dok_nl';
    private const string LOCALE_CODE = 'en';
    private const string PRODUCT_CODE = 'viagra_test';
    private const string BC_LOCAL_PRODUCT_VARIANT_CODE = 'viagra_25mg_gb_blueclinic';
    private const array PRODUCT_OPTIONS = [
        'test_form' => 'test_tablet',
        'test_dosage' => 'test_25mg',
        'test_packsize' => ['test_4pieces', 'test_20pieces', 'test_2pieces'],
    ];

    private ProductTestFactory $productTestFactory;
    private ProductInterface $product;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        $this->productTestFactory = new ProductTestFactory(
            static::getContainer(),
            [self::CHANNEL_CODE, self::CHANNEL_NL],
        );
        $this->product = $this->productTestFactory->createProduct(self::PRODUCT_CODE, self::PRODUCT_OPTIONS);
    }

    public function testGetProductWithVariants(): void
    {
        $suppliers = $this->findAndMapSuppliersByIdentifiers('my-own-chemist');

        $variants[] = $this->productTestFactory->createProductVariant(
            $this->product,
            self::BC_LOCAL_PRODUCT_VARIANT_CODE,
            self::PRODUCT_OPTIONS,
            $suppliers['my-own-chemist'],
            prescriptionRequired: true,
        );

        $variants[] = $this->productTestFactory->createProductVariant(
            $this->product,
            self::BC_LOCAL_PRODUCT_VARIANT_CODE.'-disabled',
            self::PRODUCT_OPTIONS,
            $suppliers['my-own-chemist'],
            prescriptionRequired: true,
            isEnabled: false,
        );

        foreach ($variants as $variant) {
            $this->entityManager->persist($variant);
        }

        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/products/%s', self::PRODUCT_CODE),
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseIsSuccessful();
        self::assertJsonStringEqualsJsonString(
            json_encode($this->createExpectedResponseBody()),
            json_encode($this->getResponseBody())
        );
    }

    public function testBadRequestProblemDetails(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_GET,
            '/api/shop/products/321',
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertJsonStringEqualsJsonString(
            ProblemDetailsFactory::BadRequest->json('Channel was not found for given request'),
            $this->client->getResponse()->getContent()
        );
    }

    public function testNotFoundProblemDetails(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_GET,
            '/api/shop/products/this_product_does_not_exist',
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        self::assertJsonStringEqualsJsonString(
            ProblemDetailsFactory::NotFound->json("Product with code 'this_product_does_not_exist' was not found."),
            $this->client->getResponse()->getContent()
        );
    }

    private function createExpectedResponseBody(): array
    {
        return [
            'code' => 'viagra_test',
            'slug' => 'viagra-test',
            'name' => 'Viagra test',
            'variantSelectionMethod' => 'match',
            'options' => [
                [
                    'code' => 'test_form',
                    'position' => 1,
                    'translations' => [
                        'en' => [
                            'name' => 'test_tablet',
                        ],
                    ],
                    'values' => [
                        [
                            'code' => 'test_tablet',
                            'translations' => [
                                'en' => [
                                    'name' => 'test_tablet',
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'code' => 'test_dosage',
                    'position' => 2,
                    'translations' => [
                        'en' => [
                            'name' => 'test_25mg',
                        ],
                    ],
                    'values' => [
                        [
                            'code' => 'test_25mg',
                            'translations' => [
                                'en' => [
                                    'name' => 'test_25mg',
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'code' => 'test_packsize',
                    'position' => 3,
                    'translations' => [
                        'en' => [
                            'name' => 'test_4pieces',
                        ],
                    ],
                    'values' => [
                        0 => [
                            'code' => 'test_2pieces',
                            'translations' => [
                                'en' => [
                                    'name' => 'test_2pieces',
                                ],
                            ],
                        ],
                        1 => [
                            'code' => 'test_4pieces',
                            'translations' => [
                                'en' => [
                                    'name' => 'test_4pieces',
                                ],
                            ],
                        ],
                        2 => [
                            'code' => 'test_20pieces',
                            'translations' => [
                                'en' => [
                                    'name' => 'test_20pieces',
                                ],
                            ],
                        ],
                    ],
                ],
            ],
            'startingPrice' => [
                'amount' => 1000,
                'currency' => 'GBP',
            ],
            'inStock' => true,
            'channel' => [
                'code' => 'dok_gb',
                'addPrescriptionMedicationDirectlyToCart' => true,
                'allowMultipleConsultsInCart' => true,
                'pickupPointsAllowed' => false,
                'baseCurrency' => [
                    'code' => 'GBP',
                ],
            ],
            'variants' => [
                [
                    'name' => 'Viagra 25mg 4 tablets',
                    'caption' => 'Including doctor consult and service fees',
                    'code' => 'viagra_25mg_gb_blueclinic',
                    'maximumQuantityPerOrder' => 1,
                    'optionValues' => [
                        'test_dosage' => 'test_25mg',
                        'test_form' => 'test_tablet',
                        'test_packsize' => 'test_2pieces',
                    ],
                    'prescriptionRequired' => true,
                    'price' => [
                        'amount' => 1000,
                        'currency' => 'GBP',
                    ],
                    'supplier' => [
                        'identifier' => 'my-own-chemist',
                        'name' => 'MyOwnChemist',
                    ],
                ],
            ],
            'associations' => [],
        ];
    }

    /**
     * @return array<string, Supplier>
     */
    private function findAndMapSuppliersByIdentifiers(string ...$identifiers): array
    {
        /** @var Supplier[] $suppliers */
        $suppliers = $this->entityManager
            ->getRepository(Supplier::class)
            ->findBy(['identifier' => $identifiers]);

        foreach ($suppliers as $supplier) {
            $suppliers[$supplier->getIdentifier()] = $supplier;
        }

        return $suppliers;
    }
}
