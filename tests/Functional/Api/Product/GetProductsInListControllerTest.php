<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Product;

use App\Entity\Addressing\Country;
use App\Entity\Product\ProductType;
use App\Entity\Supplier\Supplier;
use Sylius\Bundle\CoreBundle\Fixture\Factory\ExampleFactoryInterface;
use Sylius\Bundle\CoreBundle\Fixture\Factory\ProductAssociationExampleFactory;
use Symfony\Component\HttpFoundation\Request;

class GetProductsInListControllerTest extends AbstractProductListControllerTest
{
    protected const string CHANNEL_CODE = 'dok_gb';
    protected const string PAGINATION_LOCALE_CODE = 'en';

    private const array PRODUCTS = [
        'viagra_test_gb' => [
            'productCode' => 'viagra_test_gb',
            'productOptions' => [
                'test_form' => 'test_tablet',
                'test_dosage' => 'test_25mg',
                'test_packsize' => 'test_4pieces',
            ],
            'productName' => 'Viagra test',
            'locale' => 'en',
            'channelCode' => 'dok_gb',
            'productVariant' => [
                'code' => 'viagra_variant_gb',
                'prescriptionRequired' => false,
                'options' => [],
                [
                    'channelCode' => 'dok_gb',
                    'enabled' => true,
                ],
            ],
            'productType' => ProductType::MEDICATION,
        ],
        'finasteride_test_gb' => [
            'productCode' => 'finasteride_test_gb',
            'productOptions' => [
                'test_form' => 'test_tablet',
                'test_dosage' => 'test_25mg',
                'test_packsize' => 'test_4pieces',
            ],
            'productName' => 'Finasteride test',
            'locale' => 'en',
            'channelCode' => 'dok_gb',
            'productVariant' => [
                'code' => 'finasteride_test_gb',
                'prescriptionRequired' => true,
                'options' => [],
                [
                    'channelCode' => 'dok_gb',
                    'enabled' => true,
                ],
            ],
            'productType' => ProductType::MEDICATION,
        ],
        'cialis_test_gb' => [
            'productCode' => 'cialis_test_gb',
            'productOptions' => [
                'test_form' => 'test_tablet',
                'test_dosage' => 'test_25mg',
                'test_packsize' => 'test_4pieces',
            ],
            'productName' => 'Cialis test',
            'locale' => 'en',
            'channelCode' => 'dok_gb',
            'productVariant' => [
                'code' => 'cialis_test_gb',
                'prescriptionRequired' => true,
                'options' => [],
                'channelPricings' => [
                    [
                        'channelCode' => 'dok_nl',
                        'enabled' => true,
                    ],
                    [
                        'channelCode' => 'dok_gb',
                        'enabled' => false,
                    ],
                ],
            ],
            'productType' => ProductType::MEDICATION,
        ],
        'erectiestoornis_test_gb' => [
            'productCode' => 'erectiestoornis_test_gb',
            'productOptions' => [],
            'productName' => 'Consult Erectiestoornis test',
            'locale' => 'en',
            'channelCode' => 'dok_gb',
            'productVariant' => [
                'code' => 'erectiestoornis_test_gb',
                'prescriptionRequired' => false,
                'options' => [],
                [
                    'channelCode' => 'dok_gb',
                    'enabled' => true,
                ],
            ],
            'productType' => ProductType::CONSULT,
        ],
        'viagra_test_de' => [
            'productCode' => 'viagra_test_de',
            'productOptions' => [
                'test_form' => 'test_tablet',
                'test_dosage' => 'test_25mg',
                'test_packsize' => 'test_4pieces',
            ],
            'productName' => 'Viagra test de',
            'locale' => 'de',
            'channelCode' => 'dok_de',
            'productVariant' => [
                'code' => 'viagra_test_de',
                'prescriptionRequired' => true,
                'options' => [],
                [
                    'channelCode' => 'dok_de',
                    'enabled' => true,
                ],
            ],
            'productType' => ProductType::MEDICATION,
        ],
        'caverject_test_worldwide' => [
            'productCode' => 'caverject_test_worldwide',
            'productOptions' => [
                'test_form' => 'test_tablet',
                'test_dosage' => 'test_25mg',
                'test_packsize' => 'test_4pieces',
            ],
            'productName' => 'Caverject test worldwide',
            'locale' => 'gb',
            'channelCode' => 'dok_gb',
            'productVariant' => [
                'code' => 'caverject_test_worldwide',
                'prescriptionRequired' => true,
                'options' => [],
                'country' => 'GB',
                'channelPricings' => [
                    [
                        'channelCode' => 'dok_de',
                        'enabled' => false,
                    ],
                    [
                        'channelCode' => 'dok_gb',
                        'enabled' => false,
                    ],
                    [
                        'channelCode' => 'dok_nl',
                        'enabled' => true,
                    ],
                ],
            ],
            'productType' => ProductType::MEDICATION,
        ],
        'cialis_test_worldwide' => [
            'productCode' => 'cialis_test_worldwide',
            'productOptions' => [
                'test_form' => 'test_tablet',
                'test_dosage' => 'test_25mg',
                'test_packsize' => 'test_4pieces',
            ],
            'productName' => 'Cialis test worldwide',
            'locale' => 'gb',
            'channelCode' => 'dok_gb',
            'productVariant' => [
                'code' => 'cialis_test_worldwide',
                'prescriptionRequired' => true,
                'options' => [],
                'country' => 'GB',
                'enabled' => false,
                'channelPricings' => [
                    [
                        'channelCode' => 'dok_de',
                        'enabled' => true,
                    ],
                    [
                        'channelCode' => 'dok_gb',
                        'enabled' => true,
                    ],
                    [
                        'channelCode' => 'dok_nl',
                        'enabled' => true,
                    ],
                ],
            ],
            'productType' => ProductType::MEDICATION,
        ],
    ];

    private ExampleFactoryInterface $productAssociationExampleFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->productAssociationExampleFactory = new ProductAssociationExampleFactory(
            static::getContainer()->get('sylius.factory.product_association'),
            static::getContainer()->get('sylius.repository.product_association_type'),
            static::getContainer()->get('sylius.repository.product'),
        );
    }

    public function testCanGetUnfilteredListAndResponse(): void
    {
        $this->prepareProducts();

        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            '/api/shop/products',
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => 'en',
            ]
        );

        $response = json_decode((string) $client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        self::assertResponseIsSuccessful();

        $expectedResponses = [
            [
                'taxons' => [],
                'images' => [],
                'code' => 'caverject_test_worldwide',
                'name' => 'Caverject test worldwide',
                'slug' => 'caverject-test-worldwide',
            ],
            [
                'taxons' => [],
                'images' => [],
                'code' => 'cialis_test_gb',
                'name' => 'Cialis test',
                'slug' => 'cialis-test',
            ],
            [
                'taxons' => [],
                'images' => [],
                'code' => 'cialis_test_worldwide',
                'name' => 'Cialis test worldwide',
                'slug' => 'cialis-test-worldwide',
            ],
            [
                'taxons' => [],
                'images' => [],
                'code' => 'erectiestoornis_test_gb',
                'name' => 'Consult Erectiestoornis test',
                'slug' => 'consult-erectiestoornis-test',
            ],
            [
                'taxons' => [],
                'images' => [],
                'code' => 'finasteride_test_gb',
                'name' => 'Finasteride test',
                'slug' => 'finasteride-test',
            ],
            [
                'taxons' => [],
                'images' => [],
                'code' => 'viagra_test_gb',
                'name' => 'Viagra test',
                'slug' => 'viagra-test',
            ],
            [
                'taxons' => [],
                'images' => [],
                'code' => 'viagra_test_de',
                'name' => 'Viagra test de',
                'slug' => 'viagra-test-de',
            ],
        ];

        $this->validateResponse($response, $expectedResponses);
    }

    public function testCanGetUnfilteredListAndResponseWithChannel(): void
    {
        $this->prepareProducts();

        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            '/api/shop/products',
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => 'en',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $response = json_decode((string) $client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        self::assertResponseIsSuccessful();

        $expectedResponses = [
            [
                'taxons' => [],
                'images' => [],
                'code' => 'caverject_test_worldwide',
                'name' => 'Caverject test worldwide',
                'slug' => 'caverject-test-worldwide',
            ],
            [
                'taxons' => [],
                'images' => [],
                'code' => 'cialis_test_gb',
                'name' => 'Cialis test',
                'slug' => 'cialis-test',
            ],
            [
                'taxons' => [],
                'images' => [],
                'code' => 'cialis_test_worldwide',
                'name' => 'Cialis test worldwide',
                'slug' => 'cialis-test-worldwide',
            ],
            [
                'taxons' => [],
                'images' => [],
                'code' => 'erectiestoornis_test_gb',
                'name' => 'Consult Erectiestoornis test',
                'slug' => 'consult-erectiestoornis-test',
            ],
            [
                'taxons' => [],
                'images' => [],
                'code' => 'finasteride_test_gb',
                'name' => 'Finasteride test',
                'slug' => 'finasteride-test',
            ],
            [
                'taxons' => [],
                'images' => [],
                'code' => 'viagra_test_gb',
                'name' => 'Viagra test',
                'slug' => 'viagra-test',
            ],
        ];

        $this->validateResponse($response, $expectedResponses);
    }

    public function testCanFilteredByName(): void
    {
        $this->prepareProducts();

        $namesToSearch = [
            'via' => [
                [
                    'taxons' => [],
                    'images' => [],
                    'code' => 'viagra_test_gb',
                    'name' => 'Viagra test',
                    'slug' => 'viagra-test',
                ],
            ],
            'viagra test' => [
                [
                    'taxons' => [],
                    'images' => [],
                    'code' => 'viagra_test_gb',
                    'name' => 'Viagra test',
                    'slug' => 'viagra-test',
                ],
            ],
            'aster' => [
                [
                    'taxons' => [],
                    'images' => [],
                    'code' => 'finasteride_test_gb',
                    'name' => 'Finasteride test',
                    'slug' => 'finasteride-test',
                ],
            ],
            'randomName' => [],
        ];

        self::ensureKernelShutdown();
        $client = static::createClient();

        foreach ($namesToSearch as $name => $expectedResponses) {
            $client->jsonRequest(
                Request::METHOD_GET,
                '/api/shop/products?filterByProductName='.$name,
                [],
                [
                    'HTTP_ACCEPT_LANGUAGE' => 'en',
                    'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                ]
            );

            $response = json_decode(
                (string) $client->getResponse()->getContent(),
                true,
                512,
                JSON_THROW_ON_ERROR
            );
            self::assertResponseIsSuccessful();
            $this->validateResponse($response, $expectedResponses);
        }
    }

    public function testCanFilteredByPrescriptionRequired(): void
    {
        $this->prepareProducts();

        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            '/api/shop/products?filterByPrescriptionRequired=true',
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => 'en',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );
        self::assertResponseIsSuccessful();
        $responseContent = (string) $client->getResponse()->getContent();
        $this->assertStringContainsString('finasteride_test_gb', $responseContent);
        $this->assertStringNotContainsString('viagra_test_gb', $responseContent);
        $this->assertStringNotContainsString('erectiestoornis_test_gb', $responseContent);
        $this->assertStringNotContainsString('viagra_test_de', $responseContent);
    }

    public function testCanFilteredByProductType(): void
    {
        $this->prepareProducts();

        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            '/api/shop/products?filterByProductAttributeType=consult',
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => 'en',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );
        self::assertResponseIsSuccessful();
        $responseContent = (string) $client->getResponse()->getContent();
        $this->assertStringContainsString('erectiestoornis_test_gb', $responseContent);
        $this->assertStringNotContainsString('viagra_test_gb', $responseContent);
        $this->assertStringNotContainsString('finasteride_test_gb', $responseContent);
        $this->assertStringNotContainsString('viagra_test_de', $responseContent);
    }

    public function testCanFilteredByConsultProduct(): void
    {
        $this->prepareProducts();

        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            '/api/shop/products?filterByConsult=erectiestoornis_test_gb',
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => 'en',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );
        self::assertResponseIsSuccessful();
        $responseContent = (string) $client->getResponse()->getContent();
        $this->assertStringContainsString('viagra_test_gb', $responseContent);
        $this->assertStringNotContainsString('erectiestoornis_test_gb', $responseContent);
        $this->assertStringNotContainsString('finasteride_test_gb', $responseContent);
        $this->assertStringNotContainsString('viagra_test_de', $responseContent);
    }

    public function testCanFilteredByInStock(): void
    {
        $this->prepareProducts();

        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            '/api/shop/products?filterByInStock=true',
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => 'en',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );
        self::assertResponseIsSuccessful();
        $responseContent = (string) $client->getResponse()->getContent();
        $this->assertStringNotContainsString('caverject_test_worldwide', $responseContent);
        $this->assertStringNotContainsString('cialis_test_worldwide', $responseContent);
    }

    public function testCanFilteredByAllFilters(): void
    {
        $this->prepareProducts();

        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            '/api/shop/products?filterByProductAttributeType=medication&filterByPrescriptionRequired=false&filterByProductName=test&filterByConsult=erectiestoornis_test_gb&filterByInStock=true',
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => 'en',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );
        self::assertResponseIsSuccessful();
        $responseContent = (string) $client->getResponse()->getContent();
        $this->assertStringContainsString('viagra_test_gb', $responseContent);
        $this->assertStringNotContainsString('erectiestoornis_test_gb', $responseContent);
        $this->assertStringNotContainsString('finasteride_test_gb', $responseContent);
        $this->assertStringNotContainsString('viagra_test_de', $responseContent);
    }

    public function testCanSeeValidationError(): void
    {
        $this->prepareProducts();

        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            '/api/shop/products?filterByProductAttributeType=noneExistingType&filterByProductName=aa',
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => 'en',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ],
        );

        $response = json_decode((string) $client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);
        self::assertResponseStatusCodeSame(400);
        $this->assertSame(
            [
                'type' => 'about:blank',
                'title' => 'The request body contains errors.',
                'status' => 400,
                'detail' => 'Validation of JSON request body failed.',
                'violations' => [
                    [
                        'constraint' => 'choice',
                        'message' => 'The value you selected is not a valid choice.',
                        'property' => 'filterByProductAttributeType',
                    ],
                    [
                        'constraint' => 'length',
                        'message' => 'This value is too short. It should have 3 characters or more.',
                        'property' => 'filterByProductName',
                    ],
                ],
            ],
            $response
        );
    }

    protected function getEndpoint(): string
    {
        return '/api/shop/products';
    }

    private function validateResponse(array $response, array $expectedResponses): void
    {
        foreach ($response as $index => $productResponse) {
            $this->assertSame($expectedResponses[$index]['taxons'], $productResponse['taxons']);
            $this->assertSame($expectedResponses[$index]['images'], $productResponse['images']);
            $this->assertSame($expectedResponses[$index]['code'], $productResponse['code']);
            $this->assertSame($expectedResponses[$index]['name'], $productResponse['name']);
            $this->assertSame($expectedResponses[$index]['slug'], $productResponse['slug']);
        }
    }

    private function prepareProducts(): void
    {
        foreach (self::PRODUCTS as $productOption) {
            $product = $this->productTestFactory->createProduct(
                $productOption['productCode'],
                $productOption['productOptions'],
                $productOption['productName'],
                $productOption['locale'],
                $productOption['channelCode'],
                $productOption['productType'],
            );
            $productVariantOption = $productOption['productVariant'];

            $country = $this->entityManager
                ->getRepository(Country::class)
                ->findOneBy(['code' => ($productVariantOption['country'] ?? null)]);

            $supplier = $this->entityManager
                ->getRepository(Supplier::class)
                ->findOneBy(['identifier' => 'my-own-chemist']);
            self::assertInstanceOf(Supplier::class, $supplier, "Supplier with identifier 'my-own-chemist' does not exist.");

            $this->productTestFactory->createProductVariant(
                $product,
                $productVariantOption['code'],
                $productVariantOption['options'],
                $supplier,
                $country,
                $productVariantOption['options'],
                $productVariantOption['channelPricings'] ?? [],
                $productVariantOption['prescriptionRequired'],
                isEnabled: $productVariantOption['enabled'] ?? true
            );
        }

        $associations = $this->productAssociationExampleFactory->create([
            'type' => 'consult_products',
            'owner' => 'viagra_test_gb',
            'associated_products' => [
                'erectiestoornis_test_gb',
            ],
        ]);

        $this->entityManager->persist($associations);
        $this->entityManager->flush();
    }
}
