<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Product;

use App\Entity\Addressing\Country;
use App\Entity\Channel\Channel;
use App\Entity\Channel\ChannelPricing;
use App\Entity\Product\ProductTaxon;
use App\Entity\Product\ProductTaxonChannel;
use App\Entity\Product\ProductVariant;
use App\Entity\Supplier\Supplier;
use App\Entity\Taxonomy\Taxon;
use App\Entity\Taxonomy\TaxonTranslation;
use Doctrine\Common\Collections\ArrayCollection;
use Sylius\Component\Core\Model\ProductInterface;
use Sylius\Component\Product\Factory\ProductFactoryInterface;
use Sylius\Component\Product\Factory\ProductVariantFactoryInterface;
use Symfony\Component\HttpFoundation\Request;

class GetProductEnrichmentControllerTest extends AbstractProductListControllerTest
{
    private const string CHANNEL_CODE_RO = 'dok_ro';

    private const string PRODUCT_VARIANT_CODE = 'test_product_25mg_nl_blueclinic';

    public function testItCanGetProductEnrichment(): void
    {
        $product = $this->createProduct();
        $variant = $this->createProductVariant($product, 'blueclinic', self::PRODUCT_VARIANT_CODE);

        $this->entityManager->persist($variant);
        $this->entityManager->flush();

        self::ensureKernelShutdown();
        $client = static::createClient();

        $client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint().'?productCodes[]='.$product->getCode(),
        );

        static::assertResponseIsSuccessful();

        $responseBody = $client->getResponse()->getContent();
        $linkHeader = $client->getResponse()->headers->get('Link');

        static::assertNull($linkHeader);
        static::assertJson($responseBody);
        static::assertJsonStringEqualsJsonString($this->createExpectedResponseBody(), $responseBody);
    }

    public function testItReturnsAllProductsWhenNoProductCodesAreGiven(): void
    {
        $product = $this->createProduct();
        $variant = $this->createProductVariant($product, 'blueclinic', self::PRODUCT_VARIANT_CODE);

        $this->entityManager->persist($variant);
        $this->entityManager->flush();

        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint(),
        );

        static::assertResponseIsSuccessful();

        $responseBody = $client->getResponse()->getContent();
        $linkHeader = $client->getResponse()->headers->get('Link');
        static::assertIsString($linkHeader);
        static::assertJson($responseBody);
        static::assertJsonStringEqualsJsonString($this->createExpectedResponseBody(), $responseBody);
    }

    protected function getEndpoint(): string
    {
        return '/api/shop/product-enrichment';
    }

    private function createProduct(array $data = []): ProductInterface
    {
        /** @var ProductFactoryInterface $productFactory */
        $productFactory = static::getContainer()->get('sylius.factory.product');

        /** @var ProductInterface $product */
        $product = $productFactory->createNew();
        $product->setCode($data['code'] ?? 'test_product');
        $product->setName($data['name'] ?? 'Viagra test');
        $product->setSlug($data['slug'] ?? 'viagra-test');
        $product->addChannel(
            $this->entityManager->getRepository(Channel::class)->findOneBy(['code' => 'dok_nl'])
        );
        $product->addChannel(
            $this->entityManager->getRepository(Channel::class)->findOneBy(['code' => 'dok_ro'])
        );

        $this->createProductTaxonomy($product, $data);

        $this->entityManager->persist($product);

        return $product;
    }

    private function createProductVariant(
        ProductInterface $product,
        string $supplier,
        string $variantCode,
        Country $country = null,
    ): ProductVariant {
        /** @var ProductVariantFactoryInterface<ProductVariant> $productVariantFactory */
        $productVariantFactory = static::getContainer()->get('sylius.factory.product_variant');
        /** @var ProductVariant $productVariant */
        $productVariant = $productVariantFactory->createNew();
        $productVariant->setCode($variantCode);
        $productVariant->setName('Viagra 25mg 4 tablets');
        $productVariant->setCaption('Including doctor consult and service fees');
        $productVariant->setCountry($country);
        $productVariant->setSupplier(
            $this->entityManager->getRepository(Supplier::class)->findOneBy(['identifier' => $supplier])
        );
        $productVariant->setOnHand(1);

        /*
         * Tests regression for https://mv-jira-1.atlassian.net/browse/DV-4652
         */
        foreach ([self::CHANNEL_CODE, self::CHANNEL_CODE_RO] as $channelCode) {
            $productVariantChannelPricing = new ChannelPricing();
            $productVariantChannelPricing->setChannelCode($channelCode);
            $productVariantChannelPricing->setPrice(1000);
            $productVariantChannelPricing->setProductVariant($productVariant);
            $productVariantChannelPricing->setEnabled(true);

            $productVariant->addChannelPricing($productVariantChannelPricing);
        }

        $product->addVariant($productVariant);

        return $productVariant;
    }

    private function createProductTaxonomy(ProductInterface $product, array $data = []): void
    {
        $taxonTranslation = new TaxonTranslation();
        $taxonTranslation->setSlug($data['taxon']['slug'] ?? 'test-taxon');
        $taxonTranslation->setName($data['taxon']['name'] ?? 'test taxon');
        $taxonTranslation->setLocale(self::PAGINATION_LOCALE_CODE);

        $taxon = new Taxon();
        $taxon->setCode($data['taxon']['code'] ?? 'taxon_code');
        $taxon->addTranslation($taxonTranslation);

        $productTaxon = new ProductTaxon();
        $productTaxon->setProduct($product);
        $productTaxon->setTaxon($taxon);
        $product->addProductTaxon($productTaxon);

        $productTaxonChannel = new ProductTaxonChannel();
        /** @var Channel $channel */
        $channel = $this->entityManager->getRepository(Channel::class)->findOneBy(['code' => 'dok_nl']);
        $productTaxonChannel->setChannel($channel);
        $productTaxonChannel->setProductTaxon($productTaxon);
        $productTaxonChannel->setRelevance(1234);
        $productTaxonChannel->setLabel('new');
        $productTaxon->setProductTaxonChannels(new ArrayCollection([$productTaxonChannel]));

        $this->entityManager->persist($taxon);
        $this->entityManager->persist($taxonTranslation);
        $this->entityManager->persist($productTaxon);
        $this->entityManager->persist($productTaxonChannel);
    }

    private function createExpectedResponseBody(): string
    {
        return json_encode([
            [
                'code' => 'test_product',
                'enabledForChannels' => [
                    'dok_ro',
                    'dok_nl',
                ],
                'startingPrices' => [
                    'dok_nl' => [
                        'amount' => 1000,
                        'currency' => 'EUR',
                    ],
                    'dok_ro' => [
                        'amount' => 1000,
                        'currency' => 'RON',
                    ],
                ],
                'taxons' => [
                    [
                        'code' => 'taxon_code',
                        'translations' => [
                            'nl' => [
                                'name' => 'test taxon',
                                'slug' => 'test-taxon',
                            ],
                        ],
                        'productTaxonChannels' => [
                            'dok_nl' => [
                                'relevance' => 1234,
                                'label' => 'new',
                            ],
                        ],
                    ],
                ],
            ],
        ], JSON_THROW_ON_ERROR);
    }
}
