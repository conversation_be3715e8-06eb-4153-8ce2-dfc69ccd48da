<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Product;

use App\Entity\Addressing\Country;
use App\Entity\Product\ProductInterface;
use App\Entity\Supplier\SupplierInterface;
use App\Tests\Functional\Api\ProductTestFactory;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ObjectManager;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

abstract class AbstractProductListControllerTest extends WebTestCase
{
    protected const CHANNEL_CODE = 'dok_nl';
    protected const PAGINATION_LOCALE_CODE = 'nl';
    protected const PAGINATION_COUNTRY_CODE = 'NL';
    protected const PRODUCT_VARIANT_TRANSLATIONS = [
        'nl' => [
            'name' => 'Pagination Test Viagra (NL)',
            'caption' => 'Test Caption',
            'leaflet' => 'https://downloads.dokteronline.com/leaflets/nl/test-leaflet.pdf',
        ],
        'en' => [
            'name' => 'Pagination Test Viagra (EN)',
            'caption' => 'Test Caption',
            'leaflet' => 'https://downloads.dokteronline.com/leaflets/nl/test-leaflet.pdf',
        ],
    ];

    protected const array PRODUCT_OPTIONS = [
        [
            'test_form' => 'test_tablet',
            'test_dosage' => 'test_25mg',
            'test_packsize' => 'test_4pieces',
        ],
        [
            'test_form' => 'test_tablet',
            'test_dosage' => 'test_25mg',
            'test_packsize' => 'test_8pieces',
        ],
        [
            'test_form' => 'test_tablet',
            'test_dosage' => 'test_50mg',
            'test_packsize' => 'test_4pieces',
        ],
        [
            'test_form' => 'test_tablet',
            'test_dosage' => 'test_50mg',
            'test_packsize' => 'test_8pieces',
        ],
    ];

    protected ObjectManager $entityManager;
    protected ProductTestFactory $productTestFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $container = self::getContainer();

        /** @var EntityManagerInterface $entityManager */
        $entityManager = $container->get('doctrine.orm.entity_manager');
        $this->entityManager = $entityManager;

        $this->productTestFactory = new ProductTestFactory(
            $container,
            [static::CHANNEL_CODE],
        );
    }

    public function testItCanPaginate(): void
    {
        $this->createProductBatch();

        self::ensureKernelShutdown();
        $client = static::createClient();

        $client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint().'?page=1&itemsPerPage=1',
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => static::PAGINATION_LOCALE_CODE,
                'HTTP_SYLIUS_CHANNEL_CODE' => static::CHANNEL_CODE,
            ]
        );

        self::assertResponseIsSuccessful();

        $linkHeader = (string) $client->getResponse()->headers->get('Link');

        // First page
        $this->assertStringContainsString(
            sprintf('<http://localhost%s?page=1&itemsPerPage=1>; rel="first"', $this->getEndpoint()),
            $linkHeader
        );

        // Next page
        $this->assertStringContainsString(
            sprintf('<http://localhost%s?page=2&itemsPerPage=1>; rel="next"', $this->getEndpoint()),
            $linkHeader
        );

        // Last page
        $this->assertStringContainsString(
            sprintf('<http://localhost%s?page=4&itemsPerPage=1>; rel="last"', $this->getEndpoint()),
            $linkHeader
        );
    }

    public function testItCanNavigateToNextPage(): void
    {
        $this->createProductBatch();

        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint().'?page=2&itemsPerPage=1',
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => static::PAGINATION_LOCALE_CODE,
                'HTTP_SYLIUS_CHANNEL_CODE' => static::CHANNEL_CODE,
            ]
        );

        self::assertResponseIsSuccessful();

        $linkHeader = (string) $client->getResponse()->headers->get('Link');

        // First page
        $this->assertStringContainsString(
            sprintf('<http://localhost%s?page=1&itemsPerPage=1>; rel="first"', $this->getEndpoint()),
            $linkHeader
        );

        // Next page
        $this->assertStringContainsString(
            sprintf('<http://localhost%s?page=3&itemsPerPage=1>; rel="next"', $this->getEndpoint()),
            $linkHeader
        );

        // Last page
        $this->assertStringContainsString(
            sprintf('<http://localhost%s?page=4&itemsPerPage=1>; rel="last"', $this->getEndpoint()),
            $linkHeader
        );
    }

    public function testItThrowsNotFoundExceptionForOutOfBoundsPage(): void
    {
        $this->createProductBatch();

        self::ensureKernelShutdown();
        $client = static::createClient();
        $client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint().'/api/shop/product-enrichment?page=10&itemsPerPage=1',
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => static::PAGINATION_LOCALE_CODE,
                'HTTP_SYLIUS_CHANNEL_CODE' => static::CHANNEL_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    abstract protected function getEndpoint(): string;

    protected function createProductBatch(int $numberOfProducts = 4, int $numberOfVariants = 10): void
    {
        /** @var Country $country */
        $country = $this->entityManager->getRepository(Country::class)->findOneBy(
            ['code' => static::PAGINATION_COUNTRY_CODE]
        );
        $supplier = $this->productTestFactory->createOrGetSupplier('test-supplier', countries: [$country]);

        for ($i = 0; $i < $numberOfProducts; ++$i) {
            $productOptions = static::PRODUCT_OPTIONS[$i] ?? static::PRODUCT_OPTIONS[0];
            $product = $this->productTestFactory->createProduct(
                productCode: (string) $i,
                productOptions: $productOptions,
                productName: 'Test product '.$i,
                locale: static::PAGINATION_LOCALE_CODE,
                channelCode: static::CHANNEL_CODE,
            );
            $this->createProductVariants($numberOfVariants, $product, $i, $supplier, $productOptions);
            $this->entityManager->persist($product);
        }
        $this->entityManager->flush();
    }

    private function createProductVariants(
        int $numberOfVariants,
        ProductInterface $product,
        int $iteration,
        SupplierInterface $supplier,
        array $productOptions,
    ): void {
        for ($i = 0; $i <= $numberOfVariants; ++$i) {
            $productVariant = $this->productTestFactory->createProductVariant(
                product: $product,
                variantCode: $product->getCode().'_'.$iteration.'_'.$i,
                productOptions: $productOptions,
                supplier: $supplier,
                productVariantOption: ['costPrice' => $i * 100]
            );

            $this->productTestFactory->createProductVariantTranslations(
                $productVariant,
                static::PRODUCT_VARIANT_TRANSLATIONS
            );

            $this->entityManager->persist($productVariant);
        }
    }
}
