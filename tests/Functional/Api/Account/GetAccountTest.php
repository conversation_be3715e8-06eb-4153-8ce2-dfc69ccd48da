<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Account;

use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\MarketingSubscription\MarketingSubscription;
use App\Entity\User\ShopUser;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use App\Tests\Util\Factory\ProblemDetailsFactory;
use DateTimeImmutable;
use Sylius\Component\Core\Model\ShopUserInterface;
use Sylius\Component\Customer\Model\CustomerInterface as BaseCustomerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetAccountTest extends AbstractWebTestCase
{
    private const string CHANNEL_CODE = 'dok_gb';

    private const string CUSTOMER_POOL_CODE = 'dokteronline';

    private const string LOCALE_CODE = 'en';

    private const string COUNTRY_CODE = 'gb';

    private const string SHOP_ACCOUNT_ENDPOINT = '/api/shop/account';

    private const string USER_EMAIL = '<EMAIL>';

    private const string USER_PASSWORD = 'test1234';

    private ShopUserAuthenticationTestHelper $authenticationHelper;

    protected function setUp(): void
    {
        parent::setUp();

        $this->authenticationHelper = new ShopUserAuthenticationTestHelper($this->client);
    }

    public function testCanGetAccount(): void
    {
        $customer = $this->createCustomer();
        $marketingSubscription = $this->createMarketingSubscriptionForCustomer($customer);
        $this->entityManager->flush();

        $token = $this->authenticationHelper->authenticate(self::USER_EMAIL, self::USER_PASSWORD, self::CHANNEL_CODE);
        $this->assertResponseIsSuccessful();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            self::SHOP_ACCOUNT_ENDPOINT,
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        $this->assertResponseIsSuccessful();

        $response = $this->getResponseBody();

        $expectedResponse = [
            'id' => $customer->getId(),
            'email' => self::USER_EMAIL,
            'firstName' => 'John',
            'lastName' => 'Doe',
            'fullName' => 'John Doe',
            'gender' => 'm',
            'birthday' => '1997-03-09',
            'subscribedToNewsletter' => true,
            'marketingSubscriptionUuid' => $marketingSubscription->getUuid()->toString(),
            'user' => [
                'verified' => false,
            ],
        ];

        $this->assertEquals(
            $expectedResponse,
            $response
        );
    }

    public function testCannotGetCustomerInformationWhenUnauthorized(): void
    {
        $this->createCustomer();
        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            self::SHOP_ACCOUNT_ENDPOINT,
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', 'invalidToken'),
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
        self::assertJsonStringEqualsJsonString(
            ProblemDetailsFactory::Unauthorized->json(),
            (string) $this->client->getResponse()->getContent(),
        );
    }

    public function testSeeExceptionWhenNoTokenIsProvided(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_GET,
            self::SHOP_ACCOUNT_ENDPOINT,
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
        self::assertJsonStringEqualsJsonString(
            ProblemDetailsFactory::Unauthorized->json(),
            (string) $this->client->getResponse()->getContent(),
        );
    }

    private function createCustomer(): Customer
    {
        /** @var CustomerPool $customerPool */
        $customerPool = $this->entityManager->getRepository(CustomerPool::class)->findOneBy(['code' => self::CUSTOMER_POOL_CODE]);

        $customer = new Customer();
        $customer->setBirthday(new DateTimeImmutable('1997-03-09'));
        $customer->setFirstName('John');
        $customer->setLastName('Doe');
        $customer->setEmail(self::USER_EMAIL);
        $customer->setGender(BaseCustomerInterface::MALE_GENDER);
        $customer->setSubscribedToNewsletter(true);
        $customer->setCustomerPool($customerPool);
        $customer->setUser($this->createUser($customer));

        $this->entityManager->persist($customer);

        return $customer;
    }

    private function createUser(Customer $customer): ShopUserInterface
    {
        $user = new ShopUser();
        $user->setUsername(self::USER_EMAIL);
        $user->setPlainPassword(self::USER_PASSWORD);
        $user->setCustomer($customer);
        $user->enable();

        $this->entityManager->persist($user);

        return $user;
    }

    private function createMarketingSubscriptionForCustomer(Customer $customer): MarketingSubscription
    {
        /** @var BusinessUnit $businessUnit */
        $businessUnit = $this->entityManager->getRepository(BusinessUnit::class)->findOneBy(['code' => 'dokteronline']);
        $marketingSubscription = new MarketingSubscription(
            self::USER_EMAIL,
            self::LOCALE_CODE,
            self::COUNTRY_CODE,
            $businessUnit
        );
        $marketingSubscription->setCustomer($customer);

        $this->entityManager->persist($marketingSubscription);

        return $marketingSubscription;
    }
}
