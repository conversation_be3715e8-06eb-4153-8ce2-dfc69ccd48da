<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Account;

use App\Api\Command\Account\RegisterCustomer;
use App\Api\CommandHandler\Account\RegisterCustomerHandler;
use App\Entity\MarketingSubscription\MarketingSubscription;
use App\Tests\Functional\Api\AbstractWebTestCase;
use DateTime;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Functional tests for {@see RegisterCustomer} and {@see RegisterCustomerHandler}.
 */
class RegisterCustomerTest extends AbstractWebTestCase
{
    private const string CHANNEL_CODE = 'dok_de';
    private const string LOCALE_CODE = 'de';

    private const string SHOP_CUSTOMER_ENDPOINT = '/api/shop/account/register';

    private const string WEAK_PASSWORD = 'test1234';

    public function testCanRegisterNewShopUser(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_CUSTOMER_ENDPOINT,
            $this->createRequestBody(),
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        // Regression test for https://mv-jira-1.atlassian.net/browse/DV-4323
        $marketingSubscriptionRepository = $this->entityManager->getRepository(MarketingSubscription::class);
        $marketingSubscription = $marketingSubscriptionRepository->findOneBy(['emailAddress' => self::ACCOUNT_EMAIL]);

        self::assertInstanceOf(MarketingSubscription::class, $marketingSubscription);
        self::assertEquals('DE', $marketingSubscription->getCountryCode());
        self::assertEquals('de', $marketingSubscription->getLocaleCode());
    }

    public function testCanRegisterNewBlueclinicShopUser(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_CUSTOMER_ENDPOINT,
            $this->createRequestBody(),
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => 'blueclinic_nl',
                'HTTP_ACCEPT_LANGUAGE' => 'nl',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        // Regression test for https://mv-jira-1.atlassian.net/browse/DV-4323
        $marketingSubscriptionRepository = $this->entityManager->getRepository(MarketingSubscription::class);
        $marketingSubscription = $marketingSubscriptionRepository->findOneBy(['emailAddress' => self::ACCOUNT_EMAIL]);

        self::assertInstanceOf(MarketingSubscription::class, $marketingSubscription);
        self::assertEquals('NL', $marketingSubscription->getCountryCode());
        self::assertEquals('nl', $marketingSubscription->getLocaleCode());
    }

    public function testCanRegisterAndSubscribeToNewsletter(): void
    {
        $requestBody = $this->createRequestBody();
        $requestBody['subscribedToNewsletter'] = true;

        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_CUSTOMER_ENDPOINT,
            $requestBody,
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        $marketingSubscriptionRepository = $this->entityManager->getRepository(MarketingSubscription::class);

        $marketingSubscription = $marketingSubscriptionRepository->findOneBy([
            'emailAddress' => $requestBody['email'],
        ]);

        self::assertInstanceOf(MarketingSubscription::class, $marketingSubscription);
        self::assertSame($requestBody['email'], $marketingSubscription->getEmail());
        self::assertSame($requestBody['email'], $marketingSubscription->getCustomer()?->getEmail());
    }

    public function testCanNotRegisterWithInvalidGender(): void
    {
        $requestBody = $this->createRequestBody();
        $requestBody['gender'] = 'u';

        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_CUSTOMER_ENDPOINT,
            $requestBody,
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertJsonStringEqualsJsonString(
            '{
                "type": "about:blank",
                "title": "The request body contains errors.",
                "status": 400,
                "detail": "Validation of JSON request body failed.",
                "violations": [
                    {
                        "constraint": "enum",
                        "message": "Does not have a value in the enumeration [\"m\",\"f\"]",
                        "property": "gender"
                    },
                    {
                        "constraint": "allOf",
                        "message": "Failed to match all schemas",
                        "property": ""
                    }
                ]
            }',
            $this->client->getResponse()->getContent()
        );
    }

    public function testCanNotRegisterWithInvalidBirthday(): void
    {
        $requestBody = $this->createRequestBody();
        $requestBody['birthday'] = '1997-09-31';

        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_CUSTOMER_ENDPOINT,
            $requestBody,
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertJsonStringEqualsJsonString(
            '{
                "type": "about:blank",
                "title": "The request body contains errors.",
                "status": 400,
                "detail": "Validation of JSON request body failed.",
                "violations": [
                    {
                        "constraint": "format",
                        "message": "Invalid date \"1997-09-31\", expected format YYYY-MM-DD",
                        "property": "birthday"
                    },
                    {
                        "constraint": "allOf",
                        "message": "Failed to match all schemas",
                        "property": ""
                    }
                ]
            }',
            $this->client->getResponse()->getContent()
        );
    }

    public function testCanNotRegisterWithTooLongPassword(): void
    {
        $requestBody = $this->createRequestBody();
        $requestBody['password'] = $requestBody['confirmNewPassword'] = bin2hex(random_bytes(65)); // 130 characters

        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_CUSTOMER_ENDPOINT,
            $requestBody,
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertJsonStringEqualsJsonString(
            '{
                "type": "about:blank",
                "title": "The request body contains errors.",
                "status": 400,
                "detail": "Validation of JSON request body failed.",
                "violations": [
                    {
                        "constraint": "length",
                        "message": "Diese Zeichenkette ist zu lang. Sie sollte h\u00f6chstens 128 Zeichen haben.",
                        "property": "password"
                    }
                ]
            }',
            $this->client->getResponse()->getContent()
        );
    }

    public function testCanNotRegisterWithInvalidAge(): void
    {
        $requestBody = $this->createRequestBody();
        $requestBody['birthday'] = (new DateTime('-16 years'))->format('Y-m-d');

        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_CUSTOMER_ENDPOINT,
            $requestBody,
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertJsonStringEqualsJsonString(
            '{
                "detail": "Validation of JSON request body failed.",
                "status": 400,
                "title": "The request body contains errors.",
                "type": "about:blank",
                "violations": [
                    {
                        "constraint": "age_greater_than",
                        "message": "Age must be greater than 18 years.",
                        "property": "birthday"
                    }
                ]
            }',
            $this->client->getResponse()->getContent()
        );
    }

    public function testCanNotRegisterWithInvalidChannelCode(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_CUSTOMER_ENDPOINT,
            $this->createRequestBody(),
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => 'invalid_channel',
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertJsonStringEqualsJsonString(
            '{
                "detail": "Validation of JSON request body failed.",
                "status": 400,
                "title": "The request body contains errors.",
                "type": "about:blank",
                "violations": [
                    {
                        "constraint": "not_blank",
                        "message": "Channel invalid.",
                        "property": "channel"
                    }
                ]
            }',
            $this->client->getResponse()->getContent()
        );
    }

    public function testCanNotRegisterWithInvalidEmail(): void
    {
        $requestBody = $this->createRequestBody();
        $requestBody['email'] = 'test';

        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_CUSTOMER_ENDPOINT,
            $requestBody,
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertJsonStringEqualsJsonString(
            '{
                "type": "about:blank",
                "title": "The request body contains errors.",
                "status": 400,
                "detail": "Validation of JSON request body failed.",
                "violations": [
                    {
                        "constraint": "format",
                        "message": "Invalid email",
                        "property": "email"
                    },
                    {
                        "constraint": "allOf",
                        "message": "Failed to match all schemas",
                        "property": ""
                    }
                ]
            }',
            $this->client->getResponse()->getContent()
        );
    }

    public function testCanNotRegisterWithDuplicateEmail(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_CUSTOMER_ENDPOINT,
            $this->createRequestBody(),
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_CUSTOMER_ENDPOINT,
            $this->createRequestBody(),
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertJsonStringEqualsJsonString(
            '
            {
                "type": "about:blank",
                "title": "The request body contains errors.",
                "status": 400,
                "detail": "Validation of JSON request body failed.",
                "violations": [
                    {
                        "constraint": "unique_email_in_customer_pool",
                        "message": "The provided email address is already known in this customer pool",
                        "property": "email"
                    }
                ]
            }
            ',
            $this->client->getResponse()->getContent()
        );
    }

    public function testCanNotRegisterWithWeakPassword(): void
    {
        $requestBody = $this->createRequestBody();
        $requestBody['password'] = self::WEAK_PASSWORD;
        $requestBody['confirmNewPassword'] = self::WEAK_PASSWORD;

        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_CUSTOMER_ENDPOINT,
            $requestBody,
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertJsonStringEqualsJsonString(
            '
            {
                "type": "about:blank",
                "title": "The request body contains errors.",
                "status": 400,
                "detail": "Validation of JSON request body failed.",
                "violations": [
                    {
                        "constraint": "strong_password",
                        "message": "The provided password does not meet the strength requirements.",
                        "property": "password"
                    }
                ]
            }
            ',
            $this->client->getResponse()->getContent()
        );
    }

    private function createRequestBody(): array
    {
        return [
            'firstName' => 'Ricardo',
            'lastName' => 'from the CrossRoad',
            'gender' => 'm',
            'birthday' => '1997-09-03',
            'email' => self::ACCOUNT_EMAIL,
            'phoneNumber' => '+31 123 456 789',
            'password' => 'goed2-lang7-wachtwoord4',
            'confirmNewPassword' => 'goed2-lang7-wachtwoord4',
            'subscribedToNewsletter' => false,
        ];
    }
}
