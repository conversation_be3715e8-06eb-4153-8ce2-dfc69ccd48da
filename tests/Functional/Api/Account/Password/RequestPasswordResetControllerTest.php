<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Account\Password;

use App\Entity\Channel\Channel;
use App\Tests\Functional\Api\CartTestFactory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class RequestPasswordResetControllerTest extends WebTestCase
{
    private const string SHOP_ACCOUNT_RESET_PASSWORD_ENDPOINT = '/api/shop/account/reset-password';

    private KernelBrowser $client;

    private EntityManagerInterface $entityManager;

    private CartTestFactory $cartTestFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = self::createClient();
        /** @var EntityManagerInterface $entityManager */
        $entityManager = static::getContainer()->get('doctrine.orm.entity_manager');
        $this->entityManager = $entityManager;
        $this->cartTestFactory = new CartTestFactory(static::getContainer());
    }

    public function testItCanRequestPasswordReset(): void
    {
        $user = $this->cartTestFactory->createUserForCustomer($this->cartTestFactory->createCustomer());

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        static::assertNull($user->getPasswordResetToken());
        static::assertNull($user->getPasswordRequestedAt());

        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_ACCOUNT_RESET_PASSWORD_ENDPOINT,
            [
                'email' => $user->getEmail(),
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => $this->getChannel()->getCode(),
            ]
        );

        static::assertResponseStatusCodeSame(Response::HTTP_ACCEPTED);

        static::assertIsString($user->getPasswordResetToken());
        static::assertNotEmpty($user->getPasswordResetToken());
        static::assertNotNull($user->getPasswordRequestedAt());
    }

    public function testItReturnsAcceptedResponseWhenUserIsNotFound(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_ACCOUNT_RESET_PASSWORD_ENDPOINT,
            [
                'email' => '<EMAIL>',
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => $this->getChannel()->getCode(),
            ]
        );

        static::assertResponseStatusCodeSame(Response::HTTP_ACCEPTED);
    }

    public function testItReturnsBadRequestResponseWhenEmailIsNotValid(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_ACCOUNT_RESET_PASSWORD_ENDPOINT,
            [
                'email' => 'notAValidEmailAddress',
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => $this->getChannel()->getCode(),
            ]
        );

        $responseBody = $this->client->getResponse()->getContent();
        static::assertIsString($responseBody);

        $expectedResponseBody = [
            'type' => 'about:blank',
            'title' => 'The request body contains errors.',
            'status' => 400,
            'detail' => 'Validation of JSON request body failed.',
            'violations' => [
                [
                    'constraint' => 'format',
                    'message' => 'Invalid email',
                    'property' => 'email',
                ],
            ],
        ];

        static::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        /** @var string $expectedResponseBody */
        $expectedResponseBody = json_encode($expectedResponseBody, JSON_THROW_ON_ERROR);
        static::assertJsonStringEqualsJsonString($expectedResponseBody, $responseBody);
    }

    private function getChannel(): Channel
    {
        $channelRepository = $this->entityManager->getRepository(Channel::class);
        /** @var Channel $channel */
        $channel = $channelRepository->findOneBy([
            'code' => 'dok_nl',
        ]);

        return $channel;
    }
}
