<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Account\Password;

use App\Api\Validator\StrongPassword;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Sylius\Component\Core\Model\ShopUserInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class ResetPasswordTest extends WebTestCase
{
    private const string USER_PASSWORD_RESET_TOKEN = 'password-reset-token';
    private const string USER_NEW_PASSWORD = 'my-new-password';
    private const string CHANNEL_CODE = 'dok_de';

    private KernelBrowser $client;
    private EntityManagerInterface $entityManager;
    private ShopUserAuthenticationTestHelper $shopUserAuthenticationTestHelper;
    private CartTestFactory $cartTestFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = self::createClient();

        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;
        $this->shopUserAuthenticationTestHelper = new ShopUserAuthenticationTestHelper($this->client);
        $this->cartTestFactory = new CartTestFactory(self::getContainer());
    }

    public function testItChangesPasswordWithCorrectResetTokenAndNewPassword(): void
    {
        $shopUser = $this->createShopUser();

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            $this->getApiEndpointUri($shopUser->getPasswordResetToken()),
            [
                'newPassword' => self::USER_NEW_PASSWORD,
                'confirmNewPassword' => self::USER_NEW_PASSWORD,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ],
        );

        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        self::assertNull($this->shopUserAuthenticationTestHelper->authenticate());
        self::assertNotNull($this->shopUserAuthenticationTestHelper->authenticate(
            CartTestFactory::USER_EMAIL,
            self::USER_NEW_PASSWORD,
        ));
        self::assertNotNull($shopUser->getUpdatedAt());
    }

    public function testItGivesValidationErrorWithMismatchingNewPasswordAndConfirmNewPassword(): void
    {
        $shopUser = $this->createShopUser();

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            $this->getApiEndpointUri($shopUser->getPasswordResetToken()),
            [
                'newPassword' => self::USER_NEW_PASSWORD,
                'confirmNewPassword' => 'the-confirm-password-is-not-the-same-as-the-new-password',
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ],
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $content = $this->client->getResponse()->getContent();

        self::assertIsString($content);

        $response = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

        self::assertIsArray($response['violations']);
        self::assertSame('confirm_password_same_as_password', $response['violations'][0]['constraint']);
        self::assertSame('The provided confirmNewPassword is not the same as password.', $response['violations'][0]['message']);
        self::assertSame('confirmNewPassword', $response['violations'][0]['property']);
    }

    public function testItGivesValidationErrorWithIncorrectResetToken(): void
    {
        $this->createShopUser();

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            $this->getApiEndpointUri('this-reset-token-does-not-exist'),
            [
                'newPassword' => self::USER_NEW_PASSWORD,
                'confirmNewPassword' => self::USER_NEW_PASSWORD,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ],
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        /** @var string $content */
        $content = $this->client->getResponse()->getContent();

        $response = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

        self::assertIsArray($response['violations']);
        self::assertSame('password_reset_token_valid', $response['violations'][0]['constraint']);
        self::assertSame('Provided passwordResetToken is not valid.', $response['violations'][0]['message']);
        self::assertSame('passwordResetToken', $response['violations'][0]['property']);
    }

    public function testItGivesValidationErrorWithExpiredResetToken(): void
    {
        $shopUser = $this->createShopUser();
        $shopUser->setPasswordRequestedAt((new DateTime())->modify('-99 years'));

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            $this->getApiEndpointUri($shopUser->getPasswordResetToken()),
            [
                'newPassword' => self::USER_NEW_PASSWORD,
                'confirmNewPassword' => self::USER_NEW_PASSWORD,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ],
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        /** @var string $content */
        $content = $this->client->getResponse()->getContent();

        $response = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

        self::assertIsArray($response['violations']);
        self::assertSame('password_reset_token_valid', $response['violations'][0]['constraint']);
        self::assertSame('Provided passwordResetToken has expired.', $response['violations'][0]['message']);
        self::assertSame('passwordResetToken', $response['violations'][0]['property']);
    }

    public function testItGivesValidationErrorWhenPasswordIsNotStrong(): void
    {
        $shopUser = $this->createShopUser();

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            $this->getApiEndpointUri($shopUser->getPasswordResetToken()),
            [
                'newPassword' => '12345678',
                'confirmNewPassword' => '12345678',
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ],
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        /** @var string $content */
        $content = $this->client->getResponse()->getContent();

        $response = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

        self::assertIsArray($response['violations']);
        self::assertSame('strong_password', $response['violations'][0]['constraint']);
        self::assertSame(StrongPassword::PASSWORD_STRENGTH_FAILED_MESSAGE, $response['violations'][0]['message']);
        self::assertSame('newPassword', $response['violations'][0]['property']);
    }

    public function testItGivesValidationErrorWhenPasswordIsTooLong(): void
    {
        $shopUser = $this->createShopUser();

        $newPassword = bin2hex(random_bytes(65)); // 130 characters
        $this->client->jsonRequest(
            Request::METHOD_PUT,
            $this->getApiEndpointUri($shopUser->getPasswordResetToken()),
            [
                'newPassword' => $newPassword,
                'confirmNewPassword' => $newPassword,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ],
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        /** @var string $content */
        $content = $this->client->getResponse()->getContent();

        $response = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

        self::assertIsArray($response['violations']);
        self::assertSame('length', $response['violations'][0]['constraint']);
        self::assertSame('This value is too long. It should have 128 characters or less.', $response['violations'][0]['message']);
        self::assertSame('newPassword', $response['violations'][0]['property']);
    }

    public function testItGivesValidationErrorWithNoResetTokenAfterSuccessfulPasswordReset(): void
    {
        $shopUser = $this->createShopUser();
        $passwordResetToken = $shopUser->getPasswordResetToken();

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            $this->getApiEndpointUri($passwordResetToken),
            [
                'newPassword' => self::USER_NEW_PASSWORD,
                'confirmNewPassword' => self::USER_NEW_PASSWORD,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ],
        );

        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            $this->getApiEndpointUri($passwordResetToken),
            [
                'newPassword' => self::USER_NEW_PASSWORD,
                'confirmNewPassword' => self::USER_NEW_PASSWORD,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ],
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
    }

    private function getApiEndpointUri(?string $passwordResetToken): string
    {
        return sprintf('/api/shop/account/reset-password/%s', $passwordResetToken);
    }

    private function createShopUser(): ShopUserInterface
    {
        $customer = $this->cartTestFactory->createCustomer();

        $shopUser = $this->cartTestFactory->createUserForCustomer($customer);

        $shopUser->setPasswordRequestedAt(new DateTime());
        $shopUser->setPasswordResetToken(self::USER_PASSWORD_RESET_TOKEN);

        $this->entityManager->persist($customer);
        $this->entityManager->persist($shopUser);

        $this->entityManager->flush();

        return $shopUser;
    }
}
