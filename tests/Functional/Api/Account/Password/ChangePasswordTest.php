<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Account\Password;

use App\Api\Validator\StrongPassword;
use App\Entity\User\ShopUserInterface;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Webmozart\Assert\Assert;

final class ChangePasswordTest extends WebTestCase
{
    private const string API_ENDPOINT_URI_CHANGE_PASSWORD = '/api/shop/account/password';
    private const string USER_NEW_PASSWORD = 'my-new-password';

    private const string CHANNEL_CODE = 'dok_gb';

    private KernelBrowser $client;
    private EntityManagerInterface $entityManager;
    private ShopUserAuthenticationTestHelper $shopUserAuthenticationTestHelper;
    private CartTestFactory $cartTestFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = self::createClient();

        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;
        $this->shopUserAuthenticationTestHelper = new ShopUserAuthenticationTestHelper($this->client);
        $this->cartTestFactory = new CartTestFactory(self::getContainer());
    }

    public function testItChangesPasswordWithCorrectCurrentPasswordAndNewPassword(): void
    {
        $shopUser = $this->createShopUser();
        $passwordChangedAt = new DateTime('2022-09-20T17:00:00');
        $shopUser->setPasswordRequestedAt($passwordChangedAt);

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            self::API_ENDPOINT_URI_CHANGE_PASSWORD,
            [
                'currentPassword' => CartTestFactory::USER_PASSWORD,
                'newPassword' => self::USER_NEW_PASSWORD,
                'confirmNewPassword' => self::USER_NEW_PASSWORD,
            ],
            [
                'HTTP_AUTHORIZATION' => $this->getAuthenticationToken(),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        self::assertNull($this->shopUserAuthenticationTestHelper->authenticate());
        self::assertNotNull(
            $this->shopUserAuthenticationTestHelper->authenticate(
                CartTestFactory::USER_EMAIL,
                self::USER_NEW_PASSWORD,
            )
        );
        self::assertNotSame($passwordChangedAt, $shopUser->getPasswordChangedAt());
    }

    public function testItGivesValidationErrorWithIncorrectCurrentPasswordAndCorrectNewPassword(): void
    {
        $this->createShopUser();

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            self::API_ENDPOINT_URI_CHANGE_PASSWORD,
            [
                'currentPassword' => 'this-is-not-my-current-password',
                'newPassword' => self::USER_NEW_PASSWORD,
                'confirmNewPassword' => self::USER_NEW_PASSWORD,
            ],
            [
                'HTTP_AUTHORIZATION' => $this->getAuthenticationToken(),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $content = $this->client->getResponse()->getContent();

        Assert::string($content);

        $response = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

        self::assertIsArray($response['violations']);
        self::assertSame('user_password', $response['violations'][0]['constraint']);
        self::assertSame('Given currentPassword is not correct for this user.', $response['violations'][0]['message']);
        self::assertSame('currentPassword', $response['violations'][0]['property']);
    }

    public function testItGivesValidationErrorForTooLongPassword(): void
    {
        $this->createShopUser();

        $newPassword = bin2hex(random_bytes(65)); // 130 characters
        $this->client->jsonRequest(
            Request::METHOD_PUT,
            self::API_ENDPOINT_URI_CHANGE_PASSWORD,
            [
                'currentPassword' => CartTestFactory::USER_PASSWORD,
                'newPassword' => $newPassword,
                'confirmNewPassword' => $newPassword,
            ],
            [
                'HTTP_AUTHORIZATION' => $this->getAuthenticationToken(),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $content = $this->client->getResponse()->getContent();

        Assert::string($content);

        $response = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

        self::assertIsArray($response['violations']);
        self::assertSame('length', $response['violations'][0]['constraint']);
        self::assertSame('This value is too long. It should have 128 characters or less.', $response['violations'][0]['message']);
        self::assertSame('newPassword', $response['violations'][0]['property']);
    }

    public function testItGivesValidationErrorWithCorrectCurrentPasswordAndIncorrectNewPassword(): void
    {
        $this->createShopUser();

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            self::API_ENDPOINT_URI_CHANGE_PASSWORD,
            [
                'currentPassword' => CartTestFactory::USER_PASSWORD,
                'newPassword' => 'this-is-not-the-same-as-confirm-new-password',
                'confirmNewPassword' => self::USER_NEW_PASSWORD,
            ],
            [
                'HTTP_AUTHORIZATION' => $this->getAuthenticationToken(),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $content = $this->client->getResponse()->getContent();

        Assert::string($content);

        $response = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

        self::assertIsArray($response['violations']);
        self::assertSame('confirm_password_same_as_password', $response['violations'][0]['constraint']);
        self::assertSame(
            'The provided confirmNewPassword is not the same as password.',
            $response['violations'][0]['message']
        );
        self::assertSame('confirmNewPassword', $response['violations'][0]['property']);
    }

    public function testItGivesValidationErrorWithCorrectCurrentPasswordAndIncorrectConfirmNewPassword(): void
    {
        $this->createShopUser();

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            self::API_ENDPOINT_URI_CHANGE_PASSWORD,
            [
                'currentPassword' => CartTestFactory::USER_PASSWORD,
                'newPassword' => self::USER_NEW_PASSWORD,
                'confirmNewPassword' => 'this-is-not-the-same-as-new-password',
            ],
            [
                'HTTP_AUTHORIZATION' => $this->getAuthenticationToken(),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $content = $this->client->getResponse()->getContent();

        Assert::string($content);

        $response = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

        self::assertIsArray($response['violations']);
        self::assertSame('confirm_password_same_as_password', $response['violations'][0]['constraint']);
        self::assertSame(
            'The provided confirmNewPassword is not the same as password.',
            $response['violations'][0]['message']
        );
        self::assertSame('confirmNewPassword', $response['violations'][0]['property']);
    }

    public function testItGivesValidationErrorWhenCurrentPasswordIsNotSend(): void
    {
        $this->createShopUser();

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            self::API_ENDPOINT_URI_CHANGE_PASSWORD,
            [
                'newPassword' => self::USER_NEW_PASSWORD,
                'confirmNewPassword' => self::USER_NEW_PASSWORD,
            ],
            [
                'HTTP_AUTHORIZATION' => $this->getAuthenticationToken(),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $content = $this->client->getResponse()->getContent();

        Assert::string($content);

        $response = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

        self::assertIsArray($response['violations']);
        self::assertSame('required', $response['violations'][0]['constraint']);
        self::assertSame('The property currentPassword is required', $response['violations'][0]['message']);
        self::assertSame('currentPassword', $response['violations'][0]['property']);
    }

    public function testItGivesValidationErrorWhenNewPasswordIsNotSend(): void
    {
        $this->createShopUser();

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            self::API_ENDPOINT_URI_CHANGE_PASSWORD,
            [
                'currentPassword' => CartTestFactory::USER_PASSWORD,
                'confirmNewPassword' => self::USER_NEW_PASSWORD,
            ],
            [
                'HTTP_AUTHORIZATION' => $this->getAuthenticationToken(),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $content = $this->client->getResponse()->getContent();

        Assert::string($content);

        $response = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

        self::assertIsArray($response['violations']);
        self::assertSame('required', $response['violations'][0]['constraint']);
        self::assertSame('The property newPassword is required', $response['violations'][0]['message']);
        self::assertSame('newPassword', $response['violations'][0]['property']);
    }

    public function testItGivesValidationErrorWhenPasswordIsNotStrong(): void
    {
        $this->createShopUser();

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            self::API_ENDPOINT_URI_CHANGE_PASSWORD,
            [
                'currentPassword' => CartTestFactory::USER_PASSWORD,
                'newPassword' => '12345678',
                'confirmNewPassword' => '12345678',
            ],
            [
                'HTTP_AUTHORIZATION' => $this->getAuthenticationToken(),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $content = $this->client->getResponse()->getContent();

        Assert::string($content);

        $response = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

        self::assertIsArray($response['violations']);
        self::assertSame('strong_password', $response['violations'][0]['constraint']);
        self::assertSame(StrongPassword::PASSWORD_STRENGTH_FAILED_MESSAGE, $response['violations'][0]['message']);
        self::assertSame('newPassword', $response['violations'][0]['property']);
    }

    public function testItGivesValidationErrorWhenConfirmNewPasswordIsNotSend(): void
    {
        $this->createShopUser();

        $this->client->jsonRequest(
            Request::METHOD_PUT,
            self::API_ENDPOINT_URI_CHANGE_PASSWORD,
            [
                'currentPassword' => CartTestFactory::USER_PASSWORD,
                'newPassword' => self::USER_NEW_PASSWORD,
            ],
            [
                'HTTP_AUTHORIZATION' => $this->getAuthenticationToken(),
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $content = $this->client->getResponse()->getContent();

        Assert::string($content);

        $response = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

        self::assertIsArray($response['violations']);
        self::assertSame('required', $response['violations'][0]['constraint']);
        self::assertSame('The property confirmNewPassword is required', $response['violations'][0]['message']);
        self::assertSame('confirmNewPassword', $response['violations'][0]['property']);
    }

    private function getAuthenticationToken(): string
    {
        return sprintf('Bearer %s', $this->shopUserAuthenticationTestHelper->authenticate());
    }

    private function createShopUser(): ShopUserInterface
    {
        $customer = $this->cartTestFactory->createCustomer();

        $shopUser = $this->cartTestFactory->createUserForCustomer($customer);

        $this->entityManager->persist($customer);
        $this->entityManager->persist($shopUser);

        $this->entityManager->flush();

        return $shopUser;
    }
}
