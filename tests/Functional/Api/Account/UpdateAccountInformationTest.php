<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Account;

use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\User\ShopUser;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use App\Tests\Util\Factory\ProblemDetailsFactory;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class UpdateAccountInformationTest extends WebTestCase
{
    private const string CHANNEL_CODE = 'dok_gb';
    private const string LOCALE_CODE = 'en';
    private const string SHOP_ACCOUNT_ENDPOINT = '/api/shop/account';
    private const string USER_EMAIL = '<EMAIL>';
    private const string USER_PASSWORD = 'test123';

    private KernelBrowser $client;
    private ShopUserAuthenticationTestHelper $authenticationHelper;
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = self::createClient();
        $this->authenticationHelper = new ShopUserAuthenticationTestHelper($this->client);
        $this->entityManager = static::getContainer()->get(EntityManagerInterface::class);
    }

    public function testItCanUpdateAccountInformation(): void
    {
        $customer = $this->createCustomer();
        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $token = $this->authenticationHelper->authenticate(self::USER_EMAIL, self::USER_PASSWORD, self::CHANNEL_CODE);
        self::assertResponseIsSuccessful();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            self::SHOP_ACCOUNT_ENDPOINT,
            $this->createRequestBody(),
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        self::assertResponseIsSuccessful();

        $response = json_decode($this->client->getResponse()->getContent(), true);

        $expectedResponse = [
            'user' => [
                'verified' => false,
            ],
            'id' => $customer->getId(),
            'email' => '<EMAIL>',
            'firstName' => 'Jane',
            'lastName' => 'Deo',
            'phoneNumber' => '**********',
            'birthday' => '1997-09-03',
            'gender' => 'm',
            'subscribedToNewsletter' => true,
            'fullName' => 'Jane Deo',
            'marketingSubscriptionUuid' => $customer->getMarketingSubscription()?->getUuid()->toString(),
        ];

        self::assertEquals($expectedResponse, $response);
    }

    public function testSeeExceptionWhenNoTokenIsProvided(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_GET,
            self::SHOP_ACCOUNT_ENDPOINT,
            $this->createRequestBody(),
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE,
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
        self::assertJsonStringEqualsJsonString(
            ProblemDetailsFactory::Unauthorized->json(),
            (string) $this->client->getResponse()->getContent()
        );
    }

    private function createCustomer(): Customer
    {
        $customer = new Customer();
        $customer->setBirthday(new DateTimeImmutable('1997-03-09'));
        $customer->setFirstName('John');
        $customer->setLastName('Doe');
        $customer->setPhoneNumber('***********');
        $customer->setEmail(self::USER_EMAIL);
        $customer->setGender(Customer::MALE_GENDER);
        $customer->setSubscribedToNewsletter(true);

        /** @var CustomerPool $customerPool */
        $customerPool = $this->entityManager
            ->getRepository(CustomerPool::class)
            ->findOneBy(['code' => 'dokteronline']);

        $customer->setCustomerPool($customerPool);

        $user = new ShopUser();
        $user->setCustomer($customer);
        $user->setPlainPassword(self::USER_PASSWORD);
        $user->enable();

        return $customer;
    }

    private function createRequestBody(): array
    {
        return [
            'firstName' => 'Jane',
            'lastName' => 'Deo',
            'birthday' => '1997-09-03',
            'subscribedToNewsletter' => true,
            'phoneNumber' => '**********',
        ];
    }
}
