<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Payment;

use App\Entity\Customer\Customer;
use App\Entity\Order\Order;
use App\Entity\Payment\Payment;
use App\Entity\Payment\PaymentMethod;
use App\Entity\User\ShopUserInterface;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use Doctrine\ORM\EntityManagerInterface;
use Sylius\Component\Core\OrderCheckoutStates;
use Sylius\Component\Payment\Model\PaymentInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class UpdatePaymentControllerTest extends WebTestCase
{
    private const string UPDATE_PAYMENT_CHECKOUT_ENDPOINT = '/api/shop/checkout/%s/payments/%s';

    private const string UPDATE_PAYMENT_ORDER_ENDPOINT = '/api/shop/orders/%s/payments/%s';

    private const string MASTER_CARD_PAYMENT_METHOD_CODE = 'mastercard_dokteronline';

    private const string CHANNEL_CODE = 'dok_de';

    private const string LOCALE_CODE = 'de';

    private KernelBrowser $client;

    private EntityManagerInterface $entityManager;

    private CartTestFactory $cartTestFactory;

    private ShopUserAuthenticationTestHelper $authenticationHelper;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
        $this->cartTestFactory = new CartTestFactory(static::getContainer());
        /** @var EntityManagerInterface $entityManager */
        $entityManager = static::getContainer()->get('doctrine.orm.entity_manager');
        $this->entityManager = $entityManager;

        $this->authenticationHelper = new ShopUserAuthenticationTestHelper($this->client);
    }

    public function testCanUpdatePaymentForOrderWithCartState(): void
    {
        $cart = $this->prepareOrder(Order::STATE_CART);
        $cart->setCheckoutState(OrderCheckoutStates::STATE_PAYMENT_SELECTED);
        $payment = $this->getPayment($cart);

        $this->entityManager->flush();

        $requestBody = [
            'paymentMethod' => 'bank_transfer_eur_dokteronline',
        ];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::UPDATE_PAYMENT_CHECKOUT_ENDPOINT, $cart->getTokenValue(), $payment->getId()),
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $content = (string) $this->client->getResponse()->getContent();
        $decodedResponse = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

        $expectedResponse = [
            'id' => $payment->getId(),
            'state' => 'cart',
            'method' => [
                'code' => 'bank_transfer_eur_dokteronline',
                'icon' => 'bank_transfer',
                'instructions' => 'An <strong>additional fee</strong> is charged for using this payment method. In addition, the order will take <strong>two days longer</strong> to deliver.',
                'name' => 'Bank transfer',
            ],
            'amount' => 1000,
            'details' => [
                'payment_started_from' => 'checkout',
            ],
        ];

        self::assertResponseIsSuccessful();
        self::assertJsonStringEqualsJsonString(
            json_encode($expectedResponse),
            json_encode($decodedResponse['payments'][0])
        );
        self::assertSame(OrderCheckoutStates::STATE_PAYMENT_SELECTED, $cart->getCheckoutState());
    }

    public function testCanUpdatePaymentForOrderWithOrderState(): void
    {
        $cart = $this->prepareOrder(Order::STATE_NEW);
        $cart->setCheckoutState(OrderCheckoutStates::STATE_PAYMENT_SELECTED);
        $address = $this->cartTestFactory->createAddress();
        $address->setStreet('same street');
        $addressB = clone $address;
        $cart->setShippingAddress($address);
        $cart->setBillingAddress($addressB);
        $payment = $this->getPayment($cart);
        $payment->setState(PaymentInterface::STATE_NEW);
        $customer = $this->createCustomer();

        $cart->setCustomer($customer);

        $paymentMethod = $this->entityManager
            ->getRepository(PaymentMethod::class)
            ->findOneBy(['code' => 'klarna_pay_later_dokteronline']);

        self::assertInstanceOf(PaymentMethod::class, $paymentMethod);

        $paymentMethod->setEnabled(true);

        $this->entityManager->flush();

        $token = $this->authenticationHelper->authenticate();

        $requestBody = [
            'paymentMethod' => 'klarna_pay_later_dokteronline',
        ];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::UPDATE_PAYMENT_ORDER_ENDPOINT, $cart->getTokenValue(), $payment->getId()),
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        $content = (string) $this->client->getResponse()->getContent();
        $decodedResponse = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

        self::assertResponseIsSuccessful();
        self::assertJsonStringEqualsJsonString(
            json_encode($this->getExpectedValidKlarnaResponse($payment)),
            json_encode($decodedResponse['payments'][0])
        );
    }

    /**
     * @dataProvider invalidStateForOrderEndpointProvider
     */
    public function testCannotUpdatePaymentForOrderWithInvalidState(string $state): void
    {
        $cart = $this->prepareOrder($state);
        $payment = $this->getPayment($cart);
        $customer = $this->createCustomer();

        $cart->setCustomer($customer);

        $this->entityManager->flush();

        $token = $this->authenticationHelper->authenticate();

        $requestBody = [
            'paymentMethod' => 'ideal_dokteronline',
            'issuerCode' => 'BUNQNL2A',
        ];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::UPDATE_PAYMENT_ORDER_ENDPOINT, $cart->getTokenValue(), $payment->getId()),
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        $content = (string) $this->client->getResponse()->getContent();
        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => 404,
            'detail' => 'The requested order could not be found.',
        ];

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        self::assertSame(json_decode($content, true), $expectedResponse);
    }

    /**
     * @dataProvider invalidStateForCheckoutEndpointProvider
     */
    public function testCannotUpdatePaymentForCheckoutWithInvalidState(string $state): void
    {
        $cart = $this->prepareOrder($state);
        $payment = $this->getPayment($cart);

        $this->entityManager->flush();

        $requestBody = [
            'paymentMethod' => 'ideal_dokteronline',
            'issuerCode' => 'BUNQNL2A',
        ];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::UPDATE_PAYMENT_CHECKOUT_ENDPOINT, $cart->getTokenValue(), $payment->getId()),
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $content = (string) $this->client->getResponse()->getContent();
        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => 404,
            'detail' => 'The requested cart session could not be found.',
        ];

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        self::assertSame(json_decode($content, true), $expectedResponse);
    }

    public function testSeeValidationExceptionWhenPaymentMethodDoesNotExist(): void
    {
        $cart = $this->prepareOrder(Order::STATE_CART);
        $payment = $this->getPayment($cart);

        $this->entityManager->flush();

        $requestBody = [
            'paymentMethod' => 'doesNotExist',
        ];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::UPDATE_PAYMENT_CHECKOUT_ENDPOINT, $cart->getTokenValue(), $payment->getId()),
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $content = (string) $this->client->getResponse()->getContent();
        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'The request body contains errors.',
            'status' => Response::HTTP_BAD_REQUEST,
            'detail' => 'Validation of JSON request body failed.',
            'violations' => [
                [
                    'constraint' => 'payment_method_exists',
                    'message' => 'The payment method is not found.',
                    'property' => 'paymentMethod',
                ],
            ],
        ];

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertSame($expectedResponse, json_decode($content, true));
    }

    public function testCannotUpdatePaymentWithInvalidPaymentMethodWithCheckoutState(): void
    {
        $cart = $this->prepareOrder(Order::STATE_CART);
        $cart->setCheckoutState(OrderCheckoutStates::STATE_PAYMENT_SELECTED);
        $cart->setShippingAddress($this->cartTestFactory->createAddress());
        $address = $this->cartTestFactory->createAddress();
        $address->setStreet('other street');
        $cart->setBillingAddress($address);

        $payment = $this->getPayment($cart);

        $paymentMethod = $this->entityManager
            ->getRepository(PaymentMethod::class)
            ->findOneBy(['code' => 'klarna_pay_later_dokteronline']);

        self::assertInstanceOf(PaymentMethod::class, $paymentMethod);

        $paymentMethod->setEnabled(true);

        $this->entityManager->flush();

        $requestBody = [
            'paymentMethod' => 'klarna_pay_later_dokteronline',
        ];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::UPDATE_PAYMENT_CHECKOUT_ENDPOINT, $cart->getTokenValue(), $payment->getId()),
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $content = (string) $this->client->getResponse()->getContent();
        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'The request body contains errors.',
            'status' => Response::HTTP_BAD_REQUEST,
            'detail' => 'Validation of JSON request body failed.',
            'violations' => [
                [
                    'constraint' => 'payment_method_allowed_for_non_matching_addresses',
                    'message' => 'The billing- and shipping address must match for the selected payment method.',
                    'property' => 'paymentMethod',
                ],
            ],
        ];

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertSame($expectedResponse, json_decode($content, true));
    }

    public function testCannotUpdatePaymentWithInvalidPaymentMethodWithOrderState(): void
    {
        $cart = $this->prepareOrder(Order::STATE_NEW);
        $cart->setCheckoutState(OrderCheckoutStates::STATE_PAYMENT_SELECTED);
        $cart->setShippingAddress($this->cartTestFactory->createAddress());
        $address = $this->cartTestFactory->createAddress();
        $address->setStreet('other street');
        $cart->setBillingAddress($address);

        $payment = $this->getPayment($cart);
        $payment->setState(PaymentInterface::STATE_NEW);
        $customer = $this->createCustomer();

        $cart->setCustomer($customer);

        $paymentMethod = $this->entityManager
            ->getRepository(PaymentMethod::class)
            ->findOneBy(['code' => 'klarna_pay_later_dokteronline']);

        self::assertInstanceOf(PaymentMethod::class, $paymentMethod);

        $paymentMethod->setEnabled(true);

        $this->entityManager->flush();

        $token = $this->authenticationHelper->authenticate();

        $requestBody = [
            'paymentMethod' => 'klarna_pay_later_dokteronline',
        ];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::UPDATE_PAYMENT_ORDER_ENDPOINT, $cart->getTokenValue(), $payment->getId()),
            $requestBody,
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        $content = (string) $this->client->getResponse()->getContent();
        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'The request body contains errors.',
            'status' => Response::HTTP_BAD_REQUEST,
            'detail' => 'Validation of JSON request body failed.',
            'violations' => [
                [
                    'constraint' => 'payment_method_allowed_for_non_matching_addresses',
                    'message' => 'The billing- and shipping address must match for the selected payment method.',
                    'property' => 'paymentMethod',
                ],
            ],
        ];

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertSame($expectedResponse, json_decode($content, true));
    }

    public function invalidStateForOrderEndpointProvider(): array
    {
        return [
            [Order::STATE_CART],
            [Order::STATE_CANCELLED],
        ];
    }

    public function invalidStateForCheckoutEndpointProvider(): array
    {
        return [
            [Order::STATE_NEW],
            [Order::STATE_CANCELLED],
            [Order::STATE_FULFILLED],
        ];
    }

    private function prepareOrder(string $state): Order
    {
        $cart = $this->cartTestFactory->createCart(self::CHANNEL_CODE, self::LOCALE_CODE);
        $cart->setState($state);

        return $cart;
    }

    private function getPayment(Order $cart): Payment
    {
        $paymentMethod = $this->entityManager
            ->getRepository(PaymentMethod::class)
            ->findOneBy(['code' => self::MASTER_CARD_PAYMENT_METHOD_CODE]);

        $payment = new Payment();
        $payment->setAmount(1000);
        $payment->setCurrencyCode('EUR');
        $payment->setMethod($paymentMethod);

        $cart->addPayment($payment);

        $this->entityManager->persist($payment);

        return $payment;
    }

    private function createCustomer(): Customer
    {
        $customer = $this->cartTestFactory->createCustomer();
        $customer->setLastName('Test');
        $customer->setGender('m');

        /** @var ShopUserInterface $shopUser */
        $shopUser = $this->cartTestFactory->createUserForCustomer($customer);
        $shopUser->setCustomer($customer);

        $this->entityManager->persist($customer);

        return $customer;
    }

    private function getExpectedValidKlarnaResponse(Payment $payment): array
    {
        return [
            'id' => $payment->getId(),
            'state' => 'new',
            'method' => [
                'code' => 'klarna_pay_later_dokteronline',
                'name' => 'Klarna - Pay later',
                'icon' => 'klarna_pay_later',
                'instructions' => 'You will receive a digital invoice by e-mail. The invoice must be paid within 14 days.',
            ],
            'amount' => 1000,
            'details' => [
                'payment_started_from' => 'account',
            ],
        ];
    }
}
