<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Payment;

use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Api\ExchangeRateTestFactory;
use App\Tests\Mocks\AnamnesisServiceClient;
use Sylius\Component\Core\OrderPaymentStates;
use Sylius\Component\Payment\Model\PaymentInterface;
use Symfony\Component\HttpFoundation\Request;

final class CancelPaymentControllerTest extends AbstractWebTestCase
{
    private const string CHANNEL_CODE = 'dok_de';

    private const string API_URI = '/api/shop/orders/%s/payments/%s/cancel';

    protected function setUp(): void
    {
        parent::setUp();

        $exchangeRateTestFactory = new ExchangeRateTestFactory(self::getContainer());
        $exchangeRateTestFactory->createExchangeRate();
    }

    /**
     * @dataProvider provideValidPaymentStates
     */
    public function testCanCancelPayment(string $currentPaymentState): void
    {
        $tokenValue = $this->createCompletedOrder();

        $order = $this->getOrderEntityByTokenValue($tokenValue);
        $payment = $order->getLastPayment();
        self::assertInstanceOf(PaymentInterface::class, $payment);
        self::assertCount(1, $order->getPayments());

        $payment->setState($currentPaymentState);
        $this->entityManager->flush();

        $userToken = $this->loginAccount(self::CHANNEL_CODE)['token'];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::API_URI, $tokenValue, $payment->getId()),
            [],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $userToken),
            ],
        );

        $responseBody = $this->getResponseBody();

        self::assertSame('cancelled', $responseBody['payments'][0]['state']);
        self::assertSame('new', $responseBody['payments'][1]['state']);
        self::assertSame('awaiting_payment', $responseBody['paymentState']);
    }

    /**
     * Regression test for DV-4892.
     *
     * @dataProvider provideValidPaymentStates
     */
    public function testAppliesCorrectPaymentStatesAfterCompletingPaymentWithOneCancelledPayment(string $currentPaymentState): void
    {
        $tokenValue = $this->createCompletedOrder();

        $order = $this->getOrderEntityByTokenValue($tokenValue);
        $payment = $order->getLastPayment();
        self::assertInstanceOf(PaymentInterface::class, $payment);

        $payment->setState($currentPaymentState);
        $this->entityManager->flush();

        $userToken = $this->loginAccount(self::CHANNEL_CODE)['token'];

        $this->cancelPayment($tokenValue, $payment->getId(), $userToken);

        $this->forcePayOrder($tokenValue);

        $order = $this->getOrderEntityByTokenValue($tokenValue);
        $payment = $order->getLastPayment();
        self::assertInstanceOf(PaymentInterface::class, $payment);
        self::assertSame(PaymentInterface::STATE_COMPLETED, $payment->getState());

        self::assertSame(OrderPaymentStates::STATE_PAID, $order->getPaymentState());
    }

    public function provideValidPaymentStates(): iterable
    {
        yield 'Order with a payment that is processing.' => [PaymentInterface::STATE_PROCESSING];
    }

    public function provideInvalidPaymentStates(): iterable
    {
        yield 'Order with a payment that is new.' => [PaymentInterface::STATE_NEW];
        yield 'Order with a payment that is authorized.' => [PaymentInterface::STATE_AUTHORIZED];
    }

    /**
     * @dataProvider provideInvalidPaymentStates
     */
    public function testCannotCancelPayment(string $currentPaymentState): void
    {
        $tokenValue = $this->createCompletedOrder();

        $order = $this->getOrderEntityByTokenValue($tokenValue);
        $payment = $order->getLastPayment();
        self::assertInstanceOf(PaymentInterface::class, $payment);
        self::assertCount(1, $order->getPayments());

        $payment->setState($currentPaymentState);
        $this->entityManager->flush();

        $userToken = $this->loginAccount(self::CHANNEL_CODE)['token'];

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::API_URI, $tokenValue, $payment->getId()),
            [],
            [
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $userToken),
            ],
        );

        $responseBody = $this->getResponseBody();

        self::assertSame('payment.state', $responseBody['violations'][0]['property']);
        self::assertSame(sprintf(
            "State '%s' is not allowed for 'sylius_payment'. Disallowed states: 'new, authorized'.",
            $currentPaymentState,
        ), $responseBody['violations'][0]['message']);
        self::assertSame('payment.state', $responseBody['violations'][0]['constraint']);
    }

    private function createCompletedOrder(): string
    {
        $localeCode = 'de';
        $countryCode = 'DE';

        $this->prepareDatabaseWithProducts([self::CHANNEL_CODE], $localeCode);

        $tokenValue = $this->createCart($localeCode, self::CHANNEL_CODE)['tokenValue'];

        $this->addItemsToCart($tokenValue, self::CHANNEL_CODE, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
                'quantity' => 1,
            ],
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_MEDICATION,
                'quantity' => 1,
                'parentProductVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
            ],
        ]);

        $this->registerAccount(self::CHANNEL_CODE);

        $userToken = $this->loginAccount(self::CHANNEL_CODE)['token'];

        $this->addMedicalQuestionnaire($tokenValue, AnamnesisServiceClient::EXISTING_UUID);

        $this->completeMedicalQuestionnaire($tokenValue);

        $paymentId = $this->setAddress($tokenValue, $countryCode, self::CHANNEL_CODE, $userToken)['payments'][0]['id'];

        $this->setPaymentMethod($tokenValue, $paymentId, 'mastercard_dokteronline', self::CHANNEL_CODE, $userToken);

        $this->completeOrder($tokenValue, self::CHANNEL_CODE, $userToken, [
            'declare_information_truthfully',
            'terms_and_conditions',
            'collect_medical_information',
            'transfer_prescription',
        ]);

        return $tokenValue;
    }
}
