<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api;

use App\Entity\Shipping\ShippingMethod;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\DependencyInjection\ContainerInterface;

class ShipmentTestFactory
{
    public const int SHIPPING_COST = 495;
    public const string SHIPPING_METHOD_CODE = 'DHL';

    public const string CHANNEL_CODE = 'dok_nl';

    private ObjectManager $objectManager;

    public function __construct(ContainerInterface $container)
    {
        $this->objectManager = $container->get(ObjectManager::class);
    }

    public function updateShippingMethod(
        ?string $shippingMethodCode = self::SHIPPING_METHOD_CODE,
        ?string $channelCode = self::CHANNEL_CODE,
        ?int $shippingCost = self::SHIPPING_COST,
    ): ShippingMethod {
        $shippingMethod = $this->objectManager
            ->getRepository(ShippingMethod::class)
            ->findOneBy(['code' => $shippingMethodCode]);

        $shippingMethod->setConfiguration([$channelCode => ['amount' => $shippingCost]]);

        return $shippingMethod;
    }
}
