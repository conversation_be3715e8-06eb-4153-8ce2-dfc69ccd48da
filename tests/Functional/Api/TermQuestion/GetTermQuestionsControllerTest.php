<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\TermQuestion;

use App\Entity\TermQuestion\TermQuestion;
use App\Tests\Functional\Api\CartTestFactory;
use DateTimeImmutable;
use Doctrine\Persistence\ObjectManager;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetTermQuestionsControllerTest extends WebTestCase
{
    private const string CHANNEL_CODE_GB = 'dok_gb';

    private const string LOCALE_CODE_EN = 'en';

    private const string CHANNEL_CODE_NL = 'dok_nl';

    private const string LOCALE_CODE_NL = 'nl';

    private const string PRODUCT_CODE = 'viagra_test_special';

    private const string PRODUCT_VARIANT_CODE = 'viagra_25mg_4_test_special';

    private KernelBrowser $client;

    private ObjectManager $objectManager;

    private CartTestFactory $cartTestFactory;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
        $this->objectManager = static::getContainer()->get('doctrine.orm.entity_manager');
        $this->cartTestFactory = new CartTestFactory(static::getContainer());
    }

    /**
     * @dataProvider localeCodeProvider
     */
    public function testItCanGetTermQuestionsInRequestedLanguage(string $localeCode, string $expectedJson): void
    {
        $this->createTermQuestions($localeCode);

        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE_NL,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        $cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE_NL,
            $localeCode,
            [self::PRODUCT_VARIANT_CODE]
        );

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/checkout/%s/terms-conditions', $cart->getTokenValue()),
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_NL,
                'HTTP_ACCEPT_LANGUAGE' => $localeCode,
            ]
        );

        self::assertResponseIsSuccessful();

        $responseBody = $this->client->getResponse()->getContent();

        static::assertJson($responseBody);

        static::assertStringContainsString(
            $expectedJson,
            $responseBody
        );
    }

    public function testItCannotGetTermQuestionsWhenTokenValueIsNotFound(): void
    {
        $this->createTermQuestions();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            '/api/shop/checkout/non_existing_token/terms-conditions'
        );

        static::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    public function testItCannotGetTermQuestionsWhenOrderChannelDoesNotMatchRequestChannel(): void
    {
        $this->createTermQuestions();

        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE_GB,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        $cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE_GB,
            self::LOCALE_CODE_EN,
            [self::PRODUCT_VARIANT_CODE]
        );

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf('/api/shop/checkout/%s/terms-conditions', $cart->getTokenValue()),
            [],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE_NL,
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE_CODE_EN,
            ]
        );

        static::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $responseBody = $this->client->getResponse()->getContent();
        $errorMessage = [
            'detail' => sprintf('Channel with code \'%s\' does not match the channel on the order.', self::CHANNEL_CODE_NL),
            'status' => Response::HTTP_BAD_REQUEST,
            'title' => 'An error occurred.',
            'type' => 'about:blank',
        ];

        static::assertJsonStringEqualsJsonString(json_encode($errorMessage), $responseBody);
    }

    public function localeCodeProvider(): iterable
    {
        yield 'The returned language is en' => [self::LOCALE_CODE_EN, $this->createExpectedPartOfJsonString()];
        yield 'The returned language is nl' => [self::LOCALE_CODE_NL, $this->createExpectedPartOfNLJsonString()];
    }

    private function createTermQuestions(string $currentLocaleCode = self::LOCALE_CODE_EN): void
    {
        $termQuestion = new TermQuestion();
        $termQuestion->setCode('first_question');
        $termQuestion->setCreatedAt(new DateTimeImmutable());
        $termQuestion->setPosition(987);

        $termQuestionTranslation = $termQuestion->getTranslation(self::LOCALE_CODE_EN);
        $termQuestionTranslation->setName('Test question?');
        $termQuestionTranslation->setTranslatable($termQuestion);

        $termQuestionTranslationNL = $termQuestion->getTranslation(self::LOCALE_CODE_NL);
        $termQuestionTranslationNL->setName('Test vraag?');
        $termQuestionTranslationNL->setTranslatable($termQuestion);

        $otherTermQuestion = new TermQuestion();
        $otherTermQuestion->setCode('second_question');
        $otherTermQuestion->setCreatedAt(new DateTimeImmutable());
        $otherTermQuestion->setPosition(988);

        $otherTermQuestionTranslation = $otherTermQuestion->getTranslation(self::LOCALE_CODE_EN);
        $otherTermQuestionTranslation->setName('Is this the second question?');
        $otherTermQuestionTranslation->setTranslatable($otherTermQuestion);

        $otherTermQuestionTranslationNL = $otherTermQuestion->getTranslation(self::LOCALE_CODE_NL);
        $otherTermQuestionTranslationNL->setName('Is dit de tweede vraag?');
        $otherTermQuestionTranslationNL->setTranslatable($otherTermQuestion);

        // Added this line to make the test succeed. When making a normal request the currentLocale and defaultLocale
        // are set. When running the functional test they are not set, which results in an error
        $termQuestion->setCurrentLocale($currentLocaleCode);
        $otherTermQuestion->setCurrentLocale($currentLocaleCode);

        $this->objectManager->persist($termQuestion);
        $this->objectManager->persist($termQuestionTranslation);
        $this->objectManager->persist($otherTermQuestion);
        $this->objectManager->persist($otherTermQuestionTranslation);
        $this->objectManager->flush();
    }

    private function createExpectedPartOfJsonString(): string
    {
        $firstTermQuestion = json_encode([
            'code' => 'first_question',
            'name' => 'Test question?',
        ]);
        $secondTermQuestion = json_encode([
            'code' => 'second_question',
            'name' => 'Is this the second question?',
        ]);

        return sprintf('%s,%s', $firstTermQuestion, $secondTermQuestion);
    }

    private function createExpectedPartOfNLJsonString(): string
    {
        $firstTermQuestion = json_encode([
            'code' => 'first_question',
            'name' => 'Test vraag?',
        ]);
        $secondTermQuestion = json_encode([
            'code' => 'second_question',
            'name' => 'Is dit de tweede vraag?',
        ]);

        return sprintf('%s,%s', $firstTermQuestion, $secondTermQuestion);
    }
}
