<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Checkout;

use App\Api\Controller\Checkout\GetPaymentMethodsController;
use App\Tests\Functional\Api\AbstractGetPaymentMethodsTest;
use S<PERSON>ius\Component\Order\Model\OrderInterface;

/**
 * Functional test for {@see GetPaymentMethodsController}.
 */
class GetPaymentMethodsTest extends AbstractGetPaymentMethodsTest
{
    protected function getEndpoint(string $tokenValue): string
    {
        return sprintf('/api/shop/checkout/%s/payment-methods', $tokenValue);
    }

    protected function getOrderState(): string
    {
        return OrderInterface::STATE_CART;
    }

    protected function getOrderNotFoundMessage(): ?string
    {
        return 'The requested cart session could not be found.';
    }

    protected function disallowedOrderStateProvider(): array
    {
        return [
            [OrderInterface::STATE_NEW],
            [OrderInterface::STATE_CANCELLED],
        ];
    }
}
