<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Checkout;

use App\Entity\Order\Order;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Mocks\AnamnesisServiceClient;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class CompleteQuestionnaireTest extends AbstractWebTestCase
{
    private const string ENDPOINT = '/api/shop/checkout/%s/questionnaire/complete';
    private const string CHANNEL_CODE = 'dok_nl';
    private const string LOCALE_CODE = 'nl';
    private const string CUSTOMER_POOL_CODE = 'dokteronline';

    private CartTestFactory $cartTestFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cartTestFactory = new CartTestFactory(self::getContainer());
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
    }

    public function testCanCompleteOrderQuestionnaire(): void
    {
        $cart = $this->cartTestFactory->createCart(self::CHANNEL_CODE, self::LOCALE_CODE);
        $cart->setState(Order::STATE_CART);
        $cart->setMedicalQuestionnaire(Uuid::fromString(AnamnesisServiceClient::VALID_UUID));

        $customer = $this->cartTestFactory->createCustomer(self::CUSTOMER_POOL_CODE);
        $cart->setCustomer($customer);

        $this->entityManager->persist($cart);
        $this->entityManager->flush();

        $decodedResponse = $this->makeRequest($cart);

        $this->assertResponseIsSuccessful();
        $this->assertSame(AnamnesisServiceClient::VALID_UUID, $decodedResponse['medicalQuestionnaire']['uuid']);
    }

    public function testCannotCompleteQuestionnaireThatDoesNotExist(): void
    {
        $cart = $this->cartTestFactory->createCart(self::CHANNEL_CODE, self::LOCALE_CODE);
        $cart->setState(Order::STATE_CART);
        $cart->setMedicalQuestionnaire(null);

        $customer = $this->cartTestFactory->createCustomer(self::CUSTOMER_POOL_CODE);
        $cart->setCustomer($customer);

        $this->entityManager->persist($cart);
        $this->entityManager->flush();

        $decodedResponse = $this->makeRequest($cart);

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'The request body contains errors.',
            'status' => Response::HTTP_BAD_REQUEST,
            'detail' => 'Validation of JSON request body failed.',
            'violations' => [
                [
                    'constraint' => 'can_complete_medical_questionnaire',
                    'message' => 'Unable to apply transition complete_medical_questionnaire to sylius_order_checkout',
                    'property' => 'order.transition_state',
                ],
            ],
        ];

        $this->assertSame($expectedResponse, $decodedResponse);
    }

    public function testCannotCompleteQuestionnaireOnOrderWhenNotValid(): void
    {
        $cart = $this->cartTestFactory->createCart(self::CHANNEL_CODE, self::LOCALE_CODE);
        $cart->setState(Order::STATE_CART);
        $cart->setMedicalQuestionnaire(Uuid::fromString(AnamnesisServiceClient::EXISTING_UUID));

        $customer = $this->cartTestFactory->createCustomer(self::CUSTOMER_POOL_CODE);
        $cart->setCustomer($customer);

        $this->entityManager->persist($cart);
        $this->entityManager->flush();

        $decodedResponse = $this->makeRequest($cart);

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'The request body contains errors.',
            'status' => Response::HTTP_BAD_REQUEST,
            'detail' => 'Validation of JSON request body failed.',
            'violations' => [
                [
                    'constraint' => 'can_complete_medical_questionnaire',
                    'message' => 'Unable to apply transition complete_medical_questionnaire to sylius_order_checkout',
                    'property' => 'order.transition_state',
                ],
            ],
        ];

        $this->assertSame($expectedResponse, $decodedResponse);
    }

    private function makeRequest(Order $cart, ?string $token = null): array
    {
        $headers = [
            'CONTENT_TYPE' => 'application/json',
        ];

        if (is_string($token)) {
            $headers['HTTP_AUTHORIZATION'] = sprintf('Bearer %s', $token);
        }

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::ENDPOINT, $cart->getTokenValue()),
            [],
            $headers
        );

        return json_decode(
            (string) $this->client->getResponse()->getContent(),
            true,
            512,
            JSON_THROW_ON_ERROR
        );
    }
}
