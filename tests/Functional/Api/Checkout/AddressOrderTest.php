<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Checkout;

use App\Api\Validator\ChannelAllowedForShippingAddress;
use App\Entity\Customer\Customer;
use App\Entity\Order\Order;
use App\StateMachine\OrderCheckoutStates;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class AddressOrderTest extends WebTestCase
{
    private const string CHANNEL_CODE = 'dok_nl';

    private const string LOCALE_CODE = 'nl';

    private const string PRODUCT_CODE = 'viagra_test_special';

    private const string PRODUCT_VARIANT_CODE = 'viagra_25mg_4_test_special';

    private KernelBrowser $client;

    private ShopUserAuthenticationTestHelper $authenticationHelper;

    private EntityManagerInterface $entityManager;

    private CartTestFactory $cartTestFactory;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
        $this->authenticationHelper = new ShopUserAuthenticationTestHelper($this->client);
        $this->cartTestFactory = new CartTestFactory(static::getContainer());
        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
        $this->entityManager->close();
        unset($this->client, $this->entityManager);
    }

    /**
     * @dataProvider provideInvalidAddress
     */
    public function testItValidatesAddress(string $street, array $expectedViolations): void
    {
        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        $cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            self::LOCALE_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );
        $customer = $this->cartTestFactory->createCustomer();
        $this->cartTestFactory->createUserForCustomer($customer);

        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $token = $this->authenticationHelper->authenticate();
        static::assertResponseIsSuccessful();

        $cart->setCheckoutState('payment_skipped');

        $this->entityManager->flush();

        $address = [
            'firstName' => 'Voornaam',
            'lastName' => 'Achternaam',
            'phoneNumber' => '0123456789',
            'countryCode' => 'NL',
            'street' => $street,
            'city' => 'Breda',
            'postcode' => '1234AB',
        ];

        static::assertNull($cart->getCustomer());
        $tokenValue = $cart->getTokenValue();
        $cart->setCheckoutState(OrderCheckoutStates::STATE_PREFERRED_PRODUCTS_SELECTED);
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(
                '/api/shop/checkout/%s/address',
                $tokenValue
            ),
            [
                'billingAddress' => $address,
                'shippingAddress' => $address,
            ],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();
        $decodedResponseBody = json_decode($responseBody, true, 512, JSON_THROW_ON_ERROR);

        self::assertSame($expectedViolations, $decodedResponseBody['violations']);
    }

    public function testItCanAddressOrderWhenShippingCountryIsAvailableForChannelWithoutEmail(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        $cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            self::LOCALE_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );
        $customer = $this->cartTestFactory->createCustomer();
        $this->cartTestFactory->createUserForCustomer($customer);

        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $token = $this->authenticationHelper->authenticate();
        static::assertResponseIsSuccessful();

        $cart->setCheckoutState('payment_skipped');

        $this->entityManager->flush();

        $address = [
            'firstName' => 'Voornaam',
            'lastName' => 'Achternaam',
            'phoneNumber' => '0123456789',
            'countryCode' => 'NL',
            'street' => 'Testplein 1',
            'city' => 'Breda',
            'postcode' => '1234AB',
        ];

        static::assertNull($cart->getCustomer());
        $tokenValue = $cart->getTokenValue();
        $cart->setCheckoutState(OrderCheckoutStates::STATE_PREFERRED_PRODUCTS_SELECTED);
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(
                '/api/shop/checkout/%s/address',
                $tokenValue
            ),
            [
                'billingAddress' => $address,
                'shippingAddress' => $address,
            ],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        static::assertResponseIsSuccessful();

        $order = $this->entityManager->getRepository(Order::class)->findOneBy(['tokenValue' => $tokenValue]);
        static::assertInstanceOf(Order::class, $order);
        static::assertInstanceOf(Customer::class, $order->getCustomer());
        static::assertSame($customer->getId(), $order->getCustomer()->getId());
    }

    public function testCannotAddressOrderWithInvalidRequestBody(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        $cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            self::LOCALE_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );
        $customer = $this->cartTestFactory->createCustomer();
        $customer->setUser($this->cartTestFactory->createUserForCustomer($customer));
        $cart->setCustomer($customer);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(
                '/api/shop/checkout/%s/address',
                $cart->getTokenValue()
            ),
            [
                'billingAddress' => [
                    'firstName' => 'Sjaak',
                    'lastName' => 'Afhaak',
                    'phoneNumber' => '0123456789',
                    'countryCode' => 'NL',
                    'street' => 'Testplein 1',
                    'city' => 'Breda',
                    'postcode' => '1234AB',
                ],
                'shippingAddress' => (object) [],
            ],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $expectedResponseBody = json_encode([
            'title' => 'The request body contains errors.',
            'type' => 'about:blank',
            'status' => Response::HTTP_BAD_REQUEST,
            'detail' => 'Validation of JSON request body failed.',
            'violations' => [
                [
                    'constraint' => 'required',
                    'message' => 'The property street is required',
                    'property' => 'shippingAddress.street',
                ],
                [
                    'constraint' => 'required',
                    'message' => 'The property postcode is required',
                    'property' => 'shippingAddress.postcode',
                ],
                [
                    'constraint' => 'required',
                    'message' => 'The property city is required',
                    'property' => 'shippingAddress.city',
                ],
                [
                    'constraint' => 'required',
                    'message' => 'The property countryCode is required',
                    'property' => 'shippingAddress.countryCode',
                ],
                [
                    'constraint' => 'allOf',
                    'message' => 'Failed to match all schemas',
                    'property' => 'shippingAddress',
                ],
                [
                    'constraint' => 'anyOf',
                    'message' => 'Failed to match at least one schema',
                    'property' => 'shippingAddress',
                ],
            ],
        ], JSON_THROW_ON_ERROR);

        static::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        static::assertJsonStringEqualsJsonString($expectedResponseBody, (string) $this->client->getResponse()->getContent());
    }

    public function testItThrowsExceptionWhenShippingCountryIsNotAvailableForChannel(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        $cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            self::LOCALE_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );
        $customer = $this->cartTestFactory->createCustomer();
        $this->cartTestFactory->createUserForCustomer($customer);
        $cart->setCustomer($customer);

        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $address = [
            'firstName' => 'Voornaam',
            'lastName' => 'Achternaam',
            'phoneNumber' => '0123456789',
            'countryCode' => 'DE',
            'street' => 'TestPlatz 1',
            'city' => 'Berlijn',
            'postcode' => '1234AB',
        ];

        $token = $this->authenticationHelper->authenticate();
        static::assertResponseIsSuccessful();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(
                '/api/shop/checkout/%s/address',
                $cart->getTokenValue()
            ),
            [
                'billingAddress' => $address,
                'shippingAddress' => $address,
            ],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        static::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $responseBody = (string) $this->client->getResponse()->getContent();

        static::assertStringContainsString(
            sprintf(ChannelAllowedForShippingAddress::CHANNEL_NOT_ALLOWED_FOR_COUNTRY_MESSAGE, 'DE'),
            $responseBody
        );
    }

    public function testItReturnsViolationsForPostcodeExceedingMaxLength(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        $cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            self::LOCALE_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );
        $customer = $this->cartTestFactory->createCustomer();
        $this->cartTestFactory->createUserForCustomer($customer);
        $cart->setCustomer($customer);

        $this->entityManager->persist($customer);
        $this->entityManager->flush();

        $address = [
            'firstName' => 'Voornaam',
            'lastName' => 'Achternaam',
            'phoneNumber' => '1337-1337-1337-1337-1337-1337-1337',
            'countryCode' => 'DE',
            'street' => 'TestPlatz 1',
            'city' => 'Berlijn',
            'postcode' => 'This postcode exceeds 10 characters',
        ];

        $token = $this->authenticationHelper->authenticate();

        self::assertResponseIsSuccessful();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(
                '/api/shop/checkout/%s/address',
                $cart->getTokenValue()
            ),
            [
                'billingAddress' => $address,
                'shippingAddress' => $address,
            ],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $expectedViolations = [
            [
                'constraint' => 'maxLength',
                'message' => 'Must be at most 20 characters long',
                'property' => 'billingAddress.phoneNumber',
            ],
            [
                'constraint' => 'maxLength',
                'message' => 'Must be at most 10 characters long',
                'property' => 'billingAddress.postcode',
            ],
            [
                'constraint' => 'allOf',
                'message' => 'Failed to match all schemas',
                'property' => 'billingAddress',
            ],
            [
                'constraint' => 'maxLength',
                'message' => 'Must be at most 20 characters long',
                'property' => 'shippingAddress.phoneNumber',
            ],
            [
                'constraint' => 'maxLength',
                'message' => 'Must be at most 10 characters long',
                'property' => 'shippingAddress.postcode',
            ],
            [
                'constraint' => 'allOf',
                'message' => 'Failed to match all schemas',
                'property' => 'shippingAddress',
            ],
            [
                'constraint' => 'anyOf',
                'message' => 'Failed to match at least one schema',
                'property' => 'shippingAddress',
            ],
        ];

        $response = json_decode((string) $this->client->getResponse()->getContent(), true, 512, JSON_THROW_ON_ERROR);

        self::assertArrayHasKey('violations', $response);
        self::assertSame($expectedViolations, $response['violations']);
    }

    /**
     * @return iterable<string, array{0: string, 1: array}>
     */
    protected function provideInvalidAddress(): iterable
    {
        yield 'Address that is empty.' => [
            '',
            [
                [
                    'constraint' => 'not_blank',
                    'message' => 'This value should not be blank.',
                    'property' => 'billingAddress.street',
                ],
                [
                    'constraint' => 'not_blank',
                    'message' => 'This value should not be blank.',
                    'property' => 'shippingAddress.street',
                ],
            ],
        ];

        yield 'Address without a digit.' => [
            'Bucketblock',
            [
                [
                    'constraint' => 'regex',
                    'message' => 'This value should at least contain one digit.',
                    'property' => 'billingAddress.street',
                ],
                [
                    'constraint' => 'regex',
                    'message' => 'This value should at least contain one digit.',
                    'property' => 'shippingAddress.street',
                ],
            ],
        ];
    }
}
