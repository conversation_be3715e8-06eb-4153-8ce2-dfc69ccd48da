<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Checkout;

use App\Entity\Order\Order;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use App\Tests\Mocks\AnamnesisServiceClient;
use Doctrine\ORM\EntityManagerInterface;
use Ramsey\Uuid\Uuid;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

final class RegisterQuestionnaireReferenceTest extends WebTestCase
{
    private const string ENDPOINT = '/api/shop/checkout/%s/questionnaire';
    private const string CHANNEL_CODE = 'dok_nl';
    private const string LOCALE_CODE = 'nl';
    private const string CUSTOMER_POOL_CODE = 'dokteronline';

    private KernelBrowser $client;
    private CartTestFactory $cartTestFactory;
    private EntityManagerInterface $entityManager;
    private ShopUserAuthenticationTestHelper $authenticationTestFactory;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();

        $this->client = self::createClient();

        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get('doctrine.orm.entity_manager');
        $this->entityManager = $entityManager;

        $this->cartTestFactory = new CartTestFactory(self::getContainer());
        $this->authenticationTestFactory = new ShopUserAuthenticationTestHelper($this->client);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
        $this->entityManager->close();
        unset($this->entityManager);
    }

    public function testItCanRegisterQuestionnaireReference(): void
    {
        $cart = $this->cartTestFactory->createCart(self::CHANNEL_CODE, self::LOCALE_CODE);
        $cart->setState(Order::STATE_CART);

        $customer = $this->cartTestFactory->createCustomer(self::CUSTOMER_POOL_CODE);
        $this->cartTestFactory->createUserForCustomer($customer);
        $cart->setCustomer($customer);

        $this->entityManager->persist($cart);
        $this->entityManager->flush();

        $decodedResponse = $this->makeRequest(['uuid' => AnamnesisServiceClient::EXISTING_UUID], $cart);

        $this->assertResponseIsSuccessful();
        $this->assertSame(AnamnesisServiceClient::EXISTING_UUID, $decodedResponse['medicalQuestionnaire']['uuid']);
    }

    public function testCustomerIsLinkedToOrderWhenOnRegisterQuestionnaire(): void
    {
        $cart = $this->cartTestFactory->createCart(self::CHANNEL_CODE, self::LOCALE_CODE);
        $cart->setState(Order::STATE_CART);

        $customer = $this->cartTestFactory->createCustomer();
        $user = $this->cartTestFactory->createUserForCustomer($customer);

        // Customer is not linked to cart at this point
        $this->entityManager->persist($user);
        $this->entityManager->persist($customer);
        $this->entityManager->persist($cart);
        $this->entityManager->flush();

        $this->assertEquals(null, $cart->getCustomer());

        $token = $this->authenticationTestFactory->authenticate();

        $decodedResponse = $this->makeRequest(['uuid' => AnamnesisServiceClient::EXISTING_UUID], $cart, $token);

        /** @var Order $cart */
        $cart = $this->entityManager->find(Order::class, $cart->getId());

        $this->assertResponseIsSuccessful();
        $this->assertSame(AnamnesisServiceClient::EXISTING_UUID, $decodedResponse['medicalQuestionnaire']['uuid']);
        $this->assertEquals($customer->getEmail(), $cart->getCustomer()?->getEmail());
    }

    public function testCannotRegisterQuestionnaireReferenceToThatDoesNotExist(): void
    {
        $cart = $this->cartTestFactory->createCart(self::CHANNEL_CODE, self::LOCALE_CODE);
        $cart->setState(Order::STATE_CART);

        $customer = $this->cartTestFactory->createCustomer(self::CUSTOMER_POOL_CODE);
        $cart->setCustomer($customer);

        $this->entityManager->persist($cart);
        $this->entityManager->flush();

        $decodedResponse = $this->makeRequest(['uuid' => 'ee1f8058-72a6-49da-bb60-b07c6ae61fb3'], $cart);

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'The request body contains errors.',
            'status' => Response::HTTP_BAD_REQUEST,
            'detail' => 'Validation of JSON request body failed.',
            'violations' => [
                [
                    'constraint' => 'medical_questionnaire_exists',
                    'message' => 'Medical questionnaire with uuid "ee1f8058-72a6-49da-bb60-b07c6ae61fb3" does not exist.',
                    'property' => 'uuid',
                ],
            ],
        ];

        $this->assertSame($expectedResponse, $decodedResponse);
    }

    public function testCannotRegisterQuestionnaireReferenceToOrderNotInCartState(): void
    {
        $cart = $this->cartTestFactory->createCart(self::CHANNEL_CODE, self::LOCALE_CODE);
        $cart->setState(Order::STATE_NEW);

        $customer = $this->cartTestFactory->createCustomer(self::CUSTOMER_POOL_CODE);
        $cart->setCustomer($customer);

        $this->entityManager->persist($cart);
        $this->entityManager->flush();

        $decodedResponse = $this->makeRequest(['uuid' => AnamnesisServiceClient::EXISTING_UUID], $cart);

        $this->assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);

        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => Response::HTTP_NOT_FOUND,
            'detail' => 'The requested cart session could not be found.',
        ];

        $this->assertSame($expectedResponse, $decodedResponse);
    }

    public function testItCanNotRegisterQuestionnaireThatIsAlreadyRegistered(): void
    {
        $cart = $this->cartTestFactory->createCart(self::CHANNEL_CODE, self::LOCALE_CODE);
        $cart->setState(Order::STATE_CART);
        $cart->setMedicalQuestionnaire(Uuid::fromString('7d058fed-a505-4aaa-8efb-d381269b45b7'));

        $customer = $this->cartTestFactory->createCustomer(self::CUSTOMER_POOL_CODE);
        $cart->setCustomer($customer);

        $this->entityManager->persist($cart);
        $this->entityManager->flush();

        $decodedResponse = $this->makeRequest(['uuid' => AnamnesisServiceClient::EXISTING_UUID], $cart);

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'The request body contains errors.',
            'status' => Response::HTTP_BAD_REQUEST,
            'detail' => 'Validation of JSON request body failed.',
            'violations' => [
                [
                    'constraint' => 'medical_questionnaire_not_already_registered',
                    'message' => 'A medical questionnaire has already been registered to the order.',
                    'property' => 'order',
                ],
            ],
        ];

        $this->assertSame($expectedResponse, $decodedResponse);
    }

    public function testItCanNotRegisterQuestionnaireThatIsAlreadyRegisteredToAnotherOrder(): void
    {
        $existingCart = $this->cartTestFactory->createCart(self::CHANNEL_CODE, self::LOCALE_CODE);
        $existingCart->setState(Order::STATE_CART);
        $existingCart->setMedicalQuestionnaire(Uuid::fromString(AnamnesisServiceClient::EXISTING_UUID));

        $customer = $this->cartTestFactory->createCustomer(self::CUSTOMER_POOL_CODE);
        $existingCart->setCustomer($customer);

        $this->entityManager->persist($existingCart);
        $this->entityManager->flush();

        $cart = $this->cartTestFactory->createCart(self::CHANNEL_CODE, self::LOCALE_CODE);
        $cart->setState(Order::STATE_CART);
        $cart->setCustomer($customer);

        $this->entityManager->persist($cart);
        $this->entityManager->flush();

        $decodedResponse = $this->makeRequest(['uuid' => AnamnesisServiceClient::EXISTING_UUID], $cart);

        $this->assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'The request body contains errors.',
            'status' => Response::HTTP_BAD_REQUEST,
            'detail' => 'Validation of JSON request body failed.',
            'violations' => [
                [
                    'constraint' => 'medical_questionnaire_not_already_registered',
                    'message' => 'Medical questionnaire with uuid "69e5a697-00fb-4087-bf9e-880a6bcf58e1" is already registered.',
                    'property' => 'uuid',
                ],
            ],
        ];

        $this->assertSame($expectedResponse, $decodedResponse);
    }

    private function makeRequest(array $requestBody, Order $cart, ?string $token = null): array
    {
        $headers = [
            'CONTENT_TYPE' => 'application/json',
        ];

        if (is_string($token)) {
            $headers['HTTP_AUTHORIZATION'] = sprintf('Bearer %s', $token);
        }

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::ENDPOINT, $cart->getTokenValue()),
            $requestBody,
            $headers
        );

        return json_decode(
            (string) $this->client->getResponse()->getContent(),
            true,
            512,
            JSON_THROW_ON_ERROR
        );
    }
}
