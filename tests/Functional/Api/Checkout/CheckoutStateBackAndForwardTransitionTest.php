<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Checkout;

use App\Entity\Order\Order;
use App\StateMachine\OrderCheckoutStates;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Mocks\AnamnesisServiceClient;
use Sylius\Component\Core\OrderCheckoutStates as SyliusOrderCheckoutStates;

final class CheckoutStateBackAndForwardTransitionTest extends AbstractWebTestCase
{
    public function testGoingBackToMedicalQuestionnaireStepFromAddressStep(): void
    {
        $channelCode = 'dok_gb';
        $localeCode = 'en';

        $this->prepareDatabaseWithProducts([$channelCode], $localeCode);

        $tokenValue = $this->createCart($localeCode, $channelCode)['tokenValue'];

        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
                'quantity' => 1,
            ],
        ]);

        $this->addMedicalQuestionnaire($tokenValue, AnamnesisServiceClient::EXISTING_UUID);

        $this->completeMedicalQuestionnaire($tokenValue);

        $order = $this->getOrderByTokenValue($tokenValue);

        self::assertSame(OrderCheckoutStates::STATE_MEDICAL_QUESTIONNAIRE_COMPLETED, $order->getCheckoutState());

        $this->completeMedicalQuestionnaire($tokenValue);

        $order = $this->getOrderByTokenValue($tokenValue);

        self::assertSame(OrderCheckoutStates::STATE_MEDICAL_QUESTIONNAIRE_COMPLETED, $order->getCheckoutState());
    }

    public function testGoingBackToAddressStepFromPaymentMethodStep(): void
    {
        $channelCode = 'dok_gb';
        $localeCode = 'en';
        $countryCode = 'GB';

        $this->prepareDatabaseWithProducts([$channelCode], $localeCode);

        $tokenValue = $this->createCart($localeCode, $channelCode)['tokenValue'];

        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
                'quantity' => 1,
            ],
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_MEDICATION,
                'quantity' => 1,
                'parentProductVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
            ],
        ]);

        $this->registerAccount($channelCode, self::ACCOUNT_EMAIL, self::ACCOUNT_PASSWORD);

        $userToken = $this->loginAccount($channelCode, self::ACCOUNT_EMAIL, self::ACCOUNT_PASSWORD)['token'];

        $this->addMedicalQuestionnaire($tokenValue, AnamnesisServiceClient::EXISTING_UUID);

        $this->completeMedicalQuestionnaire($tokenValue);

        $this->setAddress($tokenValue, $countryCode, $channelCode, $userToken);

        $order = $this->getOrderByTokenValue($tokenValue);

        self::assertSame(SyliusOrderCheckoutStates::STATE_SHIPPING_SKIPPED, $order->getCheckoutState());

        $this->setAddress($tokenValue, $countryCode, $channelCode, $userToken);

        $order = $this->getOrderByTokenValue($tokenValue);

        self::assertSame(SyliusOrderCheckoutStates::STATE_SHIPPING_SKIPPED, $order->getCheckoutState());
    }

    public function testGoingBackToPaymentMethodStepFromConfirmStep(): void
    {
        $channelCode = 'dok_gb';
        $localeCode = 'en';
        $countryCode = 'GB';

        $this->prepareDatabaseWithProducts([$channelCode], $localeCode);

        $tokenValue = $this->createCart($localeCode, $channelCode)['tokenValue'];

        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
                'quantity' => 1,
            ],
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_MEDICATION,
                'quantity' => 1,
                'parentProductVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
            ],
        ]);

        $this->registerAccount($channelCode, '<EMAIL>', '@superComplexPassword123#');

        $userToken = $this->loginAccount($channelCode, '<EMAIL>', '@superComplexPassword123#')['token'];

        $this->addMedicalQuestionnaire($tokenValue, AnamnesisServiceClient::EXISTING_UUID);

        $this->completeMedicalQuestionnaire($tokenValue);

        $paymentId = $this->setAddress($tokenValue, $countryCode, $channelCode, $userToken)['payments'][0]['id'];

        $this->setPaymentMethod($tokenValue, $paymentId, 'mastercard_doctoronline', $channelCode, $userToken);

        $order = $this->getOrderByTokenValue($tokenValue);

        self::assertSame(SyliusOrderCheckoutStates::STATE_PAYMENT_SELECTED, $order->getCheckoutState());

        $this->setPaymentMethod($tokenValue, $paymentId, 'mastercard_doctoronline', $channelCode, $userToken);

        $order = $this->getOrderByTokenValue($tokenValue);

        self::assertSame(SyliusOrderCheckoutStates::STATE_PAYMENT_SELECTED, $order->getCheckoutState());
    }

    public function testGoingBackToMedicationPreferenceStepFromDeliveryStep(): void
    {
        $channelCode = 'dok_nl';
        $localeCode = 'nl';

        $this->prepareDatabaseWithProducts([$channelCode], $localeCode);

        $tokenValue = $this->createCart($localeCode, $channelCode)['tokenValue'];

        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
                'quantity' => 1,
            ],
        ]);

        $this->addMedicalQuestionnaire($tokenValue, AnamnesisServiceClient::EXISTING_UUID);

        $this->completeMedicalQuestionnaire($tokenValue);

        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_MEDICATION,
                'quantity' => 1,
                'parentProductVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
            ],
        ]);

        $order = $this->getOrderByTokenValue($tokenValue);

        self::assertSame(OrderCheckoutStates::STATE_PREFERRED_PRODUCTS_SELECTED, $order->getCheckoutState());

        // Reselecting same item
        $this->clearChildItemsFromCart($tokenValue, $channelCode, [
            [
                'parentProductVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
            ],
        ]);

        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_MEDICATION,
                'quantity' => 1,
                'parentProductVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
            ],
        ]);

        $order = $this->getOrderByTokenValue($tokenValue);

        self::assertSame(OrderCheckoutStates::STATE_PREFERRED_PRODUCTS_SELECTED, $order->getCheckoutState());

        // Doctor should choose medication for the customer choice
        $this->clearChildItemsFromCart($tokenValue, $channelCode, [
            [
                'parentProductVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
            ],
        ]);

        $order = $this->getOrderByTokenValue($tokenValue);

        self::assertSame(OrderCheckoutStates::STATE_MEDICAL_QUESTIONNAIRE_COMPLETED, $order->getCheckoutState());
    }

    /**
     * Regression test for https://superbrave.sentry.io/issues/**********/events/48ff898683ff4875a4fc6d205ab30fc0/?project=1501299.
     */
    public function testGoingBackToMedicationPreferenceStepFromAddressStep(): void
    {
        $channelCode = 'dok_nl';
        $localeCode = 'nl';
        $countryCode = 'NL';

        $this->prepareDatabaseWithProducts([$channelCode], $localeCode);

        $tokenValue = $this->createCart($localeCode, $channelCode)['tokenValue'];

        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
                'quantity' => 1,
            ],
        ]);

        $this->addMedicalQuestionnaire($tokenValue, AnamnesisServiceClient::EXISTING_UUID);

        $this->completeMedicalQuestionnaire($tokenValue);

        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_MEDICATION,
                'quantity' => 1,
                'parentProductVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
            ],
        ]);

        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_SERVICE_BLUECLINIC,
                'quantity' => 1,
            ],
        ]);

        $this->registerAccount($channelCode, '<EMAIL>', '@superComplexPassword123#');

        $userToken = $this->loginAccount($channelCode, '<EMAIL>', '@superComplexPassword123#')['token'];

        $this->setAddress($tokenValue, $countryCode, $channelCode, $userToken);

        $order = $this->getOrderByTokenValue($tokenValue);

        self::assertSame(SyliusOrderCheckoutStates::STATE_SHIPPING_SKIPPED, $order->getCheckoutState());

        // Reselecting same item
        $this->clearChildItemsFromCart($tokenValue, $channelCode, [
            [
                'parentProductVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
            ],
        ]);

        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_MEDICATION,
                'quantity' => 1,
                'parentProductVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
            ],
        ], $userToken);

        $order = $this->getOrderByTokenValue($tokenValue);

        self::assertSame(OrderCheckoutStates::STATE_PREFERRED_PRODUCTS_SELECTED, $order->getCheckoutState());
    }

    public function testGoingBackToPaymentStepFromConfirmStepOtcOrder(): void
    {
        $channelCode = 'dok_de';
        $localeCode = 'de';
        $countryCode = 'DE';

        $this->prepareDatabaseWithProducts([$channelCode], $localeCode);

        // Add item to cart
        $tokenValue = $this->createCart($localeCode, $channelCode)['tokenValue'];
        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_OTC,
                'quantity' => 1,
            ],
        ]);

        // Login
        $this->registerAccount($channelCode, self::ACCOUNT_EMAIL, self::ACCOUNT_PASSWORD);
        $userToken = $this->loginAccount($channelCode, self::ACCOUNT_EMAIL, self::ACCOUNT_PASSWORD)['token'];

        // Address
        $payentId = $this->setAddress($tokenValue, $countryCode, $channelCode, $userToken)['payments'][0]['id'];

        // Payment choice
        $this->setPaymentMethod($tokenValue, $payentId, 'mastercard_dokteronline', $channelCode, $userToken);

        // Confirm screen, but going back to select a new payment.
        $this->setPaymentMethod($tokenValue, $payentId, 'visa_dokteronline', $channelCode, $userToken);

        $order = $this->getOrderByTokenValue($tokenValue);
        self::assertSame(SyliusOrderCheckoutStates::STATE_PAYMENT_SELECTED, $order->getCheckoutState());
    }

    public function testGoingBackToAddresStepFromPaymentStepOtcOrder(): void
    {
        $channelCode = 'dok_de';
        $localeCode = 'de';
        $countryCode = 'DE';

        $this->prepareDatabaseWithProducts([$channelCode], $localeCode);

        // Add item to cart
        $tokenValue = $this->createCart($localeCode, $channelCode)['tokenValue'];
        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_OTC,
                'quantity' => 1,
            ],
        ]);

        // Login
        $this->registerAccount($channelCode, self::ACCOUNT_EMAIL, self::ACCOUNT_PASSWORD);
        $userToken = $this->loginAccount($channelCode, self::ACCOUNT_EMAIL, self::ACCOUNT_PASSWORD)['token'];

        // Address
        $payentId = $this->setAddress($tokenValue, $countryCode, $channelCode, $userToken)['payments'][0]['id'];

        // Payment choice
        $this->setPaymentMethod($tokenValue, $payentId, 'mastercard_dokteronline', $channelCode, $userToken);

        // Address
        $this->setAddress($tokenValue, $countryCode, $channelCode, $userToken);

        $order = $this->getOrderByTokenValue($tokenValue);
        self::assertSame(SyliusOrderCheckoutStates::STATE_SHIPPING_SKIPPED, $order->getCheckoutState());
    }

    public function testGoingBackToAddingProductsFromCartStep(): void
    {
        $channelCode = 'dok_de';
        $localeCode = 'de';

        $this->prepareDatabaseWithProducts([$channelCode], $localeCode);

        // Add item to cart
        $tokenValue = $this->createCart($localeCode, $channelCode)['tokenValue'];
        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_OTC,
                'quantity' => 1,
            ],
        ]);

        // Login
        $this->registerAccount($channelCode, self::ACCOUNT_EMAIL, self::ACCOUNT_PASSWORD);
        $userToken = $this->loginAccount($channelCode, self::ACCOUNT_EMAIL, self::ACCOUNT_PASSWORD)['token'];

        // Add item to cart again
        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_OTC,
                'quantity' => 2,
            ],
        ], $userToken);

        $order = $this->getOrderByTokenValue($tokenValue);
        self::assertSame(OrderCheckoutStates::STATE_MEDICAL_QUESTIONNAIRE_SKIPPED, $order->getCheckoutState());
    }

    public function testGoingBackToDeliveryStepFromAddressStep(): void
    {
        $channelCode = 'dok_nl';
        $localeCode = 'nl';

        $this->prepareDatabaseWithProducts([$channelCode], $localeCode);

        $tokenValue = $this->createCart($localeCode, $channelCode)['tokenValue'];

        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
                'quantity' => 1,
            ],
        ]);

        $this->addMedicalQuestionnaire($tokenValue, AnamnesisServiceClient::EXISTING_UUID);

        $this->completeMedicalQuestionnaire($tokenValue);

        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_MEDICATION,
                'quantity' => 1,
                'parentProductVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
            ],
        ]);

        // Choose Blueclinic delivery
        $serviceProductOrderItemId = $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_SERVICE_BLUECLINIC,
                'quantity' => 1,
            ],
        ])['items'][1]['id'];

        $order = $this->getOrderByTokenValue($tokenValue);

        self::assertSame(OrderCheckoutStates::STATE_DELIVERY_SERVICE_SELECTED, $order->getCheckoutState());

        // Choose Paper Prescription delivery
        $this->deleteItemFromCart($tokenValue, $channelCode, $serviceProductOrderItemId);

        $this->addItemsToCart($tokenValue, $channelCode, [
            [
                'productVariantCode' => self::PRODUCT_VARIANT_CODE_SERVICE_PRESCRIPTION,
                'quantity' => 1,
            ],
        ]);

        self::assertSame(OrderCheckoutStates::STATE_DELIVERY_SERVICE_SELECTED, $order->getCheckoutState());
    }

    private function getOrderByTokenValue(string $tokenValue): Order
    {
        $order = $this->entityManager->getRepository(Order::class)->findOneBy(['tokenValue' => $tokenValue]);
        self::assertInstanceOf(Order::class, $order);
        $this->entityManager->refresh($order);

        return $order;
    }
}
