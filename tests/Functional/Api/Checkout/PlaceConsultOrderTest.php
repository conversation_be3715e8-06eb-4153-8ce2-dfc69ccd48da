<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Checkout;

use App\Entity\Order\Order;
use App\StateMachine\OrderCheckoutStates;
use App\Tests\Functional\Api\ProductTestFactory;
use App\Tests\Mocks\AnamnesisServiceClient;
use Doctrine\ORM\EntityManagerInterface;
use Ramsey\Uuid\Uuid;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;

class PlaceConsultOrderTest extends WebTestCase
{
    private const string CHANNEL_CODE = 'dok_nl';

    private const string LOCALE = 'nl';

    private const array PRODUCTS = [
        'regular' => [
            [
                'code' => 'test_product_1',
                'name' => 'Test Product 1',
                'variantCode' => 'test_product_variant_1',
                'variantName' => 'Test Product Variant 1',
                'prescriptionRequired' => true,
            ],
            [
                'code' => 'test_product_2',
                'name' => 'Test Product 2',
                'variantCode' => 'test_product_variant_2',
                'variantName' => 'Test Product Variant 2',
                'prescriptionRequired' => true,
            ],
            [
                'code' => 'test_product_3',
                'name' => 'Test Product 3',
                'variantCode' => 'test_product_variant_3',
                'variantName' => 'Test Product Variant 3',
            ],
        ],
        'consult' => [
            [
                'code' => 'test_consult_1',
                'name' => 'Test Consult 1',
                'variantCode' => 'test_consult_variant_1',
                'variantName' => 'Test Consult Variant 1',
                'prescriptionRequired' => true,
            ],
            [
                'code' => 'test_consult_2',
                'name' => 'Test Consult 2',
                'variantCode' => 'test_consult_variant_2',
                'variantName' => 'Test Consult Variant 2',
                'prescriptionRequired' => true,
            ],
            [
                'code' => 'test_consult_3',
                'name' => 'Test Consult 3',
                'variantCode' => 'test_consult_variant_3',
                'variantName' => 'Test Consult Variant 3',
                'prescriptionRequired' => true,
            ],
        ],
        'associations' => [
            [
                'owner' => 'test_product_1',
                'associatedProducts' => [
                    'test_consult_1',
                    'test_consult_2',
                    'test_consult_3',
                ],
            ],
            [
                'owner' => 'test_product_2',
                'associatedProducts' => [
                    'test_consult_1',
                    'test_consult_3',
                ],
            ],
        ],
    ];
    private KernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
        $this->prepareDatabaseWithProducts();
    }

    public function testUserCanSelectPreferredMedicationInNl(): void
    {
        $consultProduct = self::PRODUCTS['consult'][0];
        $tokenValue = $this->createCart();
        $this->addItemToCart($tokenValue, $consultProduct['variantCode'], 1);
        $medicationProducts = $this->getListRelatedMedicationProducts($consultProduct['code'], 2);
        $this->addPreferredVariantToCart(
            $tokenValue,
            $medicationProducts[0]['defaultVariant']['code'],
            $consultProduct['variantCode'],
            1
        );
    }

    public function testUserCanReSelectPreferredMedicationInNl(): void
    {
        $consultProduct = self::PRODUCTS['consult'][0];
        $tokenValue = $this->createCart();
        $this->addItemToCart($tokenValue, $consultProduct['variantCode'], 1);
        /** @var Order $cart */
        $cart = $this->getContainer()->get(EntityManagerInterface::class)->getRepository(Order::class)->findOneBy(['tokenValue' => $tokenValue]);

        // Cheat a little to skip the medical questionnaire
        $cart->setMedicalQuestionnaire(Uuid::fromString(AnamnesisServiceClient::VALID_UUID));
        $cart->setCheckoutState(OrderCheckoutStates::STATE_MEDICAL_QUESTIONNAIRE_COMPLETED);

        $medicationProducts = $this->getListRelatedMedicationProducts($consultProduct['code'], 2);
        $preferredVariantCode = $medicationProducts[0]['defaultVariant']['code'];
        $this->addPreferredVariantToCart(
            $tokenValue,
            $preferredVariantCode,
            $consultProduct['variantCode'],
            1
        );
        $medicationProducts = $this->getListRelatedMedicationProducts(
            $consultProduct['code'],
            1,
            'Test Product 2'
        );

        $cart = $this->deleteRelatedItems($tokenValue, $consultProduct['variantCode']);
        static::assertArrayNotHasKey('preferredVariant', $cart['items'][0]);
        $this->addPreferredVariantToCart(
            $tokenValue,
            $medicationProducts[0]['defaultVariant']['code'],
            $consultProduct['variantCode'],
            1
        );
    }

    public function testUserCanSelectPreferredMedicationForMultipleConditionsInNl(): void
    {
        $firstConsultProduct = self::PRODUCTS['consult'][0];
        $secondConsultProduct = self::PRODUCTS['consult'][1];

        $tokenValue = $this->createCart();
        $this->addItemToCart($tokenValue, $firstConsultProduct['variantCode'], 1);
        $this->addItemToCart($tokenValue, $secondConsultProduct['variantCode'], 2);
        $listMedicationProducts = $this->getListRelatedMedicationProducts($firstConsultProduct['code'], 2);
        $firstPreferredVariantCode = $listMedicationProducts[0]['defaultVariant']['code'];
        $this->addPreferredVariantToCart(
            $tokenValue,
            $firstPreferredVariantCode,
            $firstConsultProduct['variantCode'],
            2
        );
        $listMedicationProducts = $this->getListRelatedMedicationProducts(
            $secondConsultProduct['code'],
            1
        );
        $secondPreferredVariantCode = $listMedicationProducts[0]['defaultVariant']['code'];
        $response = $this->addPreferredVariantToCart(
            $tokenValue,
            $secondPreferredVariantCode,
            $secondConsultProduct['variantCode'],
            2
        );

        self::assertSame($firstPreferredVariantCode, $response['items'][0]['preferredVariants'][0]['code']);
        self::assertSame($firstConsultProduct['variantCode'], $response['items'][0]['variant']['code']);

        self::assertSame($secondPreferredVariantCode, $response['items'][1]['preferredVariants'][0]['code']);
        self::assertSame($secondConsultProduct['variantCode'], $response['items'][1]['variant']['code']);
    }

    private function prepareDatabaseWithProducts(): void
    {
        $productTestFactory = new ProductTestFactory(
            static::getContainer(),
            [self::CHANNEL_CODE],
        );
        $productTestFactory->prepareDatabaseWithRegularAndConsultProducts(
            self::PRODUCTS['regular'],
            self::PRODUCTS['consult'],
            self::LOCALE,
        );
        foreach (self::PRODUCTS['associations'] as $association) {
            $productTestFactory->createConsultProductAssociations(
                $association['owner'],
                $association['associatedProducts'],
            );
        }
    }

    private function createCart(): string
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            '/api/shop/carts',
            [
                'empty' => null,
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();
        $responseBody = $this->client->getResponse()->getContent();
        $decodedResponse = json_decode($responseBody, true, 512, JSON_THROW_ON_ERROR);

        return $decodedResponse['tokenValue'];
    }

    private function deleteRelatedItems(
        string $tokenValue,
        string $parentProductVariantCode,
    ): array {
        $this->client->jsonRequest(
            Request::METHOD_DELETE,
            sprintf('/api/shop/carts/%s/child-items', $tokenValue),
            [
                [
                    'parentProductVariantCode' => $parentProductVariantCode,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();
        $responseBody = $this->client->getResponse()->getContent();

        return json_decode($responseBody, true, 512, JSON_THROW_ON_ERROR);
    }

    private function addItemToCart(
        string $tokenValue,
        string $productVariantCode,
        int $expectedItems,
    ): void {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $tokenValue),
            [
                [
                    'productVariantCode' => $productVariantCode,
                    'quantity' => 1,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();
        $responseBody = $this->client->getResponse()->getContent();
        $decodedResponse = json_decode($responseBody, true, 512, JSON_THROW_ON_ERROR);

        static::assertStringContainsString($productVariantCode, $responseBody);
        static::assertCount($expectedItems, $decodedResponse['items']);
    }

    private function getListRelatedMedicationProducts(
        string $consultCode,
        int $expectedNumberOfProducts,
        ?string $productName = null,
    ): array {
        $uri = '/api/shop/products?filterByConsult='.$consultCode;
        if ($productName !== null) {
            $uri .= sprintf('&filterByProductName=%s', $productName);
        }

        $this->client->jsonRequest(
            Request::METHOD_GET,
            $uri,
            [],
            [
                'HTTP_ACCEPT_LANGUAGE' => self::LOCALE,
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();
        $responseBody = $this->client->getResponse()->getContent();
        $decodedResponse = json_decode($responseBody, true, 512, JSON_THROW_ON_ERROR);

        self::assertCount($expectedNumberOfProducts, $decodedResponse);

        return $decodedResponse;
    }

    private function addPreferredVariantToCart(
        string $tokenValue,
        string $preferredVariantCode,
        string $parentProductVariantCode,
        int $expectedItemsInCart,
    ): array {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf('/api/shop/carts/%s/items', $tokenValue),
            [
                [
                    'productVariantCode' => $preferredVariantCode,
                    'quantity' => 1,
                    'parentProductVariantCode' => $parentProductVariantCode,
                ],
            ],
            [
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        static::assertResponseIsSuccessful();
        $responseBody = (string) $this->client->getResponse()->getContent();
        $decodedResponse = json_decode($responseBody, true, 512, JSON_THROW_ON_ERROR);
        static::assertEquals($decodedResponse['items'][0]['preferredVariants'][0]['code'], $preferredVariantCode);
        static::assertCount($expectedItemsInCart, $decodedResponse['items']);

        return $decodedResponse;
    }
}
