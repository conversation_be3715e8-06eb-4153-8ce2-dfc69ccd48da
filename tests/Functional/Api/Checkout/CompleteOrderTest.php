<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Checkout;

use App\Entity\Addressing\Address;
use App\Entity\Customer\Customer;
use App\Entity\Order\Enum\AffiliateConversionStatus;
use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Product\ProductVariant;
use App\Entity\TermQuestion\TermQuestion;
use App\Repository\AddressRepositoryInterface;
use App\Repository\OrderRepository;
use App\Repository\ProductVariantRepository;
use App\Repository\TermQuestionRepository;
use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Functional\Api\ExchangeRateTestFactory;
use App\Tests\Functional\Api\ProductTestFactory;
use App\Tests\Functional\Api\ShopUserAuthenticationTestHelper;
use App\Tests\Mocks\AnamnesisServiceClient;
use App\Tests\Util\GuzzleMockHandlerFactory;
use App\Tests\Util\MockHttpClientHandlerFactory;
use DateTime;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\Psr7\Response as GuzzleResponse;
use Helmich\JsonAssert\JsonAssertions;
use Ramsey\Uuid\Uuid;
use Sylius\Component\Core\OrderCheckoutStates as SyliusOrderCheckoutStates;
use Sylius\Component\Order\Model\OrderInterface as BaseOrderInterface;
use Symfony\Component\HttpClient\MockHttpClient;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class CompleteOrderTest extends AbstractWebTestCase
{
    use JsonAssertions;

    private const string API_ENDPOINT_URI_COMPLETE_ORDER = '/api/shop/checkout/%s/complete';
    private const string CHANNEL_CODE = 'dok_nl';
    private const string LOCALE_CODE = 'nl';
    private const string PRODUCT_CODE = 'viagra_test_special';
    private const string PRODUCT_VARIANT_CODE = 'viagra_25mg_4_test_special';
    private const string COMPLETE_NOTES = 'Doc, I need my Viagra, now!';

    private CartTestFactory $cartTestFactory;
    private TermQuestionRepository $termQuestionRepository;
    private ShopUserAuthenticationTestHelper $authenticationTestHelper;
    private MockHandler $mockHandler;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var MockHandler $mockHandler */
        $mockHandler = self::getContainer()->get(MockHandler::class);
        $this->mockHandler = $mockHandler;
        $this->cartTestFactory = new CartTestFactory(static::getContainer());
        /** @var TermQuestionRepository $termQuestionRepository */
        $termQuestionRepository = $this->entityManager?->getRepository(TermQuestion::class);
        $this->termQuestionRepository = $termQuestionRepository;
        $this->authenticationTestHelper = new ShopUserAuthenticationTestHelper($this->client);

        $exchangeRateTestFactory = new ExchangeRateTestFactory(self::getContainer());
        $exchangeRateTestFactory->createExchangeRate();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
    }

    public function testCanCompleteOtcOrder(): void
    {
        // Arrange
        $channelCode = 'dok_de';
        $localeCode = 'de';
        $countryCode = 'DE';

        $this->registerAccount($channelCode);

        $productTestFactory = new ProductTestFactory(
            self::getContainer(),
            [$channelCode],
        );

        $productTestFactory->prepareDatabaseWithRegularAndConsultProducts(
            [
                [
                    'name' => 'Candida test',
                    'code' => '2345',
                    'variantCode' => self::PRODUCT_VARIANT_CODE_OTC,
                    'variantName' => 'Test-Point Candida-Test 1 St. (für Frauen)',
                    'prescriptionRequired' => false,
                ],
            ],
            null,
            $localeCode,
        );

        // Act
        $this->createCompletedOtcOrder($channelCode, $localeCode, $countryCode);

        // Assert
        $responseBody = $this->getResponseBody();

        self::assertSame(SyliusOrderCheckoutStates::STATE_COMPLETED, $responseBody['checkoutState']);
        self::assertSame(BaseOrderInterface::STATE_NEW, $responseBody['state']);
    }

    /**
     * @return iterable<string, array{0: int, 1: DateTimeInterface, 2: int}>
     */
    public function provideDataForTestCompleteOrderIncrementsProductVariantAmountOrderedToday(): iterable
    {
        yield 'amountOrderedToday is 0 and the timestamp is now, should be amountOrderedToday = 1.' => [
            0,
            new DateTime(),
            1,
        ];

        yield 'amountOrderedToday is 1 but the timestamp is yesterday, should be amountOrderedToday = 1.' => [
            1,
            (new DateTime())->modify('-1 day, -1 second'),
            1,
        ];
    }

    /**
     * @dataProvider provideDataForTestCompleteOrderIncrementsProductVariantAmountOrderedToday
     */
    public function testCompleteOrderIncrementsProductVariantAmountOrderedToday(
        int $amountOrderedToday,
        DateTimeInterface $amountOrderedTodayUpdatedAt,
        int $expectedAmountOrderedToday,
    ): void {
        $channelCode = 'dok_de';
        $localeCode = 'de';
        $countryCode = 'DE';

        $this->registerAccount($channelCode);

        $productTestFactory = new ProductTestFactory(
            self::getContainer(),
            [$channelCode],
        );

        $productTestFactory->prepareDatabaseWithRegularAndConsultProducts(
            [
                [
                    'name' => 'Candida test',
                    'code' => '2345',
                    'variantCode' => self::PRODUCT_VARIANT_CODE_OTC,
                    'variantName' => 'Test-Point Candida-Test 1 St. (für Frauen)',
                    'prescriptionRequired' => false,
                ],
            ],
            null,
            $localeCode
        );

        $this->entityManager->getConnection()->executeQuery(
            '
            UPDATE sylius_product_variant
            SET amount_ordered_today_updated_at = :amountOrderedTodayUpdatedAt,
                amount_ordered_today = :amountOrderedToday;
            ',
            [
                'amountOrderedTodayUpdatedAt' => $amountOrderedTodayUpdatedAt->format('Y-m-d H:i:s'),
                'amountOrderedToday' => $amountOrderedToday,
            ],
        );

        $this->createCompletedOtcOrder($channelCode, $localeCode, $countryCode);

        /** @var ProductVariantRepository $productVariantRepository */
        $productVariantRepository = $this->entityManager?->getRepository(ProductVariant::class);
        /** @var ProductVariant $productVariant */
        $productVariant = $productVariantRepository->findOneBy(['code' => self::PRODUCT_VARIANT_CODE_OTC]);
        self::assertSame($expectedAmountOrderedToday, $productVariant->getAmountOrderedToday());
    }

    public function testCompleteOrder(): void
    {
        $mockResponse = new MockResponse(
            (string) json_encode([
                'order_key' => 'F1499C097FFA533D46FB05D52680AB9A',
                'expires_on' => '2020-01-31T19:00:12Z',
                'url' => 'https://cm.localhost/pay',
            ]),
            [
                'http_code' => 200,
            ],
        );

        // Seed persistent mock instances for HTTP-related services.
        // This ensures that Guzzle and Symfony HttpClient mocks retain their state
        // across kernel reboots during functional tests.
        GuzzleMockHandlerFactory::seed(new MockHandler());
        MockHttpClientHandlerFactory::seed(new \App\Tests\Functional\Mocks\MockHttpClient());

        /** @var MockHttpClient $cmHttpClientMock */
        $cmHttpClientMock = self::getContainer()->get('cm.mock_http_client');
        $cmHttpClientMock->setResponseFactory($mockResponse);

        $cart = $this->createOrderWithUser();
        $cart->setAffiliateId('test-1234');
        $cart->setAffiliateConversionStatus(AffiliateConversionStatus::NONE);
        $token = $this->authenticationTestHelper->authenticate();
        static::assertNull($cart->getAffiliateConversionId());

        $orderStateUpdatedAt = $cart->getOrderStateUpdatedAt();

        $responseBody = json_encode(
            [
                'uuid' => 'ab101d76-c14e-4e51-971f-c837f1fe30fa',
                'status' => 'new',
                'reference' => '1354368',
                'paymentProfile' => 'cars',
                'store' => 'dokteronline',
                'paymentMethods' => [
                    'iDeal',
                ],
                'amount' => [
                    'currency' => 'EUR',
                    'amount' => 27290000,
                ],
                'amountPaid' => [
                    'currency' => 'EUR',
                    'amount' => 27290000,
                ],
                'amountDue' => [
                    'currency' => 'EUR',
                    'amount' => 27290000,
                ],
                'customerPaymentLink' => 'https://dokteronline.payments.ehvg.dev/pay',
                'createdAt' => '2021-10-06T08:25:17.600Z',
                'updatedAt' => '2021-10-06T08:25:17.600Z',
            ],
            JSON_THROW_ON_ERROR
        );

        $this->mockHandler->append(
            new GuzzleResponse(201, [], $responseBody),
        );

        $this->client->disableReboot();
        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getApiUri($cart),
            [
                'notes' => self::COMPLETE_NOTES,
                'termsAnswers' => $this->getCheckoutTermsAnswers($cart),
            ],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        $this->transport('affiliate_conversion')->processOrFail(1);

        static::assertResponseIsSuccessful();

        /** @var string $responseBody */
        $responseBody = $this->client->getResponse()->getContent();

        static::assertJson($responseBody);

        /** @var array<string, mixed> $decodedResponseBody */
        $decodedResponseBody = json_decode($responseBody, true, 512, JSON_THROW_ON_ERROR);

        /** @var OrderRepository $orderRepository */
        $orderRepository = $this->entityManager?->getRepository(Order::class);
        /** @var Order $order */
        $order = $orderRepository->findOneBy([
            'tokenValue' => $decodedResponseBody['tokenValue'],
        ]);

        static::assertSame('completed', $decodedResponseBody['checkoutState']);
        static::assertSame('new', $decodedResponseBody['state']);
        static::assertTrue($order->getOrderStateUpdatedAt() > $orderStateUpdatedAt);

        /** @var array<int, array<string, mixed>> $payments */
        $payments = $decodedResponseBody['payments'];

        static::assertSame(
            [
                'payment_started_from' => 'checkout',
                'payment_url' => 'https://cm.localhost/pay',
                'cm_payments' => [
                    'order_key' => 'F1499C097FFA533D46FB05D52680AB9A',
                    'order_reference' => $payments[0]['id'].'-000000001',
                ],
            ],
            $payments[0]['details']
        );

        static::assertCount(1, $order->getOriginalItems());

        /** @var OrderItem $firstOrderItem */
        $firstOrderItem = $order->getOriginalItems()->first();
        static::assertSame('viagra_25mg_4_test_special', $firstOrderItem->getVariant()?->getCode());

        static::assertSame(self::COMPLETE_NOTES, $order->getNotes());
        static::assertNotNull($order->getAffiliateConversionId());
        static::assertEquals(AffiliateConversionStatus::PENDING->value, $order->getAffiliateConversionStatus()->value);
    }

    public function testCannotCompleteWhenCheckoutStateIsAlreadyCompleted(): void
    {
        $cart = $this->createOrderWithUser();
        $cart->setState(BaseOrderInterface::STATE_NEW);

        $this->entityManager->persist($cart);
        $this->entityManager->flush();

        $token = $this->authenticationTestHelper->authenticate();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getApiUri($cart),
            [
                'notes' => self::COMPLETE_NOTES,
                'termsAnswers' => $this->getCheckoutTermsAnswers($cart),
            ],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        static::assertResponseStatusCodeSame(404);

        /** @var string $responseBody */
        $responseBody = $this->client->getResponse()->getContent();

        static::assertJson($responseBody);

        /** @var array<string, mixed> $decodedResponseBody */
        $decodedResponseBody = json_decode($responseBody, true);

        static::assertSame($decodedResponseBody['detail'], 'The requested cart session could not be found.');
    }

    public function testCannotCompleteWhenStateIsNotCart(): void
    {
        $cart = $this->createOrderWithUser();
        $cart->setState(BaseOrderInterface::STATE_NEW);

        $this->entityManager->persist($cart);
        $this->entityManager->flush();

        $token = $this->authenticationTestHelper->authenticate();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getApiUri($cart),
            [
                'notes' => self::COMPLETE_NOTES,
                'termsAnswers' => $this->getCheckoutTermsAnswers($cart),
            ],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        static::assertResponseStatusCodeSame(404);

        /** @var string $responseBody */
        $responseBody = $this->client->getResponse()->getContent();

        static::assertJson($responseBody);

        /** @var array<string, mixed> $decodedResponseBody */
        $decodedResponseBody = json_decode($responseBody, true);

        static::assertSame($decodedResponseBody['detail'], 'The requested cart session could not be found.');
    }

    public function testAddressesAreAddedToCustomerAfterOrderComplete(): void
    {
        $cart = $this->createOrderWithUser();

        /** @var Customer $customer */
        $customer = $cart->getCustomer();

        static::assertNull($customer->getDefaultAddress());

        $token = $this->authenticationTestHelper->authenticate();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getApiUri($cart),
            [
                'notes' => self::COMPLETE_NOTES,
                'termsAnswers' => $this->getCheckoutTermsAnswers($cart),
            ],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        static::assertResponseIsSuccessful();

        /** @var string $responseBody */
        $responseBody = $this->client->getResponse()->getContent();

        static::assertJson($responseBody);

        /** @var AddressRepositoryInterface $addressRepository */
        $addressRepository = $this->entityManager?->getRepository(Address::class);
        /** @var array<Address> $addressesWithoutOrder */
        $addressesWithoutOrder = $addressRepository->findByCustomer($customer);
        $allAddresses = $addressRepository->findBy([
            'firstName' => 'Hans',
            'lastName' => 'Test',
            'street' => 'Test street 4',
            'postcode' => 'Test postcode',
            'city' => 'Breda',
            'countryCode' => 'NL',
        ]);

        // Only billing address is set resulting in one address.
        static::assertCount(1, $addressesWithoutOrder);

        // Billing address is cloned from order resulting in two addresses.
        static::assertCount(2, $allAddresses);

        /** @var Order $cart */
        $cart = $this->entityManager?->getRepository(Order::class)->findOneBy(['tokenValue' => $cart->getTokenValue()]);

        /** @var Customer $customer */
        $customer = $cart->getCustomer();

        static::assertInstanceOf(Address::class, $customer->getDefaultAddress());
    }

    public function testAddressWillNotBeSavedWithDifferentCompanyAfterOrderComplete(): void
    {
        $cart = $this->createOrderWithUser();
        $billingAddress = $this->cartTestFactory->createAddress();
        $cart->setBillingAddress($billingAddress);

        $shippingAddress = $this->cartTestFactory->createAddress();
        $company = 'Toilet store';
        $shippingAddress->setCompany($company);
        $cart->setShippingAddress($shippingAddress);

        $this->entityManager->flush();

        $token = $this->authenticationTestHelper->authenticate();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getApiUri($cart),
            [
                'notes' => self::COMPLETE_NOTES,
                'termsAnswers' => $this->getCheckoutTermsAnswers($cart),
            ],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        static::assertResponseIsSuccessful();
        static::assertJson((string) $this->client?->getResponse()->getContent());

        /** @var AddressRepositoryInterface $addressRepository */
        $addressRepository = $this->entityManager?->getRepository(Address::class);
        $addressesFromCustomer = $addressRepository->findByCustomer($cart->getCustomer());

        static::assertCount(1, $addressesFromCustomer, 'Only shipping address should result in one address.');
        static::assertSame($company, $addressesFromCustomer[0]->getCompany());
    }

    public function testItReturnsProblemExceptionIfOrderCannotTransitionToComplete(): void
    {
        $tokenValue = 'test-token-value';

        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'The request body contains errors.',
            'status' => Response::HTTP_BAD_REQUEST,
            'detail' => 'Validation of JSON request body failed.',
            'violations' => [
                [
                    'constraint' => 'can_complete',
                    'message' => 'Unable to apply transition complete to sylius_order_checkout',
                    'property' => 'order.transition_state',
                ],
            ],
        ];

        $cart = $this->createOrderWithUser();
        $cart->setTokenValue($tokenValue);
        $cart->setCheckoutState('cart');

        $this->entityManager->flush();

        $token = $this->authenticationTestHelper->authenticate();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getApiUri($cart),
            [
                'notes' => self::COMPLETE_NOTES,
                'termsAnswers' => $this->getCheckoutTermsAnswers($cart),
            ],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);
        self::assertJsonStringEqualsJsonString((string) json_encode($expectedResponse), $responseBody);
    }

    public function testItRollsBackTransactionOnException(): void
    {
        $cart = $this->createOrderWithUser();
        $cart->setMedicalQuestionnaire(Uuid::fromString(AnamnesisServiceClient::EXCEPTION_UUID));

        $token = $this->authenticationTestHelper->authenticate();

        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getApiUri($cart),
            [
                'notes' => self::COMPLETE_NOTES,
                'termsAnswers' => $this->getCheckoutTermsAnswers($cart),
            ],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $token),
            ]
        );

        /** @var Order $actualCart */
        $actualCart = $this->entityManager?->getRepository(Order::class)->findOneBy(
            ['tokenValue' => $cart->getTokenValue()]
        );

        $this->assertEquals('cart', $actualCart->getState());
        $this->assertEquals('payment_skipped', $actualCart->getCheckoutState());
        $this->assertEquals('cart', $actualCart->getShippingState());
        $this->assertEquals('cart', $actualCart->getPrescriptionState());
        $this->assertEquals('cart', $actualCart->getPaymentState());
    }

    public function testCannotCompleteOrderWithInvalidAnamnesis(): void
    {
        $channelCode = 'dok_nl';
        $localeCode = 'nl';
        $countryCode = 'NL';

        $this->prepareDatabaseWithProducts([$channelCode], $localeCode);

        // Customer creates the cart & adds the consult product & creates a new anamnesis service.
        $tokenValue = $this->createCart($localeCode, $channelCode)['tokenValue'];
        $this->addItemsToCart(
            $tokenValue,
            $channelCode,
            [
                [
                    'productVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
                    'quantity' => 1,
                ],
            ]
        );
        $this->addMedicalQuestionnaire($tokenValue, AnamnesisServiceClient::EXISTING_UUID);

        // Customer logs in
        $this->registerAccount($channelCode);
        $userToken = $this->loginAccount($channelCode)['token'];

        // Customer answers all the anamnesis questions & completes it.
        $this->completeMedicalQuestionnaire($tokenValue);

        // Customer chooses a preferred product.
        $this->addItemsToCart(
            $tokenValue,
            $channelCode,
            [
                [
                    'productVariantCode' => self::PRODUCT_VARIANT_CODE_MEDICATION,
                    'quantity' => 1,
                    'parentProductVariantCode' => self::PRODUCT_VARIANT_CODE_CONSULT,
                ],
            ]
        );

        // Mock that the anamnesis service is not completed by setting another UUID.
        // This happens when there is a preference product added to the cart, which requires additional anamnesis questions.
        $order = $this->getOrderEntityByTokenValue($tokenValue);
        self::assertInstanceOf(Order::class, $order);
        $order->setMedicalQuestionnaire(Uuid::fromString(AnamnesisServiceClient::EXISTING_UUID));
        $this->entityManager->flush();

        // Customer chooses delivery product.
        $this->addItemsToCart(
            $tokenValue,
            $channelCode,
            [
                [
                    'productVariantCode' => self::PRODUCT_VARIANT_CODE_SERVICE_BLUECLINIC,
                    'quantity' => 1,
                ],
            ]
        )['items'][1]['id'];

        // Customer creates an address
        $paymentId = $this->setAddress($tokenValue, $countryCode, $channelCode, $userToken)['payments'][0]['id'];

        // Customer chooses the payment method.
        $this->setPaymentMethod($tokenValue, $paymentId, 'mastercard_dokteronline', $channelCode, $userToken);

        // Customer completes the order, but should fail.
        $this->client->jsonRequest(
            Request::METHOD_POST,
            $this->getApiUri($order),
            [
                'termsAnswers' => [
                    'declare_information_truthfully',
                    'terms_and_conditions',
                    'collect_medical_information',
                    'transfer_prescription',
                ],
            ],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_SYLIUS_CHANNEL_CODE' => $channelCode,
                'HTTP_AUTHORIZATION' => sprintf('Bearer %s', $userToken),
            ]
        );

        $response = $this->client->getResponse()->getContent();

        self::assertJsonValueEquals($response, 'violations[0].constraint', 'can_complete');
        self::assertJsonValueEquals(
            $response,
            'violations[0].message',
            'Unable to apply transition complete to sylius_order_checkout'
        );
    }

    /**
     * @return string tokenValue of the order
     */
    private function createCompletedOtcOrder(string $channelCode, string $localeCode, string $countryCode): string
    {
        // Add item to cart
        $tokenValue = $this->createCart($localeCode, $channelCode)['tokenValue'];
        $this->addItemsToCart(
            $tokenValue,
            $channelCode,
            [
                [
                    'productVariantCode' => self::PRODUCT_VARIANT_CODE_OTC,
                    'quantity' => 1,
                ],
            ]
        );

        // Login
        $userToken = $this->loginAccount($channelCode)['token'];

        // Address
        $payentId = $this->setAddress($tokenValue, $countryCode, $channelCode, $userToken)['payments'][0]['id'];

        // Payment choice
        $this->setPaymentMethod($tokenValue, $payentId, 'mastercard_dokteronline', $channelCode, $userToken);

        // Confirm screen
        $this->completeOrder(
            $tokenValue,
            $channelCode,
            $userToken,
            [
                'declare_information_truthfully',
                'terms_and_conditions',
                'collect_medical_information',
                'transfer_prescription',
            ]
        );

        return $tokenValue;
    }

    private function getApiUri(Order $cart): string
    {
        return sprintf(
            self::API_ENDPOINT_URI_COMPLETE_ORDER,
            $cart->getTokenValue()
        );
    }

    private function createOrderWithUser(): Order
    {
        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        $cart = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            self::LOCALE_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );
        $customer = $this->cartTestFactory->createCustomer();
        $cart->setCustomer($customer);
        $this->cartTestFactory->createUserForCustomer($customer);
        $cart->setBillingAddress($this->cartTestFactory->createAddress());
        $cart->setCheckoutState('payment_skipped');
        $cart->setOrderStateUpdatedAt(new DateTime('2023-05-05 00:00:00'));

        $this->entityManager->flush();

        return $cart;
    }

    /**
     * @return array<string>
     */
    private function getCheckoutTermsAnswers(Order $cart): array
    {
        return (new ArrayCollection($this->termQuestionRepository->findAllByOrder($cart)))
            ->map(static function (TermQuestion $termQuestion) {
                /** @var string $code */
                $code = $termQuestion->getCode();

                return $code;
            })
            ->toArray();
    }
}
