<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api;

use App\Entity\Channel\Channel;
use App\Entity\Order\Order as OrderInterface;
use App\Entity\Payment\PaymentMethod;
use App\Entity\Payment\PaymentMethodChannel;
use App\Entity\Product\ProductInterface;
use App\Entity\Product\ProductType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

abstract class AbstractGetPaymentMethodsTest extends WebTestCase
{
    protected const string CHANNEL_CODE = 'dok_gb';

    private const string IDEAL_CODE_DOKTERONLINE = 'ideal_dokteronline';

    protected KernelBrowser $client;
    protected CartTestFactory $cartTestFactory;
    protected EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();

        $this->client = static::createClient();
        $this->cartTestFactory = new CartTestFactory(static::getContainer());

        /** @var EntityManagerInterface $entityManager */
        $entityManager = static::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;
    }

    public function testCanRetrievePaymentMethods(): void
    {
        $order = $this->getOrder();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint((string) $order->getTokenValue())
        );

        static::assertResponseIsSuccessful();

        $responseBody = json_decode((string) $this->client->getResponse()->getContent(), true);

        foreach ($responseBody as $responseItem) {
            if (str_contains($responseItem['code'], 'ideal_dokteronline')) {
                self::assertArrayHasKey('issuer', $responseItem);
            }

            self::assertArrayHasKey('code', $responseItem);
            self::assertArrayHasKey('icon', $responseItem);
            self::assertArrayHasKey('instructions', $responseItem);
            self::assertArrayHasKey('name', $responseItem);
        }
    }

    public function testCanRetrieveIdealBankInformationIfPaymentMethodIsIdeal(): void
    {
        $order = $this->getOrder('dok_nl', 'nl');

        $address = $this->cartTestFactory->createAddress();
        $order->setBillingAddress($address);
        $order->setShippingAddress($address);

        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint((string) $order->getTokenValue())
        );

        static::assertResponseIsSuccessful();

        $responseBody = json_decode((string) $this->client->getResponse()->getContent(), true);

        foreach ($responseBody as $responseItem) {
            if (str_contains($responseItem['code'], 'ideal_dokteronline')) {
                self::assertArrayNotHasKey('issuers', $responseItem);
            }

            self::assertArrayHasKey('code', $responseItem);
            self::assertArrayHasKey('icon', $responseItem);
            self::assertArrayHasKey('instructions', $responseItem);
            self::assertArrayHasKey('name', $responseItem);
        }
    }

    public function testCanRetrieveOnlyWhenDifferentAddressesAreAllowed(): void
    {
        $order = $this->getOrder('dok_nl', 'nl');

        $order->setBillingAddress($this->cartTestFactory->createAddress());
        $shippingAddress = $this->cartTestFactory->createAddress();
        $shippingAddress->setFirstName('different');
        $order->setShippingAddress($shippingAddress);

        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint((string) $order->getTokenValue())
        );

        static::assertResponseIsSuccessful();

        $responseBody = json_decode((string) $this->client->getResponse()->getContent(), true);

        foreach ($responseBody as $responseItem) {
            if (str_contains($responseItem['code'], 'ideal_dokteronline')) {
                self::assertArrayNotHasKey('issuers', $responseItem);
            }

            self::assertArrayHasKey('code', $responseItem);
            self::assertArrayHasKey('icon', $responseItem);
            self::assertArrayHasKey('instructions', $responseItem);
            self::assertArrayHasKey('name', $responseItem);
        }
    }

    public function testCanRetrievePaymentMethodsWhenOrderHasReshipmentService(): void
    {
        $order = $this->getOrder('dok_de', 'de', true);

        $order->setBillingAddress($this->cartTestFactory->createAddress());
        $order->setShippingAddress($this->cartTestFactory->createAddress());
        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint((string) $order->getTokenValue())
        );

        static::assertResponseIsSuccessful();

        $responseBody = json_decode((string) $this->client->getResponse()->getContent(), true);

        foreach ($responseBody as $responseItem) {
            if (str_contains($responseItem['code'], 'ideal_dokteronline')) {
                self::assertArrayHasKey('issuers', $responseItem);
            }

            self::assertArrayHasKey('code', $responseItem);
            self::assertArrayHasKey('icon', $responseItem);
            self::assertArrayHasKey('instructions', $responseItem);
            self::assertArrayHasKey('name', $responseItem);
        }
    }

    /**
     * @dataProvider maximumOrderTotalChannelProvider
     */
    public function testCanRetrieveOnlyWhenOrderTotalDoesNotExceedMaximumOrderTotalForChannel(
        string $channelCode,
        int $maximumOrderTotal,
    ): void {
        $order = $this->getOrder($channelCode, 'nl');

        $orderTotal = 10000;
        $this->entityManager->getConnection()->executeStatement(
            'UPDATE sylius_order SET total = :orderTotal WHERE id = :orderId',
            [
                'orderTotal' => $orderTotal,
                'orderId' => $order->getId(),
            ]
        );

        $this->entityManager->refresh($order);

        /** @var PaymentMethod $idealPaymentMethod */
        $idealPaymentMethod = $this->entityManager->getRepository(PaymentMethod::class)->findOneBy(
            ['code' => self::IDEAL_CODE_DOKTERONLINE]
        );

        /** @var Channel $channel */
        $channel = $this->entityManager->getRepository(Channel::class)->findOneBy(['code' => $channelCode]);

        $paymentMethodChannel = new PaymentMethodChannel();
        $paymentMethodChannel->setPaymentMethod($idealPaymentMethod);
        $paymentMethodChannel->setChannel($channel);
        $paymentMethodChannel->setMaximumOrderTotal($maximumOrderTotal);

        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint((string) $order->getTokenValue())
        );

        static::assertResponseIsSuccessful();

        $responseBody = $this->client->getResponse()->getContent();

        $responsePaymentMethodCodes = array_column(json_decode((string) $responseBody, true), 'code');

        if ($maximumOrderTotal <= 10000) {
            static::assertContains('ideal_dokteronline', $responsePaymentMethodCodes);
        }

        if ($maximumOrderTotal > 10000) {
            static::assertNotContains('ideal_dokteronline', $responsePaymentMethodCodes);
        }
    }

    public function testCannotRetrievePaymentMethodsWithUnknownTokenValue(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint('unknown')
        );

        static::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);

        $errorMessage = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => Response::HTTP_NOT_FOUND,
        ];

        $orderNotFoundMessage = $this->getOrderNotFoundMessage();
        if (is_string($orderNotFoundMessage)) {
            $errorMessage['detail'] = $orderNotFoundMessage;
        }

        $responseBody = $this->client->getResponse()->getContent();

        static::assertJsonStringEqualsJsonString((string) json_encode($errorMessage), (string) $responseBody);
    }

    public function testBlueClinicPaymentMethods(): void
    {
        $order = $this->getOrder('blueclinic_nl', 'nl');

        $this->client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint((string) $order->getTokenValue())
        );

        static::assertResponseIsSuccessful();

        $responseBody = (string) $this->client->getResponse()->getContent();

        $response = json_decode($responseBody, true);

        $responsePaymentMethodCodes = array_column($response, 'code');

        static::assertNotEmpty($responsePaymentMethodCodes);
    }

    /**
     * @dataProvider disallowedOrderStateProvider
     */
    public function testThrowsCartNotFoundProblemDetailsResponseWhenOrderIsNotInStateCart(string $disallowedState): void
    {
        $order = $this->getOrder('dok_nl', 'nl');
        $order->setState($disallowedState);
        $this->entityManager->flush();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            $this->getEndpoint((string) $order->getTokenValue())
        );

        $responseBody = (string) $this->client->getResponse()->getContent();

        static::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        static::assertJson($responseBody);

        $errorMessage = [
            'status' => Response::HTTP_NOT_FOUND,
            'title' => 'An error occurred.',
            'type' => 'about:blank',
        ];

        $orderNotFoundMessage = $this->getOrderNotFoundMessage();
        if (is_string($orderNotFoundMessage)) {
            $errorMessage['detail'] = $orderNotFoundMessage;
        }

        static::assertJsonStringEqualsJsonString((string) json_encode($errorMessage), $responseBody);
    }

    abstract protected function getEndpoint(string $tokenValue): string;

    abstract protected function getOrderState(): string;

    abstract protected function getOrderNotFoundMessage(): ?string;

    abstract protected function disallowedOrderStateProvider(): array;

    /**
     * ChannelCode, MaximumOrderTotal.
     */
    protected function maximumOrderTotalChannelProvider(): iterable
    {
        yield ['dok_nl', 9000];
        yield ['dok_be', 12000];
    }

    protected function getOrder(
        string $channelCode = self::CHANNEL_CODE,
        string $localeCode = 'en',
        bool $addReshipmentProduct = false,
    ): OrderInterface {
        $order = $this->cartTestFactory->createCart($channelCode, $localeCode);

        if ($addReshipmentProduct) {
            $this->cartTestFactory->createProductAndProductVariants(
                channelCode: 'dok_de',
                productCode: ProductInterface::SERVICE_RESHIPMENT_CODE,
                productVariantCodes: ['service_reship_1495'],
                productType: ProductType::SERVICE
            );
            $this->cartTestFactory->addItemToCart($order, 'service_reship_1495', 1);
        }

        $order->setState($this->getOrderState());

        $this->entityManager->flush();

        return $order;
    }
}
