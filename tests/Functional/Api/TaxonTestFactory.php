<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api;

use App\Entity\Channel\Channel;
use App\Entity\Product\ProductTaxon;
use App\Entity\Product\ProductTaxonChannel;
use App\Entity\Product\ProductTaxonInterface;
use App\Entity\Taxonomy\Taxon;
use App\Entity\Taxonomy\TaxonTranslation;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Sylius\Component\Core\Model\ProductInterface;
use Sylius\Component\Core\Model\TaxonInterface;
use Sylius\Component\Taxonomy\Factory\TaxonFactoryInterface;
use Sylius\Component\Taxonomy\Model\TaxonTranslationInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

class TaxonTestFactory
{
    public const string TAXON_CODE = 'taxon_code';

    /** @var TaxonFactoryInterface<Taxon> */
    private TaxonFactoryInterface $taxonFactory;

    private EntityManagerInterface $entityManager;

    public function __construct(ContainerInterface $container)
    {
        /** @var TaxonFactoryInterface<Taxon> $taxonFactory */
        $taxonFactory = $container->get(TaxonFactoryInterface::class);
        $this->taxonFactory = $taxonFactory;

        /** @var EntityManagerInterface $entityManager */
        $entityManager = $container->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;
    }

    public function createTaxon(string $taxonCode = self::TAXON_CODE): Taxon
    {
        $taxon = $this->taxonFactory->createNew();
        $taxon->setCode($taxonCode);

        $this->entityManager->persist($taxon);

        return $taxon;
    }

    public function createTaxonTranslation(TaxonInterface $taxon, string $locale = 'en'): TaxonTranslationInterface
    {
        $taxonTranslation = new TaxonTranslation();
        $taxonTranslation->setSlug('test-taxon-'.$locale);
        $taxonTranslation->setName('test taxon '.$locale);
        $taxonTranslation->setLocale($locale);
        $taxonTranslation->setTranslatable($taxon);

        $this->entityManager->persist($taxonTranslation);

        return $taxonTranslation;
    }

    public function createProductTaxon(ProductInterface $product, TaxonInterface $taxon): ProductTaxonInterface
    {
        $productTaxon = new ProductTaxon();
        $productTaxon->setProduct($product);
        $productTaxon->setTaxon($taxon);
        $product->addProductTaxon($productTaxon);

        $productTaxonChannel = new ProductTaxonChannel();
        /** @var Channel $channel */
        $channel = $product->getChannels()->first();
        $productTaxonChannel->setChannel($channel);
        $productTaxonChannel->setProductTaxon($productTaxon);
        $productTaxonChannel->setRelevance(1234);
        $productTaxonChannel->setLabel('new');
        $productTaxon->setProductTaxonChannels(new ArrayCollection([$productTaxonChannel]));

        $product->setMainTaxon($taxon);

        $this->entityManager->persist($productTaxon);
        $this->entityManager->persist($productTaxonChannel);

        return $productTaxon;
    }
}
