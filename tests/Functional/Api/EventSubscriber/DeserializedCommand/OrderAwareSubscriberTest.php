<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\EventSubscriber\DeserializedCommand;

use App\Api\Command\OrderAwareInterface;
use App\Api\EventSubscriber\DeserializedCommand\OrderAwareSubscriber;
use App\Entity\Order\Order;
use App\Repository\OrderRepositoryInterface;
use Doctrine\ORM\EntityManagerInterface;
use Sylius\Component\Order\Model\OrderInterface as SyliusOrderInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

final class OrderAwareSubscriberTest extends WebTestCase
{
    private OrderAwareSubscriber $orderAwareSubscriber;
    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var EntityManagerInterface $entityManager */
        $entityManager = self::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        /** @var OrderRepositoryInterface $orderRepository */
        $orderRepository = $this->entityManager->getRepository(Order::class);

        $this->orderAwareSubscriber = new OrderAwareSubscriber($orderRepository);
    }

    public function provideCorrectOrderStates(): iterable
    {
        yield [SyliusOrderInterface::STATE_NEW];

        yield [SyliusOrderInterface::STATE_FULFILLED];
    }

    /**
     * @dataProvider provideCorrectOrderStates
     */
    public function testExecuteFindsOrderWithCorrectState(string $state): void
    {
        // Arrange
        $command = $this->createOrderAwareCommand();

        $order = $this->createOrder($state);

        $requestStub = $this->createStub(Request::class);
        $requestStub->method('get')
            ->willReturn($order->getTokenValue());

        // Act
        $this->orderAwareSubscriber->execute($requestStub, $command);

        // Assert
        self::assertInstanceOf(Order::class, $command->getOrder());
    }

    public function provideIncorrectOrderStates(): iterable
    {
        yield [SyliusOrderInterface::STATE_CART];

        yield [SyliusOrderInterface::STATE_CANCELLED];
    }

    /**
     * @dataProvider provideIncorrectOrderStates
     */
    public function testExecuteDoesNotFindOrderWithIncorrectState(string $state): void
    {
        // Arrange
        $command = $this->createOrderAwareCommand();

        $order = $this->createOrder($state);

        $requestStub = $this->createStub(Request::class);
        $requestStub->method('get')
            ->willReturn($order->getTokenValue());

        // Assert
        self::expectException(NotFoundHttpException::class);

        // Act
        $this->orderAwareSubscriber->execute($requestStub, $command);
    }

    private function createOrderAwareCommand(): OrderAwareInterface
    {
        return new class () implements OrderAwareInterface {
            private Order $order;

            public function getAllowedStates(): ?array
            {
                return [
                    SyliusOrderInterface::STATE_NEW,
                    SyliusOrderInterface::STATE_FULFILLED,
                ];
            }

            public function getOrder(): Order
            {
                return $this->order;
            }

            public function setOrder(Order $order): void
            {
                $this->order = $order;
            }

            public function getNotFoundMessage(): string
            {
                return 'not found!';
            }
        };
    }

    private function createOrder(string $state): Order
    {
        $order = new Order();
        $order->setCurrencyCode('EUR');
        $order->setLocaleCode('NL');
        $order->setState($state);

        $this->entityManager->persist($order);
        $this->entityManager->flush();

        return $order;
    }
}
