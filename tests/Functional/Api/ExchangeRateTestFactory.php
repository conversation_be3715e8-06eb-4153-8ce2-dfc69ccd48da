<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api;

use App\Entity\Currency\Currency;
use App\Entity\Currency\ExchangeRate;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Webmozart\Assert\Assert;

final class ExchangeRateTestFactory
{
    private readonly EntityManagerInterface $entityManager;

    public function __construct(
        readonly ContainerInterface $container,
    ) {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = $container->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;
    }

    public function createExchangeRate(
        string $targetCurrencyCode = ExchangeRate::BASE_FROM_CURRENCY_CODE,
        float $rate = 1.00,
    ): void {
        $currencyRepository = $this->entityManager->getRepository(Currency::class);

        $eurCurrency = $currencyRepository->findOneBy(['code' => ExchangeRate::BASE_FROM_CURRENCY_CODE]);
        Assert::isInstanceOf($eurCurrency, Currency::class);

        $targetCurrency = $currencyRepository->findOneBy(['code' => $targetCurrencyCode]);
        Assert::isInstanceOf($targetCurrency, Currency::class);

        $exchangeRate = new ExchangeRate();
        $exchangeRate->setSourceCurrency($eurCurrency);
        $exchangeRate->setTargetCurrency($targetCurrency);
        $exchangeRate->setRatio($rate);

        $this->entityManager->persist($exchangeRate);
        $this->entityManager->flush();
    }
}
