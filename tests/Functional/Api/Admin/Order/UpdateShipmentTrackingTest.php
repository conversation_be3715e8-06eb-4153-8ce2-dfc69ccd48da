<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Admin\Order;

use App\Entity\Addressing\Zone;
use App\Entity\Order\Order;
use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShippingMethod;
use App\Entity\Supplier\Supplier;
use App\StateMachine\OrderPrescriptionStates;
use App\StateMachine\OrderShippingStates;
use App\Tests\Functional\Api\CartTestFactory;
use Doctrine\ORM\EntityManagerInterface;
use Sylius\Component\Core\OrderCheckoutStates;
use Sylius\Component\Core\OrderPaymentStates;
use Sylius\Component\Order\Model\OrderInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpClient\MockHttpClient;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class UpdateShipmentTrackingTest extends WebTestCase
{
    private const string CHANNEL_CODE = 'dok_nl';

    private const string LOCALE_CODE = 'nl';

    private const string ORDER_NUMBER = '1234';

    private const string PRODUCT_CODE = 'viagra_test_special';

    private const string PRODUCT_VARIANT_CODE = 'viagra_25mg_4_test_special';

    private const string SHIPPING_METHOD_CODE = 'carrier_code';

    private const string TRACK_AND_TRACE_CODE = 'track-and-trace-code';

    private const string SHIPMENT_ENDPOINT_URI = '/api/notify/orders/%s/shipment/%s';

    private KernelBrowser $client;

    private CartTestFactory $cartTestFactory;

    private EntityManagerInterface $entityManager;

    private MockHttpClient $mockHttpClient;

    private Supplier $supplier;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();
        $this->client = static::createClient();
        /** @var EntityManagerInterface $entityManager */
        $entityManager = static::getContainer()->get('doctrine.orm.entity_manager');
        $this->entityManager = $entityManager;
        $this->cartTestFactory = new CartTestFactory(static::getContainer());
        /** @var MockHttpClient $mockHttpClient */
        $mockHttpClient = self::getContainer()->get(MockHttpClient::class);
        $this->mockHttpClient = $mockHttpClient;
        /** @var Supplier $supplier */
        $supplier = $this->entityManager->getRepository(Supplier::class)->findOneBy(
            ['identifier' => 'apotheek-bad-nieuweschans']
        );
        $this->supplier = $supplier;
    }

    protected function tearDown(): void
    {
        parent::tearDown();

        $this->cartTestFactory->teardown();
        $this->entityManager->close();
        unset($this->entityManager);
    }

    public function testItUpdatesShipmentTracking(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(self::CHANNEL_CODE, self::PRODUCT_CODE, [self::PRODUCT_VARIANT_CODE]);

        $order = $this->cartTestFactory->createCartWithProductVariants(self::CHANNEL_CODE, self::LOCALE_CODE, [self::PRODUCT_VARIANT_CODE]);
        $order->setNumber(self::ORDER_NUMBER);
        $order->setState(OrderInterface::STATE_NEW);
        $order->setCheckoutState(OrderCheckoutStates::STATE_COMPLETED);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);
        $order->setShippingState(OrderShippingStates::STATE_PENDING);
        $order->setBillingAddress($this->cartTestFactory->createAddress());

        $shippingMethod = $this->createShippingMethod();

        /** @var Shipment $shipment */
        $shipment = $order->getShipments()->first();
        $shipment->setMethod($shippingMethod);
        $shipment->setState(OrderShippingStates::STATE_PENDING);
        $shipment->setSupplier($this->supplier);
        $shipment->setSupplierShipmentReference('c3ff89b4-dcab-487c-80ab-4a5f1d4fa219');
        $shipment->setMethod($shippingMethod);

        $this->entityManager->persist($order);
        $this->entityManager->flush();

        $mockResponse = new MockResponse(
            file_get_contents(__DIR__.DIRECTORY_SEPARATOR.'../../Notify/NotifyOrderShipmentStatusController.orderShipment.shippedResponse.json')
        );
        $this->mockHttpClient->setResponseFactory($mockResponse);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::SHIPMENT_ENDPOINT_URI, $order->getTokenValue(), $shipment->getId()),
            [
                'uuid' => 'c3ff89b4-dcab-487c-80ab-4a5f1d4fa219',
                'status' => 'shipped',
            ],
            [
                'HTTP_AUTHORIZATION' => 'Bearer valid-token',
                'HTTP_SYLIUS_CHANNEL_CODE' => self::CHANNEL_CODE,
            ]
        );

        $responseBody = $this->client->getResponse()->getContent();

        static::assertResponseIsSuccessful();
        static::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        $shipment = $this->entityManager->find(Shipment::class, $shipment->getId());

        static::assertEquals(self::TRACK_AND_TRACE_CODE, $shipment->getTracking());

        $order = $this->entityManager->find(Order::class, $order->getId());

        self::assertCount(1, $order->getShipments());

        $shipment = $order->getShipments()->first();

        static::assertEquals(Shipment::STATE_SHIPPED, $shipment->getState());
    }

    public function testItDoesNotUpdateShippingMethodWhenMethodIsNotFound(): void
    {
        $this->cartTestFactory->createProductAndProductVariants(self::CHANNEL_CODE, self::PRODUCT_CODE, [self::PRODUCT_VARIANT_CODE]);

        $order = $this->cartTestFactory->createCartWithProductVariants(self::CHANNEL_CODE, self::LOCALE_CODE, [self::PRODUCT_VARIANT_CODE]);
        $order->setNumber(self::ORDER_NUMBER);
        $order->setState(OrderInterface::STATE_NEW);
        $order->setCheckoutState(OrderCheckoutStates::STATE_COMPLETED);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);
        $order->setShippingState(OrderShippingStates::STATE_PENDING);
        $order->setBillingAddress($this->cartTestFactory->createAddress());

        $shippingMethod = $this->createShippingMethod();

        /** @var Shipment $shipment */
        $shipment = $order->getShipments()->first();
        $shipment->setMethod($shippingMethod);
        $shipment->setState(OrderShippingStates::STATE_PENDING);
        $shipment->setSupplier($this->supplier);
        $shipment->setSupplierShipmentReference('c3ff89b4-dcab-487c-80ab-4a5f1d4fa219');
        $shipment->setMethod($shippingMethod);

        $currentShippingMethodCode = $shipment->getMethod()->getCode();

        $this->entityManager->flush();

        $mockResponse = new MockResponse(
            file_get_contents(__DIR__.DIRECTORY_SEPARATOR.'../../Notify/NotifyOrderShipmentStatusController.orderShipment.shippedResponseCarrierDoesNotExist.json')
        );
        $this->mockHttpClient->setResponseFactory($mockResponse);

        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::SHIPMENT_ENDPOINT_URI, $order->getTokenValue(), $shipment->getId()),
            [
                'uuid' => 'c3ff89b4-dcab-487c-80ab-4a5f1d4fa219',
                'status' => 'shipped',
            ],
            [
                'HTTP_AUTHORIZATION' => 'Bearer valid-token',
            ]
        );

        static::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        $this->entityManager->refresh($order);

        /** @var Shipment $firstShipment */
        $firstShipment = $order->getShipments()->first();

        $this->assertSame($currentShippingMethodCode, $firstShipment->getMethod()->getCode());
    }

    public function testItThrowsExceptionWhenOrderIsNotFound(): void
    {
        $this->client->jsonRequest(
            Request::METHOD_POST,
            sprintf(self::SHIPMENT_ENDPOINT_URI, 'does-not-exist', 1337),
            [
                'uuid' => 'c3ff89b4-dcab-487c-80ab-4a5f1d4fa219',
                'status' => 'shipped',
            ],
            [
                'HTTP_AUTHORIZATION' => 'Bearer valid-token',
            ]
        );

        $responseBody = $this->client->getResponse()->getContent();

        $errorMessage = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => Response::HTTP_NOT_FOUND,
            'detail' => 'The requested order could not be found.',
        ];

        static::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        static::assertJsonStringEqualsJsonString(json_encode($errorMessage), $responseBody);
    }

    private function createShippingMethod(): ShippingMethod
    {
        $shippingMethod = new ShippingMethod();
        $shippingMethod->setCode(self::SHIPPING_METHOD_CODE);
        $shippingMethod->setCalculator('flat_rate');
        $shippingMethod->setZone(
            $this->entityManager->getRepository(Zone::class)->findOneBy(['code' => 'WORLD'])
        );
        $shippingMethod->enable();
        $shippingMethod->setPosition(99);

        $this->entityManager->persist($shippingMethod);

        return $shippingMethod;
    }
}
