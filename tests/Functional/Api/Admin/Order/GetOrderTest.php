<?php

declare(strict_types=1);

namespace App\Tests\Functional\Api\Admin\Order;

use App\Entity\Addressing\Address;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\Order\Order;
use App\StateMachine\OrderPrescriptionStates;
use App\StateMachine\OrderShippingStates;
use App\Tests\Functional\Api\CartTestFactory;
use DateTime;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Functional test for {@see GetOrderController}.
 */
class GetOrderTest extends WebTestCase
{
    private const string API_ADMIN_ORDER_ENDPOINT = '/api/admin/orders/%s';

    private const string CHANNEL_CODE = 'dok_nl';
    private const string LOCALE_CODE = 'nl';
    private const string PRODUCT_CODE = 'viagra_test_special';
    private const string PRODUCT_VARIANT_CODE = 'viagra_25mg_4_test_special';

    private KernelBrowser $client;

    private CartTestFactory $cartTestFactory;

    private EntityManagerInterface $entityManager;

    protected function setUp(): void
    {
        parent::setUp();

        self::ensureKernelShutdown();
        $this->client = static::createClient();
        $this->cartTestFactory = new CartTestFactory(static::getContainer());
        $this->entityManager = static::getContainer()->get(EntityManagerInterface::class);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        $this->cartTestFactory->teardown();
    }

    public function testItReturnsOrderByTokenValue(): void
    {
        $order = $this->createOrder();

        $this->entityManager->flush();

        $tokenValue = $order->getTokenValue();
        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf(self::API_ADMIN_ORDER_ENDPOINT, $tokenValue),
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer valid-token',
            ]
        );

        self::assertResponseIsSuccessful();

        $responseBody = $this->client->getResponse()->getContent();
        $decodedResponseBody = json_decode($responseBody, true, 512, JSON_THROW_ON_ERROR);

        $this->assertSame($tokenValue, $decodedResponseBody['tokenValue']);
        $this->assertInstanceOf(DateTime::class, new DateTime($decodedResponseBody['prescriptionStateUpdatedAt']));
    }

    public function testItReturns401WithWrongCredentials(): void
    {
        $order = $this->createOrder();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf(self::API_ADMIN_ORDER_ENDPOINT, $order->getTokenValue()),
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer invalid-token',
            ]
        );

        $responseBody = $this->client->getResponse()->getContent();

        static::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function testItReturns404WithInvalidTokenValue(): void
    {
        $this->createOrder();

        $this->client->jsonRequest(
            Request::METHOD_GET,
            sprintf(self::API_ADMIN_ORDER_ENDPOINT, 'invalid-token-value'),
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer valid-token',
            ]
        );

        $responseBody = $this->client->getResponse()->getContent();

        static::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
        $expectedResponse = [
            'type' => 'about:blank',
            'title' => 'An error occurred.',
            'status' => Response::HTTP_NOT_FOUND,
            'detail' => 'Order with tokenValue: invalid-token-value not found',
        ];
        static::assertJsonStringEqualsJsonString(json_encode($expectedResponse), $responseBody);
    }

    private function createCustomer(): Customer
    {
        /** @var CustomerPool $customerPool */
        $customerPool = $this->entityManager
            ->getRepository(CustomerPool::class)
            ->findOneBy(['code' => 'dokteronline']);

        $customer = new Customer();
        $customer->setFirstName('Hans');
        $customer->setLastName('Test');
        $customer->setBirthday(new DateTimeImmutable('2000-01-01 00:00:00'));
        $customer->setEmail('test+'.hrtime(true).'@superbrave.nl');
        $customer->setCustomerPool($customerPool);

        return $customer;
    }

    private function createAddress(): Address
    {
        $address = new Address();
        $address->setFirstName('Hans');
        $address->setLastName('Test');
        $address->setStreet('Test street');
        $address->setPostcode('Test postcode');
        $address->setCity('Breda');
        $address->setCountryCode('NL');

        return $address;
    }

    private function createOrder(): Order
    {
        $this->cartTestFactory->createProductAndProductVariants(
            self::CHANNEL_CODE,
            self::PRODUCT_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );

        $order = $this->cartTestFactory->createCartWithProductVariants(
            self::CHANNEL_CODE,
            self::LOCALE_CODE,
            [self::PRODUCT_VARIANT_CODE]
        );
        $order->setState(Order::STATE_NEW);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_READY_FOR_CONSULT);
        $order->setShippingState(OrderShippingStates::STATE_AWAITING_PRESCRIPTION);
        $order->setCustomer($this->createCustomer());
        $order->setBillingAddress($this->createAddress());

        return $order;
    }
}
