[{"id": 34374, "translations": [{"language": {"iso639": "nl"}, "name": "Isotretinoïne 10 mg 30 caps.", "dosageText": "Neem 1 pil in per dag."}, {"language": {"iso639": "en"}, "name": "Isotretinoin 10 mg 30 caps.", "dosageText": null}, {"language": {"iso639": "de"}, "name": "Isotretinoin 10 mg 30 Kaps.", "dosageText": null}, {"language": {"iso639": "fr"}, "name": "Isotretinoïne 10 mg 30 caps.", "dosageText": null}, {"language": {"iso639": "es"}, "name": "Isotretinoína 10 mg 30 cáps.", "dosageText": null}, {"language": {"iso639": "sv"}, "name": "Isotretinoin 10 mg 30 kaps.", "dosageText": null}, {"language": {"iso639": "pl"}, "name": "Izotretynoina 10 mg 30 kaps.", "dosageText": null}, {"language": {"iso639": "da"}, "name": "Isotretinoin 10 mg 30 kaps.", "dosageText": null}, {"language": {"iso639": "pt"}, "name": "Isotretinoína 10 mg 30 cáps.", "dosageText": null}, {"language": {"iso639": "fi"}, "name": "Isotretinoin 10 mg 30 kaps.", "dosageText": null}, {"language": {"iso639": "fr"}, "name": "Isotretinoïne 10 mg 30 caps.", "dosageText": null}, {"language": {"iso639": "ro"}, "name": "Isotretinoin 10 mg 30 caps.", "dosageText": null}], "product": {"id": 3666}, "productType": {"name": "capsule"}, "prescription": true, "dosageAmount": 10, "dosage": {"name": "mg"}, "packagingAmount": 30, "packaging": {"name": "piece(s)"}, "zIndex": 0, "maxVariantsPerOrderLine": 4, "productVariantWarehouses": [{"id": 51076, "pharmacy": {"id": 48, "name": "Apotheek Bad Nieuweschans BV", "supplierIdentifier": "apotheek-bad-nieuweschans"}, "code": "N15205002", "name": "Isotretinoin 10mg 30caps", "price": "6.55", "enabled": true, "minimumAmount": 0, "shippingCountry": null, "quantityMultiplier": 1, "preferredSupplier": true}, {"id": 51432, "pharmacy": {"id": 48, "name": "Apotheek Bad Nieuweschans BV", "supplierIdentifier": "apotheek-bad-nieuweschans"}, "code": "13426769", "name": "Isotretinoin Basics 10mg", "price": "8.98", "enabled": true, "minimumAmount": 0, "shippingCountry": {"iso3166String": "DE"}, "quantityMultiplier": 1, "preferredSupplier": false}, {"id": 51432, "pharmacy": {"id": 48, "name": "Apotheek Bad Nieuweschans BV", "supplierIdentifier": "apotheek-bad-nieuweschans"}, "code": "13426769", "name": "Isotretinoin Basics 10mg", "price": "8.98", "enabled": false, "minimumAmount": 0, "shippingCountry": {"iso3166String": "BE"}, "quantityMultiplier": 1, "preferredSupplier": false}, {"id": 52934, "pharmacy": {"id": 49, "name": "NDSM Apotheek B.V.", "supplierIdentifier": "ndsm-a<PERSON><PERSON>k"}, "code": "", "name": "Isotretinoin 10mg 30caps", "price": "12.00", "enabled": true, "minimumAmount": 0, "shippingCountry": null, "quantityMultiplier": 1, "preferredSupplier": false}, {"id": 54402, "pharmacy": {"id": 44, "name": "Apotheek Culemborg BV", "supplierIdentifier": "apotheek-culemborg"}, "code": "", "name": "Isotretinoin 10mg 30caps", "price": "15.00", "enabled": true, "minimumAmount": 0, "shippingCountry": null, "quantityMultiplier": 1, "preferredSupplier": false}, {"id": 56816, "pharmacy": {"id": 50, "name": "Prime-Pharmacy", "supplierIdentifier": "prime-pharmacy"}, "code": "N15205002", "name": "Isotretinoin 10mg 30caps", "price": "7.23", "enabled": true, "minimumAmount": 0, "shippingCountry": null, "quantityMultiplier": 1, "preferredSupplier": false}, {"id": 57157, "pharmacy": {"id": 50, "name": "Prime-Pharmacy", "supplierIdentifier": "prime-pharmacy"}, "code": "13426769", "name": "Isotretinoin Basics 10mg", "price": "8.98", "enabled": true, "minimumAmount": 0, "shippingCountry": {"iso3166String": "DE"}, "quantityMultiplier": 1, "preferredSupplier": false}], "productVariantCountries": [{"id": 18396, "country": {"iso3166String": "BE"}, "enabled": true, "price": 6890, "previousPrice": 6890, "sale": false}, {"id": 27531, "country": {"iso3166String": "DE"}, "enabled": true, "price": 6890, "previousPrice": 6890, "sale": false}, {"id": 35722, "country": {"iso3166String": "AT"}, "enabled": true, "price": 6890, "previousPrice": 6890, "sale": false}, {"id": 43913, "country": {"iso3166String": "LU"}, "enabled": true, "price": 6890, "previousPrice": 6890, "sale": false}, {"id": 52104, "country": {"iso3166String": "CH"}, "enabled": true, "price": 6890, "previousPrice": 6890, "sale": false}, {"id": 58396, "country": {"iso3166String": "GB"}, "enabled": false, "price": 6890, "previousPrice": 6890, "sale": false}, {"id": 65692, "country": {"iso3166String": "FR"}, "enabled": true, "price": 7120, "previousPrice": 7120, "sale": false}, {"id": 72143, "country": {"iso3166String": "ES"}, "enabled": true, "price": 6850, "previousPrice": 6850, "sale": false}, {"id": 77339, "country": {"iso3166String": "SE"}, "enabled": true, "price": 5220, "previousPrice": 5220, "sale": false}, {"id": 80093, "country": {"iso3166String": "PL"}, "enabled": true, "price": 6790, "previousPrice": 6790, "sale": false}, {"id": 82634, "country": {"iso3166String": "DK"}, "enabled": true, "price": 6090, "previousPrice": 6090, "sale": false}, {"id": 83452, "country": {"iso3166String": "PT"}, "enabled": true, "price": 6850, "previousPrice": 6850, "sale": false}, {"id": 85115, "country": {"iso3166String": "NL"}, "enabled": true, "price": 6890, "previousPrice": 4590, "sale": false}, {"id": 97301, "country": {"iso3166String": "FI"}, "enabled": true, "price": 7190, "previousPrice": 5220, "sale": false}, {"id": 108086, "country": {"iso3166String": "RO"}, "enabled": false, "price": 0, "previousPrice": 0, "sale": false}], "code": "3666_34374"}]