<?php

declare(strict_types=1);

namespace App\Tests\Functional\Catalog;

use App\Tests\Functional\Api\AbstractWebTestCase;
use App\Tests\Functional\Mocks\MockHttpClient;

abstract class AbstractCatalogLoaderTestCase extends AbstractWebTestCase
{
    private MockHttpClient $mockHttpClient;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var MockHttpClient $mockHttpClient */
        $mockHttpClient = self::getContainer()->get(MockHttpClient::class);
        $this->mockHttpClient = $mockHttpClient;
    }

    protected function setMockResponse(string|false $mockResponse): void
    {
        $this->mockHttpClient->setMockResponse($mockResponse);
    }
}
