<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin;

use App\Entity\User\AdminUser;
use App\Security\Firewall;
use App\Security\Rbac\PermissionMapInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

abstract class AbstractSyliusAdminWebTestCase extends WebTestCase
{
    protected const string FIREWALL_CONTEXT = Firewall::SyliusAdmin->value;

    protected KernelBrowser $client;
    protected EntityManagerInterface $entityManager;
    protected PermissionMapInterface $permissionMap;

    protected function setUp(): void
    {
        self::ensureKernelShutdown();
        $this->client = self::createClient();

        $container = self::getContainer();

        /** @var EntityManagerInterface $entityManager */
        $entityManager = $container->get(EntityManagerInterface::class);
        $this->entityManager = $entityManager;

        /** @var PermissionMapInterface $permissionMap */
        $permissionMap = $container->get(PermissionMapInterface::class);
        $this->permissionMap = $permissionMap;
    }

    /**
     * @param array<string> $permissions
     */
    protected function getAdminUser(
        array $permissions = [],
        bool $enablePermissionChecker = true,
    ): AdminUser {
        $adminUser = new AdminUser();
        $adminUser->setUsername('admin');
        $adminUser->setPlainPassword('admin');
        $adminUser->addRole('ROLE_API_ACCESS');
        $adminUser->setEnabled(true);
        $adminUser->setLocaleCode('en');
        $adminUser->setEnablePermissionChecker($enablePermissionChecker);

        foreach ($permissions as $permissionRoute) {
            foreach ($this->permissionMap->getScopesFromRoutePermission($permissionRoute) as $scope) {
                $adminUser->addRole($scope->getAsRole());
            }
        }

        $this->entityManager->persist($adminUser);
        $this->entityManager->flush();

        return $adminUser;
    }
}
