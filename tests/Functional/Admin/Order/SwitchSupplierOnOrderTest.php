<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Order;

use App\Entity\Order\Order;
use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShipmentInterface;
use App\Entity\Supplier\SupplierInterface;
use App\StateMachine\OrderShippingStates;
use App\Supplier\Resolver\AlternativeSuppliersResolverInterface;
use App\Tests\Functional\Admin\AbstractSyliusAdminWebTestCase;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Mocks\CsrfTokenManagerMock;
use SM\Factory\Factory;
use Sylius\Component\Core\OrderCheckoutStates as BaseOrderCheckoutStates;
use Sylius\Component\Order\OrderTransitions;
use Symfony\Component\HttpFoundation\RequestStack;

final class SwitchSupplierOnOrderTest extends AbstractSyliusAdminWebTestCase
{
    private const string PRODUCT_CODE = 'viagra_test_special';
    private const string PRODUCT_VARIANT_CODE_1 = 'viagra_25mg_bad_nieuwe_schans';
    private const string PRODUCT_VARIANT_CODE_2 = 'viagra_25mg_culemborg';
    private const string PRODUCT_VARIANT_CODE_3 = 'viagra_25mg_ndsm';

    private const string SWITCH_SUPPLIER_ENDPOINT_URI = '/admin/orders/%s/supplier/switch';
    private const string APOTHEEK_CULEMBORG = 'apotheek-culemborg';
    private const string APOTHEEK_BAD_NIEUWESCHANS = 'apotheek-bad-nieuweschans';
    private const string APOTHEEK_NDSM = 'apotheek-ndsm';

    private CartTestFactory $cartTestFactory;
    private RequestStack $requestStack;
    private AlternativeSuppliersResolverInterface $alternativeSuppliersResolver;
    private Factory $stateMachineFactory;

    public function setUp(): void
    {
        parent::setUp();

        $container = self::getContainer();
        $this->cartTestFactory = new CartTestFactory($container);
        $this->requestStack = $container->get(RequestStack::class);
        $this->alternativeSuppliersResolver = $container->get(AlternativeSuppliersResolverInterface::class);
        $this->stateMachineFactory = $container->get(Factory::class);
    }

    public function testItCannotChangeSupplierOnCart(): void
    {
        $admin = $this->getAdminUser(['app_admin_order_switch_supplier', 'sylius_admin_order_update_supplier']);
        $order = $this->createOrder();
        $suppliers = $this->alternativeSuppliersResolver->resolve($order);

        $this->client
            ->loginUser($admin, self::FIREWALL_CONTEXT)
            ->request(
                'POST',
                sprintf(self::SWITCH_SUPPLIER_ENDPOINT_URI, $order->getId()),
                [
                    'order_switch_supplier' => [
                        'supplier' => array_key_first($suppliers),
                        '_token' => CsrfTokenManagerMock::TOKEN,
                    ],
                ]
            );

        self::assertResponseStatusCodeSame(404);
    }

    public function testItCannotChangeWrongSupplierOnCart(): void
    {
        $this->getAdminUser();
        $order = $this->createOrder(Order::STATE_NEW);

        $this->client->request(
            'POST',
            sprintf(self::SWITCH_SUPPLIER_ENDPOINT_URI, $order->getId()),
            ['order_switch_supplier' => ['_token' => CsrfTokenManagerMock::TOKEN, 'supplier' => '5']]
        );

        /** @var Shipment $firstShipment */
        $firstShipment = $order->getShipments()->first();
        self::assertSame(
            self::APOTHEEK_BAD_NIEUWESCHANS,
            $firstShipment->getSupplier()?->getIdentifier()
        );
    }

    /**
     * @dataProvider wrongOrderStateProvider
     */
    public function testItCannotChangeSupplierOnInvalidOrder(
        string $state,
        string $shippingState,
    ): void {
        $this->getAdminUser();
        $order = $this->createOrder($state, $shippingState);

        $suppliers = $this->alternativeSuppliersResolver->resolve($order);

        $this->client->request(
            'POST',
            sprintf(self::SWITCH_SUPPLIER_ENDPOINT_URI, $order->getId()),
            [
                'order_switch_supplier' => [
                    '_token' => CsrfTokenManagerMock::TOKEN,
                    'supplier' => array_key_first($suppliers),
                ],
            ]
        );

        $this->entityManager->refresh($order);

        /** @var ShipmentInterface $shipment */
        $shipment = $order->getShipments()->first();
        $supplier = $shipment->getSupplier();

        self::assertResponseStatusCodeSame(302);
        self::assertInstanceOf(SupplierInterface::class, $supplier);
        self::assertSame(self::APOTHEEK_BAD_NIEUWESCHANS, $supplier->getIdentifier());
    }

    public function wrongOrderStateProvider(): iterable
    {
        yield 'Order with Shipping state returned' => [
            Order::STATE_NEW,
            OrderShippingStates::STATE_RETURNED,
        ];
        yield 'Order with Shipping state pending' => [
            Order::STATE_NEW,
            OrderShippingStates::STATE_PENDING,
        ];
        yield 'Order with Shipping state processing' => [
            Order::STATE_NEW,
            OrderShippingStates::STATE_PROCESSING,
        ];
        yield 'Order with Shipping state shipped' => [
            Order::STATE_NEW,
            OrderShippingStates::STATE_SHIPPED,
        ];
    }

    public function testItCannotChangeSupplierWithoutPermission(): void
    {
        $admin = $this->getAdminUser();
        $order = $this->createOrder(Order::STATE_NEW);

        $this->client
            ->loginUser($admin, self::FIREWALL_CONTEXT)
            ->request(
                'POST',
                sprintf(self::SWITCH_SUPPLIER_ENDPOINT_URI, $order->getId()),
                ['order_switch_supplier' => ['_token' => CsrfTokenManagerMock::TOKEN, 'supplier' => '3']]
            );

        self::assertResponseStatusCodeSame(403);
    }

    private function createOrder(
        string $state = Order::STATE_CART,
        string $shippingState = OrderShippingStates::STATE_AWAITING_PAYMENT,
    ): Order {
        $productData = [
            [
                'productCode' => self::PRODUCT_CODE,
                'productVariantCodes' => [self::PRODUCT_VARIANT_CODE_1],
                'supplierIdentifier' => self::APOTHEEK_BAD_NIEUWESCHANS,
                'price' => 1000,
            ],
            [
                'productCode' => self::PRODUCT_CODE,
                'productVariantCodes' => [self::PRODUCT_VARIANT_CODE_2],
                'supplierIdentifier' => self::APOTHEEK_CULEMBORG,
                'price' => 1500,
            ],
            [
                'productCode' => self::PRODUCT_CODE,
                'productVariantCodes' => [self::PRODUCT_VARIANT_CODE_3],
                'supplierIdentifier' => self::APOTHEEK_NDSM,
                'price' => 1500,
            ],
        ];

        foreach ($productData as $data) {
            $this->cartTestFactory->createProductAndProductVariants(
                productCode: $data['productCode'],
                productVariantCodes: $data['productVariantCodes'],
                supplierIdentifier: $data['supplierIdentifier'],
                price: $data['price'],
            );
        }

        $order = $this->cartTestFactory->createCartWithProductVariants(
            productVariantCodes: [self::PRODUCT_VARIANT_CODE_1],
        );

        if ($state === Order::STATE_CART) {
            return $order;
        }

        $customer = $this->cartTestFactory->createCustomer();
        $billingAddress = $this->cartTestFactory->createAddress();
        $shippingAddress = $this->cartTestFactory->createAddress();
        $order->setCustomer($customer);
        $order->setBillingAddress($billingAddress);
        $order->setShippingAddress($shippingAddress);
        $order->completeCheckout();
        $order->setCheckoutState(BaseOrderCheckoutStates::STATE_COMPLETED);
        $order->setShippingState($shippingState);
        $order->setNumber('000001');

        $this->entityManager->flush();

        $stateMachine = $this->stateMachineFactory->get($order, OrderTransitions::GRAPH);
        $stateMachine->apply(OrderTransitions::TRANSITION_CREATE);

        return $order;
    }
}
