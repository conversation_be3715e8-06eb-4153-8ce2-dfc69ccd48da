<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Order;

use App\Entity\Order\Order;
use App\Entity\Payment\Payment;
use App\Entity\Payment\PaymentInterface;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariant;
use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShipmentInterface;
use App\StateMachine\OrderPrescriptionStates;
use App\StateMachine\OrderShippingStates;
use App\Tests\Functional\Admin\AbstractSyliusAdminWebTestCase;
use App\Tests\Functional\Api\CartTestFactory;
use App\Tests\Mocks\CsrfTokenManagerMock;
use Sylius\Component\Core\OrderCheckoutStates as SyliusOrderCheckoutStates;
use Sylius\Component\Core\OrderPaymentStates as SyliusOrderPaymentStates;
use Sylius\Component\Order\Model\OrderInterface as SyliusOrderInterface;
use Sylius\Component\Payment\Model\PaymentInterface as SyliusPaymentInterface;
use Symfony\Component\HttpFoundation\Request;

final class UpdateOrderItemsTest extends AbstractSyliusAdminWebTestCase
{
    private const string API_ENDPOINT_URL = '/admin/orders/{id}/items';

    public function testItCanUpdateOrderItems(): void
    {
        // Arrange
        $adminUser = $this->getAdminUser(['app_admin_order_items_update', 'sylius_admin_order_items_update']);

        $order = $this->createOrder();

        $serviceProductId = $this->entityManager->getRepository(ProductVariant::class)->findOneBy(
            ['code' => 'service_reship_1000']
        )?->getId();

        // Act
        $this->client
            ->loginUser($adminUser, self::FIREWALL_CONTEXT)
            ->request(
                Request::METHOD_POST,
                strtr(self::API_ENDPOINT_URL, [
                    '{id}' => $order->getId(),
                ]),
                [
                    'service_product_variant' => [
                        '_token' => CsrfTokenManagerMock::TOKEN,
                        'serviceProductVariant' => $serviceProductId,
                    ],
                ],
            );

        self::assertResponseStatusCodeSame(302);

        // Assert
        self::assertSame(
            SyliusOrderPaymentStates::STATE_PARTIALLY_PAID,
            $order->getPaymentState(),
            'The order payment state should be partially paid.'
        );
        self::assertInstanceOf(
            PaymentInterface::class,
            $order->getLastPayment(SyliusPaymentInterface::STATE_NEW),
            'The order should have had an new payment added.'
        );
        self::assertSame(
            'service_reship_1000',
            $order->getItems()->get(1)?->getVariant()?->getCode(),
            'The service product should have been added.'
        );
    }

    private function createOrder(): Order
    {
        $cartTestFactory = new CartTestFactory(self::getContainer());

        $cartTestFactory->createProductAndProductVariants(
            'dok_de',
            '2345',
            [
                '2345_26657_ndsm_apotheek_worldwide',
            ],
            'ndsm-apotheek',
            productType: ProductType::MEDICATION,
        );

        $cartTestFactory->createProductAndProductVariants(
            'dok_de',
            'service_reship',
            [
                'service_reship_1000',
            ],
            productType: ProductType::SERVICE,
        );

        $order = $cartTestFactory->createCartWithProductVariants(
            'dok_de',
            'de',
            [
                '2345_26657_ndsm_apotheek_worldwide',
            ],
        );

        // Force states
        $order->setState(SyliusOrderInterface::STATE_FULFILLED);
        $order->setPaymentState(SyliusOrderPaymentStates::STATE_PAID);
        $order->setShippingState(OrderShippingStates::STATE_RETURNED);
        $order->setCheckoutState(SyliusOrderCheckoutStates::STATE_COMPLETED);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);

        /** @var Payment $firstPayment */
        $firstPayment = $order->getPayments()->first();
        $firstPayment->setState(SyliusPaymentInterface::STATE_COMPLETED);

        /** @var Shipment $firstShipment */
        $firstShipment = $order->getShipments()->first();
        $firstShipment->setState(ShipmentInterface::STATE_RETURNED);

        // Force required properties
        $order->setNumber('001');

        $this->entityManager->flush();

        return $order;
    }
}
