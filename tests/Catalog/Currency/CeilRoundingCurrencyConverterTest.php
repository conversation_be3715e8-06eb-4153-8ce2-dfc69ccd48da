<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Currency;

use App\Catalog\Currency\CeilRoundingCurrencyConverter;
use App\Entity\Channel\Channel;
use App\Entity\Currency\Currency;
use InvalidArgumentException;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Currency\Converter\CurrencyConverterInterface;

final class CeilRoundingCurrencyConverterTest extends TestCase
{
    private CurrencyConverterInterface&MockObject $baseCurrencyConverter;

    private CeilRoundingCurrencyConverter $currencyConverter;

    protected function setUp(): void
    {
        $this->baseCurrencyConverter = $this->createMock(CurrencyConverterInterface::class);

        $this->currencyConverter = new CeilRoundingCurrencyConverter($this->baseCurrencyConverter);
    }

    public function testCanProvideSourceAndTargetCurrencyCodesToBaseConverter(): void
    {
        $this->baseCurrencyConverter->expects($this->once())
            ->method('convert')
            ->with(100, 'EUR', 'GBP')
            ->willReturnArgument(0);

        $this->currencyConverter->convert(100, 'EUR', 'GBP');
    }

    public function testBaseCurrencyDoesntGetConverted(): void
    {
        $this->baseCurrencyConverter->expects($this->never())
            ->method('convert');

        $this->currencyConverter->convert(100, 'EUR', 'EUR');
    }

    /**
     * @dataProvider provideRoundingCases
     */
    public function testCanRoundFractionsUpToHundredsOfCents(
        int $convertedValue,
        string $fromCurrencyCode,
        int $expectedRoundedValue,
        string $toCurrencyCode,
    ): void {
        $this->baseCurrencyConverter->method('convert')
            ->willReturn($convertedValue);

        $this->assertSame(
            $expectedRoundedValue,
            $this->currencyConverter->convert($convertedValue, $fromCurrencyCode, $toCurrencyCode)
        );
    }

    /**
     * @return iterable<string, array{
     *     0: int,
     *     1: string,
     *     2: int,
     *     3: string,
     * }>
     */
    public function provideRoundingCases(): iterable
    {
        yield '1,01 EUR to GBP should round up to 2,00 GBP' => [101, 'EUR', 200, 'GBP'];
        yield '1,01 EUR to EUR should not round up and stay 1,01 EUR' => [101, 'EUR', 101, 'EUR'];

        yield '1,53 EUR to GBP should round up to 2,00 GBP' => [153, 'EUR', 200, 'GBP'];
        yield '1,53 EUR to EUR should not round up and stay 1,53 EUR' => [153, 'EUR', 153, 'EUR'];

        yield '100,50 EUR to GBP should round up to 101,00 GBP' => [10050, 'EUR', 10100, 'GBP'];
        yield '100,50 EUR to EUR should not round up and stay 100,50 EUR' => [10050, 'EUR', 10050, 'EUR'];

        yield '100,00 EUR to GBP should round up to 100,00 GBP' => [10000, 'EUR', 10000, 'GBP'];
        yield '100,00 EUR to EUR should not round up and stay 100,00 EUR' => [10000, 'EUR', 10000, 'EUR'];
    }

    public function testCanConvertCurrencyForChannel(): void
    {
        $currency = new Currency();
        $currency->setCode('GBP');

        $channel = new Channel();
        $channel->setCode('dok_nl');
        $channel->setBaseCurrency($currency);

        $this->baseCurrencyConverter->expects($this->once())
            ->method('convert')
            ->with(100, 'EUR', 'GBP')
            ->willReturnArgument(0);

        $this->currencyConverter->convertForChannel(100, 'EUR', $channel);
    }

    public function testCanNotConvertCurrencyForChannelWhenDefaultCurrencyIsNotSet(): void
    {
        $channel = new Channel();
        $channel->setCode('dok_nl');

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Provided channel "dok_nl" does not have a base currency.');

        $this->currencyConverter->convertForChannel(100, 'EUR', $channel);
    }
}
