<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Processor\ProductVariant;

use App\Catalog\Currency\ChannelCurrencyConverterInterface;
use App\Catalog\Message\ChannelPricingCollection;
use App\Catalog\Processor\ProductVariant\ChannelPricingProcessor;
use App\Entity\Channel\Channel;
use App\Entity\Channel\ChannelPricing;
use App\Entity\Product\ProductVariant;
use App\Entity\Supplier\Supplier;
use App\Event\Product\PreUpdateEvent;
use App\Repository\ChannelRepositoryInterface;
use App\Tests\Util\Factory\Catalog\Messenger\ChannelPricingFactory;
use App\Tests\Util\Factory\Catalog\Messenger\UpsertProductVariantFactory;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\ChannelPricingFactory as ChannelPricingEntityFactory;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Resource\Factory\FactoryInterface;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;

final class AddChannelPricingTest extends TestCase
{
    private ChannelRepositoryInterface&MockObject $channelRepository;
    private EntityManagerInterface&MockObject $entityManager;
    private ChannelPricingProcessor $addChannelPricing;
    private FactoryInterface&MockObject $channelPricingFactory;
    private EventDispatcherInterface&MockObject $eventDispatcher;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->channelRepository = $this->createMock(ChannelRepositoryInterface::class);
        $this->channelPricingFactory = $this->createMock(FactoryInterface::class);
        $this->eventDispatcher = $this->createMock(EventDispatcherInterface::class);

        $this->addChannelPricing = new ChannelPricingProcessor(
            $this->createMock(ChannelCurrencyConverterInterface::class),
            $this->channelRepository,
            $this->channelPricingFactory,
            $this->entityManager,
            $this->eventDispatcher
        );
    }

    public function testExecutesWithExistingChannelPricing(): void
    {
        // Arrange
        $upsertProductVariant = UpsertProductVariantFactory::create();
        $productVariant = $this->createMock(ProductVariant::class);

        $productVariant->method('getChannelPricingForChannel')->willReturn(ChannelPricingEntityFactory::create());
        $this->channelRepository->method('findOneByCode')->willReturn(ChannelFactory::create());
        $this->channelPricingFactory->method('createNew')->willReturn(ChannelFactory::create());

        // Assert
        $this->entityManager->expects($this->never())->method('persist');

        // Act
        $this->addChannelPricing->execute($upsertProductVariant, $productVariant);
    }

    public function testExecutesWithNewChannelPricing(): void
    {
        // Arrange
        $upsertProductVariant = UpsertProductVariantFactory::create(
            channelPricing: new ChannelPricingCollection(ChannelPricingFactory::create())
        );
        $productVariant = $this->createMock(ProductVariant::class);

        $productVariant->method('getChannelPricingForChannel')->willReturn(null);
        $productVariant->method('getSupplier')->willReturn($this->createStub(Supplier::class));
        $this->channelRepository->method('findOneByCode')->willReturn(ChannelFactory::create());
        $this->channelPricingFactory->method('createNew')->willReturn(ChannelPricingEntityFactory::create());

        // Assert
        $this->entityManager->expects($this->once())->method('persist');

        $this->eventDispatcher->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(PreUpdateEvent::class));

        // Act
        $this->addChannelPricing->execute($upsertProductVariant, $productVariant);
    }

    public function testDisablesUnsyncedChannelPricing(): void
    {
        // Arrange
        $upsertProductVariant = UpsertProductVariantFactory::create();
        $productVariant = $this->createMock(ProductVariant::class);
        $channelPricing = $this->createMock(ChannelPricing::class);

        $productVariant->method('getChannelPricingForChannel')->willReturn(ChannelPricingEntityFactory::create());
        $productVariant->method('getChannelPricings')->willReturn(new ArrayCollection([$channelPricing]));
        $this->channelRepository->method('findOneByCode')->willReturn(ChannelFactory::create());

        // Assert
        $channelPricing->expects($this->once())->method('setEnabled')->with(false);

        $this->eventDispatcher->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(PreUpdateEvent::class));

        // Act
        $this->addChannelPricing->execute($upsertProductVariant, $productVariant);
    }

    public function testDoesNotDisableSyncedChannelPricing(): void
    {
        // Arrange
        $upsertProductVariant = UpsertProductVariantFactory::create(
            channelPricing: new ChannelPricingCollection(ChannelPricingFactory::create(channelCode: 'test-code'))
        );
        $productVariant = $this->createMock(ProductVariant::class);
        $channelPricing = $this->createMock(ChannelPricing::class);

        $productVariant->method('getChannelPricingForChannel')->willReturn(ChannelPricingEntityFactory::create());
        $this->channelRepository->method('findOneByCode')->willReturn(ChannelFactory::create());
        $channelPricing->method('getChannelCode')->willReturn('test-code');
        $productVariant->method('getChannelPricings')->willReturn(new ArrayCollection([$channelPricing]));
        $productVariant->method('getSupplier')->willReturn($this->createStub(Supplier::class));

        // Assert
        $channelPricing->expects($this->never())->method('setEnabled');

        $this->eventDispatcher->expects($this->once())
            ->method('dispatch')
            ->with($this->isInstanceOf(PreUpdateEvent::class));

        // Act
        $this->addChannelPricing->execute($upsertProductVariant, $productVariant);
    }

    public function testItSkipsUnknownChannels(): void
    {
        // Arrange
        $upsertProductVariant = UpsertProductVariantFactory::create(
            channelPricing: new ChannelPricingCollection(ChannelPricingFactory::create())
        );
        $productVariant = $this->createMock(ProductVariant::class);

        $this->channelRepository
            ->method('findOneByCode')
            ->willReturn(null);

        // Assert
        $this->entityManager->expects(self::never())
            ->method('persist');

        $this->eventDispatcher->expects(self::never())
            ->method('dispatch');
        // Act
        $this->addChannelPricing->execute($upsertProductVariant, $productVariant);
    }

    /**
     * @return iterable<string, array{bool, bool}>
     */
    public function provideSupplierCountryShippingData(): iterable
    {
        yield 'SupplierCountryShipping is enabled.' => [true, true];

        yield 'SupplierCountryShipping is disabled.' => [false, false];
    }

    /**
     * @dataProvider provideSupplierCountryShippingData
     */
    public function testItChecksSupplierCountryShipping(
        bool $supplierCountryShippingEnabled,
        bool $expectedChannelPricingEnabled,
    ): void {
        // Arrange
        $upsertChannelPricingFactory = ChannelPricingFactory::create();
        $upsertProductVariant = UpsertProductVariantFactory::create(
            channelPricing: new ChannelPricingCollection($upsertChannelPricingFactory)
        );

        $channelStub = $this->createStub(Channel::class);
        $this->channelRepository->method('findOneByCode')->willReturn($channelStub);

        $channelPricing = new ChannelPricing();
        $channelPricing->setEnabled(!$expectedChannelPricingEnabled);

        $supplierStub = $this->createStub(Supplier::class);
        $supplierStub->method('isEnabledForChannel')->willReturn($supplierCountryShippingEnabled);

        $productVariantStub = $this->createStub(ProductVariant::class);
        $productVariantStub->method('getChannelPricingForChannel')->willReturn($channelPricing);
        $productVariantStub->method('getSupplier')->willReturn($supplierStub);

        // Act
        $this->addChannelPricing->execute($upsertProductVariant, $productVariantStub);

        // Assert
        self::assertSame($expectedChannelPricingEnabled, $channelPricing->isEnabled());
    }
}
