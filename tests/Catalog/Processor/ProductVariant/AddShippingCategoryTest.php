<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Processor\ProductVariant;

use App\Catalog\Processor\ProductVariant\ShippingCategoryProcessor;
use App\Entity\Product\ProductVariantInterface;
use App\Entity\Shipping\ShippingCategory;
use App\Repository\ShippingCategoryRepositoryInterface;
use App\Resolver\ShippingCategoryResolverInterface;
use App\Tests\Util\Factory\Catalog\Messenger\UpsertProductVariantFactory;
use App\Tests\Util\Factory\ProductVariantFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class AddShippingCategoryTest extends TestCase
{
    private ShippingCategoryProcessor $addShippingCategory;
    private ShippingCategoryResolverInterface&MockObject $shippingCategoryResolver;
    private ShippingCategoryRepositoryInterface&MockObject $shippingCategoryRepository;

    protected function setUp(): void
    {
        $this->shippingCategoryResolver = $this->createMock(ShippingCategoryResolverInterface::class);
        $this->shippingCategoryRepository = $this->createMock(ShippingCategoryRepositoryInterface::class);
        $this->addShippingCategory = new ShippingCategoryProcessor(
            $this->shippingCategoryResolver,
            $this->shippingCategoryRepository
        );
    }

    public function testExecuteWithExistingShippingCategory(): void
    {
        // Arrange
        $upsertProductVariant = UpsertProductVariantFactory::create();
        $productVariant = ProductVariantFactory::create();
        $shippingCategory = $this->createMock(ShippingCategory::class);

        $this->shippingCategoryResolver->method('resolveCodeByProductVariant')->willReturn('code');
        $this->shippingCategoryRepository->method('findOneByCode')->willReturn($shippingCategory);

        // Act
        $this->addShippingCategory->execute($upsertProductVariant, $productVariant);

        // Assert
        $this->assertSame($shippingCategory, $productVariant->getShippingCategory());
    }

    public function testExecuteWithNonExistingShippingCategory(): void
    {
        // Arrange
        $upsertProductVariant = UpsertProductVariantFactory::create();
        $productVariant = $this->createMock(ProductVariantInterface::class);

        $this->shippingCategoryResolver->method('resolveCodeByProductVariant')->willReturn('code');
        $this->shippingCategoryRepository->method('findOneByCode')->willReturn(null);

        // Act
        $this->addShippingCategory->execute($upsertProductVariant, $productVariant);

        // Assert
        $this->assertNull($productVariant->getShippingCategory());
    }
}
