<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Processor\ProductVariant;

use App\Catalog\Processor\ProductVariant\ProductOptionProcessor;
use App\Catalog\ProductOptionBuilderInterface;
use App\Entity\Product\ProductInterface;
use App\Entity\Product\ProductVariantInterface;
use App\Tests\Util\Factory\Catalog\Messenger\UpsertProductVariantFactory;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Product\Model\ProductOptionInterface;
use Sylius\Component\Product\Model\ProductOptionValueInterface;

final class ProductOptionProcessorTest extends TestCase
{
    private ProductOptionProcessor $addProductOptions;

    protected function setUp(): void
    {
        $this->addProductOptions = new ProductOptionProcessor($this->createMock(ProductOptionBuilderInterface::class));
    }

    public function testExecuteWithExistingProductOptions(): void
    {
        // Arrange
        $upsertProductVariant = UpsertProductVariantFactory::create();

        $optionValue = $this->createMock(ProductOptionValueInterface::class);
        $optionValues = new ArrayCollection([$optionValue]);

        $option = $this->createMock(ProductOptionInterface::class);
        $options = new ArrayCollection([$option]);

        $product = $this->createMock(ProductInterface::class);
        $product->method('getOptions')->willReturn($options);

        $productVariant = $this->createMock(ProductVariantInterface::class);
        $productVariant->method('getProduct')->willReturn($product);
        $productVariant->method('getOptionValues')->willReturn($optionValues);

        // Assert
        $productVariant->expects($this->once())->method('removeOptionValue');

        // Act
        $this->addProductOptions->execute($upsertProductVariant, $productVariant);

        // Assert
        $this->assertInstanceOf(ArrayCollection::class, $productVariant->getOptionValues());
        $this->assertInstanceOf(ArrayCollection::class, $product->getOptions());
    }

    public function testExecuteWithNewProductOptions(): void
    {
        // Arrange
        $upsertProductVariant = UpsertProductVariantFactory::create();

        $optionValue = $this->createMock(ProductOptionValueInterface::class);
        $optionValues = new ArrayCollection([$optionValue]);

        $optionMock = $this->createMock(ProductOptionInterface::class);
        $options = new ArrayCollection([$optionMock]);

        $productMock = $this->createMock(ProductInterface::class);
        $productMock->method('getOptions')->willReturn($options);

        $productVariantMock = $this->createMock(ProductVariantInterface::class);
        $productVariantMock->method('getProduct')->willReturn($productMock);
        $productVariantMock->method('getOptionValues')->willReturn($optionValues);

        // Assert: exactly 3 times: form, dosage, packsize.
        $productVariantMock->expects($this->exactly(3))->method('addOptionValue');

        // Act
        $this->addProductOptions->execute($upsertProductVariant, $productVariantMock);

        // Assert
        $this->assertInstanceOf(ArrayCollection::class, $productVariantMock->getOptionValues());
        $this->assertInstanceOf(ArrayCollection::class, $productMock->getOptions());
    }

    public function testExecuteWithNoProductOptions(): void
    {
        // Arrange
        $upsertProductVariant = UpsertProductVariantFactory::create(productOptions: null);

        $optionValue = $this->createMock(ProductOptionValueInterface::class);
        $optionValues = new ArrayCollection([$optionValue]);

        $productMock = $this->createMock(ProductInterface::class);

        $productVariantMock = $this->createMock(ProductVariantInterface::class);
        $productVariantMock->method('getProduct')->willReturn($productMock);
        $productVariantMock->method('getOptionValues')->willReturn($optionValues);

        // Assert
        $productVariantMock->expects(self::never())->method('addOptionValue');
        $productVariantMock->expects(self::once())->method('removeOptionValue')->with($optionValue);
        $productMock->expects(self::never())->method('addOption');
        $productMock->expects(self::never())->method('removeOption');

        // Act
        $this->addProductOptions->execute($upsertProductVariant, $productVariantMock);
    }
}
