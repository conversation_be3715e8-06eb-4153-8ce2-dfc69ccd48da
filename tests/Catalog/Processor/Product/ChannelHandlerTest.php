<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Processor\Product;

use App\Catalog\Message\AttributeCollection;
use App\Catalog\Message\ChannelCollection;
use App\Catalog\Message\ConsultProductCollection;
use App\Catalog\Message\ImageCollection;
use App\Catalog\Message\Product\UpsertProduct;
use App\Catalog\Message\ProductTypeAttribute;
use App\Catalog\Message\TaxonCollection;
use App\Catalog\Message\TranslationCollection;
use App\Catalog\Processor\Product\ChannelProcessor;
use App\Entity\Product\Product;
use App\Entity\Product\ProductType;
use App\Repository\ChannelRepositoryInterface;
use App\Tests\Util\Factory\ChannelFactory;
use PHPUnit\Framework\TestCase;

final class ChannelHandlerTest extends TestCase
{
    private const array CHANNEL_CODES = [
        'dok_ch',
        'dok_de',
        'dok_gb',
        'dok_nl',
    ];

    private ChannelProcessor $handler;
    private array $channels = [];

    protected function setUp(): void
    {
        parent::setUp();

        $channelRepository = $this->createMock(ChannelRepositoryInterface::class);
        $channelRepository
            ->expects(self::once())
            ->method('findAllIndexedByCode')
            ->willReturn($this->getChannels());

        $this->handler = new ChannelProcessor($channelRepository);
    }

    public function testCanUpdateProductChannels(): void
    {
        // Arrange
        $upsertProduct = $this->getUpsertProduct();
        $product = new Product();
        $product->addChannel($this->getChannels()['dok_ch']);
        $product->addChannel($this->getChannels()['dok_gb']);

        // Act
        $this->handler->execute($upsertProduct, $product);

        // Assert
        self::assertCount(3, $product->getChannels());
        foreach ($upsertProduct->getChannels() as $channelCode) {
            self::assertSame($this->getChannels()[$channelCode], $product->getChannelByCode($channelCode));
        }
    }

    private function getUpsertProduct(): UpsertProduct
    {
        return new UpsertProduct(
            code: '7',
            enabled: true,
            taxons: new TaxonCollection(),
            consultProducts: new ConsultProductCollection(),
            translations: new TranslationCollection(),
            images: new ImageCollection(),
            channels: new ChannelCollection('dok_nl', 'dok_gb', 'dok_de'),
            attributes: new AttributeCollection(ProductTypeAttribute::create(ProductType::MEDICATION))
        );
    }

    private function getChannels(): array
    {
        if (empty($this->channels)) {
            $this->channels = [];
            foreach (self::CHANNEL_CODES as $code) {
                $this->channels[$code] = ChannelFactory::createPrefilled(['code' => $code]);
            }
        }

        return $this->channels;
    }
}
