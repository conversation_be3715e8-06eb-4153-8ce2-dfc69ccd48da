<?php

declare(strict_types=1);

namespace App\Tests\Catalog;

use App\Catalog\CodeFactory;
use PHPUnit\Framework\TestCase;
use Webmozart\Assert\InvalidArgumentException;

class CodeFactoryTest extends TestCase
{
    public function testItJoinsStringsWithUnderscores(): void
    {
        self::assertSame(
            'joined_with_underscores',
            CodeFactory::create('joined', 'with', 'underscores')
        );
    }

    public function testItRemovesAllNonAlphaNumericalCharacters(): void
    {
        self::expectException(InvalidArgumentException::class);
        CodeFactory::create('!@#$^&*()\/+~');

        self::assertSame(
            'a',
            CodeFactory::create('!@#$^&*()a\/+~')
        );
    }

    public function testItReplacesPercentsAndDots(): void
    {
        self::assertSame(
            'this_percent_sign_and_these_dotdot_are_replaced',
            CodeFactory::create('this_%_sign_and_these_.._are_replaced')
        );
    }

    public function testItMakesAllLettersLowerCase(): void
    {
        self::assertSame('capitalletters', CodeFactory::create('CAPITALLETTERS'));
    }

    public function testItDoesNotReplaceReplacedLetters(): void
    {
        $expected = 'thisisapercentsignandthesearedotsdotdotdot';
        $replaced = CodeFactory::create('This is a % sign and these are dots...');

        self::assertSame($expected, $replaced);
        self::assertSame($expected, CodeFactory::create($replaced));
    }
}
