<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Loader\Katana\Normalizer\Responses;

use LogicException;

/**
 * Service variants fetched from Katana API:
 * - CachedServiceParentProduct: {{katanaUrl}}/Product?filterModel.productTypes[]=ParentProduct&filterModel.sku=service_reship
 * - ServiceChildProduct: {{katanaUrl}}/Product?filterModel.productTypes[]=ChildProduct&filterModel.sku=service_reship_495
 *
 * Medication variants fetched from Katana API:
 * - CachedParentProduct: {{katanaUrl}}/Product?filterModel.productTypes[]=ParentProduct&filterModel.sku=7_5
 * - ChildProduct: {{katanaUrl}}/Product?filterModel.productTypes[]=ChildProduct&filterModel.sku=7_5_apotheek_bad_nieuweschans_worldwide
 */
enum JsonFileResponseProvider: string
{
    case CachedServiceParentProduct =  __DIR__.'/Json/CachedServiceParentProduct.json';
    case ServiceChildProduct = __DIR__.'/Json/ServiceChildProduct.json';

    case CachedParentProduct =  __DIR__.'/Json/CachedParentProduct.json';
    case ChildProduct = __DIR__.'/Json/ChildProduct.json';

    /**
     * @return array<array-key, mixed>
     */
    public function toArray(): array
    {
        $contents = file_get_contents($this->value);
        if ($contents === false) {
            throw new LogicException('File not found');
        }

        return json_decode($contents, true);
    }
}
