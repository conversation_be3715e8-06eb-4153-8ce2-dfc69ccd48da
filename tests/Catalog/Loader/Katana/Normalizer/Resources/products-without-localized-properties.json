[{"Id": 20571, "CreatedOnUtc": "2024-03-28T15:53:39.9499985", "UpdatedOnUtc": "2024-04-05T07:22:37.5913204", "ProductType": 40, "ProductTypeDescription": "GrandParentProduct", "PimSeverity": "NotChecked", "PimStatus": null, "SortOrder": 0, "GrandParentExternalId": null, "ParentExternalId": null, "ExternalKey": "7", "GrandParentExternalKey": null, "ParentExternalKey": null, "ParentId": 0, "GrandParentId": 0, "Collections": {"Categories": [{"Id": 68, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Code": null, "Description": null, "DisplayOrder": 0, "ParentCategory": {"Id": 67, "Name": "<PERSON><PERSON> man<PERSON>", "Code": null, "Description": null, "ParentCategory": null}}], "Attributes": null, "Specs": [{"Id": 184739, "SpecificationAttributeId": 185, "SpecificationAttributeOptionId": 673, "Name": "Type", "Code": "type", "Description": "", "AttributeTypeId": 0, "OptionName": "Medication", "OptionCode": "medication", "OptionSubtitle": null}, {"Id": 184740, "SpecificationAttributeId": 186, "SpecificationAttributeOptionId": 677, "Name": "Discontinued", "Code": "discontinued", "Description": null, "AttributeTypeId": 0, "OptionName": "No", "OptionCode": "No", "OptionSubtitle": null}, {"Id": 184741, "SpecificationAttributeId": 187, "SpecificationAttributeOptionId": 679, "Name": "Addictive", "Code": "addictive", "Description": null, "AttributeTypeId": 0, "OptionName": "No", "OptionCode": "No", "OptionSubtitle": null}], "SpecificationGroups": [{"Id": 8, "Name": "Product", "Code": "Product"}], "ChildProducts": [], "Attachments": [], "Images": [{"Id": 2057, "Url": "https://ehvg.katanapim.com/content/images/thumbs/000/0000336_viagra.jpeg", "AltTag": "viagra"}], "Tags": [], "Labels": null, "LimitedToStores": [{"Id": 27, "SystemName": "blueclinic_nl", "Name": "Blueclinic NL"}, {"Id": 28, "SystemName": "dok_at", "Name": "Dokteronline AT"}, {"Id": 29, "SystemName": "dok_be", "Name": "Dokteronline BE"}, {"Id": 30, "SystemName": "dok_ch", "Name": "Dokteronline CH"}, {"Id": 31, "SystemName": "dok_de", "Name": "Dokteronline DE"}, {"Id": 32, "SystemName": "dok_dk", "Name": "Dokteronline DK"}, {"Id": 33, "SystemName": "dok_fi", "Name": "Dokteronline FI"}, {"Id": 34, "SystemName": "dok_fr", "Name": "Dokteronline FR"}, {"Id": 35, "SystemName": "dok_lt", "Name": "Dokteronline LT"}, {"Id": 36, "SystemName": "dok_lu", "Name": "Dokteronline LU"}, {"Id": 37, "SystemName": "dok_nl", "Name": "Dokteronline NL"}, {"Id": 38, "SystemName": "dok_pl", "Name": "Dokteronline PL"}, {"Id": 39, "SystemName": "dok_pt", "Name": "Dokteronline PT"}, {"Id": 40, "SystemName": "dok_se", "Name": "Dokteronline SE"}, {"Id": 41, "SystemName": "dok_gb", "Name": "Dokteronline UK"}, {"Id": 42, "SystemName": "dok_ro", "Name": "Dokteronline RO"}], "Manufacturers": [], "ImageGroups": [{"Name": "Locale image", "ImageTypes": [{"Name": "Product | default", "Url": "https://ehvg.katanapim.com/content/images/thumbs/000/0000336_viagra.jpeg", "AltTag": "viagra", "DisplayOrder": 0}, {"Name": "Product | en", "Url": "", "AltTag": "", "DisplayOrder": 1}, {"Name": "Product | nl", "Url": "", "AltTag": "", "DisplayOrder": 2}, {"Name": "Product | de", "Url": "", "AltTag": "", "DisplayOrder": 3}, {"Name": "Product | fr", "Url": "", "AltTag": "", "DisplayOrder": 4}, {"Name": "Product | da", "Url": "", "AltTag": "", "DisplayOrder": 5}, {"Name": "Product | sv", "Url": "", "AltTag": "", "DisplayOrder": 6}, {"Name": "Product | fi", "Url": "", "AltTag": "", "DisplayOrder": 7}, {"Name": "Product | pl", "Url": "", "AltTag": "", "DisplayOrder": 8}, {"Name": "Product | pt", "Url": "", "AltTag": "", "DisplayOrder": 9}, {"Name": "Product | lt", "Url": "", "AltTag": "", "DisplayOrder": 10}, {"Name": "Product | ro", "Url": "", "AltTag": "", "DisplayOrder": 11}]}], "AssociatedProducts": [{"Id": 20570}]}, "Dimensions": {"Weight": 0.0, "Length": 0.0, "Width": 0.0, "Height": 0.0}, "Package": {"Size": null, "Unit": "", "UnitItem": ""}, "FirstOnStockDate": "2024-03-28T15:53:39.9499985", "Settings": {"LimitedToStores": true, "DeliveryDate": null, "IsTelecommunicationsOrBroadcastingOrElectronicServices": false, "ManageInventoryMethod": "ManageStock", "UseMultipleWarehouses": false, "AllowBackInStockSubscriptions": false, "HasPriceBookItems": false, "HasDiscountsApplied": false, "AvailableStartDateTimeUtc": "1900-01-01T00:00:00", "AvailableEndDateTimeUtc": "2999-01-01T00:00:00", "Published": true, "ShowOnHomePage": false, "IsGiftcard": false}, "EcommerceSettings": {"DisableBuyButton": false, "DisableWishlistButton": false, "AvailableForPreOrder": false, "PreOrderAvailabilityStartDateTimeUtc": null}, "OrderSettings": {"OrderMinimumQuantity": 0, "OrderMaximumQuantity": 0, "IsShipEnabled": false, "AllowCancelling": false, "AllowReturns": false, "IsFreeShipping": false, "ShipSeparately": false, "AllowedQuantities": null}, "Prices": {"AdditionalShippingCharge": 0.0, "IsTaxExempt": false, "TaxCategoryId": 0, "TaxCategoryDescription": "", "OldPrice": 0.0, "CustomerEntersPrice": false, "MinimumCustomerEnteredPrice": 0.0, "MaximumCustomerEnteredPrice": 0.0, "CurrentPriceBookItem": {"CostPrice": 0.0, "Price": 0.0}, "PriceBookItems": null, "Currency": null, "SpecialPrice": null, "SpecialPriceStartDateTimeUtc": "1900-01-01T00:00:00", "SpecialPriceEndDateTimeUtc": "1900-01-01T00:00:00"}, "Stock": {"TotalStock": 0, "MinStockQuantity": 0, "LowStockActivity": "0", "NotifyAdminForQuantityBelow": 0, "BackorderMode": null, "BackorderDeliveryDate": null}, "TextFieldsModel": {"Sku": null, "Gtin": null, "Name": "Viagra", "ShortDescription": null, "FullDescription": null, "ManufacturerPartNumber": null, "EmbeddedVideo": null, "Slug": "viagra-3", "MetaKeywords": "", "MetaDescription": null, "MetaTitle": null}, "Vendor": null, "Reviews": {"AllowCustomerReviews": false, "ApprovedRatingSum": 0, "ApprovedTotalReviews": 0}, "AdditionProducts": {"RequireOtherProducts": false, "RequiredProductIds": null, "AutomaticallyAddRequiredProducts": false}, "Download": {"IsDownload": false, "MaxNumberOfDownloads": 0, "UnlimitedDownloads": false, "DownloadExpirationDays": null, "HasSampleDownload": false, "HasUserAgreement": false, "UserAgreementText": null}, "Recurring": {"IsRecurring": false, "RecurringCycleLength": 0, "RecurringCyclePeriod": "Days", "RecurringTotalCycles": 0}, "Rental": {"IsRental": false, "RentalPriceLength": 0, "RentalPricePeriod": "Days"}}]