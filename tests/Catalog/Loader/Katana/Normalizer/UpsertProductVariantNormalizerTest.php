<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Loader\Katana\Normalizer;

use App\Catalog\Loader\Katana\Cache\ResponseCacheInterface;
use App\Catalog\Loader\Katana\Normalizer\UpsertProductVariantNormalizer;
use App\Catalog\Loader\Katana\Resolver\ChannelFilterResolverInterface;
use App\Catalog\Loader\OriginatingClient;
use App\Catalog\Message\Notification;
use App\Catalog\Message\Product\UpsertSimpleProduct;
use App\Catalog\Message\ProductVariant\UpsertProductVariant;
use App\Tests\Catalog\Loader\Katana\Normalizer\Responses\JsonFileResponseProvider;
use App\Tests\Util\Factory\Catalog\Messenger\UpsertProductVariantFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Yaml\Yaml;

class UpsertProductVariantNormalizerTest extends TestCase
{
    private const array CHANNEL_FILTER_ENABLED_CHANNEL_CODES = [
        'dok_at',
        'dok_be',
        'dok_ch',
        'dok_de',
        'dok_dk',
        'dok_fi',
        'dok_fr',
        'dok_lt',
        'dok_lu',
        'dok_nl',
        'dok_pl',
        'dok_pt',
        'dok_se',
        'dok_gb',
        'dok_ro',
    ];

    private ResponseCacheInterface&MockObject $responseCacheMock;
    private ChannelFilterResolverInterface&MockObject $channelFilterResolverMock;
    private UpsertProductVariantNormalizer $upsertProductVariantDenormalizer;
    private MessageBusInterface&MockObject $messageBus;
    private LoggerInterface&MockObject $logger;

    public function setUp(): void
    {
        $this->responseCacheMock = $this->createMock(ResponseCacheInterface::class);
        $this->channelFilterResolverMock = $this->createMock(ChannelFilterResolverInterface::class);
        $this->messageBus = $this->createMock(MessageBusInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->upsertProductVariantDenormalizer = new UpsertProductVariantNormalizer(
            $this->responseCacheMock,
            $this->channelFilterResolverMock,
            $this->messageBus,
            $this->logger,
        );
    }

    public function testCanDenormalizeServiceUpsertProductVariant(): void
    {
        $this->responseCacheMock
            ->method('getItem')
            ->willReturnCallback(static fn (): array => JsonFileResponseProvider::CachedServiceParentProduct->toArray());

        $this->channelFilterResolverMock
            ->method('filterExistingChannelCodes')
            ->willReturn(self::CHANNEL_FILTER_ENABLED_CHANNEL_CODES);
        $this->channelFilterResolverMock
            ->method('hasExistingChannelCode')
            ->willReturnCallback(
                static function (string $channelCode): bool {
                    return in_array($channelCode, self::CHANNEL_FILTER_ENABLED_CHANNEL_CODES, true);
                }
            );

        $upsertProductVariants = $this->upsertProductVariantDenormalizer->denormalize(
            JsonFileResponseProvider::ServiceChildProduct->toArray(),
            UpsertProductVariant::class.'[]',
            'array'
        );

        $expectedUpsertProductVariants[] = UpsertProductVariantFactory::createFromArray(
            Yaml::parseFile(__DIR__.'/Resources/service-upsert-product-variant.yaml')
        );

        self::assertCount(1, $upsertProductVariants);
        self::assertEquals($expectedUpsertProductVariants, $upsertProductVariants);
    }

    public function testCanDenormalizeMedicationUpsertProductVariant(): void
    {
        $this->responseCacheMock
            ->method('getItem')
            ->willReturnCallback(static fn (): array => JsonFileResponseProvider::CachedParentProduct->toArray());

        $this->channelFilterResolverMock
            ->method('filterExistingChannelCodes')
            ->willReturn(self::CHANNEL_FILTER_ENABLED_CHANNEL_CODES);
        $this->channelFilterResolverMock
            ->method('hasExistingChannelCode')
            ->willReturnCallback(
                static function (string $channelCode): bool {
                    return in_array($channelCode, self::CHANNEL_FILTER_ENABLED_CHANNEL_CODES, true);
                }
            );

        $upsertProductVariants = $this->upsertProductVariantDenormalizer->denormalize(
            JsonFileResponseProvider::ChildProduct->toArray(),
            UpsertProductVariant::class.'[]',
            'array'
        );

        $expectedUpsertProductVariants[] = UpsertProductVariantFactory::createFromArray(
            Yaml::parseFile(__DIR__.'/Resources/medication-upsert-product-variant.yaml')
        );

        self::assertCount(1, $upsertProductVariants);
        self::assertEquals($expectedUpsertProductVariants, $upsertProductVariants);
    }

    public function testDenormalizeOnExceptionLogsError(): void
    {
        $this->responseCacheMock
            ->method('getItem')
            ->willReturnCallback(static fn (): array => ['Key' => 'Value']);

        $this->messageBus
            ->expects(self::once())
            ->method('dispatch')
            ->with(self::isInstanceOf(Notification::class))
            ->willReturn(new Envelope(new Notification('Expected parent product translations to be present.', 'test-sku', OriginatingClient::default(), 1336)));

        $this->logger
            ->expects(self::once())
            ->method('debug')
            ->with('Expected parent product translations to be present.');

        $upsertProductVariants = $this->upsertProductVariantDenormalizer->denormalize(
            JsonFileResponseProvider::ChildProduct->toArray(),
            UpsertProductVariant::class.'[]',
            'array'
        );

        self::assertCount(0, $upsertProductVariants);
    }

    public function testDenormalizeOnEmptyCachedParentProductDataLogsError(): void
    {
        $this->responseCacheMock
            ->method('getItem')
            ->willReturn([]);

        $this->messageBus
            ->expects(self::once())
            ->method('dispatch')
            ->with(self::isInstanceOf(Notification::class))
            ->willReturn(new Envelope(new Notification('Test error message.', 'test-sku', OriginatingClient::default(), 1336)));

        $this->logger
            ->expects(self::once())
            ->method('debug')
            ->with('Skipped loading of product variant due to missing ParentProductData.');

        $upsertProductVariants = $this->upsertProductVariantDenormalizer->denormalize(
            JsonFileResponseProvider::ChildProduct->toArray(),
            UpsertProductVariant::class.'[]',
            'array'
        );

        self::assertCount(0, $upsertProductVariants);
    }

    /**
     * @dataProvider supportsDenormalizationProvider
     */
    public function testSupportsDenormalization(string $type, string $format, bool $expectedResult): void
    {
        $actualResult = $this->upsertProductVariantDenormalizer->supportsDenormalization(
            JsonFileResponseProvider::ChildProduct->toArray(),
            $type,
            $format,
        );

        $this->assertEquals($expectedResult, $actualResult);
    }

    /**
     * @return iterable<string, array{
     *     0: string,
     *     1: string,
     *     2: bool,
     * }>
     */
    public function supportsDenormalizationProvider(): iterable
    {
        yield sprintf('with %s as array', UpsertProductVariant::class) => [
            UpsertProductVariant::class.'[]',
            'array',
            true,
        ];

        yield sprintf('with %s as single class is not supported', UpsertProductVariant::class) => [
            UpsertProductVariant::class,
            'array',
            false,
        ];

        yield sprintf('with %s as another object class is not supported', UpsertSimpleProduct::class) => [
            UpsertSimpleProduct::class,
            'array',
            false,
        ];
    }

    public function testGetSupportedTypes(): void
    {
        $supportedTypes = $this->upsertProductVariantDenormalizer->getSupportedTypes(null);

        self::assertCount(1, $supportedTypes);
        self::assertSame(
            [UpsertProductVariant::class.'[]' => true],
            $this->upsertProductVariantDenormalizer->getSupportedTypes(null)
        );
    }
}
