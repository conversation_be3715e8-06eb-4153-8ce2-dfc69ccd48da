<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Loader\Katana\Normalizer;

use App\Catalog\Loader\Katana\Cache\ResponseCacheInterface;
use App\Catalog\Loader\Katana\Normalizer\UpsertTaxonNormalizer;
use App\Catalog\Message\Taxon\Translation;
use App\Catalog\Message\Taxon\UpsertTaxon;
use App\Catalog\Message\TranslationCollection;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Locale\Provider\LocaleProviderInterface;

class UpsertTaxonNormalizerTest extends TestCase
{
    private UpsertTaxonNormalizer $taxonNormalizer;

    protected function setUp(): void
    {
        $localeProvider = $this->createMock(LocaleProviderInterface::class);
        $localeProvider->method('getAvailableLocalesCodes')->willReturn(['nl', 'de', 'en']);
        $localeProvider->method('getDefaultLocaleCode')->willReturn('en');
        $responseCacheInterface = $this->createMock(ResponseCacheInterface::class);
        $responseCacheInterface->method('getItem')->willReturn(['Slug' => 'verzorging']);

        $this->taxonNormalizer = new UpsertTaxonNormalizer($localeProvider, $responseCacheInterface);
    }

    public function testDenormalize(): void
    {
        $items = $this->getSampleItems();

        $result = $this->taxonNormalizer->denormalize($items, UpsertTaxon::class.'[]', 'array');

        $this->assertNotEmpty($result);
        $this->assertContainsOnlyInstancesOf(UpsertTaxon::class, $result);

        $upsertTaxon = $result[0];
        $this->assertSame('skin_care', $upsertTaxon->code);
        $this->assertSame('verzorging', $upsertTaxon->parentCode);

        $dutchTranslation = $this->getTaxonTranslation($upsertTaxon->translations, 'nl');

        $this->assertSame('nl', $dutchTranslation->locale);
        $this->assertSame('Huidverzorging', $dutchTranslation->name);
        $this->assertSame('Categorie voor Huidverzorging', $dutchTranslation->description);
        $this->assertSame('huidverzorging', $dutchTranslation->slug);

        $germanTranslation = $this->getTaxonTranslation($upsertTaxon->translations, 'de');
        $this->assertSame('de', $germanTranslation->locale);
        $this->assertSame('Skin Care', $germanTranslation->name); // Has fallen back to 'en'
        $this->assertSame('Category for Skincare', $germanTranslation->description); // Has fallen back to 'en'
        $this->assertSame('skin-care', $germanTranslation->slug); // Has fallen back to 'en'

        $englishTranslation = $this->getTaxonTranslation($upsertTaxon->translations, 'en');
        $this->assertSame('en', $englishTranslation->locale);
        $this->assertSame('Skin Care', $englishTranslation->name);
        $this->assertSame('Category for Skincare', $englishTranslation->description);
        $this->assertSame('skin-care', $englishTranslation->slug);
    }

    /**
     * @dataProvider provideSupportsDenormalizationData
     */
    public function testSupportsDenormalization(mixed $type, string $format, bool $expectedResult): void
    {
        $items = $this->getSampleItems();

        $this->assertSame($expectedResult, $this->taxonNormalizer->supportsDenormalization($items, $type, $format));
    }

    public function provideSupportsDenormalizationData(): iterable
    {
        yield [UpsertTaxon::class.'[]', 'json', true];
    }

    private function getTaxonTranslation(TranslationCollection $translationCollection, string $locale): Translation
    {
        $translations = array_filter(
            iterator_to_array($translationCollection),
            static function (Translation $translation) use ($locale): bool {
                return $translation->locale === $locale;
            }
        );

        return array_shift($translations);
    }

    private function getSampleItems(): array
    {
        return json_decode(file_get_contents(__DIR__.'/Resources/sampleTaxonResponse.json'), true, 512, JSON_THROW_ON_ERROR);
    }
}
