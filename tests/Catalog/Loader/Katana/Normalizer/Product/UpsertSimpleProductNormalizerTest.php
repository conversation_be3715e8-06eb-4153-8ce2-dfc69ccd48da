<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Loader\Katana\Normalizer\Product;

use App\Catalog\Loader\Katana\Normalizer\Product\UpsertSimpleProductNormalizer;
use App\Catalog\Loader\OriginatingClient;
use App\Catalog\Message\AttributeCollection;
use App\Catalog\Message\ChannelCollection;
use App\Catalog\Message\ChannelPricing;
use App\Catalog\Message\ChannelPricingCollection;
use App\Catalog\Message\LeafletCollection;
use App\Catalog\Message\Notification;
use App\Catalog\Message\Price;
use App\Catalog\Message\Product\Translation as ProductTranslation;
use App\Catalog\Message\Product\UpsertProduct;
use App\Catalog\Message\Product\UpsertSimpleProduct;
use App\Catalog\Message\ProductTypeAttribute;
use App\Catalog\Message\ProductVariant\Translation as ProductVariantTranslation;
use App\Catalog\Message\ProductVariant\UpsertProductVariant;
use App\Catalog\Message\TaxonCollection;
use App\Catalog\Message\TranslationCollection;
use App\Entity\Product\ProductType;
use Symfony\Component\Messenger\Envelope;

class UpsertSimpleProductNormalizerTest extends AbstractUpsertProductNormalizerTest
{
    private UpsertSimpleProductNormalizer $normalizer;

    protected function setUp(): void
    {
        parent::setUp();
        $this->normalizer = new UpsertSimpleProductNormalizer(
            $this->responseCache,
            $this->channelFilterResolverMock,
            $this->messageBus,
            $this->logger,
        );
    }

    /**
     * Regression test for DV-9255.
     */
    public function testDenormalizeServiceProduct(): void
    {
        // Arrange
        $jsonFile = file_get_contents(__DIR__.'/../Resources/simple-products-service.json');
        self::assertNotEmpty($jsonFile);
        $json = json_decode($jsonFile, true, 512, JSON_THROW_ON_ERROR);

        // Act
        $result = $this->normalizer->denormalize(
            $json,
            UpsertSimpleProduct::class.'[]'
        );

        // Assert
        $x = true;
        $this->assertCount(1, $result);
        $upsertSimpleProduct = $result[0];
        self::assertInstanceOf(UpsertSimpleProduct::class, $upsertSimpleProduct);
        self::assertTrue($upsertSimpleProduct->getUpsertProductVariant()->shippingRequired);
    }

    public function testDenormalizeConsult(): void
    {
        // Arrange
        $jsonFile = file_get_contents(__DIR__.'/../Resources/simple-products-consult.json');

        $productTypeAttribute = ProductTypeAttribute::create(ProductType::CONSULT);
        $attributes = new AttributeCollection($productTypeAttribute);

        $expectedUpsertSimpleProduct = new UpsertSimpleProduct(
            'consult_erectile_dysfunction',
            true,
            new TaxonCollection(),
            $this->getExpectedProductTranslations(),
            $this->getExpectedChannels(),
            $attributes,
            new UpsertProductVariant(
                code: 'consult_erectile_dysfunction',
                productCode: 'consult_erectile_dysfunction',
                enabled: true,
                translations: $this->getExpectedProductVariantTranslations(),
                channels: $this->getExpectedChannels(),
                channelPricings: $this->getExpectedChannelPricingCollection(),
                costPrice: new Price(0, 'EUR'),
                supplierVariantName: null,
                supplierVariantCode: null,
                prescriptionRequired: true,
                maximumQuantityPerOrder: 1,
                supplierCode: null,
                preferredSupplier: false,
                quantityMultiplier: 1,
                leaflets: new LeafletCollection(),
                shippingRequired: false,
            )
        );

        // Act
        $result = $this->normalizer->denormalize(
            json_decode($jsonFile, true, 512, JSON_THROW_ON_ERROR),
            UpsertSimpleProduct::class.'[]'
        );

        // Assert
        $this->assertCount(1, $result);
        $this->assertContainsOnlyInstancesOf(UpsertSimpleProduct::class, $result);
        $this->assertEquals($expectedUpsertSimpleProduct, $result[0]);
    }

    public function testDenormalizeWithInvalidArray(): void
    {
        // Arrange
        $jsonFile = file_get_contents(__DIR__.'/../Resources/simple-products-without-localized-properties.json');
        $productData = json_decode($jsonFile, true, 512, JSON_THROW_ON_ERROR);

        $this->messageBus
            ->expects(self::once())
            ->method('dispatch')
            ->with(self::isInstanceOf(Notification::class))
            ->willReturn(new Envelope(new Notification('Expected the key "LocalizedProperties" to exist.', 'test-sku', OriginatingClient::default(), 1336)));

        $this->logger
            ->expects(self::once())
            ->method('debug')
            ->with('Expected the key "LocalizedProperties" to exist.');

        // Act
        $result = $this->normalizer->denormalize(
            $productData,
            UpsertSimpleProduct::class.'[]'
        );

        self::assertEmpty($result);
    }

    public function testSupportsDenormalization(): void
    {
        // Arrange
        $expectedArray = json_decode(
            file_get_contents(__DIR__.'/../Resources/simple-products-consult.json'),
            true,
            512,
            JSON_THROW_ON_ERROR
        );
        // Act
        $result = $this->normalizer->supportsDenormalization($expectedArray, UpsertSimpleProduct::class.'[]');

        // Assert
        $this->assertTrue($result);
    }

    public function testSupportedTypes(): void
    {
        // Arrange
        $expected = [UpsertSimpleProduct::class.'[]' => true, UpsertProduct::class.'[]' => true];

        // Act
        $result = $this->normalizer->getSupportedTypes();

        // Assert
        $this->assertSame($expected, $result);
    }

    private function getExpectedChannels(): ChannelCollection
    {
        $channels = [
            'dok_at',
            'dok_be',
            'dok_ch',
            'dok_de',
            'dok_dk',
            'dok_fi',
            'dok_fr',
            'dok_lt',
            'dok_lu',
            'dok_nl',
            'dok_pl',
            'dok_pt',
            'dok_se',
            'dok_gb',
            'dok_ro',
        ];

        return new ChannelCollection(...$channels);
    }

    private function getExpectedChannelPricingCollection(): ChannelPricingCollection
    {
        $channelPricing = [
            'dok_nl',
            'dok_at',
            'dok_be',
            'dok_ch',
            'dok_de',
            'dok_dk',
            'dok_fi',
            'dok_fr',
            'dok_lt',
            'dok_lu',
            'dok_pl',
            'dok_pt',
            'dok_ro',
            'dok_se',
            'dok_gb',
        ];

        $channelPricingCollection = [];

        foreach ($channelPricing as $channelCode) {
            $price = 0;
            if (in_array($channelCode, ['dok_nl', 'blueclinic_nl'])) {
                $price = 2900;
            }

            $channelPricingCollection[] = new ChannelPricing($channelCode, true, new Price($price, 'EUR'));
        }

        return new ChannelPricingCollection(...$channelPricingCollection);
    }

    private function getExpectedProductTranslations(): TranslationCollection
    {
        $translations = [];
        foreach ($this->getTranslations() as $expectedTranslation) {
            $translations[] = new ProductTranslation(
                $expectedTranslation['locale'],
                $expectedTranslation['name'],
                $expectedTranslation['description']
            );
        }

        return new TranslationCollection(...$translations);
    }

    private function getExpectedProductVariantTranslations(): TranslationCollection
    {
        $translations = [];
        foreach ($this->getTranslations() as $expectedTranslation) {
            $translations[] = new ProductVariantTranslation(
                $expectedTranslation['locale'],
                $expectedTranslation['name'],
            );
        }

        return new TranslationCollection(...$translations);
    }

    private function getTranslations(): array
    {
        return [
            [
                'locale' => 'de',
                'name' => 'Konsultation wegen Erektionsstörung',
                'description' => '',
            ],
            [
                'locale' => 'fr',
                'name' => 'Consultation pour La dysfonction érectile',
                'description' => '',
            ],
            [
                'locale' => 'da',
                'name' => 'Konsultation til Erektionsforstyrrelse',
                'description' => '',
            ],
            [
                'locale' => 'sv',
                'name' => 'Konsultation för Erektionsproblem',
                'description' => '',
            ],
            [
                'locale' => 'fi',
                'name' => 'Konsultaatio aiheesta Erektiohäiriö',
                'description' => '',
            ],
            [
                'locale' => 'pt',
                'name' => 'Consulta para Impotência',
                'description' => '',
            ],
            [
                'locale' => 'pl',
                'name' => 'Konsultacja dotycząca: Impotencja',
                'description' => '',
            ],
            [
                'locale' => 'ro',
                'name' => 'Consultatie pentru Probleme de erecție',
                'description' => '',
            ],
            [
                'locale' => 'lt',
                'name' => 'Consultation for Erectile Dysfunction', // Default EN translation
                'description' => '',
            ],
            [
                'locale' => 'nl',
                'name' => 'Consult voor erectiestoornis',
                'description' => '',
            ],
            [
                'locale' => 'en',
                'name' => 'Consultation for Erectile Dysfunction',
                'description' => '',
            ],
        ];
    }
}
