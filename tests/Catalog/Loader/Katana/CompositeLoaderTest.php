<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Loader\Katana;

use App\Catalog\Loader\Context;
use App\Catalog\Loader\Katana\CompositeLoader;
use App\Catalog\Loader\Katana\LoaderInterface;
use App\Catalog\Loader\OriginatingClient;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Ramsey\Uuid\Uuid;

final class CompositeLoaderTest extends TestCase
{
    private LoaderInterface&MockObject $loader1;
    private LoaderInterface&MockObject $loader2;
    private LoaderInterface&MockObject $loader3;

    private CompositeLoader $command;

    protected function setUp(): void
    {
        $this->loader1 = $this->createMock(LoaderInterface::class);
        $this->loader2 = $this->createMock(LoaderInterface::class);
        $this->loader3 = $this->createMock(LoaderInterface::class);

        $this->command = new CompositeLoader([$this->loader1, $this->loader2, $this->loader3]);
    }

    public function testItCanLoadMultipleLoaders(): void
    {
        // Assert
        $this->loader1->expects(self::once())->method('execute');
        $this->loader2->expects(self::once())->method('execute');
        $this->loader3->expects(self::once())->method('execute');

        // Act
        $this->command->execute(new Context(OriginatingClient::Katana->value, Uuid::uuid7()));
    }
}
