<?php

declare(strict_types=1);

namespace App\Tests\Catalog\MessageHandler;

use App\Catalog\Loader\OriginatingClient;
use App\Catalog\Loader\UrlGeneratorInterface;
use App\Catalog\Loader\UrlGeneratorRegistryInterface;
use App\Catalog\Message\Notification;
use App\Catalog\MessageHandler\NotificationHandler;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\MockObject\Stub;
use PHPUnit\Framework\TestCase;
use Psr\Cache\CacheItemInterface;
use Psr\Cache\CacheItemPoolInterface;
use Symfony\Component\Notifier\ChatterInterface;
use Symfony\Component\Notifier\Message\ChatMessage;

final class NotificationHandlerTest extends TestCase
{
    private ChatterInterface&MockObject $chatter;
    private CacheItemPoolInterface&MockObject $cache;
    private UrlGeneratorRegistryInterface&Stub $urlRegistry;
    private UrlGeneratorInterface&Stub $urlGenerator;

    protected function setUp(): void
    {
        // Arrange
        $this->chatter = $this->createMock(ChatterInterface::class);
        $this->cache = $this->createMock(CacheItemPoolInterface::class);
        $this->urlRegistry = $this->createStub(UrlGeneratorRegistryInterface::class);
        $this->urlGenerator = $this->createStub(UrlGeneratorInterface::class);
    }

    public function testInvokeSkipsSendingWhenMessageIsCached(): void
    {
        // Arrange
        $item = $this->createMock(CacheItemInterface::class);
        $item->method('isHit')->willReturn(true);

        $this->cache
            ->method('getItem')
            ->willReturn($item);

        $this->chatter
            ->expects(self::never())
            ->method('send');

        $handler = new NotificationHandler($this->chatter, $this->cache, $this->urlRegistry);

        $notification = new Notification('Test error message', 'TEST_SKU_1337', OriginatingClient::default(), 1337);

        // Act
        $handler($notification);

        // Assert: verified by mocks
    }

    public function testInvokeSendsMessageAndCachesItWhenNotCached(): void
    {
        // Arrange
        $item = $this->createMock(CacheItemInterface::class);
        $item->method('isHit')->willReturn(false);
        $item->expects(self::once())->method('set')->with(self::isInstanceOf(ChatMessage::class));

        $this->cache
            ->method('getItem')
            ->willReturn($item);

        $this->cache
            ->expects(self::once())
            ->method('save')
            ->with($item);

        $this->urlRegistry
            ->method('get')
            ->willReturn($this->urlGenerator);

        $this->urlGenerator
            ->method('getPimUrl')
            ->willReturn('https://example.com/pim/42');

        $this->chatter
            ->expects(self::once())
            ->method('send')
            ->with(self::callback(static function (ChatMessage $message): bool {
                return $message->getSubject() === 'Error importing product (variant)'
                    && $message->getTransport() === 'slack_product_notify';
            }));

        $handler = new NotificationHandler($this->chatter, $this->cache, $this->urlRegistry);

        $notification = new Notification('Test error message', 'TEST_SKU_1337', OriginatingClient::default(), 1337);

        // Act
        $handler($notification);

        // Assert: verified by mocks
    }

    public function testInvokeDoesNotAddFixButtonIfPimIdIsNull(): void
    {
        // Arrange
        $item = $this->createMock(CacheItemInterface::class);
        $item->method('isHit')->willReturn(false);
        $item->expects(self::once())->method('set')->with(self::isInstanceOf(ChatMessage::class));

        $this->cache
            ->method('getItem')
            ->willReturn($item);

        $this->cache
            ->expects(self::once())
            ->method('save')
            ->with($item);

        $this->chatter
            ->expects(self::once())
            ->method('send')
            ->with(self::isInstanceOf(ChatMessage::class));

        $handler = new NotificationHandler($this->chatter, $this->cache, $this->urlRegistry);

        $notification = new Notification('Test error message', 'TEST_SKU_1337', OriginatingClient::default());

        // Act
        $handler($notification);

        // Assert: verified by mocks
    }
}
