<?php

declare(strict_types=1);

namespace App\Tests\Catalog\MessageHandler;

use App\Catalog\Attachment\AttachmentProcessorInterface;
use App\Catalog\Exception\ProductNotFoundException;
use App\Catalog\Loader\Context;
use App\Catalog\Loader\OriginatingClient;
use App\Catalog\Message\ImageCollection;
use App\Catalog\Message\Notification;
use App\Catalog\Message\Product\UpsertProductImages;
use App\Catalog\Message\UpsertImage;
use App\Catalog\MessageHandler\UpsertProductImagesHandler;
use App\Entity\Product\Product;
use App\Entity\Product\ProductImage;
use App\Mime\Exception\MimeTypeOrExtensionMismatchException;
use App\Repository\ProductRepository;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Ramsey\Uuid\Uuid;
use Sylius\Component\Core\Model\ImageInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

final class UpsertProductImagesHandlerTest extends TestCase
{
    private const string URL = 'https://attachments.dokteronline.com/';
    private const string PIM_URL = 'https://pim.dokteronline.com/';

    private const string IMAGE_CH = '89852099b488c3f82d91ba8e19d84f5df375ca7b54fb0507db2069ca93419f73.png';
    private const string IMAGE_DK = 'e2710984544d32de0034c68eaa9c2dbbb0641d460c053cb9b111832051b8122c.png';
    private const string IMAGE_NL = '9ce1452796da8199d08c88826de792892e5d0ddc63a181e790912d237fb95d22.png';
    private const string IMAGE_EN = 'a53da8e7936ec68569a9426d2799d958f7b87e4f4f7cf2696d386fe76cfa8ace.png';
    private const string IMAGE_DE = 'd72caf3b5119467159f62e9f426628618ce8201dcc8937986d9626eb1391210b.png';
    private const string IMAGE_FR = '7daf03ed41bb9fef758624a62085397c7da1b3241a054caa9fd7217e933d1466.png';
    private const string IMAGE_IT = 'ba8aa99f893945e8ad48f504dda3637e17ee7a026e0803be77c4f0ca1c6333e9.png';
    private const string IMAGE_NL_2 = 'f8f82eb638239b8e95be3bfb416fa50b77ac7f7b62dd70fb3e8a3aaf2bbaeb28.png';

    private EntityManagerInterface&MockObject $entityManager;

    private ProductRepository&MockObject $productRepository;
    private AttachmentProcessorInterface&MockObject $imageProcessor;
    private MessageBusInterface&MockObject $messageBus;
    private LoggerInterface&MockObject $logger;

    private UpsertProductImagesHandler $handler;

    protected function setUp(): void
    {
        parent::setUp();
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->productRepository = $this->createMock(ProductRepository::class);
        $this->imageProcessor = $this->createMock(AttachmentProcessorInterface::class);
        $this->messageBus = $this->createMock(MessageBusInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->handler = new UpsertProductImagesHandler(
            $this->entityManager,
            $this->productRepository,
            $this->imageProcessor,
            $this->messageBus,
            $this->logger
        );
    }

    public function testProductNotFound(): void
    {
        // Arrange
        $this->productRepository->expects(self::once())
            ->method('findOneByCode')
            ->with('7')
            ->willReturn(null);

        // Assert
        $this->expectException(ProductNotFoundException::class);
        $this->entityManager->expects(self::never())->method('flush');

        // Act
        ($this->handler)(new UpsertProductImages('7', new ImageCollection()));
    }

    public function testHandlerCanUpdateImages(): void
    {
        // Arrange
        $this->imageProcessor->expects(self::exactly(6))
            ->method('store')
            ->willReturnOnConsecutiveCalls(
                $this->getFullUrl(self::IMAGE_NL),
                $this->getFullUrl(self::IMAGE_NL_2),
                $this->getFullUrl(self::IMAGE_EN),
                $this->getFullUrl(self::IMAGE_DE),
                $this->getFullUrl(self::IMAGE_FR),
                $this->getFullUrl(self::IMAGE_IT),
            );

        $expectedImages = [
            ['nl', $this->getFullUrl(self::IMAGE_NL)],
            ['nl', $this->getFullUrl(self::IMAGE_NL_2)],
            ['en', $this->getFullUrl(self::IMAGE_EN)],
            ['de', $this->getFullUrl(self::IMAGE_DE)],
            ['fr', $this->getFullUrl(self::IMAGE_FR)],
            ['it', $this->getFullUrl(self::IMAGE_IT)],
        ];

        $upsertImages = [
            ['nl', self::PIM_URL.self::IMAGE_NL],
            ['nl', self::PIM_URL.self::IMAGE_NL_2],
            ['en', self::PIM_URL.self::IMAGE_EN],
            ['de', self::PIM_URL.self::IMAGE_DE],
            ['fr', self::PIM_URL.self::IMAGE_FR],
            ['it', self::PIM_URL.self::IMAGE_IT],
        ];

        $images = [];
        foreach ($upsertImages as [$locale, $link]) {
            $images[] = new UpsertImage($locale, $link, OriginatingClient::Katana);
        }
        $upsert = new UpsertProductImages('7', new ImageCollection(...$images));

        $product = $this->getProduct();
        $this->productRepository->expects(self::once())
            ->method('findOneByCode')
            ->with($upsert->code)
            ->willReturn($product);

        $this->logger->expects(self::never())->method('debug');
        $this->messageBus->expects(self::never())->method('dispatch');

        // Act
        ($this->handler)($upsert);

        // assert
        self::assertCount(6, $product->getImages());
        foreach ($expectedImages as [$locale, $path]) {
            $pathFilter = static fn (ImageInterface $productImage) => $productImage->getPath() === $path;
            $actualImages = $product->getImagesByType($locale)->filter($pathFilter);

            self::assertCount(1, $actualImages);
            self::assertInstanceOf(
                ProductImage::class,
                $actualImages->first()
            );
        }
    }

    public function testHandlerDoesNotProcessIncorrectImage(): void
    {
        // Arrange
        $exception = new MimeTypeOrExtensionMismatchException();
        $productCode = '7';

        $this->imageProcessor->expects(self::once())
            ->method('store')
            ->willThrowException($exception);

        $this->logger->expects(self::once())
            ->method('debug');

        $this->messageBus->expects(self::once())
            ->method('dispatch')
            ->willReturn(new Envelope(new Notification(
                message: $exception->getMessage(),
                sku: '8',
                client: OriginatingClient::default()
            )));

        $images[] = new UpsertImage('nl', self::PIM_URL.self::IMAGE_NL, OriginatingClient::Katana);
        $upsert = new UpsertProductImages($productCode, new ImageCollection(...$images), new Context(OriginatingClient::default()->value, Uuid::uuid7()));

        $product = $this->getProduct();
        $this->productRepository->expects(self::once())
            ->method('findOneByCode')
            ->with($upsert->code)
            ->willReturn($product);

        $this->entityManager->expects(self::never())
            ->method('persist');

        // Act
        ($this->handler)($upsert);

        // Assert
        self::assertCount(0, $product->getImages());
    }

    private function getFullUrl(string $file): string
    {
        return self::URL.$file;
    }

    private function getProduct(): Product
    {
        $product = new Product();

        $images = [
            ['nl', $this->getFullUrl(self::IMAGE_NL)],
            ['en', $this->getFullUrl(self::IMAGE_EN)],
            ['dk', $this->getFullUrl(self::IMAGE_DK)],
            ['ch', $this->getFullUrl(self::IMAGE_CH)],
        ];

        foreach ($images as [$locale, $link]) {
            $image = new ProductImage();
            $image->setType($locale);
            $image->setPath($link);
            $product->addImage($image);
        }

        return $product;
    }
}
