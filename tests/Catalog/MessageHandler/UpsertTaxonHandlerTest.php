<?php

declare(strict_types=1);

namespace App\Tests\Catalog\MessageHandler;

use App\Catalog\Exception\ParentTaxonNotFoundException;
use App\Catalog\Generator\IncrementingSlugGeneratorInterface;
use App\Catalog\Message\Taxon\Translation;
use App\Catalog\Message\Taxon\UpsertTaxon;
use App\Catalog\Message\TranslationCollection;
use App\Catalog\MessageHandler\UpsertTaxonHandler;
use App\Entity\Taxonomy\Taxon;
use App\Repository\TaxonRepositoryInterface;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Core\Model\TaxonInterface;
use Sylius\Component\Taxonomy\Model\TaxonInterface as TaxonomyTaxonInterface;

class UpsertTaxonHandlerTest extends TestCase
{
    private UpsertTaxonHandler $handler;
    private EntityManagerInterface&MockObject $entityManager;
    private TaxonRepositoryInterface&MockObject $taxonRepository;
    private IncrementingSlugGeneratorInterface&MockObject $slugGenerator;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->taxonRepository = $this->createMock(TaxonRepositoryInterface::class);
        $this->slugGenerator = $this->createMock(IncrementingSlugGeneratorInterface::class);
        $this->handler = new UpsertTaxonHandler($this->entityManager, $this->taxonRepository, $this->slugGenerator);
    }

    public function testInvokeWithNewTaxonAndNoParent(): void
    {
        // Arrange
        $this->taxonRepository
            ->expects($this->atLeastOnce())
            ->method('findOneBy')
            ->willReturn(null);

        $expectedTaxon = $this->createExpectedTaxon();

        // Assert
        $this->entityManager
            ->method('persist')
            ->with(
                self::callback(
                    static function (TaxonInterface $object) use (
                        $expectedTaxon
                    ): bool {
                        if (!$object instanceof Taxon) {
                            return true;
                        }

                        $dateTime = new DateTimeImmutable();
                        $object->setCreatedAt($dateTime);
                        $object->setUpdatedAt($dateTime);

                        $expectedTaxon->setCreatedAt($dateTime);
                        $expectedTaxon->setUpdatedAt($dateTime);

                        $object->setFallbackLocale('en');
                        $expectedTaxon->setFallbackLocale('en');

                        self::assertEquals($expectedTaxon, $object);

                        return true;
                    }
                )
            );

        // Act
        ($this->handler)(new UpsertTaxon('test', new TranslationCollection(), null));
    }

    public function testInvokeWithNonExistingParentTaxon(): void
    {
        // Assert
        $this->expectException(ParentTaxonNotFoundException::class);

        // Act
        ($this->handler)(new UpsertTaxon('test', new TranslationCollection(), 'parent'));
    }

    public function testInvokeWithExistingParentTaxon(): void
    {
        // Arrange
        $this->taxonRepository
            ->method('findOneBy')
            ->willReturn(new Taxon());

        $upsertTaxon = new UpsertTaxon('test', new TranslationCollection(), 'parent');

        /** @var Taxon $parentTaxon */
        $parentTaxon = $this->taxonRepository->findOneBy([]);
        $parentTaxon->setCode('parent');
        $this->entityManager->persist($parentTaxon);
        $this->entityManager->flush();

        // Act & Assert
        ($this->handler)($upsertTaxon);
        $this->assertNotEmpty($this->taxonRepository->findOneBy(['parentCode' => 'parent']));
    }

    public function testTranslations(): void
    {
        // Arrange
        $this->slugGenerator->method('generate')->willReturn('test-slug');

        $expectedTaxon = $this->createExpectedTaxon();
        $this->addTranslation($expectedTaxon, new Translation('en', 'testName', 'testDescription', 'test-slug'));

        $this->entityManager
            ->method('persist')
            ->with(
                self::callback(
                    static function (TaxonInterface $object) use (
                        $expectedTaxon
                    ): bool {
                        if (!$object instanceof Taxon) {
                            return true;
                        }

                        $dateTime = new DateTimeImmutable();
                        $object->setCreatedAt($dateTime);
                        $object->setUpdatedAt($dateTime);

                        $expectedTaxon->setCreatedAt($dateTime);
                        $expectedTaxon->setUpdatedAt($dateTime);

                        $object->setFallbackLocale('en');

                        $expectedTaxon->setFallbackLocale('en');
                        $expectedTaxon->setCurrentLocale('en');

                        self::assertEquals($expectedTaxon, $object);

                        return true;
                    }
                )
            );

        // Act & Assert
        ($this->handler)(new UpsertTaxon(
            'test',
            new TranslationCollection(new Translation('en', 'testName', 'testDescription', 'test-slug')),
            null
        ));
    }

    private function createExpectedTaxon(): TaxonInterface
    {
        $taxon = new Taxon();
        $taxon->setCode('test');

        return $taxon;
    }

    private function addTranslation(
        TaxonomyTaxonInterface $taxon,
        Translation $upsertTaxonTranslation,
    ): void {
        $taxonTranslation = $taxon->getTranslation($upsertTaxonTranslation->locale);

        $taxonTranslation->setLocale($upsertTaxonTranslation->locale);
        $taxonTranslation->setName($upsertTaxonTranslation->name);
        $taxonTranslation->setDescription($upsertTaxonTranslation->description);
        $taxonTranslation->setSlug($upsertTaxonTranslation->slug);
        $taxonTranslation->setTranslatable($taxon);
    }
}
