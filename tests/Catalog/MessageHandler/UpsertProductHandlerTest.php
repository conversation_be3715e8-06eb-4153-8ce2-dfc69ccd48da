<?php

declare(strict_types=1);

namespace App\Tests\Catalog\MessageHandler;

use App\Catalog\Generator\IncrementingSlugGeneratorInterface;
use App\Catalog\Loader\Context;
use App\Catalog\Loader\OriginatingClient;
use App\Catalog\Message\Attribute;
use App\Catalog\Message\AttributeCollection;
use App\Catalog\Message\ChannelCollection;
use App\Catalog\Message\ConsultProductCollection;
use App\Catalog\Message\ImageCollection;
use App\Catalog\Message\Product\Translation;
use App\Catalog\Message\Product\UpsertProduct;
use App\Catalog\Message\Product\UpsertProductImages;
use App\Catalog\Message\ProductTaxon;
use App\Catalog\Message\ProductTaxonChannelCollection;
use App\Catalog\Message\ProductTypeAttribute;
use App\Catalog\Message\TaxonCollection;
use App\Catalog\Message\TranslationCollection;
use App\Catalog\Message\UpsertImage;
use App\Catalog\MessageHandler\UpsertProductHandler;
use App\Catalog\Processor\Product\ChannelProcessor;
use App\Catalog\Processor\Product\CompositeProductProcessor;
use App\Catalog\Processor\Product\ConsultProductAssociationProcessor;
use App\Catalog\Processor\Product\ProductAttributeProcessor;
use App\Catalog\Processor\Product\ProductImageProcessor;
use App\Catalog\Processor\Product\ProductTaxonProcessor;
use App\Catalog\Processor\Product\ProductTranslationProcessor;
use App\Entity\Product\Product;
use App\Entity\Product\ProductAssociation;
use App\Entity\Product\ProductAssociationType;
use App\Entity\Product\ProductAttribute;
use App\Entity\Product\ProductAttributeInterface;
use App\Entity\Product\ProductAttributeValue;
use App\Entity\Product\ProductImage;
use App\Entity\Product\ProductInterface;
use App\Entity\Product\ProductTaxon as SyliusProductTaxon;
use App\Entity\Product\ProductTaxonChannel;
use App\Entity\Product\ProductType;
use App\Event\Product\PostUpdateEvent;
use App\Event\Product\PreUpdateEvent;
use App\Repository\ChannelRepositoryInterface;
use App\Repository\ProductRepository;
use App\Repository\TaxonRepositoryInterface;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\ProductFactory;
use App\Tests\Util\Factory\TaxonFactory;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Ramsey\Uuid\Uuid;
use Sylius\Component\Attribute\Model\AttributeInterface;
use Sylius\Component\Attribute\Model\AttributeValueInterface;
use Sylius\Component\Core\Model\TaxonInterface;
use Sylius\Component\Product\Model\ProductAssociationTypeInterface;
use Sylius\Component\Product\Repository\ProductAssociationTypeRepositoryInterface;
use Sylius\Component\Product\Repository\ProductAttributeValueRepositoryInterface;
use Sylius\Component\Product\Repository\ProductRepositoryInterface;
use Sylius\Resource\Factory\Factory;
use Sylius\Resource\Factory\FactoryInterface;
use Sylius\Resource\Model\TimestampableInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;
use Webmozart\Assert\Assert;

final class UpsertProductHandlerTest extends TestCase
{
    private const string CODE = '7';
    private const array CONSULT_CODES = ['consult_erectile_dysfunction', 'consult_alcohol_addiction'];

    private const array EXPECTED_TRANSLATIONS = [
        [
            'locale' => 'nl',
            'name' => 'Viagra nl',
            'description' => 'Viagra nl description',
            'slug' => 'viagra-nl',
        ],
        [
            'locale' => 'en',
            'name' => 'Viagra en',
            'description' => 'Viagra en description',
            'slug' => 'viagra-en',
        ],
        [
            'locale' => 'de',
            'name' => 'Viagra de',
            'description' => 'Viagra de description',
            'slug' => 'viagra-de',
        ],
    ];

    private const array EXPECTED_IMAGES = [
        ['locale' => 'nl', 'imageUrl' => 'https://dokteronline.com/images/awesome-image-nl.jpg', 'client' => 'katana'],
        ['locale' => 'en', 'imageUrl' => 'https://dokteronline.com/images/awesome-image-en.jpg', 'client' => 'katana'],
        ['locale' => 'de', 'imageUrl' => 'https://dokteronline.com/images/awesome-image-de.jpg', 'client' => 'katana'],
    ];

    private const array CHANNEL_CODES = [
        'dok_at',
        'dok_be',
        'dok_ch',
        'dok_de',
        'dok_dk',
        'dok_fi',
        'dok_fr',
        'dok_lt',
        'dok_lu',
        'dok_pl',
        'dok_pt',
        'dok_se',
        'dok_gb',
        'dok_ro',
        'blueclinic_nl',
        'dok_nl',
    ];
    private EntityManagerInterface&MockObject $entityManager;

    /** @var ProductRepositoryInterface<Product>&MockObject */
    private ProductRepositoryInterface&MockObject $productRepository;
    private ChannelRepositoryInterface&MockObject $channelRepository;

    /** @var EntityRepository<ProductAttribute>&MockObject */
    private EntityRepository&MockObject $productAttributeRepository;

    /** @var ProductAttributeValueRepositoryInterface<ProductAttributeValue>&MockObject */
    private ProductAttributeValueRepositoryInterface&MockObject $productAttributeValueRepository;

    /** @var ProductAssociationTypeRepositoryInterface<ProductAssociationType>&MockObject */
    private ProductAssociationTypeRepositoryInterface&MockObject $productAssociationTypeRepository;
    private TaxonRepositoryInterface&MockObject $taxonRepository;
    private EventDispatcherInterface&MockObject $eventDispatcher;

    private MessageBusInterface&MockObject $messageBus;
    private IncrementingSlugGeneratorInterface&MockObject $slugGenerator;

    private UpsertProductHandler $handler;

    protected function setUp(): void
    {
        parent::setUp();

        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->productRepository = $this->createMock(ProductRepository::class);
        $this->channelRepository = $this->createMock(ChannelRepositoryInterface::class);
        $this->productAttributeRepository = $this->createMock(EntityRepository::class);
        $this->productAttributeValueRepository = $this->createMock(ProductAttributeValueRepositoryInterface::class);
        $this->productAssociationTypeRepository = $this->createMock(ProductAssociationTypeRepositoryInterface::class);
        $this->taxonRepository = $this->createMock(TaxonRepositoryInterface::class);
        $this->eventDispatcher = $this->createMock(EventDispatcherInterface::class);

        $this->messageBus = $this->createMock(MessageBusInterface::class);
        $this->slugGenerator = $this->createMock(IncrementingSlugGeneratorInterface::class);

        $this->entityManager
            ->method('getRepository')
            ->willReturnCallback(function (string $class) {
                return match ($class) {
                    ProductAttribute::class => $this->productAttributeRepository,
                };
            });

        /** @var FactoryInterface<ProductAssociation> $productAssociaitionFactory */
        $productAssociaitionFactory = new Factory(ProductAssociation::class);

        $compositeProductHandler = new CompositeProductProcessor([
            new ProductTranslationProcessor($this->slugGenerator),
            new ChannelProcessor($this->channelRepository),
            new ProductAttributeProcessor($this->entityManager, $this->productAttributeValueRepository),
            new ProductImageProcessor($this->messageBus),

            new ProductTaxonProcessor(
                $this->taxonRepository,
                $this->channelRepository
            ),

            new ConsultProductAssociationProcessor(
                $this->productRepository,
                $this->productAssociationTypeRepository,
                $productAssociaitionFactory,
                $this->entityManager
            ),
        ]);

        $this->handler = new UpsertProductHandler(
            $this->entityManager,
            $this->productRepository,
            $compositeProductHandler,
            $this->eventDispatcher,
        );
    }

    /**
     * @return iterable<array{0: Context, 1: bool}>
     */
    public function contextDataProvider(): iterable
    {
        yield 'With image processor' => [new Context(OriginatingClient::default()->value, Uuid::uuid7()), false];
        yield 'Skip image processor' => [new Context(originatingClient: OriginatingClient::default()->value, importTag: Uuid::uuid7(), skipImages: true), true];
    }

    /**
     * @dataProvider contextDataProvider
     */
    public function testCanCreateProduct(Context $context, bool $skipImageProcessor): void
    {
        // Arrange upsert product
        $upsertProduct = new UpsertProduct(
            code: self::CODE,
            enabled: false,
            taxons: new TaxonCollection(
                new ProductTaxon(
                    'erectile_dysfunction',
                    new ProductTaxonChannelCollection('dok_nl', 'dok_gb', 'dok_de', 'dok_dk'),
                    0
                ),
                new ProductTaxon(
                    'consult_alcohol_addiction',
                    new ProductTaxonChannelCollection('dok_nl', 'dok_gb'),
                    1
                ),
            ),
            consultProducts: new ConsultProductCollection('consult_erectile_dysfunction', 'consult_alcohol_addiction'),
            translations: new TranslationCollection(...array_map(static function (array $translation) {
                unset($translation['slug']);

                return new Translation(...$translation);
            }, self::EXPECTED_TRANSLATIONS)),
            images: new ImageCollection(...array_map(static function (array $imageData) {
                $imageData['client'] = OriginatingClient::from($imageData['client']);

                return new UpsertImage(...$imageData);
            }, self::EXPECTED_IMAGES)),
            channels: new ChannelCollection('dok_nl', 'dok_gb', 'dok_de'),
            attributes: new AttributeCollection(
                ProductTypeAttribute::create(ProductType::MEDICATION),
                Attribute::createFromAddictive(true),
            )
        );

        $upsertProduct->setContext($context);

        // Arrange objects
        $channels = $this->getChannels();

        $existingProduct = new Product();
        $existingProduct->setCode(self::CODE);
        $existingProduct->setVariantSelectionMethod(ProductInterface::VARIANT_SELECTION_MATCH);
        $existingProduct->setEnabled(true);
        $existingProduct->addChannel($channels['dok_nl']);
        $existingProduct->addChannel($channels['dok_gb']);
        $existingProduct->addChannel($channels['dok_dk']);

        $productImage = new ProductImage();
        $productImage->setType('nl');
        $productImage->setPath($this->createS3FileUrl('awesome-image-nl.jpg'));
        $existingProduct->addImage($productImage);
        $productImage = new ProductImage();
        $productImage->setType('sv');
        $productImage->setPath($this->createS3FileUrl('awesome-image-sv.jpg'));
        $existingProduct->addImage($productImage);

        $consultProducts = [];
        foreach (self::CONSULT_CODES as $consultCode) {
            $consultProducts[$consultCode] = ProductFactory::createPrefilled(
                ['code' => $consultCode],
                ProductType::CONSULT
            );
        }

        $productTypeAttribute = ProductFactory::createProductAttribute([
            'code' => 'type',
            'type' => 'select',
            'storage_type' => 'json',
        ]);

        $productAddictiveAttribute = ProductFactory::createProductAttribute([
            'code' => 'addictive',
            'type' => 'checkbox',
            'storageType' => 'boolean',
        ]);

        $productAssociationType = new ProductAssociationType();
        $productAssociationType->setCode('consult_products');
        $productAssociationType->setCurrentLocale('en');
        $productAssociationType->setFallbackLocale('en');
        $productAssociationType->setName('Consult Products');

        $taxonErectileDysfunction = TaxonFactory::createPrefilled(['code' => 'erectile_dysfunction']);
        $taxonAlcoholAddiction = TaxonFactory::createPrefilled(['code' => 'consult_alcohol_addiction']);

        if ($skipImageProcessor) {
            $this->messageBus->expects(self::never())
                ->method('dispatch');
        } else {
            $upsertProductImages = new UpsertProductImages($upsertProduct->getCode(), $upsertProduct->getImages(), $context);
            $this->messageBus->expects(self::once())
                ->method('dispatch')
                ->with($upsertProductImages)
                ->willReturn(new Envelope($upsertProductImages));
        }

        // Arrange repositories
        $this->channelRepository
            ->expects(self::exactly(2))
            ->method('findAllIndexedByCode')
            ->willReturn($channels);

        $this->productRepository
            ->method('findOneByCode')
            ->willReturnCallback(static function (string $code) use ($existingProduct, $consultProducts) {
                if ($code === $existingProduct->getCode()) {
                    return $existingProduct;
                }

                if (array_key_exists($code, $consultProducts)) {
                    return $consultProducts[$code];
                }

                return null;
            });

        $this->slugGenerator->method('generate')
            ->willReturnCallback(static function (string $name, string $code) {
                return str_replace(' ', '-', strtolower($name));
            });

        $this->taxonRepository
            ->method('findByCodeIndexedByCode')
            ->willReturn([
                $taxonErectileDysfunction->getCode() => $taxonErectileDysfunction,
                $taxonAlcoholAddiction->getCode() => $taxonAlcoholAddiction,
            ]);

        $this->productAttributeRepository->expects(self::exactly(2))
            ->method('findOneBy')
            ->willReturnCallback(static function (array $query) use (
                $productTypeAttribute,
                $productAddictiveAttribute
            ) {
                return match ($query) {
                    ['code' => ProductAttributeInterface::ATTRIBUTE_CODE_TYPE] => $productTypeAttribute,
                    ['code' => ProductAttributeInterface::ATTRIBUTE_CODE_ADDICTIVE] => $productAddictiveAttribute,
                };
            })
            ->with();

        $this->productAssociationTypeRepository->expects(self::once())
            ->method('findOneBy')
            ->with(['code' => 'consult_products'])
            ->willReturn($productAssociationType);

        // Arrange expected
        $expectedProduct = $this->getExpectedProduct(
            $channels,
            $productAssociationType,
            $consultProducts,
            $productAddictiveAttribute,
            $taxonErectileDysfunction,
            $taxonAlcoholAddiction
        );

        // Assert
        $this->entityManager
            ->method('persist')
            ->with(self::callback(static function (object $product) use ($expectedProduct) {
                if (!$product instanceof ProductInterface) {
                    return true;
                }
                $datetime = new DateTimeImmutable();
                $setTimestampable = static function (TimestampableInterface $association) use ($datetime) {
                    $association->setCreatedAt($datetime);
                    $association->setUpdatedAt($datetime);
                };

                $setTimestampable($expectedProduct);
                $setTimestampable($product);

                $expectedProduct->setCurrentLocale('en');
                $expectedProduct->setFallbackLocale('en');
                $product->setCurrentLocale('en');
                $product->setFallbackLocale('en');

                // @todo make it cleaner
                $expectedChannels = $expectedProduct->getChannels()->getValues();
                $actualChannels = $product->getChannels()->getValues();
                self::assertEquals($expectedChannels, $actualChannels);
                $expectedProduct->getChannels()->clear();
                $product->getChannels()->clear();

                // Set the same datetimes for the attributes...
                $setDateTimes = static function (AttributeValueInterface $attributeValue) use ($setTimestampable) {
                    $attribute = $attributeValue->getAttribute();
                    Assert::isInstanceOf($attribute, AttributeInterface::class);
                    $setTimestampable($attribute);

                    return $attributeValue;
                };

                $product->getAttributes()->map($setDateTimes);
                $expectedProduct->getAttributes()->map($setDateTimes);

                $expectedProduct->getImages()->clear();
                $product->getImages()->clear();

                $product->getAssociations()->map($setTimestampable);
                $expectedProduct->getAssociations()->map($setTimestampable);

                self::assertEquals($expectedProduct, $product);

                return true;
            }));

        $this->eventDispatcher->expects(self::exactly(2))
            ->method('dispatch')
            ->willReturnCallback(
                static function (object $event) use ($expectedProduct, $existingProduct) {
                    if ($event instanceof PreUpdateEvent) {
                        self::assertEquals($event, new PreUpdateEvent($existingProduct));
                    }

                    if ($event instanceof PostUpdateEvent) {
                        self::assertEquals($event, new PostUpdateEvent($expectedProduct));
                    }

                    return $event;
                }
            );

        // Act
        ($this->handler)($upsertProduct);
    }

    private function getExpectedProduct(
        array $channels,
        ProductAssociationTypeInterface $productAssociationType,
        array $expectedConsultProducts,
        ProductAttributeInterface $addictiveAttribute,
        TaxonInterface $taxonErectileDysfunction,
        TaxonInterface $taxonAlcoholAddiction,
    ): Product {
        $expectedProduct = new Product();
        $expectedProduct->setCode(self::CODE);
        $expectedProduct->setVariantSelectionMethod(ProductInterface::VARIANT_SELECTION_MATCH);
        $expectedProduct->setEnabled(false);
        $expectedProduct->addChannel($channels['dok_nl']);
        $expectedProduct->addChannel($channels['dok_gb']);
        $expectedProduct->addChannel($channels['dok_de']);

        foreach (self::EXPECTED_TRANSLATIONS as $translation) {
            $expectedProduct->setCurrentLocale($translation['locale']);
            $expectedProduct->setFallbackLocale($translation['locale']);
            $expectedProduct->setName($translation['name']);
            $expectedProduct->setDescription($translation['description']);
            $expectedProduct->setSlug($translation['slug']);
        }

        foreach (self::EXPECTED_IMAGES as $image) {
            $productImage = new ProductImage();
            $productImage->setOwner($expectedProduct);
            $productImage->setPath($this->createS3FileUrl($image['imageUrl']));
            $productImage->setType($image['locale']);
            $expectedProduct->addImage($productImage);
        }

        $expectedProduct->addAttribute(
            ProductFactory::createProductAttributeValue(
                [
                    'product' => $expectedProduct,
                    'value' => [ProductType::MEDICATION->value],
                    'attribute' => ProductFactory::createProductAttribute(
                        [
                            'code' => 'type',
                            'type' => 'select',
                            'storageType' => 'json',
                        ]
                    ),
                ]
            )
        );

        $productAssociation = new ProductAssociation();
        $productAssociation->setType($productAssociationType);
        $productAssociation->setOwner($expectedProduct);
        foreach ($expectedConsultProducts as $expectedConsultProduct) {
            $productAssociation->addAssociatedProduct($expectedConsultProduct);
        }

        $productAddictiveAttributeValue = new ProductAttributeValue();
        $productAddictiveAttributeValue->setAttribute($addictiveAttribute);
        $productAddictiveAttributeValue->setValue(true);

        $expectedProduct->addAssociation($productAssociation);
        $expectedProduct->addAttribute($productAddictiveAttributeValue);

        $productTaxon = new SyliusProductTaxon();
        $productTaxon->setTaxon($taxonErectileDysfunction);
        foreach (['dok_nl', 'dok_gb', 'dok_de', 'dok_dk'] as $channelCode) {
            $productTaxonChannel = new ProductTaxonChannel();
            $productTaxonChannel->setProductTaxon($productTaxon);
            $productTaxonChannel->setChannel($channels[$channelCode]);
            $productTaxon->addProductTaxonChannel($productTaxonChannel);
        }
        $expectedProduct->addProductTaxon($productTaxon);
        $expectedProduct->setMainTaxon($taxonErectileDysfunction);

        $productTaxon = new SyliusProductTaxon();
        $productTaxon->setTaxon($taxonAlcoholAddiction);
        foreach (['dok_nl', 'dok_gb'] as $channelCode) {
            $productTaxonChannel = new ProductTaxonChannel();
            $productTaxonChannel->setProductTaxon($productTaxon);
            $productTaxonChannel->setChannel($channels[$channelCode]);
            $productTaxon->addProductTaxonChannel($productTaxonChannel);
        }
        $expectedProduct->addProductTaxon($productTaxon);

        return $expectedProduct;
    }

    private function getChannels(): array
    {
        $channels = [];
        foreach (self::CHANNEL_CODES as $code) {
            $channels[$code] = ChannelFactory::createPrefilled(['code' => $code]);
        }

        return $channels;
    }

    private function createS3FileUrl(string $imageUrl): string
    {
        return 'https://localhost/images/'.hash('sha256', $imageUrl).'.jpg';
    }
}
