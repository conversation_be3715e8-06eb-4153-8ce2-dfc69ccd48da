<?php

declare(strict_types=1);

namespace App\Tests\Catalog\MessageHandler;

use App\Catalog\Loader\Context;
use App\Catalog\Loader\OriginatingClient;
use App\Catalog\Message\ChannelCollection;
use App\Catalog\Message\ChannelPricing as UpsertChannelPricing;
use App\Catalog\Message\ChannelPricingCollection;
use App\Catalog\Message\LeafletCollection;
use App\Catalog\Message\Price;
use App\Catalog\Message\ProductVariant\Amount;
use App\Catalog\Message\ProductVariant\ProductOptions;
use App\Catalog\Message\ProductVariant\Translation;
use App\Catalog\Message\ProductVariant\UpsertProductVariant;
use App\Catalog\Message\TranslationCollection;
use App\Catalog\Message\UpsertLeaflet;
use App\Catalog\ProductOptionBuilder;
use App\Entity\Addressing\Country;
use App\Entity\Channel\Channel;
use App\Entity\Channel\ChannelPricing;
use App\Entity\Product\ProductInterface;
use App\Entity\Product\ProductOption;
use App\Entity\Product\ProductOptionValue;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariant;
use App\Entity\Product\ProductVariantTranslation;
use App\Entity\Shipping\ShippingCategory;
use App\Entity\Supplier\SupplierInterface;
use App\Event\Product\AbstractUpdateEvent;
use App\Event\Product\PostUpdateEvent;
use App\Event\Product\PreUpdateEvent;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\ChannelPricingFactory;
use App\Tests\Util\Factory\CountryFactory;
use App\Tests\Util\Factory\CurrencyFactory;
use App\Tests\Util\Factory\ProductFactory;
use App\Tests\Util\Factory\ProductVariantFactory as AppProductVariantFactory;
use App\Tests\Util\Factory\SupplierFactory;
use DateTimeImmutable;
use Ramsey\Uuid\Uuid;
use ReflectionObject;
use Symfony\Component\Messenger\Envelope;

final class CreateProductVariantWithUpsertProductVariantHandlerTest extends AbstractUpsertProductVariantHandlerTest
{
    protected const string PRODUCT_CODE = '7';
    protected const string PRODUCT_VARIANT_CODE = '7_5_prime_pharmacy_de';

    protected const array EXPECTED_TRANSLATIONS = [
        [
            'locale' => 'nl',
            'name' => 'Viagra 25 mg 4 tabl.',
            'defaultUsageAdvice' => 'Neem 1 tablet 30 tot 60 minuten vóór seksuele activiteit. Neem niet meer dan 1 tablet per dag. Neem binnen 3 dagen geen andere medicijnen voor een erectiestoornis. Lees de bijsluiter voor meer informatie.',
            'leafletUrl' => 'https://www.example.com/leaflet/nl/7_5_prime_pharmacy_nl',
        ],
        [
            'locale' => 'de',
            'name' => 'Viagra 25 mg 4 Tabl.',
            'defaultUsageAdvice' => 'Nehmen Sie 1 Tabl. 30 - 60 Min. vor sexueller Aktitivtät ein. Nehmen Sie nicht mehr als 1 Tabl. pro Tag ein. Nehmen Sie innerhalb von 3 Tagen keine anderen Medikamente gegen Erektionsstörung ein. Bitte lesen Sie den Beipackzettel für mehr Infos.',
            'leafletUrl' => 'https://www.example.com/leaflet/de/7_5_prime_pharmacy_de',
        ],
        [
            'locale' => 'fr',
            'name' => 'Viagra 25 mg 4 compr.',
            'defaultUsageAdvice' => 'Prenez 1 comprimé environ 30 à 60 minutes avant l\'activité sexuelle . Ne prenez pas plus d\'un comprimé par jour. Ne prenez pas d\'autres médicaments contre les troubles érectiles durant 3 jours . Lisez la notice d\'utilisation pour plus d\'informatio',
        ],
        [
            'locale' => 'it',
            'name' => 'Viagra 25 mg 4 compr.',
            'defaultUsageAdvice' => '1 compressa ogni 24 ore quando necessario',
        ],
        [
            'locale' => 'es',
            'name' => 'Viagra 25 mg 4 past.',
            'defaultUsageAdvice' => 'Tome 1 comprimido de 30 a 60 minutos antes de la actividad sexual . No tome más de 1 comprimido al día . Absténgase de tomar ningún otro medicamento contra la disfunción eréctil en los 3 próximos días . Consulte la información adicional del prospecto.',
        ],
        [
            'locale' => 'sv',
            'name' => 'Viagra 25 mg 4 tabl.',
            'defaultUsageAdvice' => 'Ta 1 tablett under en halvtimma till en timma innan sexuell aktivitet . Ta inte fler än 1 tabletter om dagen . Ta ingen annan medicin för erektionsproblem inom de 3 närmsta dagarna . Läs vidare information i bipacksedeln.',
        ],
        [
            'locale' => 'pl',
            'name' => 'Viagra 25 mg 4 tabl.',
            'defaultUsageAdvice' => 'Przyjmować 1 tabletkę na 30 do 60 minut przed planowaną aktywnością seksualną . Nie więcej niż 1 tabletka na dobę . W ciągu 3 dni nie należy przyjmować żadnych innych leków na zaburzenia',
        ],
        [
            'locale' => 'da',
            'name' => 'Viagra 25 mg 4 tabl.',
            'defaultUsageAdvice' => 'Tag 1 tablet 30 til 60 minutter før seksuel aktivitet . Tag ikke mere end 1 tablet om dagen . Tag ikke andre produkter til erektil dysfunktion inden for 3 dage . Læs venligst indlægssedlen for yderligere information.',
        ],
        [
            'locale' => 'fi',
            'name' => 'Viagra 25 mg 4 tabl.',
            'defaultUsageAdvice' => 'Ota VIAGRA noin tunti ennen suunnittelemaasi seksuaalista kanssakäymistä . Älä ota VIAGRA - valmistetta useammin kuin kerran päivässä',
        ],
        [
            'locale' => 'en',
            'name' => 'Viagra 25 mg 4 tabl.',
            'defaultUsageAdvice' => 'Take 1 tablet 30 to 60 minutes prior to sexual activity. Do not take more than 1 tablet a day. Do not take other products for erectile dysfunction within 3 days. Please read package leaflet for further information.',
        ],
    ];

    protected const array EXPECTED_CHANNEL_PRICING = [
        'dok_be' => ['channelCode' => 'dok_be', 'enabled' => true, 'price' => 8750],
        'dok_de' => ['channelCode' => 'dok_de', 'enabled' => true, 'price' => 6770],
        'dok_at' => ['channelCode' => 'dok_at', 'enabled' => true, 'price' => 6770],
        'dok_dk' => ['channelCode' => 'dok_dk', 'enabled' => true, 'price' => 17580],
        // Should be converted from EUR to DKK
        'dok_nl' => ['channelCode' => 'dok_nl', 'enabled' => true, 'price' => 8970],
        'dok_ro' => ['channelCode' => 'dok_ro', 'enabled' => true, 'price' => 8490],
        // No conversion between RON and RON
        'blueclinic_nl' => ['channelCode' => 'blueclinic_nl', 'enabled' => true, 'price' => 8970],
        'dok_lu' => ['channelCode' => 'dok_lu', 'enabled' => false, 'price' => 6770],
        'dok_ch' => ['channelCode' => 'dok_ch', 'enabled' => false, 'price' => 6770],
        'dok_gb' => ['channelCode' => 'dok_gb', 'enabled' => false, 'price' => 9850],
        'dok_fr' => ['channelCode' => 'dok_fr', 'enabled' => false, 'price' => 8990],
        'dok_se' => ['channelCode' => 'dok_se', 'enabled' => false, 'price' => 8570],
        'dok_pl' => ['channelCode' => 'dok_pl', 'enabled' => false, 'price' => 8520],
        'dok_pt' => ['channelCode' => 'dok_pt', 'enabled' => false, 'price' => 9470],
        'dok_fi' => ['channelCode' => 'dok_fi', 'enabled' => false, 'price' => 8570],
    ];

    /**
     * @return iterable<array{0: Context, 1: bool}>
     */
    public function contextDataProvider(): iterable
    {

        yield 'Execute the leaflets processor' => [new Context(originatingClient: OriginatingClient::default()->value, importTag: Uuid::uuid7(), skipLeaflets: false), false];
        yield 'Skip the leaflets processor' => [new Context(originatingClient: OriginatingClient::default()->value, importTag: Uuid::uuid7(), skipLeaflets: true), true];
    }

    /**
     * @dataProvider contextDataProvider
     */
    public function testCreateNewProductVariant(Context $context, bool $skipLeaflets): void
    {
        // Arrange objects
        $product = ProductFactory::create(
            [
                'code' => self::PRODUCT_CODE,
                'attributes' => [
                    ProductFactory::createProductAttributeValue(
                        [
                            'value' => [ProductType::MEDICATION],
                            'attribute' => ProductFactory::createProductAttribute(
                                [
                                    'code' => 'type',
                                    'type' => 'select',
                                    'storageType' => 'json',
                                ]
                            ),
                        ]
                    ),
                ],
            ]
        );

        $supplier = SupplierFactory::createPrefilled(['identifier' => 'prime-pharmacy']);
        $shippingCategory = new ShippingCategory();
        $shippingCategory->setCode(ShippingCategory::SHIPPING_CATEGORY_RX_MEDICATION);
        $country = CountryFactory::create(['code' => 'DE']);

        [
            $formProductOption,
            $formProductOptionValue,
            $dosageProductOption,
            $dosageProductOptionValue,
            $packsizeProductOption,
            $packsizeProductOptionValue,
        ] = $this->arrangeProductOptions();

        $channels = $this->arrangeChannels();

        // Arrange repositories and services
        $this->channelRepository
            ->method('findOneByCode')
            ->willReturnCallback(
                static function (string $code) use ($channels) {
                    return $channels[$code] ?? null;
                }
            );

        $this->productRepository
            ->expects(self::once())
            ->method('findOneBy')
            ->willReturn($product);

        $this->supplierRepository
            ->method('findOneBy')
            ->with(['identifier' => 'prime-pharmacy'])
            ->willReturn($supplier);

        $this->shippingCategoryRepository
            ->method('findOneByCode')
            ->with(ShippingCategory::SHIPPING_CATEGORY_RX_MEDICATION)
            ->willReturn($shippingCategory);

        $this->countryRepository
            ->method('findOneBy')
            ->with(['code' => $country->getCode()])
            ->willReturn($country);

        $this->currencyConverter
            ->expects(self::never())
            ->method('convert');

        $this->currencyConverter
            ->expects(self::exactly(15))
            ->method('convertForChannel')
            ->willReturnCallback(
                static function (
                    int $price,
                    string $sourceCurrencyCode,
                    Channel $targetChannel,
                ) {
                    $targetCurrency = $targetChannel->getBaseCurrency()?->getCode();

                    return match ("$sourceCurrencyCode-$targetCurrency") {
                        'EUR-EUR', 'RON-RON' => $price,
                        'EUR-DKK' => $price * 2,
                        'DKK-EUR' => $price / 2,
                    };
                }
            );

        $this->productOptionBuilder
            ->expects(self::exactly(3))
            ->method('fetchOrCreateProductOption')
            ->willReturnCallback(
                static function ($code, $name) use (
                    $formProductOption,
                    $dosageProductOption,
                    $packsizeProductOption
                ) {
                    return match ($code) {
                        ProductOptionBuilder::FORM_PRODUCT_OPTION_CODE => $formProductOption,
                        '7_dosage' => $dosageProductOption,
                        '7_packsize' => $packsizeProductOption,
                    };
                }
            );

        $this->productOptionBuilder
            ->expects(self::exactly(3))
            ->method('fetchOrCreateProductOptionValue')
            ->willReturnCallback(
                static function ($code, $name) use (
                    $formProductOptionValue,
                    $dosageProductOptionValue,
                    $packsizeProductOptionValue
                ) {
                    return match ($code) {
                        'tablet' => $formProductOptionValue,
                        '7_dosage_25_mg' => $dosageProductOptionValue,
                        '7_packsize_4_pieces' => $packsizeProductOptionValue,
                    };
                }
            );

        // Arrange input
        $upsert = $this->createUpsertProductVariantMessage($context);

        // Arrange Expected output
        $expectedProductVariant = $this->createExpectedProductVariant(
            $product,
            $supplier,
            $shippingCategory,
            $country,
            [
                $formProductOptionValue,
                $dosageProductOptionValue,
                $packsizeProductOptionValue,
            ]
        );

        // Assert
        $calledCount = 0;
        $this->entityManager
            ->method('persist')
            ->with(
                self::callback(
                    static function (ProductVariant|ChannelPricing $object) use (
                        $expectedProductVariant,
                        $calledCount
                    ): bool {
                        if (!$object instanceof ProductVariant) {
                            return true;
                        }
                        ++$calledCount;

                        $dateTime = new DateTimeImmutable();
                        $object->setCreatedAt($dateTime);
                        $object->setUpdatedAt($dateTime);

                        $expectedProductVariant->setCreatedAt($dateTime);
                        $expectedProductVariant->setUpdatedAt($dateTime);

                        $object->setFallbackLocale('en');
                        $expectedProductVariant->setFallbackLocale('en');


                        $refObject = new ReflectionObject($object);
                        $refProperty = $refObject->getProperty('id');
                        $refProperty->setValue($object, 7);

                        if ($calledCount > 1) {
                            self::assertEquals($expectedProductVariant, $object);
                        }

                        return true;
                    }
                )
            );

        if ($skipLeaflets) {
            $this->messageBus->expects(self::never())->method('dispatch');
        } else {
            $this->messageBus->expects(self::exactly(2))
                ->method('dispatch')
                ->willReturn(new Envelope(new UpsertLeaflet('de', 'dummy', OriginatingClient::Katana)));
        }

        $expectedCount = (count(self::EXPECTED_CHANNEL_PRICING) * 2) + 2;
        $invocation = self::exactly($expectedCount);
        $this->eventDispatcher
            ->expects($invocation)
            ->method('dispatch')
            ->with(
                $this->callback(
                    static function (AbstractUpdateEvent $event) use ($invocation, $expectedCount) {
                        $invocationCount = $invocation->getInvocationCount();

                        // The first event must be the PreUpdateEvent for the ProductVariant
                        if ($invocationCount === 1) {
                            self::assertInstanceOf(PreUpdateEvent::class, $event);
                            self::assertInstanceOf(ProductVariant::class, $event->getEntity());

                            return true;
                        }

                        // The final event must be the PostUpdateEvent for the ProductVariant
                        if ($invocationCount === $expectedCount) {
                            self::assertInstanceOf(PostUpdateEvent::class, $event);
                            self::assertInstanceOf(ProductVariant::class, $event->getEntity());

                            return true;
                        }

                        // The rest of the events must be ChannelPricing events
                        self::assertInstanceOf(ChannelPricing::class, $event->getEntity());
                        self::assertInstanceOf(
                            $expectedCount / 2 >= $invocationCount
                                ? PreUpdateEvent::class // The first half of the events must be PreUpdateEvent
                                : PostUpdateEvent::class, // The second half of the events must be PostUpdateEvent
                            $event
                        );

                        return true;
                    }
                )
            );

        // Act
        ($this->handler)($upsert);
    }

    private function arrangeExpectedChannelPricing(): array
    {
        $pricing = [];
        foreach (self::EXPECTED_CHANNEL_PRICING as $channelData) {
            $pricing[] = ChannelPricingFactory::create($channelData);
        }

        return $pricing;
    }

    private function arrangeChannels(): array
    {
        $channelCodes = [
            ['code' => 'dok_be', 'currency' => 'EUR', 'addPrescriptionMedicationDirectlyToCart' => false],
            ['code' => 'dok_de', 'currency' => 'EUR', 'addPrescriptionMedicationDirectlyToCart' => false],
            ['code' => 'dok_at', 'currency' => 'EUR', 'addPrescriptionMedicationDirectlyToCart' => false],
            ['code' => 'dok_lu', 'currency' => 'EUR', 'addPrescriptionMedicationDirectlyToCart' => false],
            ['code' => 'dok_ch', 'currency' => 'EUR', 'addPrescriptionMedicationDirectlyToCart' => false],
            ['code' => 'dok_gb', 'currency' => 'EUR', 'addPrescriptionMedicationDirectlyToCart' => false],
            ['code' => 'dok_fr', 'currency' => 'EUR', 'addPrescriptionMedicationDirectlyToCart' => false],
            ['code' => 'dok_se', 'currency' => 'EUR', 'addPrescriptionMedicationDirectlyToCart' => false],
            ['code' => 'dok_pl', 'currency' => 'EUR', 'addPrescriptionMedicationDirectlyToCart' => false],
            ['code' => 'dok_dk', 'currency' => 'DKK', 'addPrescriptionMedicationDirectlyToCart' => false],
            ['code' => 'dok_pt', 'currency' => 'EUR', 'addPrescriptionMedicationDirectlyToCart' => false],
            ['code' => 'blueclinic_nl', 'currency' => 'EUR', 'addPrescriptionMedicationDirectlyToCart' => true],
            ['code' => 'dok_nl', 'currency' => 'EUR', 'addPrescriptionMedicationDirectlyToCart' => false],
            ['code' => 'dok_fi', 'currency' => 'EUR', 'addPrescriptionMedicationDirectlyToCart' => false],
            ['code' => 'dok_ro', 'currency' => 'RON', 'addPrescriptionMedicationDirectlyToCart' => false],
        ];

        $channels = [];
        foreach ($channelCodes as $data) {
            $currency = CurrencyFactory::create(['code' => $data['currency']]);
            $data['currencies'] = [$currency];
            $data['baseCurrency'] = $currency;
            $channels[$data['code']] = ChannelFactory::createPrefilled($data);
        }

        return $channels;
    }

    /**
     * @return array<ProductOption|ProductOptionValue>
     */
    private function arrangeProductOptions(): array
    {
        $formProductOption = new ProductOption();
        $formProductOption->setCode('form');
        $formProductOptionValue = new ProductOptionValue();
        $formProductOptionValue->setOption($formProductOption);
        $formProductOptionValue->setCode('tablet');
        $formProductOption->addValue($formProductOptionValue);

        $dosageProductOption = new ProductOption();
        $dosageProductOption->setCode('7_dosage');
        $dosageProductOptionValue = new ProductOptionValue();
        $dosageProductOptionValue->setOption($formProductOption);
        $dosageProductOptionValue->setCode('7_dosage_25_mg');
        $dosageProductOption->addValue($formProductOptionValue);

        $packsizeProductOption = new ProductOption();
        $packsizeProductOption->setCode('7_packsize');
        $packsizeProductOptionValue = new ProductOptionValue();
        $packsizeProductOptionValue->setOption($formProductOption);
        $packsizeProductOptionValue->setCode('7_packsize_4_pieces');
        $packsizeProductOption->addValue($formProductOptionValue);

        return [
            $formProductOption,
            $formProductOptionValue,
            $dosageProductOption,
            $dosageProductOptionValue,
            $packsizeProductOption,
            $packsizeProductOptionValue,
        ];
    }

    private function arrangeExpectedTranslations(): array
    {
        $translations = [];
        foreach (self::EXPECTED_TRANSLATIONS as $translationData) {
            $translation = new ProductVariantTranslation();
            $translation->setLocale($translationData['locale']);
            $translation->setName($translationData['name']);
            $translation->setDefaultUsageAdvice($translationData['defaultUsageAdvice']);
            $translation->setLeaflet($translationData['leafletUrl'] ?? '');
            $translations[] = $translation;
        }

        return $translations;
    }

    private function createUpsertProductVariantMessage(Context $context): UpsertProductVariant
    {
        $translations = [];
        foreach (self::EXPECTED_TRANSLATIONS as $translationData) {
            unset($translationData['leafletUrl']);
            $translations[] = new Translation(...$translationData);
        }

        $upsert = new UpsertProductVariant(
            code: self::PRODUCT_VARIANT_CODE,
            productCode: self::PRODUCT_CODE,
            enabled: true,
            translations: new TranslationCollection(...$translations),
            channels: new ChannelCollection(
                'dok_be',
                'dok_de',
                'dok_at',
                'dok_dk',
                'dok_nl',
                'dok_ro',
                'blueclinic_nl',
                'dok_lu',
                'dok_ch',
                'dok_gb',
                'dok_fr',
                'dok_se',
                'dok_pl',
                'dok_pt',
                'dok_fi',
            ),
            channelPricings: new ChannelPricingCollection(
                new UpsertChannelPricing('dok_be', true, new Price(8750, 'EUR')),
                new UpsertChannelPricing('dok_de', true, new Price(6770, 'EUR')),
                new UpsertChannelPricing('dok_at', true, new Price(13540, 'DKK')),
                new UpsertChannelPricing('dok_dk', true, new Price(8790, 'EUR')), // Channel has DKK currency
                new UpsertChannelPricing('dok_nl', true, new Price(8970, 'EUR')),
                new UpsertChannelPricing('dok_ro', true, new Price(8490, 'RON')), // Channel has RON currency
                new UpsertChannelPricing('blueclinic_nl', true, new Price(8970, 'EUR')),
                new UpsertChannelPricing('dok_lu', false, new Price(6770, 'EUR')),
                new UpsertChannelPricing('dok_ch', false, new Price(6770, 'EUR')),
                new UpsertChannelPricing('dok_gb', false, new Price(9850, 'EUR')),
                new UpsertChannelPricing('dok_fr', false, new Price(8990, 'EUR')),
                new UpsertChannelPricing('dok_se', false, new Price(8570, 'EUR')),
                new UpsertChannelPricing('dok_pl', false, new Price(8520, 'EUR')),
                new UpsertChannelPricing('dok_pt', false, new Price(9470, 'EUR')),
                new UpsertChannelPricing('dok_fi', false, new Price(8570, 'EUR')),
            ),
            costPrice: new Price(1429, 'EUR'),
            supplierVariantName: 'Viagra 25 mg 4 tabl.',
            supplierVariantCode: 'MCUKED151',
            prescriptionRequired: true,
            maximumQuantityPerOrder: 8,
            supplierCode: 'prime-pharmacy',
            preferredSupplier: false,
            quantityMultiplier: 1,
            leaflets: new LeafletCollection(
                new UpsertLeaflet('nl', 'https://www.example.com/leaflet/nl/7_5_prime_pharmacy_nl', OriginatingClient::Katana),
                new UpsertLeaflet('de', 'https://www.example.com/leaflet/de/7_5_prime_pharmacy_de', OriginatingClient::Katana),
            ),
            productOptions: new ProductOptions(
                form: 'tablet',
                dosage: new Amount('25', 'mg'),
                packSize: new Amount('4', 'pieces')
            ),
            preferredVariantForMinimumDailyOrders: null,
            zIndex: '14223244'
        );

        $upsert->setContext($context);

        return $upsert;
    }

    private function createExpectedProductVariant(
        ProductInterface $product,
        SupplierInterface $supplier,
        ShippingCategory $shippingCategory,
        Country $country,
        array $optionValues,
    ): ProductVariant {
        return AppProductVariantFactory::create(
            [
                'id' => 7,
                'code' => self::PRODUCT_VARIANT_CODE,
                'product' => $product,
                'supplier' => $supplier,
                'prescriptionRequired' => true,
                'maximumQuantityPerOrder' => 8,
                'preferredSupplier' => false,
                'preferredVariantForMinimumDailyOrders' => null,
                'supplierVariantName' => 'Viagra 25 mg 4 tabl.',
                'supplierVariantCode' => 'MCUKED151',
                'costPrice' => 1429,
                'attributes' => [
                    'zIndex' => '14223244',
                ],
                'shippingCategory' => $shippingCategory,
                'country' => $country,
                'optionValues' => $optionValues,
                'channelPricings' => $this->arrangeExpectedChannelPricing(),
                'translations' => $this->arrangeExpectedTranslations(),
                'version' => 1,
                'onHold' => 0,
                'onHand' => 0,
            ]
        );
    }
}
