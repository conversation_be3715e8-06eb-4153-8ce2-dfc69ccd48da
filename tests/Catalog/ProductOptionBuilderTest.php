<?php

declare(strict_types=1);

namespace App\Tests\Catalog;

use App\Catalog\Exception\ProductOptionValueNotFoundException;
use App\Catalog\ProductOptionBuilder;
use App\Entity\Product\ProductOptionValue;
use App\Entity\Product\ProductOptionValueTranslation;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Bundle\CoreBundle\Doctrine\ORM\ProductOptionRepository;
use Sylius\Component\Product\Model\ProductOption;
use Sylius\Resource\Doctrine\Persistence\RepositoryInterface;
use Sylius\Resource\Factory\FactoryInterface;

final class ProductOptionBuilderTest extends TestCase
{
    private const string OPTION_CODE = '7_dosage';
    private const string OPTION_NAME = 'dosage';
    private const string OPTION_VALUE_CODE = '7_dosage_25_mg';
    private const string GENERIC_DOSAGE_UNIT = 'generic_dosage_unit';
    private const string VALUE_WITHOUT_UNIT = '25';
    private const string UNIT_OF_MEASUREMENT = 'mg';

    private ProductOptionBuilder $productOptionBuilder;
    /** @var ProductOptionRepository<ProductOption>&MockObject */
    private ProductOptionRepository&MockObject $productOptionRepository;
    /** @var FactoryInterface<ProductOption>&MockObject */
    private FactoryInterface&MockObject $productOptionFactory;
    /** @var RepositoryInterface<ProductOptionValue>&MockObject */
    private RepositoryInterface&MockObject $productOptionValueRepository;
    /** @var FactoryInterface<ProductOptionValue>&MockObject */
    private FactoryInterface&MockObject $productOptionValueFactory;
    private EntityManagerInterface&MockObject $entityManager;

    protected function setUp(): void
    {
        $this->productOptionRepository = $this->createMock(ProductOptionRepository::class);
        $this->productOptionFactory = $this->createMock(FactoryInterface::class);
        $this->productOptionValueRepository = $this->createMock(RepositoryInterface::class);
        $this->productOptionValueFactory = $this->createMock(FactoryInterface::class);
        $this->entityManager = $this->createMock(EntityManagerInterface::class);

        $this->productOptionBuilder = new ProductOptionBuilder(
            $this->productOptionRepository,
            $this->productOptionFactory,
            $this->createMock(RepositoryInterface::class),
            $this->productOptionValueRepository,
            $this->productOptionValueFactory,
            $this->entityManager
        );
    }

    public function testCreateProductOption(): void
    {
        $productOption = new ProductOption();
        $productOption->setCurrentLocale('en');
        $productOption->setFallbackLocale('en');

        $this->productOptionRepository->expects(self::once())
            ->method('findOneBy')
            ->with(['code' => self::OPTION_CODE])
            ->willReturn(null);

        $this->productOptionFactory->expects(self::once())
            ->method('createNew')
            ->willReturn($productOption);

        $this->entityManager->expects(self::once())
            ->method('persist')
            ->with($productOption);

        $result = $this->productOptionBuilder->fetchOrCreateProductOption(self::OPTION_CODE, self::OPTION_NAME);

        $expectedOption = $this->createProductOption(self::OPTION_CODE, self::OPTION_NAME);

        self::assertSame($expectedOption->getCode(), $result->getCode());
        self::assertSame($expectedOption->getName(), $result->getName());
    }

    public function testFetchProductOption(): void
    {
        $productOption = $this->createProductOption(self::OPTION_CODE, self::OPTION_NAME);

        $this->productOptionRepository->expects(self::once())
            ->method('findOneBy')
            ->with(['code' => self::OPTION_CODE])
            ->willReturn($productOption);

        $this->productOptionFactory->expects($this->never())
            ->method('createNew');

        $this->entityManager->expects($this->never())
            ->method('persist');

        $this->entityManager->expects($this->never())
            ->method('flush');

        $result = $this->productOptionBuilder->fetchOrCreateProductOption(self::OPTION_CODE, self::OPTION_NAME);

        self::assertEquals($productOption, $result);
    }

    public function testCreateProductOptionValue(): void
    {
        // Arrange
        $productOption = $this->createProductOption(self::OPTION_CODE, self::OPTION_NAME);

        $productOptionValue = new ProductOptionValue();
        $productOptionValue->setCurrentLocale('en');
        $productOptionValue->setFallbackLocale('en');
        $this->productOptionValueFactory->method('createNew')
            ->willReturn($productOptionValue);

        // Act
        $result = $this->productOptionBuilder->fetchOrCreateProductOptionValue(
            self::OPTION_VALUE_CODE,
            $productOption,
            self::OPTION_VALUE_CODE
        );

        // Assert
        self::assertSame(self::OPTION_VALUE_CODE, $result->getCode());
        self::assertEquals($productOption, $result->getOption());
    }

    public function testSyncProductOptionValueTranslations(): void
    {
        $genericProductOptionCode = self::GENERIC_DOSAGE_UNIT;
        $genericProductOptionValueCode = self::GENERIC_DOSAGE_UNIT.'_mg';

        $productOption = $this->createProductOption(self::OPTION_CODE, self::OPTION_NAME);
        $productOptionValue = $this->createProductOptionValue(self::OPTION_VALUE_CODE, $productOption);

        $genericProductOption = $this->createProductOption($genericProductOptionCode, self::OPTION_NAME);
        $genericProductOptionValue = $this->createProductOptionValue($genericProductOptionValueCode, $genericProductOption);

        foreach (['en', 'de', 'nl'] as $locale) {
            $translation = new ProductOptionValueTranslation();
            $translation->setLocale($locale);
            $translation->setValue($genericProductOptionValueCode);

            $genericProductOptionValue->addTranslation($translation);
        }

        $this->productOptionValueRepository->expects(self::once())
            ->method('findOneBy')
            ->with(['code' => $genericProductOptionValueCode])
            ->willReturn($genericProductOptionValue);

        $this->entityManager->expects(self::exactly(3))
            ->method('persist');

        $this->productOptionBuilder->syncProductOptionValueTranslations(
            self::VALUE_WITHOUT_UNIT,
            self::UNIT_OF_MEASUREMENT,
            $productOptionValue,
            $genericProductOptionCode
        );
    }

    public function testSyncProductOptionValueTranslationsThrowsException(): void
    {
        $expectedGenericProductOptionValueCode = self::GENERIC_DOSAGE_UNIT.'_mg';

        $this->expectException(ProductOptionValueNotFoundException::class);
        $this->expectExceptionMessage("Generic product option value with code '$expectedGenericProductOptionValueCode' not found");

        $this->productOptionBuilder->syncProductOptionValueTranslations(
            '25',
            'mg',
            new ProductOptionValue(),
            self::GENERIC_DOSAGE_UNIT
        );
    }

    private function createProductOption(string $optionCode, string $name): ProductOption
    {
        $productOption = new ProductOption();
        $productOption->setCurrentLocale('en');
        $productOption->setFallbackLocale('en');
        $productOption->setCode($optionCode);
        $productOption->setName($name);

        return $productOption;
    }

    private function createProductOptionValue(string $valueCode, ProductOption $productOption): ProductOptionValue
    {
        $productOptionValue = new ProductOptionValue();
        $productOptionValue->setCurrentLocale('en');
        $productOptionValue->setFallbackLocale('en');
        $productOptionValue->setCode($valueCode);
        $productOptionValue->setValue($valueCode);
        $productOptionValue->setOption($productOption);

        return $productOptionValue;
    }
}
