<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Message;

use App\Catalog\Message\Price;
use InvalidArgumentException;
use PHPUnit\Framework\TestCase;

final class PriceTest extends TestCase
{
    /**
     * @dataProvider provideValidFloatAmount
     * @dataProvider provideValidFloatAsStringAmount
     */
    public function testValidFromFloatOrFloatAsStringAmount(float|string $amount, int $expectedAmount): void
    {
        $actualPrice = Price::fromFloatAmount($amount);

        self::assertSame($expectedAmount, $actualPrice->amount);
        self::assertSame('EUR', $actualPrice->currency);
    }

    /**
     * @return iterable<string, list<float|int>>
     */
    public function provideValidFloatAmount(): iterable
    {
        yield 'with valid float amount 0' => [0, 0];
        yield 'with valid float amount 1' => [1, 100];
        yield 'with valid float amount 1.2' => [1.2, 120];
        yield 'with valid float amount 1.23' => [1.23, 123];
        yield 'with valid float amount 1.234' => [1.234, 123];
        yield 'with valid float amount 10.23' => [10.23, 1023];
        yield 'with valid float amount 10.231' => [10.231, 1023];
        yield 'with valid float amount 10.235' => [10.235, 1024];
        yield 'with valid float amount 10.239' => [10.239, 1024];
        yield 'with valid float amount 100' => [100, 10000];
        yield 'with valid float amount 101.23' => [101.23, 10123];
        yield 'with valid float amount 101.231' => [101.231, 10123];
    }

    /**
     * @return iterable<string, list<string|int>>
     */
    public function provideValidFloatAsStringAmount(): iterable
    {
        yield 'with valid string amount 0' => ['0', 0];
        yield 'with valid string amount 1' => ['1', 100];
        yield 'with valid string amount 1.2' => ['1.2', 120];
        yield 'with valid string amount 1.23' => ['1.23', 123];
        yield 'with valid string amount 1.234' => ['1.234', 123];
        yield 'with valid string amount 10.23' => ['10.23', 1023];
        yield 'with valid string amount 10.231' => ['10.231', 1023];
        yield 'with valid string amount 10.235' => ['10.235', 1024];
        yield 'with valid string amount 10.239' => ['10.239', 1024];
        yield 'with valid string amount 100' => ['100', 10000];
        yield 'with valid string amount 101.23' => ['101.23', 10123];
        yield 'with valid string amount 101.231' => ['101.231', 10123];
    }

    /**
     * @dataProvider provideInvalidFloatAmountOrFloatAsStringAmountOrInvalidCurrency
     */
    public function testInvalidArgumentExceptionFromFloatAmount(
        float|string $amount,
        string $currency,
        string $expectedExceptionMessage,
    ): void {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage($expectedExceptionMessage);

        Price::fromFloatAmount($amount, $currency);
    }

    /**
     * @return iterable<string, list<string|int>>
     */
    public function provideInvalidFloatAmountOrFloatAsStringAmountOrInvalidCurrency(): iterable
    {
        // invalid float amount
        yield 'with float amount' => [-100, 'EUR', 'Expected cent amount to be greater or equal to zero.'];

        // invalid string amount
        yield 'with invalid empty string' => ['', 'EUR', 'Expected amount to be a numeric value.'];
        yield 'with negative string amount' => ['-100', 'EUR', 'Expected cent amount to be greater or equal to zero.'];

        // invalid currency
        yield 'with too long currency length' => [100, 'EURO', 'Expected currency to be a three-letter string.'];
        yield 'with too short currency length' => [100, 'EU', 'Expected currency to be a three-letter string.'];
        yield 'with lower case currency' => [100, 'eur', 'Expected currency to be upper case.'];
    }
}
