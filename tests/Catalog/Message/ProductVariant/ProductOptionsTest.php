<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Message\ProductVariant;

use App\Catalog\Message\ProductVariant\Amount;
use App\Catalog\Message\ProductVariant\ProductOptions;
use PHPUnit\Framework\TestCase;

final class ProductOptionsTest extends TestCase
{
    public function testItCreatesProductOptions(): void
    {
        $productOptions = new ProductOptions('form', new Amount('1', 'mg'), new Amount('2', 'ml'));

        self::assertSame('form', $productOptions->form);
        self::assertSame('1', $productOptions->dosage->amount);
        self::assertSame('mg', $productOptions->dosage->unit);
        self::assertSame('2', $productOptions->packSize->amount);
        self::assertSame('ml', $productOptions->packSize->unit);
    }
}
