<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Attachment;

use App\Catalog\Attachment\Downloader;
use App\Catalog\Attachment\DownloaderInterface;
use App\Tests\Util\Factory\Catalog\Messenger\UpsertAttachmentFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class DownloaderTest extends TestCase
{
    private DownloaderInterface&MockObject $downloader1;
    private DownloaderInterface&MockObject $downloader2;
    private DownloaderInterface&MockObject $downloader3;

    private Downloader $command;

    protected function setUp(): void
    {
        $this->downloader1 = $this->createMock(DownloaderInterface::class);
        $this->downloader2 = $this->createMock(DownloaderInterface::class);
        $this->downloader3 = $this->createMock(DownloaderInterface::class);

        $this->command = new Downloader([$this->downloader1, $this->downloader2, $this->downloader3]);
    }

    public function testItCanLoadMultipleLoaders(): void
    {
        // Assert
        $this->downloader1->expects(self::once())->method('supports')->willReturn(false);
        $this->downloader1->expects(self::never())->method('download');

        $this->downloader2->expects(self::once())->method('supports')->willReturn(true);
        $this->downloader2->expects(self::once())->method('download');

        $this->downloader3->expects(self::never())->method('supports')->willReturn(false);
        $this->downloader3->expects(self::never())->method('download');

        // Act
        $this->command->download(UpsertAttachmentFactory::createImage());
    }
}
