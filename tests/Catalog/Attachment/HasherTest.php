<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Attachment;

use App\Catalog\Attachment\Hasher;
use PHPUnit\Framework\TestCase;

final class HasherTest extends TestCase
{
    private Hasher $hasher;

    protected function setUp(): void
    {
        $this->hasher = new Hasher();
    }

    /**
     * @dataProvider hasherDataProvider
     */
    public function testItCanHash(string $contents, string $expected): void
    {
        $hash = $this->hasher->hash($contents);
        self::assertSame($expected, $hash);
    }

    protected function hasherDataProvider(): iterable
    {
        yield 'empty string' => ['', 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855'];
        yield 'string' => ['hello', '2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824'];
    }
}
