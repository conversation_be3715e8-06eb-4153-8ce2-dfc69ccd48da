<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Attachment;

use App\Catalog\Attachment\LeafletProcessor;
use App\Tests\Util\Factory\Catalog\Messenger\UpsertAttachmentFactory;

final class LeafletProcessorTest extends AbstractAttachmentProcessorTest
{
    public function getExpectedUrl(string $filename): string
    {
        return 'https://localhost/leaflets/'.$filename.'.pdf';
    }

    public function provideValidFiles(): iterable
    {
        yield 'fileContents with application/pdf' => [
            '%PDF-1.4\n%âãÏÓ\n1 0 obj\n<< /Type /Catalog >>\nendobj\n%%EOF',
            UpsertAttachmentFactory::createLeaflet(),
        ];
    }

    public function provideInvalidFiles(): iterable
    {
        yield 'fileContents with application/zip' => [
            (string) hex2bin('504B030414000000080000002100000000000000000000000000000000000000000000504B010214000000080000002100000000000000000000000000000000000000000000000000000000000000504B050600000000010001003E0000002D0000000000'),
        ];
        yield 'fileContents with application/msword' => [
            (string) file_get_contents(__DIR__.'/Resources/valid_word_document.doc'),
        ];
        yield 'fileContents with application/vnd.openxmlformats-officedocument.wordprocessingml.document' => [
            (string) file_get_contents(__DIR__.'/Resources/valid_word_document.docx'),
        ];
    }

    protected function setProcessor(): void
    {
        $this->attachmentProcessor = new LeafletProcessor(
            $this->filesystem,
            $this->downloader,
            $this->hasher,
            'localhost'
        );
    }
}
