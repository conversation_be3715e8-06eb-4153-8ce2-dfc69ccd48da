<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Attachment;

use App\Catalog\Attachment\ImageProcessor;
use App\Tests\Util\Factory\Catalog\Messenger\UpsertAttachmentFactory;

final class ImageProcessorTest extends AbstractAttachmentProcessorTest
{
    public function getExpectedUrl(string $filename): string
    {
        return 'https://localhost/images/'.$filename.'.jpg';
    }

    public function provideValidFiles(): iterable
    {
        $attachment = UpsertAttachmentFactory::createImage();

        yield 'fileContents with image/jpeg' => [
            (string) hex2bin('FFD8FFE000104A46494600010101006000600000FFD9'),
            $attachment,
        ];
        yield 'fileContents with image/png' => [
            (string) hex2bin('89504E470D0A1A0A0000000D49484452'),
            $attachment,
        ];
        yield 'fileContents with image/gif' => [
            'GIF89a'.str_repeat("\x00", 13),
            $attachment,
        ];
        yield 'fileContents with image/webp' => [
            (string) hex2bin('524946461A0000005745425056503820'),
            $attachment,
        ];
    }

    public function provideInvalidFiles(): iterable
    {
        yield 'fileContents with application/zip' => [
            (string) hex2bin('504B030414000000080000002100000000000000000000000000000000'),
        ];
        yield 'fileContents with text/plain' => [
            'This is a plain text file.',
        ];
        yield 'fileContents with invalid gif' => [
            'G1F88z',
        ];
    }

    protected function setProcessor(): void
    {
        $this->attachmentProcessor = new ImageProcessor(
            $this->filesystem,
            $this->downloader,
            $this->hasher,
            'localhost'
        );
    }
}
