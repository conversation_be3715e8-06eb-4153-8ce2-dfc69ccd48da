<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Attachment;

use App\Catalog\Attachment\AttachmentProcessorInterface;
use App\Catalog\Attachment\CompositeDownloadInterface;
use App\Catalog\Attachment\Hasher;
use App\Catalog\Message\AttachmentInterface;
use App\Mime\Exception\MimeTypeOrExtensionMismatchException;
use App\Tests\Util\Factory\Catalog\Messenger\UpsertAttachmentFactory;
use League\Flysystem\FilesystemOperator;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

abstract class AbstractAttachmentProcessorTest extends TestCase
{
    protected FilesystemOperator&MockObject $filesystem;
    protected CompositeDownloadInterface&MockObject $downloader;
    protected Hasher $hasher;

    protected AttachmentProcessorInterface $attachmentProcessor;

    protected function setUp(): void
    {
        parent::setUp();

        $this->filesystem = $this->createMock(FilesystemOperator::class);
        $this->downloader = $this->createMock(CompositeDownloadInterface::class);
        $this->hasher = new Hasher();

        $this->setProcessor();
    }

    abstract public function getExpectedUrl(string $filename): string;

    /**
     * @dataProvider provideValidFiles
     */
    public function testCanStore(string $fileContents, AttachmentInterface $attachment): void
    {
        // Arrange
        $filename = hash('sha256', $fileContents);

        // Assert
        $this->downloader->expects(self::once())
            ->method('download')
            ->willReturn($fileContents);

        $this->filesystem->expects(self::once())
            ->method('fileExists')
            ->willReturn(false);

        $this->filesystem->expects(self::once())
            ->method('write');

        // Act
        $filePath = $this->attachmentProcessor->store($attachment);

        // Assert
        self::assertSame(
            $this->getExpectedUrl($filename),
            $filePath
        );
    }

    /**
     * @return array<string, array<array-key, string|AttachmentInterface>>
     */
    abstract public function provideValidFiles(): iterable;

    /**
     * @dataProvider provideInvalidFiles
     */
    public function testStoreThrowsMimeTypeOrExtensionMismatchException(string $fileContents): void
    {
        // Assert
        $this->downloader->expects(self::once())
            ->method('download')
            ->willReturn($fileContents);

        $this->filesystem->expects(self::never())
            ->method('fileExists');

        $this->filesystem->expects(self::never())
            ->method('write');

        $this->expectException(MimeTypeOrExtensionMismatchException::class);

        // Act
        $this->attachmentProcessor->store(UpsertAttachmentFactory::createLeaflet());
        $this->attachmentProcessor->store(UpsertAttachmentFactory::createInvalidFile());
    }

    /**
     * @return array<string, array<array-key, string>>
     */
    abstract public function provideInvalidFiles(): iterable;

    public function testCanRemove(): void
    {
        // Arrange
        $filePath = 'https://localhost/images/123.jpg';

        // Assert
        $this->filesystem->expects(self::once())
            ->method('fileExists')
            ->willReturn(true);

        $this->filesystem->expects(self::once())
            ->method('delete');

        // Act
        $this->attachmentProcessor->remove($filePath);
    }

    abstract protected function setProcessor(): void;
}
