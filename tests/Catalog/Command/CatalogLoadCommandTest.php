<?php

declare(strict_types=1);

namespace App\Tests\Catalog\Command;

use App\Catalog\Command\CatalogLoadCommand;
use App\Catalog\Loader\CatalogLoaderInterface;
use App\Catalog\Loader\Context;
use DateTimeImmutable;
use P<PERSON>Unit\Framework\MockObject\MockObject;
use PHPUnit\Framework\MockObject\Stub;
use PHPUnit\Framework\TestCase;
use Psr\Cache\CacheItemInterface;
use Psr\Cache\CacheItemPoolInterface;
use Ramsey\Uuid\Uuid;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

final class CatalogLoadCommandTest extends TestCase
{
    private CatalogLoaderInterface&MockObject $loader1;
    private CatalogLoaderInterface&MockObject $loader2;

    private InputInterface&MockObject $input;
    private OutputInterface&Stub $output;

    private CacheItemPoolInterface&MockObject $cacheItemPool;
    private CacheItemInterface&MockObject $cacheItem;

    private CatalogLoadCommand $command;

    protected function setUp(): void
    {
        $this->loader1 = $this->createMock(CatalogLoaderInterface::class);
        $this->loader2 = $this->createMock(CatalogLoaderInterface::class);

        $this->input = $this->createMock(InputInterface::class);
        $this->output = $this->createStub(OutputInterface::class);

        $this->cacheItemPool = $this->createMock(CacheItemPoolInterface::class);
        $this->cacheItem = $this->createMock(CacheItemInterface::class);

        $this->command = new CatalogLoadCommand($this->cacheItemPool, [$this->loader1, $this->loader2]);
    }

    public function testItCanLoadMultipleLoaders(): void
    {
        // Assert
        $this->loader1->expects(self::exactly(3))->method('getName')->willReturn('loader 1');
        $this->loader2->expects(self::exactly(3))->method('getName')->willReturn('loader 2');

        $this->loader1->expects(self::once())->method('execute');
        $this->loader2->expects(self::once())->method('execute');

        $this->cacheItemPool->expects(self::exactly(3))
            ->method('getItem')
            ->willReturn($this->cacheItem);

        $this->cacheItem->expects(self::exactly(2))
            ->method('get')
            ->willReturn(null);

        $this->cacheItem->expects(self::once())->method('set');
        $this->cacheItem->expects(self::once())->method('expiresAfter');
        $this->cacheItemPool->expects(self::once())->method('save');

        // Act
        $this->command->execute($this->input, $this->output);
    }

    public function testItCanLoadOnlyTheSpecifiedLoader(): void
    {
        // Arrange
        $this->input->method('getArgument')->willReturn('loader 2');

        // Assert
        $this->loader1->expects(self::exactly(2))->method('getName')->willReturn('loader 1');
        $this->loader2->expects(self::exactly(5))->method('getName')->willReturn('loader 2');

        $this->loader1->expects(self::never())->method('execute');
        $this->loader2->expects(self::once())->method('execute');

        $this->cacheItemPool->expects(self::exactly(2))
            ->method('getItem')
            ->willReturn($this->cacheItem);

        $this->cacheItem->expects(self::once())
            ->method('get')
            ->willReturn(null);

        // Act
        $this->command->execute($this->input, $this->output);
    }

    public function testItCanUseTheFlags(): void
    {
        // Arrange
        $importTag = Uuid::uuid7();
        $this->input->method('getOption')
            ->willReturnCallback(static fn (string $option) => match ($option) {
                CatalogLoadCommand::OPTION_ALL_KEY,
                CatalogLoadCommand::OPTION_SKIP_IMAGES => true,
                CatalogLoadCommand::OPTION_IMPORT_TAG => $importTag->toString(),
                'product' => ['7'],
                default => null,
            });

        $this->input->method('hasOption')->willReturn(true);

        // Assert
        $this->loader1->method('getName')->willReturn('loader 1');
        $this->loader2->method('getName')->willReturn('loader 2');

        $expectedContext = new Context('loader 1', $importTag, skipImages: true, skipLeaflets: false, productCodes: ['7']);
        $this->loader1->expects(self::once())->method('execute')->with($expectedContext);

        $expectedContext = new Context('loader 2', $importTag, skipImages: true, skipLeaflets: false, productCodes: ['7']);
        $this->loader2->expects(self::once())->method('execute')->with($expectedContext);

        $this->cacheItemPool->expects(self::never())->method('getItem');
        $this->cacheItemPool->expects(self::never())->method('save');

        // Act
        $this->command->execute($this->input, $this->output);
    }

    public function testItCanUseASpecificDateTime(): void
    {
        // Arrange
        $importTag = Uuid::uuid7();
        $expectedDateTime = new DateTimeImmutable('2024-01-01 10:10:10');
        $this->input->method('getOption')
            ->willReturnCallback(static fn (string $option) => match ($option) {
                CatalogLoadCommand::OPTION_ALL_KEY, CatalogLoadCommand::OPTION_SKIP_IMAGES, CatalogLoadCommand::OPTION_SKIP_LEAFLETS => false,
                CatalogLoadCommand::OPTION_LAST_UPDATED_AT_KEY => '2024-01-01 10:10:10',
                CatalogLoadCommand::OPTION_PRODUCT => null,
                CatalogLoadCommand::OPTION_IMPORT_TAG => $importTag->toString(),
                default => null,
            });

        $this->input->method('hasOption')->willReturn(true);

        // Assert
        $this->loader1->method('getName')->willReturn('loader 1');
        $this->loader2->method('getName')->willReturn('loader 2');

        $context = new Context(originatingClient: 'loader 1', importTag: $importTag, importFromDateTime: $expectedDateTime);
        $this->loader1->expects(self::once())->method('execute')->with($context);
        $context = new Context(originatingClient: 'loader 2', importTag: $importTag, importFromDateTime: $expectedDateTime);
        $this->loader2->expects(self::once())->method('execute')->with($context);

        $this->cacheItemPool->expects(self::once())
            ->method('getItem')
            ->willReturn($this->cacheItem);

        $this->cacheItem->expects(self::never())->method('get');

        $this->cacheItem->expects(self::once())->method('set');
        $this->cacheItem->expects(self::once())->method('expiresAfter');
        $this->cacheItemPool->expects(self::once())->method('save');

        // Act
        $this->command->execute($this->input, $this->output);
    }
}
