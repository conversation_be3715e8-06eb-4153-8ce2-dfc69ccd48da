<?php

declare(strict_types=1);

namespace App\Tests\OrderProcessing\Warnings;

use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Order\OrderItemUnit;
use App\Entity\Product\ProductVariant;
use App\OrderProcessing\Warnings\WarnWhenMaximumQuantityPerOrderIsExceededOrderProcessor;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Core\Model\OrderInterface;
use Webmozart\Assert\InvalidArgumentException;

class WarnWhenMaximumQuantityPerOrderIsExceededOrderProcessorTest extends TestCase
{
    public function testItAddsWarningWhenMaximumQuantityPerOrderIsExceeded(): void
    {
        $variant = new ProductVariant();
        $variant->setMaximumQuantityPerOrder(2);

        $orderItem = new OrderItem();
        $orderItem->setVariant($variant);

        new OrderItemUnit($orderItem);
        new OrderItemUnit($orderItem);
        new OrderItemUnit($orderItem);

        $order = new Order();
        $order->addItem($orderItem);

        $this->assertCount(0, $orderItem->getWarnings());

        $processor = new WarnWhenMaximumQuantityPerOrderIsExceededOrderProcessor();
        $processor->process($order);

        $this->assertCount(1, $orderItem->getWarnings());
    }

    public function testItDoesNotAddWarningWhenMaximumQuantityPerOrderIsNotExceeded(): void
    {
        $variant = new ProductVariant();
        $variant->setMaximumQuantityPerOrder(2);

        $orderItem = new OrderItem();
        $orderItem->setVariant($variant);

        new OrderItemUnit($orderItem);

        $order = new Order();
        $order->addItem($orderItem);

        $this->assertCount(0, $orderItem->getWarnings());

        $processor = new WarnWhenMaximumQuantityPerOrderIsExceededOrderProcessor();
        $processor->process($order);

        $this->assertCount(0, $orderItem->getWarnings());
    }

    public function testItThrowsInvalidArgumentExceptionWhenNoOrderEntityIsGiven(): void
    {
        $this->expectException(InvalidArgumentException::class);

        $orderMock = $this->createMock(OrderInterface::class);
        $processor = new WarnWhenMaximumQuantityPerOrderIsExceededOrderProcessor();
        $processor->process($orderMock);
    }
}
