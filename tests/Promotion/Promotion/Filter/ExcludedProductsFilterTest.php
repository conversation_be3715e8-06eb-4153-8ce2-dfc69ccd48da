<?php

declare(strict_types=1);

namespace App\Tests\Promotion\Promotion\Filter;

use App\Entity\Order\OrderItem;
use App\Entity\Product\ProductInterface;
use App\Promotion\Promotion\Filter\ExcludedProductsFilter;
use PHPUnit\Framework\TestCase;

class ExcludedProductsFilterTest extends TestCase
{
    /**
     * Test filtering with empty configuration.
     */
    public function testFilterWithEmptyConfiguration(): void
    {
        // Arrange
        $filter = new ExcludedProductsFilter();
        $items = [$this->createMock(OrderItem::class)];
        $configuration = ['products_filter' => ['products' => []]];

        // Act
        $result = $filter->filter($items, $configuration);

        // Assert
        $this->assertEquals($items, $result, 'When configuration has no excluded products, the result should be the same as input items.');
    }

    public function testFilterWithNoExcludedProducts(): void
    {
        // Arrange
        $filter = new ExcludedProductsFilter();
        $items = [];
        $configuration = ['products_filter' => ['products' => ['product_code_1', 'product_code_2']]];

        // Act
        $result = $filter->filter($items, $configuration);

        // Assert
        $this->assertEquals($items, $result, 'When there are no excluded products, the result should be the same as input items.');
    }

    public function testFilterWithExcludedProducts(): void
    {
        // Arrange
        $filter = new ExcludedProductsFilter();

        // Create product mocks
        $product1 = $this->createMock(ProductInterface::class);
        $product1->method('getCode')->willReturn('product_code_1');

        $product2 = $this->createMock(ProductInterface::class);
        $product2->method('getCode')->willReturn('product_code_2');

        // Create item mocks
        $item1 = $this->createMock(OrderItem::class);
        $item1->method('getProduct')->willReturn($product1);

        $item2 = $this->createMock(OrderItem::class);
        $item2->method('getProduct')->willReturn($product2);

        $items = [$item1, $item2];
        $configuration = ['products_filter' => ['products' => ['product_code_1']]];

        // Act
        $result = $filter->filter($items, $configuration);

        // Assert
        $this->assertCount(1, $result, 'Only $item2 should be in the result since $item1 has an excluded product.');
        $this->assertSame($item2, $result[0], '$item2 should be the only item in the result.');
    }
}
