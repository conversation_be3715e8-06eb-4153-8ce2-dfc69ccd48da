<?php

declare(strict_types=1);

namespace App\Tests\Promotion;

use App\Entity\Order\Order;
use App\Promotion\Checker\Eligibility\PromotionCouponDurationEligibilityChecker;
use DateTime;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Promotion\Model\PromotionCouponInterface;

class PromotionCouponDurationEligibilityCheckerTest extends TestCase
{
    public function testIsEligibleWithValidCoupon(): void
    {
        $promotionSubject = $this->createMock(Order::class);
        $checkoutCompletedAt = new DateTime('-5 days');
        $promotionSubject->method('getCheckoutCompletedAt')->willReturn($checkoutCompletedAt);

        $promotionCoupon = $this->createMock(PromotionCouponInterface::class);
        $expiresAt = new DateTime('+1 day');
        $promotionCoupon->method('getExpiresAt')->willReturn($expiresAt);

        $checker = new PromotionCouponDurationEligibilityChecker();

        $eligible = $checker->isEligible($promotionSubject, $promotionCoupon);

        $this->assertTrue($eligible);
    }

    public function testIsEligibleWithExpiredCoupon(): void
    {
        $promotionSubject = $this->createMock(Order::class);
        $checkoutCompletedAt = new DateTime('-5 days');
        $promotionSubject->method('getCheckoutCompletedAt')->willReturn($checkoutCompletedAt);

        $promotionCoupon = $this->createMock(PromotionCouponInterface::class);
        $expiresAt = new DateTime('-7 days');
        $promotionCoupon->method('getExpiresAt')->willReturn($expiresAt);

        $checker = new PromotionCouponDurationEligibilityChecker();

        $eligible = $checker->isEligible($promotionSubject, $promotionCoupon);

        $this->assertFalse($eligible);
    }

    public function testIsEligibleWithoutCheckoutCompletedAt(): void
    {
        $promotionSubject = $this->createMock(Order::class);
        $promotionSubject->method('getCheckoutCompletedAt')->willReturn(null);

        $promotionCoupon = $this->createMock(PromotionCouponInterface::class);
        $expiresAt = new DateTime('+ 1 day');
        $promotionCoupon->method('getExpiresAt')->willReturn($expiresAt);

        $checker = new PromotionCouponDurationEligibilityChecker();

        $eligible = $checker->isEligible($promotionSubject, $promotionCoupon);

        $this->assertTrue($eligible);
    }
}
