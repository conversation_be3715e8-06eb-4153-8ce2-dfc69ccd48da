<?php

declare(strict_types=1);

namespace App\Tests\Promotion;

use App\Entity\Order\Order;
use App\Promotion\Checker\Eligibility\PromotionCouponDurationEligibilityChecker;
use DateTime;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Promotion\Model\PromotionCouponInterface;

class PromotionDurationEligibilityCheckerTest extends TestCase
{
    public function testIsEligibleWithValidCoupon(): void
    {
        // Mock PromotionSubjectInterface (e.g., OrderInterface)
        $promotionSubject = $this->createMock(Order::class);
        $checkoutCompletedAt = new DateTime('-1 day');
        $promotionSubject->method('getCheckoutCompletedAt')->willReturn($checkoutCompletedAt);

        // Mock PromotionCouponInterface
        $promotionCoupon = $this->createMock(PromotionCouponInterface::class);
        $expiresAt = new DateTime('+1 day');
        $promotionCoupon->method('getExpiresAt')->willReturn($expiresAt);

        // Create an instance of the PromotionCouponDurationEligibilityChecker
        $checker = new PromotionCouponDurationEligibilityChecker();

        // Check if the coupon is eligible
        $eligible = $checker->isEligible($promotionSubject, $promotionCoupon);

        // Assert that the coupon is eligible
        $this->assertTrue($eligible);
    }

    public function testIsEligibleWithValidCouponAndWithoutCheckoutCompletedAt(): void
    {
        // Mock PromotionSubjectInterface (e.g., OrderInterface)
        $promotionSubject = $this->createMock(Order::class);
        $promotionSubject->method('getCheckoutCompletedAt')->willReturn(null);

        // Mock PromotionCouponInterface
        $promotionCoupon = $this->createMock(PromotionCouponInterface::class);
        $expiresAt = new DateTime('+1 day');
        $promotionCoupon->method('getExpiresAt')->willReturn($expiresAt);

        // Create an instance of the PromotionCouponDurationEligibilityChecker
        $checker = new PromotionCouponDurationEligibilityChecker();

        // Check if the coupon is eligible
        $eligible = $checker->isEligible($promotionSubject, $promotionCoupon);

        // Assert that the coupon is eligible
        $this->assertTrue($eligible);
    }

    public function testIsEligibleWithExpiredCoupon(): void
    {
        // Mock PromotionSubjectInterface (e.g., OrderInterface)
        $promotionSubject = $this->createMock(Order::class);
        $checkoutCompletedAt = new DateTime('-1 day');
        $promotionSubject->method('getCheckoutCompletedAt')->willReturn($checkoutCompletedAt);

        // Mock PromotionCouponInterface with an expired coupon
        $promotionCoupon = $this->createMock(PromotionCouponInterface::class);
        $expiresAt = new DateTime('-5 days');
        $promotionCoupon->method('getExpiresAt')->willReturn($expiresAt);

        // Create an instance of the PromotionCouponDurationEligibilityChecker
        $checker = new PromotionCouponDurationEligibilityChecker();

        // Check if the coupon is eligible
        $eligible = $checker->isEligible($promotionSubject, $promotionCoupon);

        // Assert that the coupon is not eligible
        $this->assertFalse($eligible);
    }
}
