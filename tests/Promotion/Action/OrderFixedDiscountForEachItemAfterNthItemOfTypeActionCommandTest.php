<?php

declare(strict_types=1);

namespace App\Tests\Promotion\Action;

use App\Entity\Channel\Channel;
use App\Entity\Order\Order;
use App\Entity\Promotion\Promotion;
use App\Promotion\Action\OrderFixedDiscountForEachItemAfterNthItemOfTypeActionCommand;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\PromotionFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Core\Distributor\ProportionalIntegerDistributorInterface;
use Sylius\Component\Core\Promotion\Applicator\UnitsPromotionAdjustmentsApplicatorInterface;
use Sylius\Component\Core\Promotion\Filter\FilterInterface;
use Sylius\Component\Promotion\Model\PromotionSubjectInterface;
use Sylius\Resource\Exception\UnexpectedTypeException;

class OrderFixedDiscountForEachItemAfterNthItemOfTypeActionCommandTest extends TestCase
{
    private OrderFixedDiscountForEachItemAfterNthItemOfTypeActionCommand $discountForEachItemAfterNthItemOfTypeActionCommand;

    private FilterInterface&MockObject $genericFilter;
    private ProportionalIntegerDistributorInterface&MockObject $distributor;
    private UnitsPromotionAdjustmentsApplicatorInterface&MockObject $unitsPromotionAdjustmentsApplicator;

    protected function setUp(): void
    {
        $this->genericFilter = $this->createMock(FilterInterface::class);

        $this->distributor = $this->createMock(ProportionalIntegerDistributorInterface::class);
        $this->unitsPromotionAdjustmentsApplicator = $this->createMock(UnitsPromotionAdjustmentsApplicatorInterface::class);

        $this->discountForEachItemAfterNthItemOfTypeActionCommand = new OrderFixedDiscountForEachItemAfterNthItemOfTypeActionCommand(
            $this->genericFilter,
            $this->distributor,
            $this->unitsPromotionAdjustmentsApplicator
        );
    }

    public function testItAddsPromotionAdjustmentsWhenMatchingConfiguration(): void
    {
        // Arrange promotion
        $action = PromotionFactory::createActionForDiscountForEachItemAfterNthItem(1000, 1, ['dok_nl']);
        $promotion = PromotionFactory::createWithArguments(
            code: 'test-promotion',
            coupons: [PromotionFactory::createCoupon()],
            actions: [$action]
        );

        // Arrange Order
        $order = OrderFactory::createPrefilled();

        // Mock data
        $this->genericFilter->expects(self::once())->method('filter')->willReturn($order->getItems()->toArray());
        $this->unitsPromotionAdjustmentsApplicator->expects(self::once())->method('apply');

        // Act
        $result = $this->discountForEachItemAfterNthItemOfTypeActionCommand->execute(
            $order,
            $action->getConfiguration(),
            $promotion
        );

        // Assert
        self::assertTrue($result, 'The action should be executed successfully.');
    }

    public function testItThrowsExceptionWhenInvalidSubjectIsGiven(): void
    {
        $this->expectException(UnexpectedTypeException::class);

        $promotionSubjectMock = $this->createMock(PromotionSubjectInterface::class);
        $this->discountForEachItemAfterNthItemOfTypeActionCommand->execute(
            $promotionSubjectMock,
            [],
            new Promotion()
        );
    }

    public function testItReturnsFalseWhenChannelIsNotSupported(): void
    {
        $channel = new Channel();
        $channel->setCode('dok_nl');

        $order = new Order();
        $order->setChannel($channel);

        $result = $this->discountForEachItemAfterNthItemOfTypeActionCommand->execute(
            $order,
            [],
            new Promotion()
        );

        $this->assertFalse($result);
    }

    public function testItReturnsFalseWhenPromotionAmountIsZeroForGivenChannel(): void
    {
        $channel = new Channel();
        $channel->setCode('dok_nl');

        $order = new Order();
        $order->setChannel($channel);

        $result = $this->discountForEachItemAfterNthItemOfTypeActionCommand->execute(
            $order,
            [
                $channel->getCode() => [
                    'amount' => 0,
                ],
            ],
            new Promotion()
        );

        $this->assertFalse($result);
    }

    public function testItReturnsFalseWhenConfigurationIsNotValid(): void
    {
        $channel = new Channel();
        $channel->setCode('dok_nl');

        $order = new Order();
        $order->setChannel($channel);

        $result = $this->discountForEachItemAfterNthItemOfTypeActionCommand->execute(
            $order,
            [
                $channel->getCode() => [
                    'amount' => 'No amount',
                ],
            ],
            new Promotion()
        );

        $this->assertFalse($result);
    }

    public function testItReturnsFalseWhenAfterNthItemIsSmallerThanOne(): void
    {
        $channel = new Channel();
        $channel->setCode('dok_nl');

        $order = new Order();
        $order->setChannel($channel);

        $result = $this->discountForEachItemAfterNthItemOfTypeActionCommand->execute(
            $order,
            [
                $channel->getCode() => [
                    'amount' => 1111,
                ],
                'afterNthItem' => 0,
            ],
            new Promotion()
        );

        $this->assertFalse($result);
    }
}
