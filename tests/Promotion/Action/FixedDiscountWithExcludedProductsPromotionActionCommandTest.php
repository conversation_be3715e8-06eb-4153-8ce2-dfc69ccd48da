<?php

declare(strict_types=1);

namespace App\Tests\Promotion\Action;

use App\Entity\Channel\Channel;
use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Promotion\Action\FixedDiscountWithExcludedProductsPromotionActionCommand;
use App\Tests\Util\Factory\OrderFactory;
use Doctrine\Common\Collections\ArrayCollection;
use InvalidArgumentException;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Core\Distributor\ProportionalIntegerDistributorInterface;
use Sylius\Component\Core\Promotion\Applicator\UnitsPromotionAdjustmentsApplicatorInterface;
use Sylius\Component\Core\Promotion\Filter\FilterInterface;
use Sylius\Component\Promotion\Model\PromotionInterface;
use Sylius\Component\Promotion\Model\PromotionSubjectInterface;

class FixedDiscountWithExcludedProductsPromotionActionCommandTest extends TestCase
{
    private FixedDiscountWithExcludedProductsPromotionActionCommand $command;
    private ProportionalIntegerDistributorInterface&MockObject $distributor;
    private FilterInterface&MockObject $productFilter;
    private UnitsPromotionAdjustmentsApplicatorInterface&MockObject $unitsPromotionAdjustmentsApplicator;

    protected function setUp(): void
    {
        $this->distributor = $this->createMock(ProportionalIntegerDistributorInterface::class);
        $this->unitsPromotionAdjustmentsApplicator = $this->createMock(UnitsPromotionAdjustmentsApplicatorInterface::class);
        $this->productFilter = $this->createMock(FilterInterface::class);

        $this->command = new FixedDiscountWithExcludedProductsPromotionActionCommand(
            $this->distributor,
            $this->unitsPromotionAdjustmentsApplicator,
            $this->productFilter
        );
    }

    public function testExecuteWithInvalidSubject(): void
    {
        // Arrange
        /** @var Order $subject */
        $subject = $this->createMock(PromotionSubjectInterface::class);
        $promotion = $this->createMock(PromotionInterface::class);

        // Assert
        $this->expectException(InvalidArgumentException::class);

        // Act
        $this->command->execute($subject, [], $promotion);
    }

    public function testExecuteWithInvalidChannel(): void
    {
        // Arrange
        $subject = $this->createMock(Order::class);
        $subject->method('getItems')->willReturn(new ArrayCollection([new OrderItem()]));
        $subject->method('countItems')->willReturn(1);
        $subject->method('getChannel')->willReturn(null);
        $promotion = $this->createMock(PromotionInterface::class);

        // Assert
        $this->expectException(InvalidArgumentException::class);

        // Act
        $this->command->execute($subject, [], $promotion);
    }

    public function testExecuteWithInvalidConfiguration(): void
    {
        // Arrange
        $subject = $this->createMock(Order::class);
        $subject->method('getItems')->willReturn(new ArrayCollection([new OrderItem()]));
        $subject->method('countItems')->willReturn(1);
        $channel = $this->createMock(Channel::class);
        $channel->method('getCode')->willReturn('channel_code');
        $subject->method('getChannel')->willReturn($channel);
        $promotion = $this->createMock(PromotionInterface::class);

        // Act
        $result = $this->command->execute($subject, ['channel_code' => []], $promotion);

        // Assert
        $this->assertFalse($result);
    }

    public function testExecuteWithNoFilteredItems(): void
    {
        // Arrange
        $subject = $this->createMock(Order::class);
        $subject->method('getItems')->willReturn(new ArrayCollection([new OrderItem()]));
        $subject->method('countItems')->willReturn(1);
        $channel = $this->createMock(Channel::class);
        $channel->method('getCode')->willReturn('channel_code');
        $subject->method('getChannel')->willReturn($channel);
        $subject->method('getItems')->willReturn(new ArrayCollection());
        $promotion = $this->createMock(PromotionInterface::class);
        $this->productFilter->method('filter')->willReturn([]);

        // Act
        $result = $this->command->execute($subject, ['channel_code' => ['amount' => 100]], $promotion);

        // Assert
        $this->assertFalse($result);
    }

    public function testExecuteWithZeroPromotionAmount(): void
    {
        // Arrange
        $subject = $this->createMock(Order::class);
        $subject->method('getItems')->willReturn(new ArrayCollection([new OrderItem()]));
        $subject->method('countItems')->willReturn(1);
        $channel = $this->createMock(Channel::class);
        $channel->method('getCode')->willReturn('channel_code');
        $subject->method('getChannel')->willReturn($channel);
        $subject->method('getItems')->willReturn(new ArrayCollection([new OrderItem()]));
        $promotion = $this->createMock(PromotionInterface::class);
        $this->productFilter->method('filter')->willReturn([new OrderItem()]);

        // Act
        $result = $this->command->execute($subject, ['channel_code' => ['amount' => 0]], $promotion);

        // Assert
        $this->assertFalse($result);
    }

    public function testExecuteWithValidConfiguration(): void
    {
        // Arrange
        $subject = $this->createMock(Order::class);
        $channel = $this->createMock(Channel::class);
        $channel->method('getCode')->willReturn('channel_code');
        $subject->method('getChannel')->willReturn($channel);
        $subject->method('getItems')->willReturn(new ArrayCollection([new OrderItem()]));
        $subject->method('countItems')->willReturn(1);
        $promotion = $this->createMock(PromotionInterface::class);
        $promotion->method('getAppliesToDiscounted')->willReturn(true);
        $subject->method('getPromotionSubjectTotal')->willReturn(100);

        $this->productFilter->method('filter')->willReturn([new OrderItem()]);
        $this->distributor->method('distribute')->willReturn([100]);

        // Act
        $result = $this->command->execute($subject, ['channel_code' => ['amount' => 100]], $promotion);

        // Assert
        $this->assertTrue($result);
    }

    public function testExecuteWithExcludedProduct(): void
    {
        // Arrange
        $discount = 100;

        $subject = OrderFactory::createPrefilled();
        $promotion = $this->createMock(PromotionInterface::class);
        $promotion->method('getAppliesToDiscounted')->willReturn(false);

        /** @var OrderItem $consultOrderItem */
        $consultOrderItem = $subject->getItems()->first();
        /** @var OrderItem $medicationOrderItem */
        $medicationOrderItem = $subject->getItems()->last();

        $this->productFilter->method('filter')->willReturn([$medicationOrderItem]);
        $this->distributor->method('distribute')->willReturn([0, $discount]);

        $configuration = [
            'dok_nl' => ['amount' => $discount],
            'products_filter' => [
                'products' => [
                    $consultOrderItem->getProduct()?->getCode(),
                ],
            ],
        ];

        $this->unitsPromotionAdjustmentsApplicator
            ->expects($this->once())
            ->method('apply')
            ->with($subject, $promotion, [0, $discount]);

        // Act
        $result = $this->command->execute($subject, $configuration, $promotion);

        // Assert
        $this->assertTrue($result);
    }
}
