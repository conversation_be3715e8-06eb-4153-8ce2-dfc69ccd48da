<?php

declare(strict_types=1);

namespace App\Tests\Promotion\Action;

use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Promotion\Action\PercentageDiscountWithExcludedProductsPromotionActionCommand;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Core\Model\PromotionInterface;
use Sylius\Component\Core\Promotion\Filter\FilterInterface;
use Sylius\Component\Promotion\Model\PromotionSubjectInterface;
use Sylius\Resource\Factory\FactoryInterface;

class PercentageDiscountWithExcludedProductsPromotionActionCommandTest extends TestCase
{
    public function testExecuteWithInvalidSubject(): void
    {
        // Arrange
        $adjustmentFactory = $this->createMock(FactoryInterface::class);
        $productFilter = $this->createMock(FilterInterface::class);
        $command = new PercentageDiscountWithExcludedProductsPromotionActionCommand($adjustmentFactory, $productFilter);

        $subject = $this->createMock(PromotionSubjectInterface::class);
        $configuration = ['percentage' => 0.2];
        $promotion = $this->createMock(PromotionInterface::class);

        // Act
        $result = $command->execute($subject, $configuration, $promotion);

        // Assert
        $this->assertFalse($result, 'Execution should return false when the subject is invalid.');
    }

    public function testExecuteWithInvalidConfiguration(): void
    {
        // Arrange
        $adjustmentFactory = $this->createMock(FactoryInterface::class);
        $productFilter = $this->createMock(FilterInterface::class);
        $command = new PercentageDiscountWithExcludedProductsPromotionActionCommand($adjustmentFactory, $productFilter);

        $subject = $this->createMock(Order::class);
        $configuration = ['percentage' => 1.5]; // Invalid percentage value
        $promotion = $this->createMock(PromotionInterface::class);

        // Act
        $result = $command->execute($subject, $configuration, $promotion);

        // Assert
        $this->assertFalse($result, 'Execution should return false when the configuration is invalid.');
    }

    public function testExecuteWithNoFilteredItems(): void
    {
        // Arrange
        $adjustmentFactory = $this->createMock(FactoryInterface::class);
        $productFilter = $this->createMock(FilterInterface::class);
        $command = new PercentageDiscountWithExcludedProductsPromotionActionCommand($adjustmentFactory, $productFilter);

        $subject = $this->createMock(Order::class);
        $subject->method('getItems')->willReturn(new ArrayCollection());

        $configuration = ['percentage' => 0.2];
        $promotion = $this->createMock(PromotionInterface::class);

        // Mock product filter to return an empty array
        $productFilter->method('filter')->willReturn([]);

        // Act
        $result = $command->execute($subject, $configuration, $promotion);

        // Assert
        $this->assertFalse($result, 'Execution should return false when there are no filtered items.');
    }

    public function testExecuteWithFilteredItems(): void
    {
        // Arrange
        $adjustmentFactory = $this->createMock(FactoryInterface::class);
        $productFilter = $this->createMock(FilterInterface::class);
        $command = new PercentageDiscountWithExcludedProductsPromotionActionCommand($adjustmentFactory, $productFilter);

        $subject = $this->createMock(Order::class);
        $subject->method('getItems')->willReturn(new ArrayCollection());

        $configuration = ['percentage' => 0.2];
        $promotion = $this->createMock(PromotionInterface::class);

        // Mock product filter to return some items
        $item1 = $this->createMock(OrderItem::class);
        $item2 = $this->createMock(OrderItem::class);
        $productFilter->method('filter')->willReturn([$item1, $item2]);

        // Act
        $result = $command->execute($subject, $configuration, $promotion);

        // Assert
        $this->assertTrue($result, 'Execution should return true when there are filtered items.');
    }
}
