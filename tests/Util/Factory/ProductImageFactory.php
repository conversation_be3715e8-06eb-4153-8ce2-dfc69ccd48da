<?php

declare(strict_types=1);

namespace App\Tests\Util\Factory;

use App\Tests\Mocks\Entity\TestProductImage;
use SplFileInfo;
use Sylius\Component\Core\Model\ProductImageInterface;

final class ProductImageFactory
{
    public static function create(array $data): ProductImageInterface
    {
        $image = new TestProductImage();

        $image->setId($data['id'] ?? null);
        $image->setOwner($data['owner'] ?? null);
        $image->setFile($data['file'] ?? null);
        $image->setPath($data['path'] ?? null);
        $image->setType($data['type'] ?? null);

        return $image;
    }

    public static function createPreFilled(array $data = []): ProductImageInterface
    {
        return self::create(array_merge([
            'id' => 1337,
            'file' => new SplFileInfo('test-filename.extension'),
            'path' => 'path/to/file',
            'type' => $data['type'] ?? 'nl', // Type is abused for locale
        ], $data));
    }
}
