<?php

declare(strict_types=1);

namespace App\Tests\CanopyDeploy;

use App\CanopyDeploy\Client\CanopyDeployRetryStrategy;
use App\CanopyDeploy\Client\Webhook\CanopyDeployWebhookClient;
use App\CanopyDeploy\Client\Webhook\WebhookName;
use App\CanopyDeploy\Message\WebhookMessageInterface;
use App\Entity\BusinessUnit\BusinessUnit;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Symfony\Component\HttpClient\RetryableHttpClient;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class WebhookClientTest extends TestCase
{
    public function testSeeExceptionWhenBearerTokenIsNotConfigured(): void
    {
        $businessUnit = new BusinessUnit();
        $businessUnit->setCode('dokteronline');
        $businessUnit->setCanopyDeployOrderWebhookUrl('some/url');

        $businessUnitRepository = $this->createMock(EntityRepository::class);
        $businessUnitRepository
            ->expects($this->exactly(2))
            ->method('findOneBy')
            ->willReturn($businessUnit);

        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager
            ->expects($this->exactly(2))
            ->method('getRepository')
            ->willReturn($businessUnitRepository);

        $httpClient = $this->createMock(HttpClientInterface::class);
        $retryableHttpClient = new RetryableHttpClient(
            $httpClient,
            new CanopyDeployRetryStrategy(),
            3
        );

        $orderWebhookClient = new CanopyDeployWebhookClient(
            $retryableHttpClient,
            $entityManager,
            [
                'blueclinic' => ['order' => 'blueclinicTokenForOrder'],
            ],
            $this->createStub(LoggerInterface::class),
            false,
        );

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage(
            'Token for "dokteronline/order" not found. Check if it is correctly configured in canopy_deploy.yaml'
        );

        /** @var WebhookMessageInterface&MockObject $payloadMock */
        $payloadMock = $this->createMock(WebhookMessageInterface::class);
        $payloadMock->method('getWebhookName')->willReturn(WebhookName::ORDER);
        $payloadMock->method('getBusinessUnitCode')->willReturn('dokteronline');
        $payloadMock->method('getSerializedPayload')->willReturn('{"some": "json"}');

        $orderWebhookClient->send($payloadMock);
    }
}
