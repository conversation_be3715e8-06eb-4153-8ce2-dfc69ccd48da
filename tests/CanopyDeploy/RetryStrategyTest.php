<?php

declare(strict_types=1);

namespace App\Tests\CanopyDeploy;

use App\CanopyDeploy\Client\CanopyDeployRetryStrategy;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpClient\Exception\TransportException;
use Symfony\Component\HttpClient\MockHttpClient;
use Symfony\Component\HttpClient\Response\AsyncContext;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

final class RetryStrategyTest extends TestCase
{
    /**
     * @dataProvider provideRetryable
     */
    public function testShouldRetry(string $method, int $code, ?TransportExceptionInterface $exception): void
    {
        $strategy = new CanopyDeployRetryStrategy();

        self::assertTrue($strategy->shouldRetry($this->getContext(0, $method, $code), null, $exception));
    }

    /**
     * @dataProvider provideNotRetryable
     */
    public function testShouldNotRetry(string $method, int $code, ?TransportExceptionInterface $exception): void
    {
        $strategy = new CanopyDeployRetryStrategy();

        self::assertFalse($strategy->shouldRetry($this->getContext(0, $method, $code), null, $exception));
    }

    public function provideRetryable(): iterable
    {
        yield ['GET', 200, new TransportException()];
        yield ['GET', 500, null];
        yield ['GET', 510, null];
        yield ['GET', 566, null];
        yield ['GET', 599, null];
        yield ['POST', 429, null];
    }

    public function provideNotRetryable(): iterable
    {
        yield ['POST', 200, null];
        yield ['POST', 200, new TransportException()];
        yield ['POST', 400, null];
        yield ['POST', 418, null];
        yield ['POST', 600, null];
        yield ['GET', 499, null];
    }

    /**
     * @dataProvider provideDelay
     */
    public function testGetDelay(int $delay, int $previousRetries, int $expectedDelay): void
    {
        $strategy = new CanopyDeployRetryStrategy();

        self::assertSame($expectedDelay, $strategy->getDelay($this->getContext($previousRetries, 'GET', 200), null, null));
    }

    public function provideDelay(): iterable
    {
        // delay, multiplier, maxDelay, retries, expectedDelay
        yield [2000, 0, 2000];
        yield [2000, 1, 2000];
        yield [2000, 2, 2000];
        yield [2000, 3, 2000];
        yield [2000, 4, 2000];
    }

    private function getContext(int $retryCount, string $method, int $statusCode): AsyncContext
    {
        $passthru = null;
        $info = [
            'retry_count' => $retryCount,
            'http_method' => $method,
            'url' => 'https://example.com/',
            'http_code' => $statusCode,
        ];
        $response = new MockResponse('', $info);

        return new AsyncContext($passthru, new MockHttpClient(), $response, $info, null, 0);
    }
}
