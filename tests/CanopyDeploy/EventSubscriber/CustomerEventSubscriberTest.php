<?php

declare(strict_types=1);

namespace App\Tests\CanopyDeploy\EventSubscriber;

use App\CanopyDeploy\EventSubscriber\CustomerEventSubscriber;
use App\CanopyDeploy\Message\CreateOrUpdateCustomerWithMarketingSubscription;
use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Channel\Channel;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Event\CustomerEvent;
use App\Event\Enum\CustomerEventName;
use App\Serializer\Webhook\OpenApiWebhookPayloadSerializerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Channel\Context\ChannelContextInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

final class CustomerEventSubscriberTest extends TestCase
{
    private ChannelContextInterface&MockObject $channelContextMock;
    private MessageBusInterface&MockObject $messageBusMock;
    private OpenApiWebhookPayloadSerializerInterface&MockObject $openapiSerializerMock;
    private CustomerEventSubscriber $customerEventSubscriber;

    protected function setUp(): void
    {
        $this->channelContextMock = $this->createMock(ChannelContextInterface::class);
        $this->messageBusMock = $this->createMock(MessageBusInterface::class);
        $this->openapiSerializerMock = $this->createMock(OpenApiWebhookPayloadSerializerInterface::class);
        $this->customerEventSubscriber = new CustomerEventSubscriber(
            $this->channelContextMock,
            $this->messageBusMock,
            $this->openapiSerializerMock
        );
    }

    /**
     * @dataProvider eventNameProvider
     */
    public function testCustomerWasCreatedEvent(CustomerEventName $eventName): void
    {
        $businessUnit = new BusinessUnit();
        $businessUnit->setCode('dokteronline');

        $channel = new Channel();
        $channel->setCode('dok_nl');
        $channel->setBusinessUnit($businessUnit);

        $customerPool = new CustomerPool();
        $customerPool->setCode('dokteronline');
        $customerPool->addChannel($channel);

        $customer = new Customer();
        $customer->setCustomerPool($customerPool);

        $expectedMessage = new CreateOrUpdateCustomerWithMarketingSubscription(
            $businessUnit,
            '{"some": "json"}',
        );

        $this->messageBusMock
            ->expects($this->once())
            ->method('dispatch')
            ->with($expectedMessage)
            ->willReturn(new Envelope($expectedMessage));

        $this->openapiSerializerMock
            ->method('serialize')
            ->willReturn('{"some": "json"}');

        $event = new CustomerEvent($eventName, $customer, $businessUnit);

        $this->customerEventSubscriber->dispatchCreateOrUpdateCustomer($event);
    }

    /**
     * @return iterable<string, array{0: CustomerEventName}>
     */
    public function eventNameProvider(): iterable
    {
        yield 'with customer was created event name' => [CustomerEventName::CustomerWasCreated];
        yield 'with customer was updated event name' => [CustomerEventName::CustomerWasUpdated];
    }
}
