<?php

declare(strict_types=1);

namespace App\Tests\CanopyDeploy\EventSubscriber;

use App\CanopyDeploy\EventSubscriber\MarketingSubscriptionEventSubscriber;
use App\CanopyDeploy\Message\CreateOrUpdateCustomerWithMarketingSubscription;
use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\MarketingSubscription\MarketingSubscription;
use App\Event\Enum\MarketingSubscriptionEventName;
use App\Event\MarketingSubscriptionEvent;
use App\Serializer\Webhook\OpenApiWebhookPayloadSerializerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

final class MarketingSubscriptionEventSubscriberTest extends TestCase
{
    private MessageBusInterface&MockObject $messageBusMock;
    private OpenApiWebhookPayloadSerializerInterface&MockObject $openapiSerializerMock;
    private MarketingSubscriptionEventSubscriber $marketingSubscriptionEventSubscriber;

    protected function setUp(): void
    {
        $this->messageBusMock = $this->createMock(MessageBusInterface::class);
        $this->openapiSerializerMock = $this->createMock(OpenApiWebhookPayloadSerializerInterface::class);

        $this->marketingSubscriptionEventSubscriber = new MarketingSubscriptionEventSubscriber($this->messageBusMock, $this->openapiSerializerMock);
    }

    /**
     * @dataProvider eventNameProvider
     */
    public function testMarketingSubscriptionEvents(MarketingSubscriptionEventName $eventName): void
    {
        $businessUnit = new BusinessUnit();
        $businessUnit->setCode('dokteronline');

        $marketingSubscription = new MarketingSubscription(
            '<EMAIL>',
            'nl',
            'nl',
            $businessUnit
        );

        $expectedMessage = new CreateOrUpdateCustomerWithMarketingSubscription(
            $businessUnit,
            '{"some": "json"}',
        );

        $this->messageBusMock
            ->expects($this->once())
            ->method('dispatch')
            ->with($expectedMessage)
            ->willReturn(new Envelope($expectedMessage));

        $this->openapiSerializerMock
            ->method('serialize')
            ->willReturn('{"some": "json"}');

        $event = new MarketingSubscriptionEvent($eventName, $marketingSubscription);

        $this->marketingSubscriptionEventSubscriber->dispatchCreateOrUpdateCustomerWithMarketingSubscription($event);
    }

    /**
     * @return iterable<string, array{0: MarketingSubscriptionEventName}>
     */
    public function eventNameProvider(): iterable
    {
        yield 'with marketing subscription was created event name' => [MarketingSubscriptionEventName::MarketingSubscriptionWasCreated];
        yield 'with marketing subscription was updated event name' => [MarketingSubscriptionEventName::MarketingSubscriptionWasUpdated];
        yield 'with marketing subscription email opt in was requested event name' => [MarketingSubscriptionEventName::EmailOptInWasRequested];
    }
}
