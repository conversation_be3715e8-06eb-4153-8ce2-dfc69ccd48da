<?php

declare(strict_types=1);

namespace App\Tests\CanopyDeploy\EventSubscriber;

use App\CanopyDeploy\EventSubscriber\OrderEventSubscriber;
use App\CanopyDeploy\Message\CreateOrUpdateCartOrOrder;
use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Channel\Channel;
use App\Entity\Customer\Customer;
use App\Entity\Order\Order;
use App\Event\Enum\OrderEventName;
use App\Event\OrderEvent;
use App\Serializer\Webhook\OpenApiWebhookPayloadSerializerInterface;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\CustomerFactory;
use App\Tests\Util\Factory\OrderFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use stdClass;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

class OrderEventSubscriberTest extends TestCase
{
    private MessageBusInterface&MockObject $messageBusMock;
    private OrderEventSubscriber $orderEventSubscriber;

    protected function setUp(): void
    {
        $this->messageBusMock = $this->createMock(MessageBusInterface::class);
        $openapiSerializerMock = $this->createMock(OpenApiWebhookPayloadSerializerInterface::class);
        $openapiSerializerMock->method('serialize')->willReturn('{"some": "json"}');

        $this->orderEventSubscriber = new OrderEventSubscriber($this->messageBusMock, $openapiSerializerMock);
    }

    public function testGetSubscribedEventsCallsExistingMethods(): void
    {
        foreach ($this->orderEventSubscriber::getSubscribedEvents() as $subscribedEvent) {
            self::assertTrue(method_exists($this->orderEventSubscriber::class, $subscribedEvent));
        }
    }

    public function testGetSubscribedEventsSubscribesToBackedEnumValues(): void
    {
        $this->assertSame(
            $this->orderEventSubscriber::getSubscribedEvents(),
            [
                OrderEventName::CartWasCreated->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::CartWasUpdated->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::CartWasAbandoned->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderWasCreated->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderWasUpdated->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderPaymentWasRequested->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderWasPaid->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderWasPaidWithAuthorization->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderWasPaidAfterAuthorization->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderWasCancelled->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderWasMarkedForReshipment->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderPrescriptionResponseWasRequestedByDoctor->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderPrescriptionWasPrescribedByDoctor->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderPrescriptionWasDeclinedByDoctor->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderShipmentWasRegisteredAtSupplier->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderShipmentWasRecalledBySupplier->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderShipmentWasReturnedToSupplier->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderShipmentWasCancelledBySupplier->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderShipmentWasSentBySupplier->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderAftercareDoctorHasResponded->name => 'dispatchCreateOrUpdateCartOrOrder',
                OrderEventName::OrderWasFulfilled->name => 'dispatchCreateOrUpdateCartOrOrder',
            ]
        );
    }

    /**
     * @dataProvider provideOrderEvent
     */
    public function testDispatchCreateOrUpdateCartOrOrderDispatchesMessageToBus(OrderEvent $orderEvent): void
    {
        $this->messageBusMock
            ->expects($this->once())
            ->method('dispatch')
            ->with(self::isInstanceOf(CreateOrUpdateCartOrOrder::class))
            ->willReturn(new Envelope(new stdClass()));

        $this->orderEventSubscriber->dispatchCreateOrUpdateCartOrOrder($orderEvent);
    }

    /**
     * @dataProvider provideOrderEventWithoutCustomer
     */
    public function testCustomerIsRequiredForOrder(OrderEvent $orderEvent): void
    {
        $this->messageBusMock
            ->expects($this->never())
            ->method('dispatch')
            ->with(self::isInstanceOf(CreateOrUpdateCartOrOrder::class))
            ->willReturn(new Envelope(new stdClass()));

        $this->orderEventSubscriber->dispatchCreateOrUpdateCartOrOrder($orderEvent);
    }

    /**
     * @dataProvider provideExcludedOrderEvents
     */
    public function testExcludedEventsAreNotTriggered(OrderEvent $orderEvent, bool $dispatchMessage): void
    {
        $this->messageBusMock
            ->expects($dispatchMessage ? $this->once() : $this->never())
            ->method('dispatch')
            ->with(self::isInstanceOf(CreateOrUpdateCartOrOrder::class))
            ->willReturn(new Envelope(new stdClass()));

        $this->orderEventSubscriber->dispatchCreateOrUpdateCartOrOrder($orderEvent);
    }

    public function provideOrderEvent(): iterable
    {
        foreach (OrderEventName::cases() as $orderEventName) {
            $businessUnit = new BusinessUnit();
            $businessUnit->setCode('dokteronline');

            $channel = new Channel();
            $channel->setCode('dok_nl');
            $channel->setBusinessUnit($businessUnit);

            $order = new Order();
            $order->setChannel($channel);

            if (!in_array(
                $orderEventName->name,
                [OrderEventName::CartWasCreated->name, OrderEventName::CartWasUpdated->name],
                true
            )
            ) {
                $order->setCustomer(new Customer());
                yield sprintf('With event name %s', $orderEventName->name) => [new OrderEvent($orderEventName, $order)];
            }
        }
    }

    public function provideOrderEventWithoutCustomer(): iterable
    {
        foreach (OrderEventName::cases() as $orderEventName) {
            if (in_array($orderEventName, [OrderEventName::CartWasCreated, OrderEventName::CartWasUpdated], true)) {
                continue;
            }

            $order = new Order();
            yield sprintf('With event name %s', $orderEventName->name) => [new OrderEvent($orderEventName, $order)];
        }
    }

    public function provideExcludedOrderEvents(): iterable
    {
        $order = OrderFactory::create([
            'state' => 'cart',
            'customer' => CustomerFactory::createPrefilled(),
            'channel' => ChannelFactory::createPrefilled(),
        ]);
        $orderEvent = new OrderEvent(OrderEventName::CartWasCreated, $order);

        yield 'Order with state cart was created' => [$orderEvent, false];

        $order = OrderFactory::create([
            'state' => 'cart',
            'customer' => CustomerFactory::createPrefilled(),
            'channel' => ChannelFactory::createPrefilled(),
        ]);
        $orderEvent = new OrderEvent(OrderEventName::CartWasUpdated, $order);

        yield 'Order with state cart was updated' => [$orderEvent, false];

        $order = OrderFactory::create([
            'state' => 'new',
            'customer' => CustomerFactory::createPrefilled(),
            'channel' => ChannelFactory::createPrefilled(),
        ]);
        $orderEvent = new OrderEvent(OrderEventName::OrderWasCreated, $order);

        yield 'Order with state new was created' => [$orderEvent, true];

        $order = OrderFactory::create([
            'state' => 'new',
            'customer' => CustomerFactory::createPrefilled(),
            'channel' => ChannelFactory::createPrefilled(),
        ]);
        $orderEvent = new OrderEvent(OrderEventName::OrderWasUpdated, $order);

        yield 'Order with state new was updated' => [$orderEvent, true];
    }
}
