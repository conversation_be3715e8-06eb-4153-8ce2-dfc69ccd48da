<?php

declare(strict_types=1);

namespace App\Tests\CanopyDeploy\EventSubscriber;

use App\CanopyDeploy\EventSubscriber\OrderItemSubscriber;
use App\CanopyDeploy\Message\RemoveItemFromOrder;
use App\Entity\Channel\Channel;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Event\OrderItem\RemoveOrderItemEvent;
use App\Serializer\Webhook\OpenApiWebhookPayloadSerializerInterface;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

class OrderItemSubscriberTest extends TestCase
{
    private MessageBusInterface&MockObject $messageBusMock;
    private OpenApiWebhookPayloadSerializerInterface&MockObject $openapiSerializerMock;

    protected function setUp(): void
    {
        $this->messageBusMock = $this->createMock(MessageBusInterface::class);
        $this->openapiSerializerMock = $this->createMock(OpenApiWebhookPayloadSerializerInterface::class);
        $this->openapiSerializerMock->method('serialize')->willReturn('{"some": "json"}');
    }

    public function testCanSubscribeToRemoveOrderItems(): void
    {
        $channel = $this->createMock(Channel::class);

        $customerPool = $this->createMock(CustomerPool::class);
        $customerPool->method('getChannels')->willReturn(new ArrayCollection([$channel]));

        $customer = $this->createMock(Customer::class);
        $customer->method('getCustomerPool')->willReturn($customerPool);

        $order = $this->createMock(Order::class);
        $order->method('getCustomer')->willReturn($customer);

        $orderItem = $this->createMock(OrderItem::class);
        $orderItem->method('getOrder')->willReturn($order);

        $expectedMessage = new RemoveItemFromOrder(
            $order,
            '{"some": "json"}'
        );

        $this->messageBusMock->expects($this->once())
            ->method('dispatch')
            ->willReturn(new Envelope($expectedMessage));

        $subscriber = new OrderItemSubscriber($this->messageBusMock, $this->openapiSerializerMock);
        $subscriber->onRemoveOrderItem(new RemoveOrderItemEvent($orderItem, $order));
    }

    public function testSubscriberWontDispatchOrderItemWithoutChannel(): void
    {
        $order = $this->createMock(Order::class);
        $orderItem = $this->createMock(OrderItem::class);

        $this->messageBusMock->expects($this->never())
            ->method('dispatch');

        $subscriber = new OrderItemSubscriber($this->messageBusMock, $this->openapiSerializerMock);
        $subscriber->onRemoveOrderItem(new RemoveOrderItemEvent($orderItem, $order));
    }
}
