<?php

declare(strict_types=1);

namespace App\Tests\CanopyDeploy\EventSubscriber;

use App\CanopyDeploy\EventSubscriber\ProductUpdateEventSubscriber;
use App\CanopyDeploy\Message\ProductBackInStock;
use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Product\Product;
use App\Event\Enum\ProductUpdateEventName;
use App\Event\ProductUpdateEvent;
use App\Serializer\Webhook\OpenApiWebhookPayloadSerializerInterface;
use App\Tests\Util\Factory\BusinessUnitFactory;
use App\Tests\Util\Factory\ProductFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use stdClass;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

final class ProductUpdateEventSubscriberTest extends TestCase
{
    private MockObject&MessageBusInterface $messageBus;

    private ProductUpdateEventSubscriber $productUpdateEventSubscriber;

    protected function setUp(): void
    {
        $this->messageBus = $this->createMock(MessageBusInterface::class);

        $this->productUpdateEventSubscriber = new ProductUpdateEventSubscriber(
            $this->messageBus,
            $this->createMock(OpenApiWebhookPayloadSerializerInterface::class),
        );
    }

    public function testGetSubscribedEventsShouldSubscribeToAllProductUpdateEventNames(): void
    {
        // Arrange
        $subscribedEvents = ProductUpdateEventSubscriber::getSubscribedEvents();

        // Assert
        self::assertSame(
            array_column(ProductUpdateEventName::cases(), 'name'),
            array_keys($subscribedEvents),
        );
    }

    public function testDispatchProductBackInStock(): void
    {
        // Arrange
        $businessUnitCode = 'dok_de';

        /** @var Product $product */
        $product = ProductFactory::create([
            'code' => $businessUnitCode,
        ]);

        /** @var BusinessUnit $businessUnit */
        $businessUnit = BusinessUnitFactory::create([
            'canopyDeployProductBackInStockWebhookUrl' => '123-product-back-in-stock',
        ]);

        $productUpdateEvent = new ProductUpdateEvent(
            $product,
            $businessUnit,
        );

        // Assert
        $this->messageBus->expects(self::once())
            ->method('dispatch')
            ->with(self::isInstanceOf(ProductBackInStock::class))
            ->willReturn(new Envelope(new stdClass()));

        // Act
        $this->productUpdateEventSubscriber->dispatchProductBackInStock($productUpdateEvent);
    }
}
