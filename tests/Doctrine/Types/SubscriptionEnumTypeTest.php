<?php

declare(strict_types=1);

namespace App\Tests\Doctrine\Types;

use App\Doctrine\Types\SubscriptionEnumType;
use App\Entity\MarketingSubscription\Subscription;
use App\Entity\Product\ProductType;
use Doctrine\DBAL\Platforms\AbstractPlatform;
// use Doctrine\DBAL\Platforms\MariaDb1052Platform;
use Doctrine\DBAL\Platforms\MySQL80Platform;
use Doctrine\DBAL\Platforms\SqlitePlatform;
use InvalidArgumentException;
use PHPUnit\Framework\TestCase;
use ValueError;

class SubscriptionEnumTypeTest extends TestCase
{
    private SubscriptionEnumType $type;

    protected function setUp(): void
    {
        $this->type = new SubscriptionEnumType();
    }

    /**
     * @dataProvider platformProviderForGetSqlDeclarationWithoutDefaultValue
     */
    public function testGetSqlDeclarationWithoutDefaultValue(array $fieldDeclaration, AbstractPlatform $platform, string $expected): void
    {
        $this->assertEquals($expected, $this->type->getSqlDeclaration($fieldDeclaration, $platform));
        $this->assertTrue($this->type->requiresSQLCommentHint($platform));
    }

    public static function platformProviderForGetSqlDeclarationWithoutDefaultValue(): iterable
    {
        yield 'mysql' => [
            ['name' => 'position'],
            new MySQL80Platform(),
            'VARCHAR(255)',
        ];
        yield 'sqlite' => [
            ['name' => 'position'],
            new SqlitePlatform(),
            'VARCHAR(255)',
        ];
        //        yield 'mariadb' => [
        //            ['name' => 'position'],
        //            new MariaDb1052Platform(),
        //            'VARCHAR(255)',
        //        ];
    }

    public function testConvertToDatabaseValue(): void
    {
        $this->assertNull($this->type->convertToDatabaseValue(null, new MySQL80Platform()));
        $this->assertEquals(Subscription::DOUBLE->value, $this->type->convertToDatabaseValue(Subscription::DOUBLE, new MySQL80Platform()));
    }

    public function testInvalidArgumentExceptionInConvertToDatabaseValue(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->type->convertToDatabaseValue(ProductType::SERVICE, new MySQL80Platform());
    }

    public function testConvertToPHPValue(): void
    {
        $this->assertNull($this->type->convertToPHPValue(null, new MySQL80Platform()));
        $this->assertEquals(Subscription::SINGLE, $this->type->convertToPHPValue(Subscription::SINGLE->value, new MySQL80Platform()));
    }

    public function testInvalidArgumentConvertToPHPValue(): void
    {
        $this->expectException(ValueError::class);
        $this->type->convertToPHPValue(ProductType::SERVICE->value, new MySQL80Platform());
    }
}
