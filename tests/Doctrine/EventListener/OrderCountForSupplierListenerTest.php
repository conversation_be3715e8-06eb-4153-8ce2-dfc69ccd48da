<?php

declare(strict_types=1);

namespace App\Tests\Doctrine\EventListener;

use App\Doctrine\EventListener\OrderCountForSupplierListener;
use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShipmentInterface;
use App\Entity\Supplier\Supplier;
use App\Repository\SupplierRepositoryInterface;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Event\PostUpdateEventArgs;
use Doctrine\ORM\UnitOfWork;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class OrderCountForSupplierListenerTest extends TestCase
{
    private const string SUPPLIER_IDENTIFIER = 'supplier-identifier';

    private SupplierRepositoryInterface&MockObject $supplierRepositoryMock;
    private UnitOfWork&MockObject $unitOfWork;
    private EntityManager&MockObject $objectManager;
    private OrderCountForSupplierListener $listener;
    private ShipmentInterface $shipment;

    protected function setUp(): void
    {
        $this->supplierRepositoryMock = $this->createMock(SupplierRepositoryInterface::class);
        $this->listener = new OrderCountForSupplierListener(
            $this->supplierRepositoryMock
        );

        $supplier = new Supplier();
        $supplier->setIdentifier(self::SUPPLIER_IDENTIFIER);

        $this->shipment = new Shipment();
        $this->shipment->setSupplier($supplier);
        $this->objectManager = $this->createMock(EntityManager::class);
        $this->unitOfWork = $this->createMock(UnitOfWork::class);
        $this->objectManager->method('getUnitOfWork')
            ->willReturn($this->unitOfWork);
    }

    /**
     * @dataProvider relevantChangeSetProvider
     */
    public function testPostUpdateWithSupplierShipmentReferenceChange(array $changeSet): void
    {
        $this->unitOfWork->method('getEntityChangeSet')
            ->willReturn($changeSet);

        $this->supplierRepositoryMock
            ->expects($this->once())
            ->method('incrementCurrentDailyOrderCountByIdentifier')
            ->with(self::SUPPLIER_IDENTIFIER);

        $this->listener->postUpdate($this->shipment, new PostUpdateEventArgs($this->shipment, $this->objectManager));
    }

    public function relevantChangeSetProvider(): iterable
    {
        yield 'Shipment has been created in Pharmacy Service' => [
            [
                'supplierShipmentReference' => [
                    0 => null,
                    1 => 'ef1ca29f-0c18-477e-a7aa-a8e520938f76',
                ],
            ],
        ];

        yield 'Shipment has been re-created in Pharmacy Service with other supplier' => [
            [
                'supplierShipmentReference' => [
                    0 => 'ab1ca29f-0c18-577e-a7aa-a8e520938f71',
                    1 => 'ef1ca29f-0c18-477e-a7aa-a8e520938f76',
                ],
            ],
        ];
    }

    /**
     * @dataProvider irrelevantChangeSetProvider
     */
    public function testItSkipsIfReferenceIsNotChanged(array $changeSet): void
    {
        $this->unitOfWork->method('getEntityChangeSet')
            ->willReturn($changeSet);

        $this->supplierRepositoryMock
            ->expects($this->never())
            ->method('incrementCurrentDailyOrderCountByIdentifier');

        $this->listener->postUpdate($this->shipment, new PostUpdateEventArgs($this->shipment, $this->objectManager));
    }

    public function irrelevantChangeSetProvider(): iterable
    {
        yield 'Supplier shipment reference with object' => [
            [
                'supplierShipmentReference' => [
                    0 => null,
                    1 => new Supplier(),
                ],
            ],
        ];

        yield 'Other shipment property' => [
            [
                'otherProperty' => [
                    0 => null,
                    1 => 'ef1ca29f-0c18-477e-a7aa-a8e520938f76',
                ],
            ],
        ];
    }
}
