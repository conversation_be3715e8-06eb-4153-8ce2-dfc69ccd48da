<?php

declare(strict_types=1);

namespace App\Tests\Doctrine\EventListener;

use App\Doctrine\EventListener\CustomerCreated;
use App\Entity\Addressing\Country;
use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\BusinessUnit\BusinessUnitAddress;
use App\Entity\Channel\Channel;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\MarketingSubscription\MarketingSubscription;
use App\Repository\MarketingSubscriptionRepositoryInterface;
use App\Tests\Util\Factory\BusinessUnitFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Channel\Context\ChannelContextInterface;
use Sylius\Component\Locale\Context\LocaleContextInterface;

final class CustomerCreatedTest extends TestCase
{
    private CustomerCreated $customerCreated;

    private MarketingSubscriptionRepositoryInterface&MockObject $marketingSubscriptionRepositoryMock;
    private ChannelContextInterface&MockObject $channelContextMock;
    private LocaleContextInterface&MockObject $localeContextMock;

    protected function setUp(): void
    {
        $this->marketingSubscriptionRepositoryMock = $this->createMock(MarketingSubscriptionRepositoryInterface::class);
        $this->channelContextMock = $this->createMock(ChannelContextInterface::class);
        $this->localeContextMock = $this->createMock(LocaleContextInterface::class);

        $this->customerCreated = new CustomerCreated(
            $this->channelContextMock,
            $this->localeContextMock,
            $this->marketingSubscriptionRepositoryMock,
        );
    }

    public function testPrePersistLinksMarketingSubscription(): void
    {
        // Arrange
        $email = '<EMAIL>';

        $businessUnit = BusinessUnitFactory::createFromArguments();

        $marketingSubscription = new MarketingSubscription($email, 'nl', 'NL', $businessUnit);

        $this->marketingSubscriptionRepositoryMock
            ->expects(self::once())
            ->method('findOneBy')
            ->with([
                'emailAddress' => $email,
                'businessUnit' => $businessUnit,
            ])
            ->willReturn($marketingSubscription);

        $channel = new Channel();
        $channel->setCode('dok_gb');
        $channel->setBusinessUnit($businessUnit);

        $this->channelContextMock
            ->method('getChannel')
            ->willReturn($channel);

        $customerPool = new CustomerPool();
        $customerPool->addChannel($channel);

        $customer = new Customer();
        $customer->setCustomerPool($customerPool);
        $customer->setEmail($email);

        // Act
        $this->customerCreated->prePersist($customer);

        // Assert
        self::assertSame($marketingSubscription, $customer->getMarketingSubscription());
    }

    public function testPrePersistDoesNothingWhenCustomerAlreadyHasMarketingSubscriptionLinked(): void
    {
        // Arrange
        $email = '<EMAIL>';

        $businessUnit = new BusinessUnit();

        $marketingSubscription = new MarketingSubscription($email, 'nl', 'NL', $businessUnit);

        $customer = new Customer();
        $customer->setMarketingSubscription($marketingSubscription);

        // Act
        $this->customerCreated->prePersist($customer);

        // Assert
        self::assertSame($marketingSubscription, $customer->getMarketingSubscription());
    }

    public function testPrePersistCreatesANewMarketingSubscriptionForCustomer(): void
    {
        // Arrange
        $email = '<EMAIL>';
        $countryCode = 'NL';
        $localeCode = 'nl';

        $address = new BusinessUnitAddress();

        $businessUnit = new BusinessUnit();
        $businessUnit->setAddress($address);

        $country = new Country();
        $country->setCode($countryCode);

        $channel = new Channel();
        $channel->setCode('dok_gb');
        $channel->setBusinessUnit($businessUnit);
        $channel->addCountry($country);

        $customerPool = new CustomerPool();
        $customerPool->addChannel($channel);

        $customer = new Customer();
        $customer->setCustomerPool($customerPool);
        $customer->setEmail($email);

        $this->channelContextMock
            ->method('getChannel')
            ->willReturn($channel);

        $this->localeContextMock
            ->method('getLocaleCode')
            ->willReturn($localeCode);

        // Act
        $this->customerCreated->prePersist($customer);

        // Assert
        $marketingSubscription = $customer->getMarketingSubscription();
        self::assertInstanceOf(MarketingSubscription::class, $marketingSubscription);
        self::assertSame($email, $marketingSubscription->getEmail());
        self::assertSame($countryCode, $marketingSubscription->getCountryCode());
        self::assertSame($localeCode, $marketingSubscription->getLocaleCode());
        self::assertSame($businessUnit, $marketingSubscription->getBusinessUnit());
    }
}
