<?php

declare(strict_types=1);

namespace App\Tests\Security\UserProvider;

use App\Entity\Order\Order;
use App\Entity\User\AdminUser;
use App\Entity\User\AdminUserInterface;
use App\Factory\User\AdminUserFactory;
use App\Logging\Context\Context;
use App\Logging\Context\Tags;
use App\Repository\AdminUserRepositoryInterface;
use App\Security\Rbac\PermissionMapInterface;
use App\Security\Rbac\Scope;
use App\Security\UserProvider\OAuthAdminUserProvider;
use Doctrine\ORM\EntityManagerInterface;
use HWI\Bundle\OAuthBundle\OAuth\Response\AbstractUserResponse;
use HWI\Bundle\OAuthBundle\OAuth\Response\UserResponseInterface;
use HWI\Bundle\OAuthBundle\Security\Core\Authentication\Token\OAuthToken;
use LogicException;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Sylius\Component\User\Model\User;

final class OAuthAdminUserProviderTest extends TestCase
{
    public const string AUTH0_IDENTIFIER = 'auth0|654cda176d488d6b9ed17b9c';
    public const string AUTH0_EMAIL = '<EMAIL>';

    private OAuthAdminUserProvider $provider;
    private AdminUserFactory $adminUserFactory;

    private MockObject&EntityManagerInterface $entityManagerMock;
    private MockObject&AdminUserRepositoryInterface $adminUserRepositoryMock;
    private MockObject&LoggerInterface $loggerMock;

    protected function setUp(): void
    {
        $this->adminUserFactory = new AdminUserFactory();

        $this->entityManagerMock = $this->createMock(EntityManagerInterface::class);
        $this->adminUserRepositoryMock = $this->createMock(AdminUserRepositoryInterface::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);

        $this->provider = new OAuthAdminUserProvider(
            $this->entityManagerMock,
            $this->adminUserFactory,
            $this->adminUserRepositoryMock,
            $this->loggerMock,
        );
    }

    public function testLoadUserByOauthUserResponseTriggersErrorLogForInvalidScope(): void
    {
        // Arrange
        $response = $this->getUserResponseWithInvalidScope();

        // Assert
        $this->loggerMock->expects(self::once())
            ->method('error')
            ->with(
                "Invalid scope 'invalid-scope' received from Auth0.",
                [
                    Context::Tags->value => [
                        Tags::UserEmail->value => $response->getEmail(),
                    ],
                ],
            );

        // Act
        $this->provider->loadUserByOAuthUserResponse($response);
    }

    public function testLoadUserByOauthUserResponseCreatesNewUser(): void
    {
        // Assert
        $this->entityManagerMock->expects(self::once())
            ->method('persist')
            ->with(self::isInstanceOf(AdminUserInterface::class));

        $this->entityManagerMock->expects(self::once())
            ->method('flush');

        $this->loggerMock->expects(self::never())
            ->method('error');

        // Act
        $adminUser = $this->provider->loadUserByOAuthUserResponse($this->getUserResponse());

        // Assert
        self::assertSame($adminUser->getAuth0Id(), 'auth0|654cda176d488d6b9ed17b9c');
        self::assertSame($adminUser->getEmail(), '<EMAIL>');
        self::assertSame($adminUser->getEmailCanonical(), '<EMAIL>');
        self::assertSame($adminUser->getUsernameCanonical(), 'kk');
        foreach (Scope::cases() as $scope) {
            self::assertTrue($adminUser->hasRole($scope->getAsRole()));
        }

        foreach (OAuthAdminUserProvider::DEFAULT_AUTH0_SCOPES as $scope) {
            self::assertFalse($adminUser->hasRole($scope));
        }

        foreach (PermissionMapInterface::DEFAULT_ADMIN_ROLES as $role) {
            self::assertTrue($adminUser->hasRole($role));
        }
    }

    public function testLoadUserByOauthUserResponseUpdatesExistingUser(): void
    {
        // Arrange
        $adminUser = $this->adminUserFactory->createNew();
        $adminUser->setEmail(self::AUTH0_EMAIL);

        $this->adminUserRepositoryMock->method('findOneByAuth0IdOrEmail')
            ->with(self::AUTH0_IDENTIFIER, self::AUTH0_EMAIL)
            ->willReturn($adminUser);

        // Assert
        $this->entityManagerMock->expects(self::never())
            ->method('persist');

        $this->entityManagerMock->expects(self::once())
            ->method('flush');

        // Act
        $adminUser = $this->provider->loadUserByOAuthUserResponse($this->getUserResponse());

        // Assert
        self::assertSame(self::AUTH0_IDENTIFIER, $adminUser->getAuth0Id());
        foreach (Scope::cases() as $scope) {
            self::assertTrue($adminUser->hasRole($scope->getAsRole()));
        }
        foreach (PermissionMapInterface::DEFAULT_ADMIN_ROLES as $role) {
            self::assertTrue($adminUser->hasRole($role));
        }
    }

    /**
     * @dataProvider provideClassNames
     */
    public function testSupportsAdminUserClass(bool $supports, string $class): void
    {
        self::assertSame($supports, $this->provider->supportsClass($class));
    }

    public function testLoadUserByIdentifier(): void
    {
        // Arrange
        $adminUser = new AdminUser();

        // Assert
        $this->adminUserRepositoryMock->expects(self::once())
            ->method('findOneByAuth0Id')
            ->with('auth0-id')
            ->willReturn($adminUser);

        // Act & Assert
        self::assertSame(
            $adminUser,
            $this->provider->loadUserByIdentifier('auth0-id'),
        );
    }

    public function testRefreshUser(): void
    {
        // Arrange
        $adminUser = new AdminUser();

        // Assert
        $this->entityManagerMock->expects(self::once())
            ->method('refresh')
            ->with($adminUser);

        // Act
        $this->provider->refreshUser($adminUser);
    }

    /**
     * @return iterable<array{bool, string}>
     */
    public function provideClassNames(): iterable
    {
        yield [true, AdminUser::class];

        yield [false, User::class];

        yield [false, Order::class];

        yield [false, ''];

        yield [false, 'AdminUser'];
    }

    private function getUserResponseWithInvalidScope(): UserResponseInterface
    {
        return new class () extends AbstractUserResponse {
            public function getUsername(): string
            {
                throw new LogicException('Not implemented');
            }

            public function getNickname(): string
            {
                return 'KK';
            }

            public function getFirstName(): string
            {
                throw new LogicException('Not implemented');
            }

            public function getLastName(): string
            {
                throw new LogicException('Not implemented');
            }

            public function getRealName(): string
            {
                return 'Kevin';
            }

            public function getUserIdentifier(): string
            {
                return OAuthAdminUserProviderTest::AUTH0_IDENTIFIER;
            }

            public function getEmail(): string
            {
                return OAuthAdminUserProviderTest::AUTH0_EMAIL;
            }

            public function getOAuthToken(): OAuthToken
            {
                return new OAuthToken([
                    'access_token' => 'access_token',
                    'scope' => implode(' ', OAuthAdminUserProvider::DEFAULT_AUTH0_SCOPES)
                        .' '.Scope::getAllScopesAsString().' invalid-scope',
                ]);
            }
        };
    }

    private function getUserResponse(): UserResponseInterface
    {
        return new class () extends AbstractUserResponse {
            public function getUsername(): string
            {
                throw new LogicException('Not implemented');
            }

            public function getNickname(): string
            {
                return 'KK';
            }

            public function getFirstName(): string
            {
                throw new LogicException('Not implemented');
            }

            public function getLastName(): string
            {
                throw new LogicException('Not implemented');
            }

            public function getRealName(): string
            {
                return 'Kevin';
            }

            public function getUserIdentifier(): string
            {
                return OAuthAdminUserProviderTest::AUTH0_IDENTIFIER;
            }

            public function getEmail(): string
            {
                return OAuthAdminUserProviderTest::AUTH0_EMAIL;
            }

            public function getOAuthToken(): OAuthToken
            {
                return new OAuthToken([
                    'access_token' => 'access_token',
                    'scope' => implode(' ', OAuthAdminUserProvider::DEFAULT_AUTH0_SCOPES)
                        .' '.Scope::getAllScopesAsString(),
                ]);
            }
        };
    }
}
