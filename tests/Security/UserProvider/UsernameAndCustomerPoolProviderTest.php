<?php

declare(strict_types=1);

namespace App\Tests\Security\UserProvider;

use App\CustomerPool\Doctrine\FindShopUserByUsernameAndCustomerPoolQuery;
use App\Entity\Customer\Customer;
use App\Entity\CustomerPool\CustomerPool;
use App\Entity\User\AdminUser;
use App\Entity\User\ShopUser;
use App\Security\UserProvider\UsernameAndCustomerPoolProvider;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Bundle\UserBundle\Provider\UserProviderInterface;
use Sylius\Component\Core\Model\ShopUserInterface;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Core\User\InMemoryUser;

class UsernameAndCustomerPoolProviderTest extends TestCase
{
    private const int VALID_CUSTOMER_POOL_ID = 99;
    private const int INVALID_CUSTOMER_POOL_ID = 88;

    private UserProviderInterface&MockObject $innerUserProvider;
    private FindShopUserByUsernameAndCustomerPoolQuery&MockObject $findShopUserByUsernameAndCustomerPoolQuery;
    private EntityManagerInterface&MockObject $entityManager;
    private UsernameAndCustomerPoolProvider $usernameAndCustomerPoolProvider;

    protected function setUp(): void
    {
        $this->innerUserProvider = $this->createMock(UserProviderInterface::class);
        $this->findShopUserByUsernameAndCustomerPoolQuery = $this->createMock(
            FindShopUserByUsernameAndCustomerPoolQuery::class
        );
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->usernameAndCustomerPoolProvider = new UsernameAndCustomerPoolProvider(
            $this->innerUserProvider,
            $this->findShopUserByUsernameAndCustomerPoolQuery,
            $this->entityManager
        );
    }

    public function testCanLoadUserByIdentifierAndPayload(): void
    {
        $payload = [UsernameAndCustomerPoolProvider::CUSTOMER_POOL_ID => self::VALID_CUSTOMER_POOL_ID];

        $customerPool = $this->createMock(CustomerPool::class);
        $customerPool->method('getId')
            ->willReturn(self::VALID_CUSTOMER_POOL_ID);

        $customer = new Customer();
        $customer->setCustomerPool($customerPool);

        $expectedShopUser = new ShopUser();
        $expectedShopUser->setUsername('testuser');
        $expectedShopUser->setCustomer($customer);

        $this->entityManager->expects(self::once())
            ->method('getReference')
            ->with(CustomerPool::class, self::VALID_CUSTOMER_POOL_ID)
            ->willReturn($customerPool);

        $this->findShopUserByUsernameAndCustomerPoolQuery->expects(self::once())
            ->method('findOneByUsernameAndCustomerPool')
            ->with('testuser', $customerPool)
            ->willReturn($expectedShopUser);

        $user = $this->usernameAndCustomerPoolProvider->loadUserByIdentifierAndPayload('testuser', $payload);

        self::assertSame($expectedShopUser, $user);
    }

    public function testCanLoadUserByIdentifier(): void
    {
        $payload = [];

        $expectedAdminUser = new AdminUser();
        $expectedAdminUser->setUsername('testAdmin');

        $this->innerUserProvider->expects($this->once())
            ->method('loadUserByUsername')
            ->willReturn($expectedAdminUser);

        $user = $this->usernameAndCustomerPoolProvider->loadUserByIdentifierAndPayload('testAdmin', $payload);

        self::assertSame($expectedAdminUser, $user);
    }

    public function testCanThrowExceptionWhenUserIsNotFoundInCustomerPool(): void
    {
        $payload = [UsernameAndCustomerPoolProvider::CUSTOMER_POOL_ID => self::INVALID_CUSTOMER_POOL_ID];

        $customerPool = $this->createMock(CustomerPool::class);
        $customerPool->method('getId')
            ->willReturn(self::VALID_CUSTOMER_POOL_ID);
        $customerPool->setCode('valid_customer_pool_code');

        $otherCustomerPool = $this->createMock(CustomerPool::class);
        $otherCustomerPool->method('getId')
            ->willReturn(self::INVALID_CUSTOMER_POOL_ID);
        $otherCustomerPool->setCode('invalid_customer_pool_code');

        $expectedShopUser = new ShopUser();
        $expectedShopUser->setUsername('testuser');

        $customer = new Customer();
        $customer->setCustomerPool($customerPool);

        $expectedShopUser->setCustomer($customer);

        $this->entityManager->expects(self::once())
            ->method('getReference')
            ->with(CustomerPool::class, self::INVALID_CUSTOMER_POOL_ID)
            ->willReturn($otherCustomerPool);

        $this->findShopUserByUsernameAndCustomerPoolQuery->expects(self::once())
            ->method('findOneByUsernameAndCustomerPool')
            ->with('testuser', $otherCustomerPool)
            ->willReturn(null);

        $this->expectException(UserNotFoundException::class);
        $this->expectExceptionMessage(
            sprintf(
                'Username "%s" does not exist in "%s" customer pool.',
                'testuser',
                $otherCustomerPool->getCode(),
            )
        );

        $this->usernameAndCustomerPoolProvider->loadUserByIdentifierAndPayload('testuser', $payload);
    }

    /**
     * @dataProvider provideUnmodifiedMethodCalls
     */
    public function testItForwardsCallsToUnmodifiedMethodsToDecoratedUserProvider(
        string $methodName,
        array $arguments,
        mixed $expectedReturnValue,
    ): void {
        $this->innerUserProvider->expects($this->once())
            ->method($methodName)
            ->with(...$arguments)
            ->willReturn($expectedReturnValue);

        $returnValue = $this->usernameAndCustomerPoolProvider->{$methodName}(...$arguments);

        $this->assertSame($expectedReturnValue, $returnValue);
    }

    public function provideUnmodifiedMethodCalls(): array
    {
        return [
            'refreshUser' => [
                'refreshUser',
                [new InMemoryUser('testuser', 'foobar')],
                new InMemoryUser('testuser', 'foobar'),
            ],
            'supportsClass' => [
                'supportsClass',
                [ShopUserInterface::class],
                true,
            ],
            'loadUserByUsername' => [
                'loadUserByUsername',
                ['mister_test'],
                new InMemoryUser('mister_test', 'notapassword'),
            ],
        ];
    }
}
