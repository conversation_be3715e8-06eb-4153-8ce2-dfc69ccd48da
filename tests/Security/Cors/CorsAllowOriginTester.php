<?php

declare(strict_types=1);

namespace App\Tests\Security\Cors;

use PHPUnit\Framework\TestCase;

final class CorsAllowOriginTester extends TestCase
{
    /**
     * @dataProvider provideEnvironmentAndUrls
     *
     * @param array<string> $urls
     */
    public function testCorsAllowOrigin(string $env, array $urls): void
    {
        // Arrange
        $corsAllowOrigin = $this->getCorsAllowOriginFromEnv($env);
        self::assertNotEmpty($corsAllowOrigin);

        // Act
        foreach ($urls as $url) {
            $match = preg_match("/$corsAllowOrigin/", $url);

            // Assert
            self::assertNotFalse(
                (bool) $match,
                sprintf("The url '%s' is not in the CorsAllowOrigin for environment '%s'.", $url, $env)
            );
        }
    }

    protected function provideEnvironmentAndUrls(): iterable
    {
        yield [
            '.env.dev',
            [
                'https://dokteronline.commerce.ehvg.dev',
                'https://dokteronline.commerce.ehvg.dev',
                'https://consult.ehvg.dev',
                'https://literally-any-valid-url.localhost',
                'or-not-valid-urls-we-dont-care-in-dev-env',
            ],
        ];

        yield [
            '.env.seeme_dev',
            [
                'https://seemenopause.commerce.ehvg.dev',
                'https://consult.ehvg.dev',
                'https://literally-any-valid-url.localhost',
                'or-not-valid-urls-we-dont-care-in-dev-env',
            ],
        ];

        yield [
            '.env.dta_test',
            [
                'https://deploy-preview-1337--blueclinic.netlify.app',
                'https://deploy-preview-420--dokteronline.netlify.app',
                'https://deploy-preview-777--doctoronline-uk.netlify.app',
                'https://test--anamnesis-admin-dokteronline.netlify.app',
                'https://test--doctoronline-uk.netlify.app',
                'https://consult.sbtest.nl',
            ],
        ];

        yield [
            '.env.seeme_dta_test',
            [
                'https://deploy-preview-6--seemenopause.netlify.app',
                'https://deploy-preview-666--seemenopause.netlify.app',
                'https://consult.sbtest.nl',
                'http://seemenopause.dev.loc', // BitPuma development address
                'https://seemenopause-mvp.bitpuma.nl', // BitPuma acceptance address?
                'https://seeme-nopause-pentest.bitpuma.nl',
                'https://test--anamnesis-admin-see-me.netlify.app',
            ],
        ];

        yield [
            '.env.accept',
            [
                'https://acceptance--dokteronline.netlify.app',
                'https://acceptance--blueclinic.netlify.app',
                'https://acceptance--doctoronline-uk.netlify.app',
                'https://acceptance--anamnesis-admin-dokteronline.netlify.app',
                'https://imperium-acceptance--dokteronline.netlify.app',
                'https://consult.sbaccept.nl',
            ],
        ];

        yield [
            '.env.seeme_accept',
            [
                'https://acceptance--seemenopause.netlify.app',
                'https://consult.sbaccept.nl',
                'https://seeme-nopause-acceptatie.bitpuma.nl',
                'https://acceptance--anamnesis-admin-see-me.netlify.app',
            ],
        ];

        yield [
            '.env.prod',
            [
                'https://anamnesis.dokteronline.com',
                'https://anamnesis.doctoronline.com',
                'https://dokteronline.com',
                'https://doctoronline.co.uk',
                'https://www.dokteronline.com',
                'https://www.doctoronline.co.uk',
                'https://dokteronline.netlify.app',
                'https://doctoronline-uk.netlify.app',
                'https://blueclinic.co.uk',
                'https://www.blueclinic.co.uk',
                'https://consult.ehealthventuresgroup.com',
                'https://imperium-preview--dokteronline.netlify.app',
            ],
        ];

        yield [
            '.env.seeme_prod',
            [
                'https://anamnesis.seemenopause.com',
                'https://seemenopause.com',
                'https://www.seemenopause.com',
                'https://consult.ehealthventuresgroup.com',
            ],
        ];

        yield [
            '.env.test',
            [
                'https://dokteronline.commerce.ehvg.dev',
                'https://seemenopause.commerce.ehvg.dev',
                'https://dokteronline.commerce.ehvg.dev',
            ],
        ];
    }

    private function getCorsAllowOriginFromEnv(string $env): string
    {
        $fileContent = file_get_contents(__DIR__.'/../../../'.$env);

        preg_match("/^CORS_ALLOW_ORIGIN='(.*)'$/m", $fileContent, $matches);

        self::assertArrayHasKey(1, $matches, sprintf("Could not find CORS_ALLOW_ORIGIN in env file '%s'.", $env));

        return $matches[1];
    }
}
