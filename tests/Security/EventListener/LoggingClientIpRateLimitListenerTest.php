<?php

declare(strict_types=1);

namespace App\Tests\Security\EventListener;

use App\Security\EventListener\LoggingClientIpRateLimitListener;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\RateLimiter\RateLimiterFactory;
use Symfony\Component\RateLimiter\Storage\InMemoryStorage;

final class LoggingClientIpRateLimitListenerTest extends TestCase
{
    private LoggerInterface&MockObject $logger;
    private RequestEvent&MockObject $event;

    private LoggingClientIpRateLimitListener $listener;

    protected function setUp(): void
    {
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->event = $this->createMock(RequestEvent::class);

        $authenticateLimiterFactory = new RateLimiterFactory(
            [
                'id' => 'authenticate_api',
                'policy' => 'sliding_window',
                'limit' => 5,
                'interval' => '5 minutes',
            ],
            new InMemoryStorage()
        );

        $registerLimiterFactory = new RateLimiterFactory(
            [
                'id' => 'register_api',
                'policy' => 'sliding_window',
                'limit' => 10,
                'interval' => '60 minutes',
            ],
            new InMemoryStorage()
        );

        $this->listener = new LoggingClientIpRateLimitListener(
            authenticateApiLimiter:  $authenticateLimiterFactory,
            registerApiLimiter: $registerLimiterFactory,
            logger: $this->logger,
        );
    }

    public function testItLogsWhenRegisterLimitExceeded(): void
    {
        $ip = '***************';
        $path = '/api/shop/account/register';
        $content = (string) json_encode(['email' => '<EMAIL>'], JSON_THROW_ON_ERROR);

        $request = new Request(
            server: [
                'REQUEST_URI' => $path,
                'REMOTE_ADDR' => $ip,
            ],
            content: $content
        );

        $this->event->method('isMainRequest')->willReturn(true);
        $this->event->method('getRequest')->willReturn($request);

        for ($i = 0; $i < 10; ++$i) {
            $this->listener->__invoke($this->event);
        }

        $this->logger
            ->expects($this->once())
            ->method('error')
            ->with(
                'Many registration attempts detected from the same ip address.',
                $this->callback(function (array $context) use ($ip): bool {
                    return isset($context['clientIpHash']) && $context['clientIpHash'] === sha1($ip);
                })
            );

        $this->listener->__invoke($this->event);
    }

    public function testItDoesNotLogWhenRegisterLimitNotExceeded(): void
    {
        $request = new Request(
            server: [
                'REQUEST_URI' => '/api/shop/account/register',
                'REMOTE_ADDR' => '*********',
            ],
            content: 'register_payload'
        );

        $this->event->method('isMainRequest')->willReturn(true);
        $this->event->method('getRequest')->willReturn($request);

        $this->logger->expects($this->never())->method('error');

        $this->listener->__invoke($this->event);
    }
}
