<?php

declare(strict_types=1);

namespace App\Tests\Security\EventListener;

use App\Security\EventListener\RateLimitListener;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Component\User\Canonicalizer\Canonicalizer;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\RateLimiter\RateLimiterFactory;
use Symfony\Component\RateLimiter\Storage\InMemoryStorage;

final class RateLimitListenerTest extends TestCase
{
    private RequestEvent&MockObject $event;
    private RateLimitListener $listener;

    protected function setUp(): void
    {
        $rateLimiterFactory = new RateLimiterFactory(
            [
                'id' => 'authenticate_api',
                'policy' => 'sliding_window',
                'limit' => 5,
                'interval' => '5 minutes',
            ],
            new InMemoryStorage()
        );

        $this->listener = new RateLimitListener($rateLimiterFactory, new Canonicalizer());
        $this->event = $this->createMock(RequestEvent::class);
    }

    public function testOnKernelRequestNonMainRequest(): void
    {
        // Arrange
        $request = new Request(server: ['REQUEST_URI' => '/api/shop/account/authenticate']);
        $this->event->method('isMainRequest')->willReturn(false);
        $this->event->method('getRequest')->willReturn($request);

        // Act
        $this->listener->__invoke($this->event);

        // Assert
        $this->assertNull($this->event->getResponse());
    }

    public function testOnKernelRequestNonAuthenticatePath(): void
    {
        // Arrange
        $request = new Request(server: ['REQUEST_URI' => '/another/path']);
        $this->event->method('isMainRequest')->willReturn(true);
        $this->event->method('getRequest')->willReturn($request);

        // Act
        $this->listener->__invoke($this->event);

        // Assert
        $this->assertNull($this->event->getResponse());
    }

    public function testOnKernelRequestWithoutEmailInRequest(): void
    {
        // Arrange
        $request = new Request(server: ['REQUEST_URI' => '/api/shop/account/authenticate']);
        $request->initialize(content: (string) json_encode([], JSON_THROW_ON_ERROR));
        $this->event->method('isMainRequest')->willReturn(true);
        $this->event->method('getRequest')->willReturn($request);

        // Act
        $this->listener->__invoke($this->event);

        // Assert
        $this->assertNull($this->event->getResponse());
    }

    public function testOnKernelRequestAcceptedRequest(): void
    {
        // Arrange
        $requestData = json_encode(['email' => '<EMAIL>'], JSON_THROW_ON_ERROR);
        $request = new Request(server: ['REQUEST_URI' => '/api/shop/account/authenticate'], content: $requestData);
        $this->event->method('isMainRequest')->willReturn(true);
        $this->event->method('getRequest')->willReturn($request);

        // Act
        $this->listener->__invoke($this->event);

        // Assert
        $this->assertNull($this->event->getResponse());
    }

    public function testOnKernelRequestRejectedRequest(): void
    {
        // Arrange
        $requestData = json_encode(['email' => '<EMAIL>'], JSON_THROW_ON_ERROR);
        $request = new Request(server: ['REQUEST_URI' => '/api/shop/account/authenticate'], content: $requestData);

        $this->event->method('isMainRequest')->willReturn(true);
        $this->event->method('getRequest')->willReturn($request);
        $this->event->expects($this->once())->method('setResponse')->with(
            new JsonResponse(
                [
                    'type' => 'about:blank',
                    'title' => 'An error occurred.',
                    'status' => 429,
                    'detail' => 'Too Many Requests.',
                ],
                429
            )
        );

        // Act
        $this->listener->__invoke($this->event);
        $this->listener->__invoke($this->event);
        $this->listener->__invoke($this->event);
        $this->listener->__invoke($this->event);
        $this->listener->__invoke($this->event);
        $this->listener->__invoke($this->event);
    }
}
