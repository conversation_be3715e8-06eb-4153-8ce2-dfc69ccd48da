<?php

declare(strict_types=1);

namespace App\Tests\Security\Rbac\Templating\Helper;

use App\Entity\User\AdminUserInterface;
use App\Security\Rbac\Context\AdminUserContextInterface;
use App\Security\Rbac\Resolver\AdminPermissionResolverInterface;
use App\Security\Rbac\Templating\Helper\AclHelper;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Bundle\ResourceBundle\Controller\RequestConfiguration;
use Sylius\Bundle\ResourceBundle\Grid\View\ResourceGridView;
use Sylius\Component\Grid\Definition\Action;

final class AclHelperTest extends TestCase
{
    private AdminPermissionResolverInterface&MockObject $permissionResolver;
    private AdminUserContextInterface&MockObject $userContext;
    private AclHelper $aclHelper;

    protected function setUp(): void
    {
        $this->permissionResolver = $this->createMock(AdminPermissionResolverInterface::class);
        $this->userContext = $this->createMock(AdminUserContextInterface::class);
        $this->aclHelper = new AclHelper($this->permissionResolver, $this->userContext);
    }

    public function testGetAvailableGridActionsWithAvailableAction(): void
    {
        // Arrange
        $resourceGridView = $this->createMock(ResourceGridView::class);
        $requestConfiguration = $this->createMock(RequestConfiguration::class);
        $resourceGridView->method('getRequestConfiguration')->willReturn($requestConfiguration);

        $action = Action::fromNameAndType('edit', 'link');
        $action->setOptions(['link' => ['route' => 'edit_route']]);
        $actions = [$action];

        $this->permissionResolver->method('resolvePermission')->with('edit_route')->willReturn(true);

        // Act
        $result = $this->aclHelper->getAvailableGridActions($resourceGridView, $actions);

        // Assert
        $this->assertCount(1, $result);
        $this->assertEquals($action, $result[0]);
    }

    public function testGetAvailableGridActionsWithUnavailableAction(): void
    {
        // Arrange
        $resourceGridView = $this->createMock(ResourceGridView::class);
        $requestConfiguration = $this->createMock(RequestConfiguration::class);
        $resourceGridView->method('getRequestConfiguration')->willReturn($requestConfiguration);

        $action = Action::fromNameAndType('edit', 'link');
        $action->setOptions(['link' => ['route' => 'edit_route']]);
        $actions = [$action];

        $this->permissionResolver->method('resolvePermission')->with('edit_route')->willReturn(false);

        // Act
        $result = $this->aclHelper->getAvailableGridActions($resourceGridView, $actions);

        // Assert
        $this->assertCount(0, $result);
    }

    public function testHasPermission(): void
    {
        // Arrange
        $this->permissionResolver->method('resolvePermission')->with('some_permission')->willReturn(true);

        // Act
        $result = $this->aclHelper->hasPermission('some_permission');

        // Assert
        $this->assertTrue($result);
    }

    public function testIsAdmin(): void
    {
        // Arrange
        $adminUser = $this->createMock(AdminUserInterface::class);
        $this->userContext->method('getAdminUser')->willReturn($adminUser);

        // Act
        $result = $this->aclHelper->isAdmin();

        // Assert
        $this->assertTrue($result);
    }

    public function testGetName(): void
    {
        // Act
        $result = $this->aclHelper->getName();

        // Assert
        $this->assertEquals('sylius_rbac', $result);
    }
}
