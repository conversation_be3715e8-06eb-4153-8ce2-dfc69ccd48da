<?php

declare(strict_types=1);

namespace App\Tests\Security\Rbac\Privilege;

use App\Security\Rbac\Exception\PrivilegeArgumentNotImplementedException;
use App\Security\Rbac\Privilege\ResourcePrivilege;
use P<PERSON>Unit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Routing\Route;
use Symfony\Component\Routing\RouteCollection;
use Symfony\Component\Routing\RouterInterface;

final class ResourcePrivilegeTest extends TestCase
{
    /**
     * @var array<string, array<string, mixed>>
     */
    private array $permissions;

    private RouterInterface&MockObject $router;

    private ResourcePrivilege $resourcePrivilege;

    protected function setUp(): void
    {
        // Arrange: Mock the RouterInterface
        $this->router = $this->createMock(RouterInterface::class);

        // Arrange: Define the permissions array
        $this->permissions = [
            'app_test_permission' => [
                'enabled' => true,
                'label' => 'Test Permission',
            ],
            'app_disabled_permission' => [
                'enabled' => false,
            ],
        ];

        // Arrange: Create an instance of ResourcePrivilege
        $this->resourcePrivilege = new ResourcePrivilege($this->router, $this->permissions);
    }

    public function testGetSupportedRouteMapWithAdminSection(): void
    {
        // Arrange: Set up a route collection with a route having admin section
        $routeCollection = new RouteCollection();
        $route = new Route('/admin/test');
        $route->setDefault('_sylius', ['permission' => true, 'section' => 'admin']);
        $route->setDefault('_controller', 'foo:bar.baz.qux');
        $routeCollection->add('app_admin_test', $route);

        $this->router->method('getRouteCollection')->willReturn($routeCollection);

        // Act: Call getSupportedRouteMap
        $supportedRouteMap = $this->resourcePrivilege->getSupportedRouteMap();

        // Assert: Verify the expected output
        $this->assertArrayHasKey('app_admin_test', $supportedRouteMap);
        $this->assertEquals('app_admin_test', $supportedRouteMap['app_admin_test']);
    }

    public function testGetSupportedRouteMapWithCustomPermissionsDisabled(): void
    {
        // Arrange: Set up a route collection without the specific custom permission
        $routeCollection = new RouteCollection();
        $this->router->method('getRouteCollection')->willReturn($routeCollection);

        // Act: Call getSupportedRouteMap
        $supportedRouteMap = $this->resourcePrivilege->getSupportedRouteMap([], false);

        // Assert: Verify that disabled permissions are not included
        $this->assertArrayNotHasKey('app_disabled_permission', $supportedRouteMap);
    }

    public function testAddCustomPermissionsWithoutParentThrowsException(): void
    {
        // Arrange: Set up a route collection and modify permissions
        $routeCollection = new RouteCollection();
        $this->router->method('getRouteCollection')->willReturn($routeCollection);
        $this->permissions = [
            'app_test_permission_without_parent' => [
                'enabled' => true,
                'label' => 'Test Permission Without Parent',
            ],
        ];
        $this->resourcePrivilege = new ResourcePrivilege($this->router, $this->permissions);

        // Expectations: Assert that an exception is thrown
        $this->expectException(PrivilegeArgumentNotImplementedException::class);

        // Act: Call getSupportedRouteMap expecting an exception
        $this->resourcePrivilege->getSupportedRouteMap([], true);
    }

    public function testGetSupportedRouteMapWithAdminAjax(): void
    {
        // Arrange: Set up a route collection with a route having admin ajax
        $routeCollection = new RouteCollection();
        $route = new Route('/admin/ajax/test');
        $route->setDefault('_sylius', ['permission' => true]);
        $route->setDefault('_controller', 'foo:bar.baz.qux');

        $routeCollection->add('app_admin_ajax_test', $route);

        $this->router->method('getRouteCollection')->willReturn($routeCollection);

        // Act: Call getSupportedRouteMap
        $supportedRouteMap = $this->resourcePrivilege->getSupportedRouteMap();

        // Assert: Verify the expected output
        $this->assertArrayHasKey('app_admin_ajax_test', $supportedRouteMap);
        $this->assertEquals('ajax.test', $supportedRouteMap['app_admin_ajax_test']);
    }

    public function testIsStateMachineAction(): void
    {
        // Arrange: Set up a route with state machine
        $routeCollection = new RouteCollection();
        $route = new Route('/admin/machine_action');
        $route->setDefault('_sylius', ['permission' => true, 'state_machine' => ['transition' => 'test_transition']]);
        $route->setDefault('_controller', 'foo:bar.baz.qux');

        $routeCollection->add('app_machine_action', $route);

        $this->router->method('getRouteCollection')->willReturn($routeCollection);

        // Act: Call getSupportedRouteMap
        $supportedRouteMap = $this->resourcePrivilege->getSupportedRouteMap();

        // Assert: Verify the expected output
        $this->assertArrayHasKey('app_machine_action', $supportedRouteMap);
        $this->assertEquals('action.qux_test_transition', $supportedRouteMap['app_machine_action']);
    }
}
