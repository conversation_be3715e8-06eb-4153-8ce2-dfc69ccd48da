<?php

declare(strict_types=1);

namespace App\Tests\Security\Rbac\Checker;

use App\Security\Rbac\Checker\AuthorizationCheckerInterface;
use App\Security\Rbac\Checker\CompositeAuthorizationChecker;
use PHPUnit\Framework\TestCase;

final class CompositeAuthorizationCheckerTest extends TestCase
{
    // Test that `isGranted` returns true when no authorizations are added.
    public function testIsGrantedReturnsTrueWhenNoCheckersPresent(): void
    {
        // Arrange: Create an instance of CompositeAuthorizationChecker with no authorization checkers.
        $checker = new CompositeAuthorizationChecker();

        // Act: Call the isGranted method for a random permission.
        $result = $checker->isGranted('example_permission');

        // Assert: Ensure the result is true since no checkers should imply granted.
        $this->assertTrue($result);
    }

    // Test that `isGranted` returns true when all checkers return true.
    public function testIsGrantedReturnsTrueWhenAllCheckersGrant(): void
    {
        // Arrange: Create mock checkers that grant permission.
        $checker1 = $this->createMock(AuthorizationCheckerInterface::class);
        $checker1->method('isGranted')->willReturn(true);

        $checker2 = $this->createMock(AuthorizationCheckerInterface::class);
        $checker2->method('isGranted')->willReturn(true);

        // Arrange: Add the mock checkers to the CompositeAuthorizationChecker.
        $compositeChecker = new CompositeAuthorizationChecker();
        $compositeChecker->addAuthorizationChecker($checker1);
        $compositeChecker->addAuthorizationChecker($checker2);

        // Act: Check if permission is granted.
        $result = $compositeChecker->isGranted('example_permission');

        // Assert: Result should be true since all checkers grant access.
        $this->assertTrue($result);
    }

    // Test that `isGranted` returns false when at least one checker denies permission.
    public function testIsGrantedReturnsFalseWhenAnyCheckerDenies(): void
    {
        // Arrange: Create mock checkers, with one denying permission.
        $checker1 = $this->createMock(AuthorizationCheckerInterface::class);
        $checker1->method('isGranted')->willReturn(true);

        $checker2 = $this->createMock(AuthorizationCheckerInterface::class);
        $checker2->method('isGranted')->willReturn(false);

        // Arrange: Add these mock checkers to the CompositeAuthorizationChecker.
        $compositeChecker = new CompositeAuthorizationChecker();
        $compositeChecker->addAuthorizationChecker($checker1);
        $compositeChecker->addAuthorizationChecker($checker2);

        // Act: Check if permission is granted.
        $result = $compositeChecker->isGranted('example_permission');

        // Assert: Result should be false since one checker denies access.
        $this->assertFalse($result);
    }
}
