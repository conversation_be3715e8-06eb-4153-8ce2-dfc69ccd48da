<?php

declare(strict_types=1);

namespace App\Tests\Security\Rbac\Checker;

use App\Entity\User\AdminUser;
use App\Security\Rbac\Checker\PermissionChecker;
use App\Security\Rbac\Context\AdminUserContextInterface;
use App\Security\Rbac\PermissionMapInterface;
use App\Security\Rbac\Scope;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

final class PermissionCheckerTest extends TestCase
{
    private AdminUserContextInterface&MockObject $adminUserContext;
    private PermissionMapInterface&MockObject $permissionMap;
    private LoggerInterface&MockObject $logger;
    private AdminUser&MockObject $adminUser;
    private PermissionChecker $checker;

    protected function setUp(): void
    {
        // Arrange: Create mocks for dependencies
        $this->adminUserContext = $this->createMock(AdminUserContextInterface::class);
        $this->permissionMap = $this->createMock(PermissionMapInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->adminUser = $this->createMock(AdminUser::class);

        // Arrange: Instantiate the PermissionChecker
        $this->checker = new PermissionChecker(
            $this->adminUserContext,
            $this->permissionMap,
            $this->logger
        );
    }

    public function testIsGrantedReturnsFalseIfNoAdminUser(): void
    {
        // Arrange: Simulate no admin user
        $this->adminUserContext->method('getAdminUser')->willReturn(null);

        // Act: Call isGranted
        $result = $this->checker->isGranted('some_permission');

        // Assert: Check the result is false
        $this->assertFalse($result);
    }

    public function testIsGrantedReturnsFalseIfNoScopesFound(): void
    {
        // Arrange: Simulate an admin user and no scopes found
        $this->adminUserContext->method('getAdminUser')->willReturn($this->adminUser);
        $this->permissionMap->method('getScopesFromRoutePermission')->willReturn([]);
        $this->permissionMap->method('permissionExists')->willReturn(false);

        // Assert: Ensure logging occurs
        $this->logger->expects($this->once())->method('error');

        // Act: Call isGranted
        $result = $this->checker->isGranted('some_permission');

        // Assert: Result is false
        $this->assertFalse($result);
    }

    public function testIsGrantedReturnsFalseIfUserLacksRole(): void
    {
        // Arrange: Simulate an admin user with missing roles
        $this->adminUserContext->method('getAdminUser')->willReturn($this->adminUser);
        $this->permissionMap->method('getScopesFromRoutePermission')->willReturn([Scope::ADMIN_CONFIGURATION]);
        $this->adminUser->method('hasRole')->with('ROLE_ADMIN_CONFIGURATION')->willReturn(false);

        // Act: Call isGranted
        $result = $this->checker->isGranted('some_permission');

        // Assert: Check the result is false due to missing role
        $this->assertFalse($result);
    }

    public function testIsGrantedReturnsTrueIfUserHasRole(): void
    {
        // Arrange: Simulate an admin user with required roles
        $this->adminUserContext->method('getAdminUser')->willReturn($this->adminUser);
        $this->permissionMap->method('getScopesFromRoutePermission')->willReturn([Scope::ADMIN_CONFIGURATION]);

        $this->adminUser->method('hasRole')->with('ROLE_ADMIN_CONFIGURATION')->willReturn(true);

        // Act: Call isGranted
        $result = $this->checker->isGranted('some_permission');

        // Assert: Ensure the user is granted the permission
        $this->assertTrue($result);
    }
}
