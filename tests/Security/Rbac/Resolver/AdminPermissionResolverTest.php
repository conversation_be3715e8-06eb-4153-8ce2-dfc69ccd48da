<?php

declare(strict_types=1);

namespace App\Tests\Security\Rbac\Resolver;

use App\Entity\User\AdminUser;
use App\Security\Rbac\Checker\AuthorizationCheckerInterface;
use App\Security\Rbac\Privilege\PrivilegeInterface;
use App\Security\Rbac\Resolver\AdminPermissionResolver;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestMatcherInterface;
use Symfony\Component\HttpFoundation\RequestStack;

final class AdminPermissionResolverTest extends TestCase
{
    private AuthorizationCheckerInterface&MockObject $authorizationChecker;
    private PrivilegeInterface&MockObject $compositePrivilege;
    private Security&MockObject $security;
    private RequestStack $requestStack;
    private RequestMatcherInterface&MockObject $adminRequestMatcher;
    private AdminPermissionResolver $resolver;

    protected function setUp(): void
    {
        $this->authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $this->compositePrivilege = $this->createMock(PrivilegeInterface::class);
        $this->security = $this->createMock(Security::class);
        $this->requestStack = new RequestStack();
        $this->adminRequestMatcher = $this->createMock(RequestMatcherInterface::class);

        $this->resolver = new AdminPermissionResolver(
            $this->authorizationChecker,
            $this->compositePrivilege,
            $this->security,
            $this->requestStack,
            $this->adminRequestMatcher
        );
    }

    public function testResolvePermissionReturnsTrueWhenRequestDoesNotMatch(): void
    {
        // Arrange
        $request = new Request();
        $this->requestStack->push($request);
        $this->adminRequestMatcher
            ->expects($this->once())
            ->method('matches')
            ->with($request)
            ->willReturn(false);

        // Act
        $result = $this->resolver->resolvePermission('some_permission');

        // Assert
        $this->assertTrue($result);
    }

    public function testResolvePermissionReturnsTrueForUnsupportedPermission(): void
    {
        // Arrange
        $request = new Request();
        $this->requestStack->push($request);
        $this->adminRequestMatcher
            ->method('matches')
            ->willReturn(true);

        $this->compositePrivilege
            ->method('getSupportedRouteMap')
            ->willReturn([]);

        // Act
        $result = $this->resolver->resolvePermission('unsupported_permission');

        // Assert
        $this->assertTrue($result);
    }

    public function testResolvePermissionReturnsTrueForAdminAccessPermission(): void
    {
        // Arrange
        $request = new Request();
        $this->requestStack->push($request);
        $this->adminRequestMatcher
            ->method('matches')
            ->willReturn(true);

        // Act
        $result = $this->resolver->resolvePermission(AdminPermissionResolver::PERMISSION_ADMIN_ACCESS);

        // Assert
        $this->assertTrue($result);
    }

    public function testResolvePermissionReturnsFalseWhenUserIsNotAdmin(): void
    {
        // Arrange
        $request = new Request();
        $this->requestStack->push($request);
        $this->adminRequestMatcher
            ->method('matches')
            ->willReturn(true);

        $this->compositePrivilege
            ->method('getSupportedRouteMap')
            ->willReturn(['some_permission' => 'some_value']);

        $this->security
            ->method('getUser')
            ->willReturn(null);

        // Act
        $result = $this->resolver->resolvePermission('some_permission');

        // Assert
        $this->assertFalse($result);
    }

    public function testResolvePermissionChecksAuthorizationForAdminUser(): void
    {
        // Arrange
        $request = new Request();
        $this->requestStack->push($request);
        $this->adminRequestMatcher
            ->method('matches')
            ->willReturn(true);

        $this->compositePrivilege
            ->method('getSupportedRouteMap')
            ->willReturn(['some_permission' => 'some_value']);

        $adminUser = $this->createMock(AdminUser::class);
        $this->security
            ->method('getUser')
            ->willReturn($adminUser);

        $this->authorizationChecker
            ->expects($this->once())
            ->method('isGranted')
            ->with('some_permission')
            ->willReturn(true);

        // Act
        $result = $this->resolver->resolvePermission('some_permission');

        // Assert
        $this->assertTrue($result);
    }

    public function testResolvePermissionChecksAuthorizationForAdminDashboard(): void
    {
        // Arrange
        $request = new Request();
        $this->requestStack->push($request);
        $this->adminRequestMatcher
            ->method('matches')
            ->willReturn(true);

        $this->compositePrivilege
            ->method('getSupportedRouteMap')
            ->willReturn(['some_permission' => 'some_value']);

        $adminUser = $this->createMock(AdminUser::class);
        $this->security
            ->method('getUser')
            ->willReturn($adminUser);

        $this->authorizationChecker
            ->expects($this->never())
            ->method('isGranted');

        // Act
        $result = $this->resolver->resolvePermission('sylius_admin_dashboard');

        // Assert
        $this->assertTrue($result);
    }
}
