<?php

declare(strict_types=1);

namespace App\Tests\HttpClient\CommunicationMessage;

use App\Entity\Order\Order;
use App\HttpClient\CommunicationMessage\ConsultMessageClient;
use App\HttpClient\CommunicationMessage\Response\Author;
use App\HttpClient\CommunicationMessage\Response\ConsultCommunicationMessage;
use ArrayIterator;
use DateTimeImmutable;
use DateTimeInterface;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;
use SuperBrave\ConsultSystemClient\Client;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpClient\MockHttpClient;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\ServiceUnavailableHttpException;

class ConsultMessageClientTest extends KernelTestCase
{
    /**
     * @var ArrayIterator<int, MockResponse>
     */
    private ArrayIterator $mockResponses;

    private ConsultMessageClient $messageClient;

    protected function setUp(): void
    {
        $this->mockResponses = new ArrayIterator();
        $this->messageClient = new ConsultMessageClient(
            new Client(new MockHttpClient($this->mockResponses)),
        );
    }

    public function testOrderRequiresConsultSystemReference(): void
    {
        $orderStub = $this->createStub(Order::class);
        $orderStub->method('getTokenValue')->willReturn('4UmBdM6B7p');
        $orderStub->method('getConsultSystemReference')->willReturn(null);

        [$encodedExpectedMessages] = $this->createExpectedMessageResponse();

        $this->mockResponses->append(new MockResponse($encodedExpectedMessages));

        $actualMessages = $this->messageClient->sendMessage($orderStub, 'Hello Dr. Doak.');
        self::assertSame($actualMessages, []);
    }

    public function testCanSendMessage(): void
    {
        $orderStub = $this->createStub(Order::class);
        $orderStub->method('getTokenValue')->willReturn('4UmBdM6B7p');
        $orderStub->method('getConsultSystemReference')->willReturn(Uuid::uuid4());

        [$encodedExpectedMessages, $expectedMessages] = $this->createExpectedMessageResponse();

        // First response is from createConsultRequestMessage endpoint
        $this->mockResponses->append(new MockResponse($encodedExpectedMessages));

        // Second response is from listConsultRequestMessages endpoint
        $this->mockResponses->append(new MockResponse($encodedExpectedMessages));

        $actualMessages = $this->messageClient->sendMessage($orderStub, 'Hello Dr. Doak.');

        self::assertEquals($expectedMessages, $actualMessages);
    }

    public function testSendMessageThrowsServiceUnavailableHttpException(): void
    {
        $orderStub = $this->createStub(Order::class);
        $orderStub->method('getTokenValue')->willReturn('4UmBdM6B7p');
        $orderStub->method('getConsultSystemReference')->willReturn(Uuid::uuid4());

        $this->mockResponses->append(new MockResponse(
            'Service unavailable',
            ['http_code' => Response::HTTP_SERVICE_UNAVAILABLE]
        ));

        $this->expectException(ServiceUnavailableHttpException::class);
        $this->expectExceptionMessage('Unable to send or receive message(s).');

        $this->messageClient->sendMessage($orderStub, 'Hello Dr. Doak.');
    }

    public function testCanListMessages(): void
    {
        $orderStub = $this->createStub(Order::class);
        $orderStub->method('getTokenValue')->willReturn('4UmBdM6B7p');
        $orderStub->method('getConsultSystemReference')->willReturn(Uuid::uuid4());

        [$encodedExpectedMessages, $expectedMessages] = $this->createExpectedMessageResponse();

        $this->mockResponses->append(new MockResponse($encodedExpectedMessages));

        $actualMessages = $this->messageClient->getMessages($orderStub);

        self::assertEquals($expectedMessages, $actualMessages);
    }

    public function testListMessagesThrowsServiceUnavailableHttpException(): void
    {
        $orderStub = $this->createStub(Order::class);
        $orderStub->method('getTokenValue')->willReturn('4UmBdM6B7p');
        $orderStub->method('getConsultSystemReference')->willReturn(Uuid::uuid4());

        $this->mockResponses->append(new MockResponse(
            'Service unavailable',
            ['http_code' => Response::HTTP_SERVICE_UNAVAILABLE]
        ));

        $this->expectException(ServiceUnavailableHttpException::class);
        $this->expectExceptionMessage('Unable to send or receive message(s).');

        $this->messageClient->getMessages($orderStub);
    }

    /**
     * @dataProvider supportsProvider
     */
    public function testSupports(?UuidInterface $uuid, bool $expectedResult): void
    {
        // Act
        $orderStub = $this->createStub(Order::class);
        $orderStub->method('getConsultSystemReference')->willReturn($uuid);

        // Assert
        self::assertEquals($expectedResult, $this->messageClient->supports($orderStub));
    }

    /**
     * @return iterable<string, array{UuidInterface|null, bool}>
     */
    public function supportsProvider(): iterable
    {
        yield 'feature toggle enabled and with reference' => [Uuid::uuid4(), true];
        yield 'feature toggle enabled and without reference' => [null, false];
    }

    /**
     * @return array{string, array<ConsultCommunicationMessage>}
     */
    private function createExpectedMessageResponse(): array
    {
        $mockResponseMessages = [
            [
                'uuid' => 'd5c72f4d-e426-4a5e-b916-724e1245099d',
                'author' => [
                    'type' => 'client',
                    'name' => 'Mr. Customer',
                ],
                'message' => 'Hello Dr. Doak',
                'createdAt' => '2022-12-14T14:20:04+00:00',
            ],
            [
                'uuid' => '7d004386-8d85-4df7-a537-43346405d56b',
                'author' => [
                    'type' => 'doctor',
                    'name' => 'Dr. Doak',
                    'uuid' => '7d004386-8d85-4df7-a537-43346405d56c',
                ],
                'message' => 'Hello Mr. Customer',
                'createdAt' => '2022-12-14T14:20:03+00:00',
            ],
        ];

        $expectedMessages = array_map(
            static function (array $mockMessage) {
                return new ConsultCommunicationMessage(
                    $mockMessage['uuid'],
                    $mockMessage['message'],
                    new Author(
                        $mockMessage['author']['type'],
                        $mockMessage['author']['name'],
                    ),
                    // @phpstan-ignore-next-line
                    DateTimeImmutable::createFromFormat(DateTimeInterface::ATOM, $mockMessage['createdAt'])
                );
            },
            $mockResponseMessages
        );

        $encodedExpectedMessages = (string) json_encode($mockResponseMessages);

        return [$encodedExpectedMessages, $expectedMessages];
    }
}
