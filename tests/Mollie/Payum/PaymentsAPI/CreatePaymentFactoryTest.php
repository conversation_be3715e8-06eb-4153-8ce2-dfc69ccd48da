<?php

declare(strict_types=1);

namespace App\Tests\Mollie\Payum\PaymentsAPI;

use App\Entity\Payment\Payment;
use App\Mollie\Payum\PaymentsAPI\CreatePaymentFactory;
use App\Mollie\Payum\PaymentsAPI\Model\CaptureMode;
use App\Mollie\Payum\PaymentsAPI\Model\MolliePayment;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\PaymentFactory;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

class CreatePaymentFactoryTest extends KernelTestCase
{
    private CreatePaymentFactory $createPaymentFactory;

    protected function setUp(): void
    {
        $normalizer = self::getContainer()->get(NormalizerInterface::class);
        $this->createPaymentFactory = new CreatePaymentFactory(
            $normalizer
        );
    }

    public function testItPopulatesCreateMolliePayment(): void
    {
        // Arrange
        $order = OrderFactory::createPrefilled();
        $payment = PaymentFactory::create([
            'amount' => 100000,
            'paymentMethod' => PaymentFactory::createPaymentMethod([]),
        ]);
        $order->addPayment($payment);

        $lastPayment = $order->getPayments()->last();
        self::assertInstanceOf(Payment::class, $lastPayment);

        // Act
        $molliePayment = $this->createPaymentFactory->create(
            $order,
            $lastPayment,
            'ideal_dokteronline',
            'https://return-url',
            'https://return-url',
            'seeme',
            CaptureMode::Automatic,
            'KNABNL2H'
        );
        $normalized = $this->createPaymentFactory->normalizePayment($molliePayment, $order);

        // Assert
        self::assertInstanceOf(MolliePayment::class, $molliePayment);
        self::assertIsArray($normalized);

        // The total order amount is 5 euro
        self::assertSame(500, $order->getTotal());

        // There is already 1 euro paid
        self::assertCount(2, $order->getPayments());

        $firstPayment = $order->getPayments()->first();
        self::assertInstanceOf(Payment::class, $firstPayment);
        self::assertSame(100, $firstPayment->getAmount());

        // There is still a payment left of 1000 euro
        self::assertArrayHasKey('amount', $normalized);
        self::assertSame(['currency' => 'EUR', 'value' => '1000.00'], $normalized['amount']);

        self::assertArrayHasKey('billingAddress', $normalized);
        self::assertSame(
            [
                'streetAndNumber' => 'Test Street',
                'city' => 'Test City',
                'country' => 'NL',
                'postalCode' => '1234AZ',
            ],
            $normalized['billingAddress']
        );

        self::assertArrayHasKey('metadata', $normalized);
        self::assertSame(['orderNumber' => 'DO1337', 'storeName' => 'seeme'], $normalized['metadata']);

        self::assertArrayHasKey('locale', $normalized);
        self::assertSame('nl_NL', $normalized['locale']);
        self::assertArrayHasKey('redirectUrl', $normalized);
        self::assertSame('https://return-url', $normalized['redirectUrl']);
        self::assertArrayHasKey('method', $normalized);
        self::assertSame('ideal_dokteronline', $normalized['method']);
        self::assertArrayHasKey('issuer', $normalized);
        self::assertSame('ideal_KNABNL2H', $normalized['issuer']);
        self::assertArrayHasKey('webhookUrl', $normalized);
        self::assertSame('https://return-url', $normalized['webhookUrl']);
    }
}
