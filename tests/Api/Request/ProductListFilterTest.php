<?php

declare(strict_types=1);

namespace App\Tests\Api\Request;

use App\Api\Request\ProductListFilter;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;

class ProductListFilterTest extends TestCase
{
    public function testCanGetEmptyFilter(): void
    {
        $request = new Request();

        $filter = ProductListFilter::createFilterByRequest($request);

        $this->assertNull($filter->isFilterByPrescriptionRequired());
        $this->assertNull($filter->getFilterByProductName());
        $this->assertNull($filter->getFilterByProductAttributeType());
        $this->assertNull($filter->getFilterByConsult());
        $this->assertNull($filter->getFilterByInStock());
    }

    public function testCanGetFilterByProductAttributeType(): void
    {
        $attributeType = 'consult';

        $request = new Request([ProductListFilter::QUERY_KEY_PRODUCT_ATTRIBUTE_TYPE => $attributeType]);

        $filter = ProductListFilter::createFilterByRequest($request);

        $this->assertNull($filter->isFilterByPrescriptionRequired());
        $this->assertNull($filter->getFilterByProductName());
        $this->assertNull($filter->getFilterByConsult());
        $this->assertSame($attributeType, $filter->getFilterByProductAttributeType());
        $this->assertNull($filter->getFilterByInStock());
    }

    public function testCanGetFilterByProductName(): void
    {
        $productName = 'viagra';

        $request = new Request([ProductListFilter::QUERY_KEY_PRODUCT_NAME => $productName]);

        $filter = ProductListFilter::createFilterByRequest($request);

        $this->assertNull($filter->isFilterByPrescriptionRequired());
        $this->assertNull($filter->getFilterByProductAttributeType());
        $this->assertNull($filter->getFilterByConsult());
        $this->assertSame($productName, $filter->getFilterByProductName());
    }

    public function testCanGetFilterByConsult(): void
    {
        $consultProductCode = 'erectiestoornis';

        $request = new Request([ProductListFilter::QUERY_KEY_FILTER_BY_CONSULT => $consultProductCode]);

        $filter = ProductListFilter::createFilterByRequest($request);

        $this->assertNull($filter->isFilterByPrescriptionRequired());
        $this->assertNull($filter->getFilterByProductAttributeType());
        $this->assertNull($filter->getFilterByProductName());
        $this->assertSame($consultProductCode, $filter->getFilterByConsult());
    }

    public function testCanGetFilterByPrescriptionRequired(): void
    {
        $request = new Request([ProductListFilter::QUERY_KEY_PRESCRIPTION_REQUIRED => true]);

        $filter = ProductListFilter::createFilterByRequest($request);

        $this->assertNull($filter->getFilterByProductName());
        $this->assertNull($filter->getFilterByProductAttributeType());
        $this->assertNull($filter->getFilterByConsult());
        $this->assertTrue($filter->isFilterByPrescriptionRequired());
    }

    public function testCanGetFilterByInStock(): void
    {
        $request = new Request([ProductListFilter::QUERY_KEY_FILTER_BY_IN_STOCK => true]);

        $filter = ProductListFilter::createFilterByRequest($request);

        $this->assertNull($filter->getFilterByProductName());
        $this->assertNull($filter->getFilterByProductAttributeType());
        $this->assertNull($filter->getFilterByConsult());
        $this->assertNull($filter->isFilterByPrescriptionRequired());
        $this->assertTrue($filter->getFilterByInStock());
    }
}
