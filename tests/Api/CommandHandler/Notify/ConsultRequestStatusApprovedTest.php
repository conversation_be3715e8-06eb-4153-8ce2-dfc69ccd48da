<?php

declare(strict_types=1);

namespace App\Tests\Api\CommandHandler\Notify;

use App\Api\Command\Admin\Order\AddOrderItemOperation;
use App\Api\Command\Admin\Order\CompositeOrderItemOperations;
use App\Api\Command\Admin\Order\TransitionPrescriptionState\Approve;
use App\Api\Command\Admin\Order\UpdateOrderItemOperation;
use App\Api\Command\Notify\ConsultRequestStatus;
use App\Api\CommandHandler\Admin\CompositeOrderItemOperationsHandler;
use App\Api\CommandHandler\Admin\Order\TransitionPrescriptionStateHandler;
use App\ConsultSystem\Enum\ConsultRequestNotification;
use App\ConsultSystem\Model\NotifyConsultRequest;
use App\Tests\ConsultSystem\ConsultSystemClientHelper;
use Ramsey\Uuid\Uuid;
use stdClass;
use SuperBrave\ConsultSystemClient\Model\Enum\LastMessageInteractionState;
use SuperBrave\ConsultSystemClient\Model\Enum\State;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Stamp\HandledStamp;

class ConsultRequestStatusApprovedTest extends AbstractConsultRequestStatusHandlerTest
{
    use ConsultSystemClientHelper;

    public function testItCanDispatchDoctorApprovedMessage(): void
    {
        $uuid = Uuid::fromString(self::CONSULT_REQUEST_REFERENCE);

        $this->consultServiceClientMock
            ->method('getConsultRequest')
            ->willReturn($this->getConsultRequest(['state' => State::Approved]));

        $order = $this->getOrder();

        $expectedApproveMessage = new Approve(
            null,
            self::DOCTOR_NAME,
            self::REGISTRATION_NUMBER,
            Uuid::fromString(self::DOCTOR_UUID),
        );
        $expectedApproveMessage->setOrder($order);

        $expectedCompositeOrderItemOperations = new CompositeOrderItemOperations(
            [
                new AddOrderItemOperation(
                    'Take together with test medication',
                    '7_28_test-variant-code-medication',
                    1,
                    '7_25_test-variant-code-consult',
                ),
                new UpdateOrderItemOperation(
                    2,
                    '7_25_test-variant-code-medication',
                    2,
                    'One a day',
                ),
            ]
        );
        $expectedCompositeOrderItemOperations->setOrder($order);

        $handledStampCompositeOrderItemOperations = new HandledStamp($order, CompositeOrderItemOperationsHandler::class);
        $envelopeCompositeOrderItemOperations = new Envelope(new stdClass(), [$handledStampCompositeOrderItemOperations]);

        $handledStampApprove = new HandledStamp($order, TransitionPrescriptionStateHandler::class);
        $envelopeApprove = new Envelope(new stdClass(), [$handledStampApprove]);

        $this->messageBusMock
            ->expects($this->exactly(2))
            ->method('dispatch')
            ->withConsecutive(
                [$expectedCompositeOrderItemOperations],
                [$expectedApproveMessage]
            )
            ->willReturnOnConsecutiveCalls(
                $envelopeCompositeOrderItemOperations,
                $envelopeApprove,
            );

        $notifyConsultRequestStatus = new ConsultRequestStatus(
            new NotifyConsultRequest($uuid, State::Approved, LastMessageInteractionState::None),
            ConsultRequestNotification::ConsultRequestWasApproved->value,
        );

        ($this->consultRequestStatusHandler)($notifyConsultRequestStatus);
    }

    public function testItDoesNotExecuteCompositeOrderItemOperationsForCancelledOrder(): void
    {
        // Arrange
        $uuid = Uuid::fromString(self::CONSULT_REQUEST_REFERENCE);

        $this->consultServiceClientMock
            ->method('getConsultRequest')
            ->willReturn($this->getConsultRequest(['state' => State::Approved]));

        $order = $this->getOrder();
        $order->setState('cancelled'); // Cancel the order.

        $expectedApproveMessage = new Approve(
            prescriptionFilename: null,
            doctorName: self::DOCTOR_NAME,
            doctorRegistrationNumber: self::REGISTRATION_NUMBER,
            doctorUuid: Uuid::fromString(self::DOCTOR_UUID),
        );
        $expectedApproveMessage->setOrder($order);

        $handledStampApprove = new HandledStamp($order, TransitionPrescriptionStateHandler::class);
        $envelopeApprove = new Envelope(new stdClass(), [$handledStampApprove]);

        // Arrange / assert
        $this->messageBusMock
            ->expects($this->once())
            ->method('dispatch')
            ->with($expectedApproveMessage)
            ->willReturn($envelopeApprove);

        $notifyConsultRequestStatus = new ConsultRequestStatus(
            new NotifyConsultRequest($uuid, State::Approved, LastMessageInteractionState::None),
            ConsultRequestNotification::ConsultRequestWasApproved->value,
        );

        // Act
        ($this->consultRequestStatusHandler)($notifyConsultRequestStatus);
    }
}
