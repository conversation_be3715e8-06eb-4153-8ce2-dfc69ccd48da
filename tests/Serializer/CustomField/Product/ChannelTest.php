<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Product;

use App\Entity\Product\Product;
use App\Entity\Product\ProductVariant;
use App\Repository\ProductVariantRepositoryInterface;
use App\Serializer\CustomField\Product\Channel;
use App\Serializer\CustomField\Product\Util\ProductContextUtil;
use App\Supplier\Resolver\SupplierIdentifierResolverInterface;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\ProductFactory;
use Sylius\Component\Channel\Context\ChannelNotFoundException;

class ChannelTest extends AbstractCustomFieldTest
{
    private const string LOCALE = 'nl';

    private Channel $channel;

    public function setUp(): void
    {
        parent::setUp();

        /** @var ProductVariantRepositoryInterface $productVariantRepositoryMock */
        $productVariantRepositoryMock = $this->createMock(ProductVariantRepositoryInterface::class);
        $supplierIdentifierResolverMock = $this->createMock(SupplierIdentifierResolverInterface::class);

        $this->channel = new Channel(
            $this->normalizer,
            $this->propertyAccessor,
            $productVariantRepositoryMock,
            new ProductContextUtil(
                $this->channelContext,
                $this->orderContext,
                $productVariantRepositoryMock,
                $supplierIdentifierResolverMock
            )
        );
    }

    public function testItSupportsProductInterface(): void
    {
        $this->assertTrue($this->channel->supports(new Product(), $this->getContext()));
    }

    public function testItDoesNotSupportProductVariantInterface(): void
    {
        $this->assertFalse($this->channel->supports(new ProductVariant(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->channel->supports(new Product(), []));
    }

    public function testItAddsChannel(): void
    {
        $product = ProductFactory::createWithValues('Test Product', 'test-product', self::LOCALE);

        $data = [];
        $this->channel->add($product, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('channel', $data);
    }

    public function testItDoesNotAddChannelIfNoChannelIsInContext(): void
    {
        $product = ProductFactory::createWithValues('Test Product', 'test-product', self::LOCALE);

        $this->channelContext->method('getChannel')
            ->willThrowException(new ChannelNotFoundException('Channel not found'));

        $data = [];
        $this->channel->add($product, $data, 'json', $this->getContext());

        $this->assertArrayNotHasKey('channel', $data);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'channel' => [],
            ],
        ];
    }
}
