<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Product;

use App\Entity\Product\Product;
use App\Entity\Product\ProductOption;
use App\Entity\Product\ProductOptionValue;
use App\Entity\Product\ProductVariant;
use App\Repository\ProductVariantRepository;
use App\Serializer\CustomField\Product\Options;
use App\Serializer\CustomField\Product\Util\OptionsNaturalSorter;
use App\Serializer\CustomField\Product\Util\ProductContextUtil;
use App\Supplier\Resolver\SupplierIdentifierResolverInterface;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\ProductFactory;
use PHPUnit\Framework\MockObject\MockObject;

final class OptionsTest extends AbstractCustomFieldTest
{
    private const string LOCALE = 'nl';

    private Options $options;

    public function setUp(): void
    {
        parent::setUp();

        $supplierIdentifierResolverMock = $this->createMock(SupplierIdentifierResolverInterface::class);

        /** @var ProductVariantRepository&MockObject $productVariantRepository */
        $productVariantRepository = $this->createMock(ProductVariantRepository::class);
        $productVariantRepository->method('findProductVariantsFilteredByRanking')
            ->willReturnCallback(
                function () {
                    $return = [];
                    for ($i = 1; $i <= 2; ++$i) {
                        $productVariant = new ProductVariant();
                        $optionValue = new ProductOptionValue();
                        $optionValue->setCode('value-code-'.$i);
                        $productOption = new ProductOption();
                        $productOption->setCode('option-code-'.$i);
                        $optionValue->setOption($productOption);

                        $productVariant->addOptionValue($optionValue);
                        $return[] = $productVariant;
                    }

                    return $return;
                }
            );

        $this->options = new Options(
            new ProductContextUtil($this->channelContext, $this->orderContext, $productVariantRepository, $supplierIdentifierResolverMock),
            new OptionsNaturalSorter(),
        );
    }

    public function testItSupportsProductInterface(): void
    {
        $this->assertTrue($this->options->supports(new Product(), $this->getContext()));
    }

    public function testItDoesNotSupportProductVariantInterface(): void
    {
        $this->assertFalse($this->options->supports(new ProductVariant(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->options->supports(new Product(), []));
    }

    public function testItSkipsIfNoOptions(): void
    {
        $product = ProductFactory::createWithValues('Test Product', 'test-product', self::LOCALE);
        $data = [];
        $this->options->add($product, $data, 'json', $this->getContext());
        $this->assertEquals([], $data);
    }

    public function testItAddsOptions(): void
    {
        $product = ProductFactory::createWithValues('Test Product', 'test-product', self::LOCALE);

        $data = [
            'options' => [
                [
                    'code' => 'option-code-1',
                    'values' => [
                        ['code' => 'value-code-1'],
                    ],
                ],
                [
                    'code' => 'option-code-2',
                    'values' => [
                        ['code' => 'value-code-2'],
                    ],
                ],
                [
                    'code' => 'option-code-3',
                    'values' => [
                        ['code' => 'value-code-3'],
                    ],
                ],
            ],
        ];
        $this->options->add($product, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('options', $data);

        $this->assertCount(1, $data['options'][0]['values']);
        $this->assertCount(1, $data['options'][1]['values']);
        $this->assertCount(0, $data['options'][2]['values']);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'options' => [],
            ],
        ];
    }
}
