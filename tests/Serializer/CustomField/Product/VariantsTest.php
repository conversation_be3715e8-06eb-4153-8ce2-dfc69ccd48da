<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Product;

use App\Api\Context\OrderContextInterface;
use App\Entity\Channel\Channel;
use App\Entity\Order\Order;
use App\Entity\Product\Product;
use App\Entity\Product\ProductVariant;
use App\Repository\ProductVariantRepositoryInterface;
use App\Serializer\CustomField\Product\Util\ProductContextUtil;
use App\Serializer\CustomField\Product\Variants;
use App\Supplier\Resolver\SupplierIdentifierResolverInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use stdClass;
use Sylius\Component\Channel\Context\ChannelContextInterface;
use Sylius\Component\Channel\Context\ChannelNotFoundException;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

final class VariantsTest extends TestCase
{
    private NormalizerInterface&MockObject $normalizerMock;
    private PropertyAccessorInterface&MockObject $propertyAccessorMock;
    private ChannelContextInterface&MockObject $channelContextMock;
    private OrderContextInterface&MockObject $orderContextMock;
    private ProductVariantRepositoryInterface&MockObject $productVariantRepositoryMock;

    private Variants $variants;

    public function setUp(): void
    {
        $this->normalizerMock = $this->createMock(NormalizerInterface::class);
        $this->propertyAccessorMock = $this->createMock(PropertyAccessorInterface::class);
        $this->channelContextMock = $this->createMock(ChannelContextInterface::class);
        $this->orderContextMock = $this->createMock(OrderContextInterface::class);
        $this->productVariantRepositoryMock = $this->createMock(ProductVariantRepositoryInterface::class);
        $supplierIdentifierResolverMock = $this->createMock(SupplierIdentifierResolverInterface::class);

        $this->variants = new Variants(
            $this->normalizerMock,
            $this->propertyAccessorMock,
            new ProductContextUtil(
                $this->channelContextMock,
                $this->orderContextMock,
                $this->productVariantRepositoryMock,
                $supplierIdentifierResolverMock
            )
        );
    }

    public function testSupportsReturnsTrue(): void
    {
        $this->assertTrue(
            $this->variants->supports(
                new Product(),
                [
                    'attributes' => [
                        'variants' => [],
                    ],
                ]
            )
        );
    }

    /**
     * @dataProvider invalidSupportsProvider
     */
    public function testSupportsReturnsFalse(object $object, array $context): void
    {
        $this->assertFalse(
            $this->variants->supports($object, $context)
        );
    }

    public function invalidSupportsProvider(): iterable
    {
        yield 'With invalid object type' => [new stdClass(), ['attributes' => ['variants' => []]]];
        yield 'With missing attributes' => [new Product(), []];
        yield 'With missing attributes variants key' => [new Product(), ['attributes' => []]];
    }

    public function testAddTerminatesWhenChannelIsNotInstanceOfChannelInterface(): void
    {
        $data = [];

        $this->channelContextMock
            ->expects($this->once())
            ->method('getChannel')
            ->willThrowException(new ChannelNotFoundException());

        $this->propertyAccessorMock
            ->expects($this->never())
            ->method('getValue');

        $this->variants->add(new Product(), $data);
    }

    public function testAddTerminatesWhenVariantsCountIsZero(): void
    {
        $data = [];

        $this->channelContextMock
            ->method('getChannel')
            ->willReturn(new Channel());

        $this->orderContextMock
            ->method('getOrder')
            ->willReturn(new Order());

        $this->productVariantRepositoryMock
            ->method('findProductVariantsFilteredByRanking')
            ->willReturn([]);

        $this->propertyAccessorMock
            ->expects($this->never())
            ->method('getValue');

        $this->variants->add(new Product(), $data);
    }

    public function testAddAppendsProductVariantsToDataArgument(): void
    {
        $data = [];
        $productVariants = [new ProductVariant()];

        $channel = new Channel();
        $channel->setCode('dok_gb');

        $this->channelContextMock
            ->method('getChannel')
            ->willReturn($channel);

        $this->orderContextMock
            ->method('getOrder')
            ->willReturn(new Order());

        $this->productVariantRepositoryMock
            ->method('findProductVariantsFilteredByRanking')
            ->willReturn($productVariants);

        $this->propertyAccessorMock
            ->expects($this->once())
            ->method('getValue');

        $this->normalizerMock
            ->expects($this->once())
            ->method('normalize')
            ->with(
                reset($productVariants),
                'json',
                ['attributes' => null],
            )
            ->willReturn(
                json_encode(reset($productVariants))
            );

        $this->variants->add(new Product(), $data, 'json');

        $this->assertJson(reset($data['variants']));
    }
}
