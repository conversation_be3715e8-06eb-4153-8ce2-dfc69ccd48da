<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Product;

use App\Entity\Channel\Channel;
use App\Entity\Channel\ChannelPricing;
use App\Entity\Product\Product;
use App\Entity\Product\ProductVariant;
use App\Repository\ProductVariantRepository;
use App\Serializer\CustomField\Product\StartingPrices;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\ProductFactory;
use PHPUnit\Framework\MockObject\MockObject;

final class StartingPricesTest extends AbstractCustomFieldTest
{
    private const string LOCALE = 'nl';

    private const array CHANNEL_CODES = [
        'dok_nl',
        'blueclinic_nl',
    ];

    private StartingPrices $startingPrices;

    public function setUp(): void
    {
        parent::setUp();

        /** @var ProductVariantRepository&MockObject $productVariantRepository */
        $productVariantRepository = $this->createMock(ProductVariantRepository::class);
        $productVariantRepository->method('findCheapestByProductAndChannel')
            ->willReturnCallback(
                function () {
                    $productVariant = new ProductVariant();
                    $channelPricing = new ChannelPricing();
                    $channelPricing->setEnabled(true);
                    $channelPricing->setPrice(500);
                    $channelPricing->setChannelCode(self::CHANNEL_CODES[0]);
                    $productVariant->addChannelPricing($channelPricing);

                    return $productVariant;
                }
            );

        $this->startingPrices = new StartingPrices(
            $this->normalizer,
            $this->propertyAccessor,
            $productVariantRepository,
        );
    }

    public function testItSupportsProductInterface(): void
    {
        $this->assertTrue($this->startingPrices->supports(new Product(), $this->getContext()));
    }

    public function testItDoesNotSupportProductVariantInterface(): void
    {
        $this->assertFalse($this->startingPrices->supports(new ProductVariant(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->startingPrices->supports(new Product(), []));
    }

    public function testItAddsStartingPrices(): void
    {
        $product = ProductFactory::createWithValues('Test Product', 'test-product', self::LOCALE);

        $channel = new Channel();
        $channel->setCode('dok_nl');
        $product->addChannel($channel);

        $data = [];

        $this->startingPrices->add($product, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('startingPrices', $data);
        $this->assertEquals(500, $data['startingPrices'][self::CHANNEL_CODES[0]]['amount']);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'startingPrices' => [],
            ],
        ];
    }
}
