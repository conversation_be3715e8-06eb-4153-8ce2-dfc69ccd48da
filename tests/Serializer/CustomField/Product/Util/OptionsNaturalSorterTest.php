<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Product\Util;

use App\Serializer\CustomField\Product\Util\OptionsNaturalSorter;
use PHPUnit\Framework\TestCase;

final class OptionsNaturalSorterTest extends TestCase
{
    private OptionsNaturalSorter $naturalSorter;

    public function setUp(): void
    {
        $this->naturalSorter = new OptionsNaturalSorter();
    }

    /**
     * @dataProvider provideRandomizedOptions
     */
    public function testCanNaturalSortOptions(array $actualOptions): void
    {
        $expectedOptions = [
            'values' => [
                ['code' => '1337_dosage_0dot001_mg'],
                ['code' => '1337_dosage_0dot001_percent'],
                ['code' => '1337_dosage_0dot01_mg'],
                ['code' => '1337_dosage_0dot01_percent'],
                ['code' => '1337_dosage_0dot1_mg'],
                ['code' => '1337_dosage_0dot1_percent'],
                ['code' => '1337_dosage_0dot2_ml'],
                ['code' => '1337_dosage_0dot25_mg'],
                ['code' => '1337_dosage_0dot25_ml'],
                ['code' => '1337_dosage_0dot25_percent'],
                ['code' => '1337_dosage_0dot5_mg'],
                ['code' => '1337_dosage_0dot5_ml'],
                ['code' => '1337_dosage_0dot5_percent'],
                ['code' => '1337_dosage_0dot75_mg'],
                ['code' => '1337_dosage_1_mg'],
                ['code' => '1337_dosage_1_percent'],
                ['code' => '1337_dosage_10_mg'],
                ['code' => '1337_dosage_10_percent'],
                ['code' => '1337_dosage_100_mg'],
                ['code' => '1337_dosage_100_percent'],
            ],
        ];

        $this->naturalSorter->sort($actualOptions);

        self::assertSame($expectedOptions, $actualOptions);
    }

    public function testCanNaturalSortOptionsWithoutFourCodeParts(): void
    {
        $actualOptions = [
            'values' => [
                ['code' => '1337_dosage'],
                ['code' => '1337_dosage_mg'],
                ['code' => '1337_0dot001_mg'],
            ],
        ];

        $expectedOptions = [
            'values' => [
                ['code' => '1337_0dot001_mg'],
                ['code' => '1337_dosage'],
                ['code' => '1337_dosage_mg'],
            ],
        ];

        $this->naturalSorter->sort($actualOptions);

        self::assertSame($expectedOptions, $actualOptions);
    }

    public function provideRandomizedOptions(): iterable
    {
        yield 'Randomized options' => [
            [
                'values' => [
                    ['code' => '1337_dosage_0dot75_mg'],
                    ['code' => '1337_dosage_100_mg'],
                    ['code' => '1337_dosage_0dot01_mg'],
                    ['code' => '1337_dosage_0dot25_ml'],
                    ['code' => '1337_dosage_0dot1_mg'],
                    ['code' => '1337_dosage_0dot5_mg'],
                    ['code' => '1337_dosage_1_mg'],
                    ['code' => '1337_dosage_0dot001_mg'],
                    ['code' => '1337_dosage_10_mg'],
                    ['code' => '1337_dosage_0dot25_mg'],
                    ['code' => '1337_dosage_0dot01_percent'],
                    ['code' => '1337_dosage_0dot001_percent'],
                    ['code' => '1337_dosage_0dot1_percent'],
                    ['code' => '1337_dosage_0dot5_ml'],
                    ['code' => '1337_dosage_1_percent'],
                    ['code' => '1337_dosage_0dot5_percent'],
                    ['code' => '1337_dosage_100_percent'],
                    ['code' => '1337_dosage_0dot2_ml'],
                    ['code' => '1337_dosage_0dot25_percent'],
                    ['code' => '1337_dosage_10_percent'],
                ],
            ],
        ];

        yield 'Not sorted by dosage type' => [
            [
                'values' => [
                    ['code' => '1337_dosage_0dot001_mg'],
                    ['code' => '1337_dosage_0dot001_percent'],
                    ['code' => '1337_dosage_0dot01_mg'],
                    ['code' => '1337_dosage_0dot01_percent'],
                    ['code' => '1337_dosage_0dot1_mg'],
                    ['code' => '1337_dosage_0dot1_percent'],
                    ['code' => '1337_dosage_0dot25_mg'],
                    ['code' => '1337_dosage_0dot25_ml'],
                    ['code' => '1337_dosage_0dot25_percent'],
                    ['code' => '1337_dosage_0dot2_ml'],
                    ['code' => '1337_dosage_0dot5_mg'],
                    ['code' => '1337_dosage_0dot5_ml'],
                    ['code' => '1337_dosage_0dot5_percent'],
                    ['code' => '1337_dosage_0dot75_mg'],
                    ['code' => '1337_dosage_100_mg'],
                    ['code' => '1337_dosage_100_percent'],
                    ['code' => '1337_dosage_10_mg'],
                    ['code' => '1337_dosage_10_percent'],
                    ['code' => '1337_dosage_1_mg'],
                    ['code' => '1337_dosage_1_percent'],
                ],
            ],
        ];

        yield 'Same as expected' => [
            [
                'values' => [
                    ['code' => '1337_dosage_0dot001_mg'],
                    ['code' => '1337_dosage_0dot01_mg'],
                    ['code' => '1337_dosage_0dot1_mg'],
                    ['code' => '1337_dosage_0dot25_mg'],
                    ['code' => '1337_dosage_0dot5_mg'],
                    ['code' => '1337_dosage_0dot75_mg'],
                    ['code' => '1337_dosage_1_mg'],
                    ['code' => '1337_dosage_10_mg'],
                    ['code' => '1337_dosage_100_mg'],
                    ['code' => '1337_dosage_0dot2_ml'],
                    ['code' => '1337_dosage_0dot25_ml'],
                    ['code' => '1337_dosage_0dot5_ml'],
                    ['code' => '1337_dosage_0dot001_percent'],
                    ['code' => '1337_dosage_0dot01_percent'],
                    ['code' => '1337_dosage_0dot1_percent'],
                    ['code' => '1337_dosage_0dot25_percent'],
                    ['code' => '1337_dosage_0dot5_percent'],
                    ['code' => '1337_dosage_1_percent'],
                    ['code' => '1337_dosage_10_percent'],
                    ['code' => '1337_dosage_100_percent'],
                ],
            ],
        ];
    }
}
