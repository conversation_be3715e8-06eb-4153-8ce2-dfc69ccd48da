<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Product;

use App\Entity\Product\Product;
use App\Entity\Product\ProductVariant;
use App\Serializer\CustomField\Product\Taxons;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\ProductFactory;

final class TaxonsTest extends AbstractCustomFieldTest
{
    private const string LOCALE = 'nl';

    private const string TAXON_CODE = 'test_taxon';

    private Taxons $taxons;

    public function setUp(): void
    {
        parent::setUp();

        $this->normalizer->method('normalize')
            ->willReturnCallback(
                function () {
                    return [];
                }
            );

        $this->propertyAccessor->method('getValue')
            ->willReturn([]);

        $this->taxons = new Taxons(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsProductInterface(): void
    {
        $this->assertTrue($this->taxons->supports(new Product(), $this->getContext()));
    }

    public function testItDoesNotSupportProductVariantInterface(): void
    {
        $this->assertFalse($this->taxons->supports(new ProductVariant(), $this->getContext()));
    }

    public function testItDoesNotInvalidContext(): void
    {
        $this->assertFalse($this->taxons->supports(new Product(), []));
    }

    public function testItAddsTaxons(): void
    {
        $product = ProductFactory::createWithValues('Test Product', 'test-product', self::LOCALE);
        ProductFactory::addTaxon($product, self::CHANNEL_CODE, self::TAXON_CODE);

        $data = [];

        $this->taxons->add($product, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('taxons', $data);
        $this->assertArrayHasKey('productTaxonChannels', $data['taxons'][0]);
        $this->assertArrayHasKey(self::CHANNEL_CODE, $data['taxons'][0]['productTaxonChannels']);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'taxons' => [
                    'productTaxonChannels' => [],
                ],
            ],
        ];
    }
}
