<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Product;

use App\Entity\Channel\ChannelPricing;
use App\Entity\Product\Product;
use App\Entity\Product\ProductVariant;
use App\Repository\ProductVariantRepository;
use App\Serializer\CustomField\Product\StartingPrice;
use App\Serializer\CustomField\Product\Util\ProductContextUtil;
use App\Supplier\Resolver\SupplierIdentifierResolverInterface;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\ProductFactory;
use PHPUnit\Framework\MockObject\MockObject;

final class StartingPriceTest extends AbstractCustomFieldTest
{
    private const string LOCALE = 'nl';

    private StartingPrice $startingPrice;

    public function setUp(): void
    {
        parent::setUp();

        $supplierIdentifierResolverMock = $this->createMock(SupplierIdentifierResolverInterface::class);

        /** @var ProductVariantRepository&MockObject $productVariantRepository */
        $productVariantRepository = $this->createMock(ProductVariantRepository::class);
        $productVariantRepository->method('findProductVariantsFilteredByRanking')
            ->willReturnCallback(
                function () {
                    $return = [];
                    for ($i = 1; $i <= 2; ++$i) {
                        $productVariant = new ProductVariant();
                        $channelPricing = new ChannelPricing();
                        $channelPricing->setEnabled(true);
                        $channelPricing->setPrice($i * 500);
                        $channelPricing->setChannelCode('test-channel');
                        $productVariant->addChannelPricing($channelPricing);
                        $return[] = $productVariant;
                    }

                    return $return;
                }
            );

        $this->startingPrice = new StartingPrice(
            $this->normalizer,
            $this->propertyAccessor,
            $productVariantRepository,
            new ProductContextUtil($this->channelContext, $this->orderContext, $productVariantRepository, $supplierIdentifierResolverMock)
        );
    }

    public function testItSupportsProductInterface(): void
    {
        $this->assertTrue($this->startingPrice->supports(new Product(), $this->getContext()));
    }

    public function testItDoesNotSupportProductVariantInterface(): void
    {
        $this->assertFalse($this->startingPrice->supports(new ProductVariant(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->startingPrice->supports(new Product(), []));
    }

    public function testItAddsStartingPrice(): void
    {
        $product = ProductFactory::createWithValues('Test Product', 'test-product', self::LOCALE);

        $data = [];

        $this->startingPrice->add($product, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('startingPrice', $data);
        $this->assertEquals(500, $data['startingPrice']['amount']);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'startingPrice' => [],
            ],
        ];
    }
}
