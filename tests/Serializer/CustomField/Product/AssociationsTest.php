<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Product;

use App\Entity\Product\Product;
use App\Entity\Product\ProductVariant;
use App\Repository\ProductVariantRepositoryInterface;
use App\Serializer\CustomField\Product\Associations;
use App\Serializer\CustomField\Product\Util\ProductContextUtil;
use App\Supplier\Resolver\SupplierIdentifierResolverInterface;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\ProductFactory;

final class AssociationsTest extends AbstractCustomFieldTest
{
    private const string LOCALE = 'nl';

    private Associations $associations;

    public function setUp(): void
    {
        parent::setUp();

        $productVariantRepository = $this->createMock(ProductVariantRepositoryInterface::class);
        $supplierIdentifierResolverMock = $this->createMock(SupplierIdentifierResolverInterface::class);

        $this->associations = new Associations(
            $this->normalizer,
            $this->propertyAccessor,
            $productVariantRepository,
            new ProductContextUtil($this->channelContext, $this->orderContext, $productVariantRepository, $supplierIdentifierResolverMock)
        );
    }

    public function testItSupportsProductInterface(): void
    {
        $this->assertTrue($this->associations->supports(new Product(), $this->getContext()));
    }

    public function testItDoesNotSupportProductVariantInterface(): void
    {
        $this->assertFalse($this->associations->supports(new ProductVariant(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->associations->supports(new Product(), []));
    }

    public function testItAddsAssociations(): void
    {
        $product = ProductFactory::createWithValues('Test Product', 'test-product', self::LOCALE);
        ProductFactory::addAssociation($product, 'associatedProducts');

        $data = [];
        $this->associations->add($product, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('associations', $data);
        $this->assertArrayHasKey('associatedProducts', $data['associations']);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'associations' => [],
            ],
        ];
    }
}
