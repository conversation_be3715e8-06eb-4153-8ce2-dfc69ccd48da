<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Product;

use App\Entity\Product\Product;
use App\Entity\Product\ProductVariant;
use App\Repository\ProductVariantRepository;
use App\Serializer\CustomField\Product\InStock;
use App\Serializer\CustomField\Product\Util\ProductContextUtil;
use App\Supplier\Resolver\SupplierIdentifierResolverInterface;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\ProductFactory;
use PHPUnit\Framework\MockObject\MockObject;
use Sylius\Component\Channel\Context\ChannelNotFoundException;

final class InStockTest extends AbstractCustomFieldTest
{
    private const string LOCALE = 'nl';

    private InStock $inStock;

    public function setUp(): void
    {
        parent::setUp();

        /** @var ProductVariantRepository&MockObject $productVariantRepository */
        $productVariantRepository = $this->createMock(ProductVariantRepository::class);
        $productVariantRepository->method('findNumberAvailableForProductAndCountry')
            ->willReturn(12);
        $supplierIdentifierResolverMock = $this->createMock(SupplierIdentifierResolverInterface::class);

        $this->inStock = new InStock(
            $this->normalizer,
            $this->propertyAccessor,
            $productVariantRepository,
            new ProductContextUtil($this->channelContext, $this->orderContext, $productVariantRepository, $supplierIdentifierResolverMock)
        );
    }

    public function testItSupportsProductInterface(): void
    {
        $this->assertTrue($this->inStock->supports(new Product(), $this->getContext()));
    }

    public function testItDoesNotSupportProductVariantInterface(): void
    {
        $this->assertFalse($this->inStock->supports(new ProductVariant(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->inStock->supports(new Product(), []));
    }

    public function testItAddsInStock(): void
    {
        $product = ProductFactory::createWithValues('Test Product', 'test-product', self::LOCALE);

        $data = [];
        $this->inStock->add($product, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('inStock', $data);
    }

    public function testItDoesNotAddChannelIfNoChannelIsInContext(): void
    {
        $product = ProductFactory::createWithValues('Test Product', 'test-product', self::LOCALE);

        $this->channelContext->method('getChannel')
            ->willThrowException(new ChannelNotFoundException('Channel not found'));

        $data = [];
        $this->inStock->add($product, $data, 'json', $this->getContext());

        $this->assertArrayNotHasKey('inStock', $data);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'inStock',
            ],
        ];
    }
}
