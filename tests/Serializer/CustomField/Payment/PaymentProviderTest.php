<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Payment;

use App\Entity\Payment\GatewayConfig;
use App\Entity\Payment\Payment;
use App\Entity\Payment\PaymentMethod;
use App\Entity\Payment\PaymentMethodGatewayConfig;
use App\Serializer\CustomField\Payment\PaymentProvider;
use PHPUnit\Framework\TestCase;

final class PaymentProviderTest extends TestCase
{
    /**
     * @param array<array-key, mixed> $context
     *
     * @dataProvider supportsProvider
     */
    public function testSupports(Payment $payment, array $context, bool $expected): void
    {
        // Act
        $paymentProvider = new PaymentProvider();

        // Assert
        self::assertSame($expected, $paymentProvider->supports($payment, $context));
    }

    /**
     * @return iterable<string, array<array-key, mixed>>
     */
    public function supportsProvider(): iterable
    {
        yield 'valid context with paymentProvider' => [new Payment(), ['attributes' => ['paymentProvider' => []]], true];
        yield 'empty attributes' => [new Payment(), ['attributes' => []], false];
        yield 'different attribute with details' => [new Payment(), ['attributes' => ['details' => []]], false];
    }

    public function testAdd(): void
    {
        // Arrange
        $paymentMethod = new PaymentMethod();
        $paymentMethod->setCode('foo');

        $gatewayConfig = new GatewayConfig();
        $gatewayConfig->setConfig(['factory' => 'stripe']);
        $gatewayConfig->setFactoryName('old_stripe_key');

        $paymentMethodGatewayConfig = new PaymentMethodGatewayConfig(
            'google_pay',
            $paymentMethod,
            $gatewayConfig
        );

        $payment = new Payment();
        $payment->setPaymentMethodGatewayConfig($paymentMethodGatewayConfig);

        // Act
        $data = [];
        $paymentProvider = new PaymentProvider();
        $paymentProvider->add($payment, $data);

        // Assert
        $expectedData = ['paymentProvider' => ['identifier' => 'stripe', 'paymentMethodIdentifier' => 'google_pay']];
        self::assertSame($expectedData, $data);
    }
}
