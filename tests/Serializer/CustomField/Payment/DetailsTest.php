<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Payment;

use App\Entity\Payment\Payment;
use App\Serializer\CustomField\Payment\Details;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;

final class DetailsTest extends AbstractCustomFieldTest
{
    private Details $details;

    protected function setUp(): void
    {
        parent::setUp();

        $this->details = new Details();
    }

    public function testItSupportsPaymentInterface(): void
    {
        $this->assertTrue($this->details->supports(new Payment(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->details->supports(new Payment()));
    }

    public function testItRemovesEmptyDetails(): void
    {
        $data = [
            'details' => [],
        ];

        $payment = new Payment();
        $payment->setState(Payment::STATE_NEW);

        $this->details->add($payment, $data, 'json', $this->getContext());

        $this->assertArrayNotHasKey('details', $data);
    }

    /**
     * @dataProvider providePaymentsInCompletedStates
     */
    public function testItRemovesPaymentsInCompletedStates(Payment $payment): void
    {
        $data = [
            'details' => ['paymentId' => 'XXX'],
        ];

        $this->details->add($payment, $data, 'json', $this->getContext());

        $this->assertArrayNotHasKey('details', $data);
    }

    /**
     * @return iterable<string, array{0: Payment}>
     */
    public function providePaymentsInCompletedStates(): iterable
    {
        $states = [
            Payment::STATE_CANCELLED,
            Payment::STATE_COMPLETED,
            Payment::STATE_REFUNDED,
            Payment::STATE_FAILED,
            Payment::STATE_EXPIRED,
        ];

        foreach ($states as $state) {
            $payment = new Payment();
            $payment->setState($state);

            yield sprintf('payment in state %s', $state) => [$payment];
        }
    }

    public function testItKeepsDetails(): void
    {
        $data = [
            'details' => [
                'uuid' => 'ab101d76-c14e-4e51-971f-c837f1fe30fa',
                'payment_url' => 'https://dokteronline.payments.ehvg.dev/pay',
            ],
        ];

        $payment = new Payment();
        $payment->setState(Payment::STATE_NEW);

        $this->details->add($payment, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('details', $data);
    }

    private function getContext()
    {
        return [
            'attributes' => [
                'details' => [],
            ],
        ];
    }
}
