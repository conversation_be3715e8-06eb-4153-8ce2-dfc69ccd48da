<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\ProductAsWebhook;

use App\Entity\Channel\Channel;
use App\Entity\Product\Product;
use App\Repository\ChannelRepository;
use App\Repository\ProductRepository;
use App\Serializer\CustomField\ProductAsWebhook\InStockByChannel;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\ChannelPricingFactory;
use App\Tests\Util\Factory\ProductFactory;
use App\Tests\Util\Factory\ProductVariantFactory;
use PHPUnit\Framework\MockObject\MockObject;
use stdClass;

final class inStockByChannelTest extends AbstractCustomFieldTest
{
    private ChannelRepository&MockObject $channelRepository;
    private ProductRepository&MockObject $productRepository;

    private InStockByChannel $inStockByChannel;

    protected function setUp(): void
    {
        parent::setUp();

        $this->channelRepository = $this->createMock(ChannelRepository::class);
        $this->productRepository = $this->createMock(ProductRepository::class);

        $this->inStockByChannel = new InStockByChannel(
            $this->channelRepository,
            $this->productRepository,
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    /**
     * @return iterable<string, array{object, array<mixed>, bool}>
     */
    public function provideObjectAndContextForSupports(): iterable
    {
        yield 'invalid object and context not supported' => [
            new stdClass(),
            [],
            false,
        ];

        yield 'valid object and invalid context not supported' => [
            new Product(),
            [],
            false,
        ];

        yield 'valid object and context supported' => [
            new Product(),
            [
                'context' => 'webhook',
                'attributes' => [
                    InStockByChannel::PROPERTY_NAME => true,
                ],
            ],
            true,
        ];
    }

    /**
     * @dataProvider provideObjectAndContextForSupports
     * @param array<mixed> $context
     */
    public function testSupports(
        object $object,
        array $context,
        bool $expectedSupports,
    ): void {
        self::assertSame(
            $expectedSupports,
            $this->inStockByChannel->supports($object, $context),
        );
    }

    public function testAdd(): void
    {
        // Arrange
        $channelCode = 'dok_de';

        $this->productRepository->expects(self::once())
            ->method('findEnabledChannels')
            ->willReturn([$channelCode]);

        /** @var Channel $channel */
        $channel = ChannelFactory::create([
            'code' => $channelCode,
        ]);
        $channelBe = ChannelFactory::create([
            'code' => 'dok_be',
        ]);

        $this->channelRepository->method('findAllIndexedByCode')
            ->willReturn([
                $channel->getCode() => $channel,
                $channelBe->getCode() => $channelBe,
            ]);

        $channelPricing = ChannelPricingFactory::create([
            'channelCode' => $channel->getCode(),
        ]);

        $productVariant = ProductVariantFactory::create([
            'channelPricings' => [
                $channelPricing,
            ],
        ]);

        $product = ProductFactory::create([
            'channels' => [
                $channel,
                $channelBe,
            ],
            'variants' => [
                $productVariant,
            ],
        ]);

        $data = [];

        // Act
        $this->inStockByChannel->add($product, $data);

        // Assert
        self::assertTrue($data[InStockByChannel::PROPERTY_NAME][$channelCode]);
        self::assertFalse($data[InStockByChannel::PROPERTY_NAME]['dok_be']);
    }
}
