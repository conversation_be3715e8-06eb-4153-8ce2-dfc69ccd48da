<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\ProductAsWebhook;

use App\Entity\Product\Product;
use App\Serializer\CustomField\ProductAsWebhook\NameByLocale;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\ProductFactory;
use stdClass;

final class NameByLocaleTest extends AbstractCustomFieldTest
{
    private NameByLocale $nameByLocale;

    protected function setUp(): void
    {
        parent::setUp();

        $this->nameByLocale = new NameByLocale(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    /**
     * @return iterable<string, array{object, array<mixed>, bool}>
     */
    public function provideObjectAndContextForSupports(): iterable
    {
        yield 'invalid object and context not supported' => [
            new stdClass(),
            [],
            false,
        ];

        yield 'valid object and invalid context not supported' => [
            new Product(),
            [],
            false,
        ];

        yield 'valid object and context supported' => [
            new Product(),
            [
                'context' => 'webhook',
                'attributes' => [
                    NameByLocale::PROPERTY_NAME => true,
                ],
            ],
            true,
        ];
    }

    /**
     * @dataProvider provideObjectAndContextForSupports
     *
     * @param array<mixed> $context
     */
    public function testSupports(
        object $object,
        array $context,
        bool $expectedSupports,
    ): void {
        self::assertSame(
            $expectedSupports,
            $this->nameByLocale->supports($object, $context),
        );
    }

    public function testAdd(): void
    {
        // Arrange
        /** @var Product $product */
        $product = ProductFactory::create();

        ProductFactory::createTranslation($product, 'en', 'english-name');
        ProductFactory::createTranslation($product, 'de', 'german-name');

        $data = [];

        // Act
        $this->nameByLocale->add($product, $data);

        // Assert
        self::assertSame(
            [
                NameByLocale::PROPERTY_NAME => [
                    'en' => 'english-name',
                    'de' => 'german-name',
                ],
            ],
            $data,
        );
    }
}
