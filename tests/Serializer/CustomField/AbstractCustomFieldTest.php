<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField;

use App\Api\Context\OrderContextInterface;
use App\Entity\Addressing\Country;
use App\Entity\Channel\Channel as ChannelEntity;
use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Product\ProductVariant;
use App\Tests\Mocks\Entity\TestSupplier;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Channel\Context\ChannelContextInterface;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

abstract class AbstractCustomFieldTest extends TestCase
{
    protected const string CHANNEL_CODE = 'test-channel';
    protected const string COUNTRY_CODE = 'NL';

    protected NormalizerInterface&MockObject $normalizer;
    protected PropertyAccessorInterface&MockObject $propertyAccessor;
    protected ChannelContextInterface&MockObject $channelContext;
    protected OrderContextInterface&MockObject $orderContext;

    protected function setUp(): void
    {
        parent::setUp();

        $this->normalizer = $this->createMock(NormalizerInterface::class);
        $this->channelContext = $this->createMock(ChannelContextInterface::class);
        $this->channelContext->method('getChannel')
            ->willReturnCallback(
                function () {
                    $channel = new ChannelEntity();

                    $channel->setName('Test Channel');
                    $channel->setCode(static::CHANNEL_CODE);

                    $country = new Country();
                    $country->setCode(self::COUNTRY_CODE);
                    $channel->addCountry($country);

                    return $channel;
                }
            );

        $this->orderContext = $this->createMock(OrderContextInterface::class);
        $this->orderContext->method('getOrder')
            ->willReturnCallback(
                function () {
                    $supplier = new TestSupplier();
                    $supplier->setId(1337);
                    $supplier->setName('Test Supplier');

                    $productVariant = new ProductVariant();
                    $productVariant->setSupplier($supplier);

                    $orderItem = new OrderItem();
                    $orderItem->setVariant($productVariant);

                    $order = new Order();
                    $order->addItem($orderItem);

                    return $order;
                }
            );

        $this->propertyAccessor = $this->createMock(PropertyAccessorInterface::class);
    }
}
