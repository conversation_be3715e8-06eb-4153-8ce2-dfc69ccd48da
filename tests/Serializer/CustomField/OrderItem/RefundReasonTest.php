<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\OrderItem;

use App\Admin\Form\OrderItemRefundReasons;
use App\Entity\Order\OrderItem;
use App\Entity\Order\OrderItemRefund;
use App\Entity\Refund\RefundPayment;
use App\Serializer\CustomField\OrderItem\RefundReason;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTestCase;
use App\Tests\Util\Factory\OrderItemFactory;

final class RefundReasonTest extends AbstractCustomFieldTestCase
{
    public function testAdd(): void
    {
        // Arrange
        $refundReason = OrderItemRefundReasons::OutOfStock->value;

        $orderItem = new OrderItem();

        $orderItemRefund = new OrderItemRefund(
            orderItem: $orderItem,
            refundPayment: $this->createStub(RefundPayment::class),
            reason: $refundReason,
        );
        $orderItem->setOrderItemRefund($orderItemRefund);

        $data = [];

        // Act
        $this->customField->add($orderItem, $data);

        // Assert
        $this->assertArrayHasKey('refundReason', $data);
        $this->assertSame($refundReason, $data['refundReason']);
    }

    public function provideObjectAndContext(): iterable
    {
        yield 'not supported: Order item is not refunded with incorrect context' => [
            new OrderItem(),
            [],
            false,
        ];

        yield 'not supported: Order item is not refunded with correct context' => [
            new OrderItem(),
            ['attributes' => ['refundReason']],
            false,
        ];

        yield 'not supported: Order item is refunded with incorrect context' => [
            OrderItemFactory::create(['orderItemRefund' => $this->createStub(OrderItemRefund::class)]),
            [],
            false,
        ];

        yield 'is supported: Order item is refunded with correct context' => [
            OrderItemFactory::create(['orderItemRefund' => $this->createStub(OrderItemRefund::class)]),
            ['attributes' => ['refundReason']],
            true,
        ];
    }

    protected function getCustomFieldClass(): string
    {
        return RefundReason::class;
    }
}
