<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\OrderItem;

use App\Entity\Order\OrderItem;
use App\Entity\Product\Product;
use App\Entity\Product\ProductVariant;
use App\Entity\Taxonomy\Taxon;
use App\Serializer\CustomField\OrderItem\MainTaxon;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;

class MainTaxonTest extends AbstractCustomFieldTest
{
    private MainTaxon $mainTaxon;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mainTaxon = new MainTaxon(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsOrderItemInterface(): void
    {
        $this->assertTrue($this->mainTaxon->supports(new OrderItem(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->mainTaxon->supports(new OrderItem()));
    }

    public function testItAddsMainTaxon(): void
    {
        $taxon = new Taxon();
        $taxon->setCode('my_very_special_taxon');

        $product = new Product();
        $product->setMainTaxon($taxon);

        $variant = new ProductVariant();
        $variant->setProduct($product);

        $orderItem = new OrderItem();
        $orderItem->setVariant($variant);

        $data = [];
        $this->mainTaxon->add($orderItem, $data, 'json', $this->getContext());

        $expected = [
            'mainTaxon' => [
                'code' => 'my_very_special_taxon',
            ],
        ];

        self::assertSame($expected, $data);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'mainTaxon' => [
                    0 => 'code',
                ],
            ],
        ];
    }
}
