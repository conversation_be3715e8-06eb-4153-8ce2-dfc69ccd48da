<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Shipment;

use App\Entity\Addressing\Country;
use App\Entity\Channel\Channel;
use App\Entity\Order\Order;
use App\Entity\Product\Product;
use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShippingMethod;
use App\Entity\Shipping\ShippingMethodInterface;
use App\Serializer\CustomField\Shipment\TrackAndTraceLink;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\TrackAndTrace\TrackAndTraceLinkResolverInterface;
use PHPUnit\Framework\MockObject\MockObject;
use Sylius\Component\Core\Model\ShipmentInterface;

final class TrackAndTraceLinkTest extends AbstractCustomFieldTest
{
    private const string LOCALE_CODE = 'nl';

    private const string TRACK_AND_TRACE_LINK = 'https://track.and.trace.link';

    private TrackAndTraceLink $trackAndTraceLink;

    private TrackAndTraceLinkResolverInterface&MockObject $trackAndTraceLinkResolver;

    public function setUp(): void
    {
        parent::setUp();

        $this->trackAndTraceLinkResolver = $this->createMock(TrackAndTraceLinkResolverInterface::class);

        $this->trackAndTraceLink = new TrackAndTraceLink(
            $this->normalizer,
            $this->propertyAccessor,
            $this->trackAndTraceLinkResolver,
        );
    }

    public function testItSupportsProductVariantInterface(): void
    {
        $this->assertTrue($this->trackAndTraceLink->supports(new Shipment(), $this->getContext()));
    }

    public function testItDoesNotSupportProductInterface(): void
    {
        $this->assertFalse($this->trackAndTraceLink->supports(new Product(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->trackAndTraceLink->supports(new Shipment(), []));
    }

    public function testItDoesNotAddTrackAndTraceLinkIfOrderIsNotAvailable(): void
    {
        $shipment = $this->getShipment();
        $shipment->setOrder(null);

        $data = [];

        $this->trackAndTraceLink->add($shipment, $data, 'json', $this->getContext());
        $this->assertArrayNotHasKey('trackAndTraceLink', $data);
    }

    public function testItDoesNotAddTrackAndTraceLinkIfNoTrackAndTraceLinkIsAvailable(): void
    {
        $shipment = $this->getShipment();
        /** @var ShippingMethodInterface $shippingMethod */
        $shippingMethod = $shipment->getMethod();
        $shippingMethod->setTrackAndTraceLink(null);

        $data = [];

        $this->trackAndTraceLink->add($shipment, $data, 'json', $this->getContext());
        $this->assertArrayNotHasKey('trackAndTraceLink', $data);
    }

    public function testItDoesNotAddTrackAndTraceLinkIfOrderHasNoChannel(): void
    {
        $shipment = $this->getShipment(null);

        $data = [];

        $this->trackAndTraceLink->add($shipment, $data, 'json', $this->getContext());
        $this->assertArrayNotHasKey('trackAndTraceLink', $data);
    }

    public function testItAddsTrackAndTraceLink(): void
    {
        $trackAndTraceLink = self::TRACK_AND_TRACE_LINK;
        $this->trackAndTraceLinkResolver->method('resolveForShipment')->willReturn($trackAndTraceLink);

        $shipment = $this->getShipment();

        $data = [];

        $this->trackAndTraceLink->add($shipment, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('trackAndTraceLink', $data);
        $this->assertEquals($trackAndTraceLink, $data['trackAndTraceLink']);
    }

    private function getShipment(?string $channelCode = 'test-channel'): ShipmentInterface
    {
        $country = new Country();
        $country->setCode('test-country');

        $order = new Order();

        if (is_string($channelCode)) {
            $channel = new Channel();
            $channel->setCode('test-channel');
            $channel->addCountry($country);
            $order->setChannel($channel);
        }

        $order->setLocaleCode(self::LOCALE_CODE);

        $shippingMethod = new ShippingMethod();
        $shippingMethod->setCode('test-shipping-method');
        $shippingMethod->setTrackAndTraceLink(self::TRACK_AND_TRACE_LINK);

        $shipment = new Shipment();
        $shipment->setTracking('test-tracking');
        $shipment->setOrder($order);
        $shipment->setMethod($shippingMethod);

        return $shipment;
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'trackAndTraceLink',
            ],
        ];
    }
}
