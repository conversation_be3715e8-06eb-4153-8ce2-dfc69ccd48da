<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Shipment;

use App\Entity\Shipping\Shipment;
use App\Serializer\CustomField\Shipment\Items;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use stdClass;

final class ItemsTest extends AbstractCustomFieldTest
{
    private Items $customField;

    protected function setUp(): void
    {
        parent::setUp();

        $this->customField = new Items(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsShipmentWithValidContext(): void
    {
        $this->assertTrue($this->customField->supports(new Shipment(), $this->getContext()));
    }

    public function testItDoesNotSupportNonShipmentWithValidContext(): void
    {
        $this->assertFalse($this->customField->supports(new stdClass(), $this->getContext()));
    }

    /**
     * @dataProvider provideInvalidContext
     */
    public function testItDoesNotSupportShipmentWithoutValidContext(array $context): void
    {
        $this->assertFalse($this->customField->supports(new Shipment(), $context));
    }

    public function testItAddsItemsToShipment(): void
    {
        // Arrange
        $data = [];

        $shipment = new Shipment();
        $shipment->setVariantNames([
            1 => ['name' => 'Variant translation 1', 'type' => 'medication'],
            3 => ['name' => 'Variant translation 3', 'type' => 'medication'],
        ]);

        // Act
        $this->customField->add($shipment, $data, 'json', $this->getContext());

        // Assert
        self::assertSame([
            ['name' => 'Variant translation 1', 'type' => 'medication'],
            ['name' => 'Variant translation 3', 'type' => 'medication'],
        ], $data['items']);
    }

    public function provideInvalidContext(): iterable
    {
        yield [[]];

        yield [['attributes' => []]];

        yield [['attributes' => ['shipments']]];
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'items' => [
                    'name',
                    'type',
                ],
            ],
        ];
    }
}
