<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Shipment;

use App\Entity\Shipping\ShipmentInterface;
use App\Serializer\CustomField\Shipment\State;
use App\StateMachine\OrderFraudCheckStates;
use App\StateMachine\OrderPrescriptionStates;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\ShipmentFactory;
use Sylius\Component\Order\Model\OrderInterface as SyliusOrderInterface;
use Sylius\Component\Shipping\Model\ShipmentInterface as SyliusShipmentInterface;

final class StateTest extends AbstractCustomFieldTest
{
    private State $state;

    protected function setUp(): void
    {
        parent::setUp();

        $this->state = new State(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    /**
     * @return iterable<string, array{0: string, 1: ShipmentInterface}>
     */
    public function shipmentProvider(): iterable
    {
        $shipment = ShipmentFactory::create(['state' => ShipmentInterface::STATE_AWAITING_FRAUD_CHECK]);
        OrderFactory::createPrefilled(['fraudCheckState' => OrderFraudCheckStates::STATE_READY_FOR_FRAUD_CHECK, 'prescriptionState' => OrderPrescriptionStates::STATE_AWAITING_FRAUD_CHECK, 'shipments' => [$shipment]]);
        yield 'ShipmentState shows awaiting prescription when awaiting fraud check' => [ShipmentInterface::STATE_AWAITING_PRESCRIPTION, $shipment];

        $shipment = ShipmentFactory::create(['state' => ShipmentInterface::STATE_AWAITING_FRAUD_CHECK]);
        OrderFactory::createPrefilled(['fraudCheckState' => OrderFraudCheckStates::STATE_READY_FOR_FRAUD_CHECK, 'prescriptionState' => OrderPrescriptionStates::STATE_SKIPPED, 'shipments' => [$shipment]]);
        yield 'ShipmentState shows ready when awaiting fraud check' => [SyliusShipmentInterface::STATE_READY, $shipment];

        $shipment = ShipmentFactory::create(['state' => ShipmentInterface::STATE_PROCESSING]);
        OrderFactory::createPrefilled(['prescriptionState' => OrderPrescriptionStates::STATE_APPROVED, 'shipments' => [$shipment]]);
        yield 'ShipmentState shows pending when processing' => [ShipmentInterface::STATE_PENDING, $shipment];

        $shipment = ShipmentFactory::create(['state' => SyliusShipmentInterface::STATE_CANCELLED]);
        OrderFactory::createPrefilled(['shipments' => [$shipment]]);
        yield 'ShipmentState shows cancelled when cancelled and order state is fulfilled' => [SyliusShipmentInterface::STATE_CANCELLED, $shipment];

        $shipment = ShipmentFactory::create(['state' => SyliusShipmentInterface::STATE_CANCELLED]);
        OrderFactory::createPrefilled(['state' => SyliusOrderInterface::STATE_CANCELLED, 'shipments' => [$shipment]]);
        yield 'ShipmentState shows cancelled when cancelled and order state is cancelled' => [SyliusShipmentInterface::STATE_CANCELLED, $shipment];
    }

    /**
     * @dataProvider shipmentProvider
     */
    public function testAdd(
        string $expectedState,
        ShipmentInterface $shipment,
    ): void {
        $data = [];
        $context = ['attributes' => ['state']];

        // Testing add method
        $this->state->add($shipment, $data, null, $context);
        $this->assertArrayHasKey('state', $data);
        $this->assertEquals($expectedState, $data['state']);
    }

    public function testSupports(): void
    {
        $shipment = ShipmentFactory::create(['state' => ShipmentInterface::STATE_AWAITING_FRAUD_CHECK]);
        OrderFactory::createPrefilled(['shipments' => [$shipment]]);
        $this->assertTrue($this->state->supports($shipment, ['attributes' => ['state']]));
        $this->assertFalse($this->state->supports($shipment, []));
    }
}
