<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\PaymentMethod;

use App\Api\Context\OrderContextInterface;
use App\Api\Context\OrderNotFoundException;
use App\Entity\Channel\Channel;
use App\Entity\Order\Order;
use App\Entity\Payment\PaymentMethod;
use App\Serializer\CustomField\PaymentMethod\AvailableForOrder;
use App\Tests\Mocks\Entity\TestOrder;
use App\Tests\Util\Builder\PaymentMethodBuilder;
use App\Tests\Util\Factory\AddressFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use stdClass;
use Sylius\Component\Addressing\Comparator\AddressComparatorInterface;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

final class AvailableForOrderTest extends TestCase
{
    private const array DATA = [
        'code' => 'test_visa',
        'icon' => 'visa',
        'name' => 'Visa',
        'instructions' => 'Click the button to pay with Visa',
    ];

    private const array CONTEXT = [
        'attributes' => [
            'code',
            'name',
            'instructions',
            'icon',
            'available',
            'unavailableDueTo',
        ],
    ];

    private AddressComparatorInterface&MockObject $addressComparatorMock;
    private OrderContextInterface&MockObject $orderContextMock;
    private NormalizerInterface&MockObject $normalizerMock;
    private PropertyAccessorInterface&MockObject $propertyAccessorMock;

    private AvailableForOrder $availableForOrder;

    public function setUp(): void
    {
        $this->addressComparatorMock = $this->createMock(AddressComparatorInterface::class);
        $this->orderContextMock = $this->createMock(OrderContextInterface::class);

        $this->propertyAccessorMock = $this->createMock(PropertyAccessorInterface::class);
        $this->normalizerMock = $this->createMock(NormalizerInterface::class);

        $this->availableForOrder = new AvailableForOrder(
            $this->addressComparatorMock,
            $this->orderContextMock,
            $this->normalizerMock,
            $this->propertyAccessorMock
        );
    }

    public function testSupportsReturnsTrue(): void
    {
        self::assertTrue(
            $this->availableForOrder->supports(
                new PaymentMethod(),
                ['attributes' => ['available', 'unavailableDueTo']]
            )
        );
    }

    /**
     * @param array<mixed> $context
     *
     * @dataProvider provideSupportsFalse
     */
    public function testSupportsReturnsFalse(object $object, array $context): void
    {
        self::assertFalse(
            /** @phpstan-ignore-next-line */
            $this->availableForOrder->supports($object, $context)
        );
    }

    /**
     * @return iterable<string, array{0: object, 1: array<mixed>}>
     */
    public function provideSupportsFalse(): iterable
    {
        yield sprintf('not an instance of %s', PaymentMethod::class) => [
            new stdClass(),
            ['attributes' => ['available', 'unavailableDueTo']],
        ];

        yield 'missing context attribute unavailableDueTo' => [new PaymentMethod(), ['attributes' => ['available']]];
        yield 'missing context attribute available' => [new PaymentMethod(), ['attributes' => ['unavailableDueTo']]];
    }

    public function testAddTerminatesWhenOrderContextThrowsOrderNotFoundException(): void
    {
        // Arrange
        $this->orderContextMock
            ->expects(self::once())
            ->method('getOrder')
            ->willThrowException(new OrderNotFoundException());

        $object = PaymentMethodBuilder::create()
            ->setPaymentMethod(code: 'test_visa')
            ->addTranslation()
            ->getPaymentMethod();

        $data = self::DATA;

        $expectedData = $data; // expected data is unmodified after calling add (by reference)

        // Act
        $this->availableForOrder->add($object, $data, 'json', self::CONTEXT);

        // Assert
        self::assertSame($expectedData, $data);
    }

    public function testAddAddsAvailableToDataArgument(): void
    {
        // Arrange
        $this->orderContextMock
            ->expects(self::once())
            ->method('getOrder')
            ->willReturn(new Order());

        $object = PaymentMethodBuilder::create()
            ->setPaymentMethod(code: 'test_visa')
            ->addTranslation()
            ->getPaymentMethod();

        $data = self::DATA;

        // Act
        $this->availableForOrder->add($object, $data, 'json', self::CONTEXT);

        // Assert
        $expectedData = array_merge(self::DATA, ['available' => true]); // available is added to the referenced data

        self::assertSame($expectedData, $data);
    }

    public function testAddAddsAvailableToAndUnavailableDueToDataArgumentFromDifferentAddress(): void
    {
        // Arrange
        $order = new Order();
        $order->setBillingAddress(AddressFactory::createWithArguments());
        $order->setShippingAddress(AddressFactory::createWithArguments());

        $this->orderContextMock
            ->expects(self::once())
            ->method('getOrder')
            ->willReturn($order);

        $this->addressComparatorMock
            ->expects(self::once())
            ->method('equal')
            ->willReturn(false);

        $object = PaymentMethodBuilder::create()
            ->setPaymentMethod(code: 'test_visa')
            ->addTranslation()
            ->getPaymentMethod();
        $object->setDifferentAddressAllowed(false);

        $data = self::DATA;

        // Act
        $this->availableForOrder->add($object, $data, 'json', self::CONTEXT);

        // Assert
        $expectedData = array_merge(
            self::DATA,
            [
                'available' => false,
                'unavailableDueTo' => ['different_address_not_allowed'],
            ]
        );

        self::assertSame($expectedData, $data);
    }

    public function testAddAddsAvailableToAndUnavailableDueToDataArgumentFromMaximumOrderAmountReached(): void
    {
        // Arrange
        $channel = new Channel();
        $channel->setCode('dok_de');

        $order = new TestOrder();
        $order->setTotal(1000);
        $order->setChannel($channel);

        $this->orderContextMock
            ->expects(self::once())
            ->method('getOrder')
            ->willReturn($order);

        $object = PaymentMethodBuilder::create()
            ->setPaymentMethod(code: 'test_visa')
            ->addChannels($channel)
            ->addTranslation()
            ->addMaximumOrderTotalForChannel($channel->getCode(), 100)
            ->getPaymentMethod();

        $data = self::DATA;

        // Act
        $this->availableForOrder->add($object, $data, 'json', self::CONTEXT);

        // Assert
        $expectedData = array_merge(
            self::DATA,
            [
                'available' => false,
                'unavailableDueTo' => ['maximum_order_amount_reached'],
            ]
        );

        self::assertSame($expectedData, $data);
    }

    public function testAddAddsAvailableToAndUnavailableDueToDataArgumentFromDifferentAddressAndMaximumOrderAmount(): void
    {
        // Arrange
        $channel = new Channel();
        $channel->setCode('dok_de');

        $order = new TestOrder();
        $order->setTotal(1000);
        $order->setChannel($channel);
        $order->setBillingAddress(AddressFactory::createWithArguments());
        $order->setShippingAddress(AddressFactory::createWithArguments());

        $this->orderContextMock
            ->expects(self::once())
            ->method('getOrder')
            ->willReturn($order);

        $this->addressComparatorMock
            ->expects(self::once())
            ->method('equal')
            ->willReturn(false);

        $object = PaymentMethodBuilder::create()
            ->setPaymentMethod(code: 'test_visa')
            ->addTranslation()
            ->addChannels($channel)
            ->addMaximumOrderTotalForChannel($channel->getCode(), 100)
            ->getPaymentMethod();
        $object->setDifferentAddressAllowed(false);

        $data = self::DATA;

        // Act
        $this->availableForOrder->add($object, $data, 'json', self::CONTEXT);

        // Assert
        $expectedData = array_merge(
            self::DATA,
            [
                'available' => false,
                'unavailableDueTo' => [
                    'different_address_not_allowed',
                    'maximum_order_amount_reached',
                ],
            ]
        );

        self::assertSame($expectedData, $data);
    }
}
