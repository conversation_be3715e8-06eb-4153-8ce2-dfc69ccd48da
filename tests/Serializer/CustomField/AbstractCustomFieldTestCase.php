<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField;

use App\Serializer\CustomField\AbstractCustomField;
use PHPUnit\Framework\TestCase;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

abstract class AbstractCustomFieldTestCase extends TestCase
{
    protected AbstractCustomField $customField;

    protected function setUp(): void
    {
        $customFieldClass = $this->getCustomFieldClass();
        $customField = new $customFieldClass(
            $this->createStub(NormalizerInterface::class),
            $this->createStub(PropertyAccessorInterface::class),
        );
        self::assertInstanceOf(AbstractCustomField::class, $customField);

        $this->customField = $customField;
    }

    abstract public function testAdd(): void;

    /**
     * @return iterable<string, array{0: object, 1: array<mixed>, 2: bool}>
     */
    abstract public function provideObjectAndContext(): iterable;

    /**
     * @dataProvider provideObjectAndContext
     * @param array<mixed> $context
     */
    public function testSupports(
        object $object,
        array $context,
        bool $expectedSupports,
    ): void {
        self::assertSame($expectedSupports, $this->customField->supports($object, $context));
    }

    /**
     * @return class-string
     */
    abstract protected function getCustomFieldClass(): string;
}
