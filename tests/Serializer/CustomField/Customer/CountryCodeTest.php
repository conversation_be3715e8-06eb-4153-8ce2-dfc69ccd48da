<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Customer;

use App\Entity\Customer\Customer;
use App\Serializer\CustomField\Customer\CountryCode;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\CustomerFactory;

final class CountryCodeTest extends AbstractCustomFieldTest
{
    private CountryCode $countryCode;

    protected function setUp(): void
    {
        parent::setUp();

        $this->countryCode = new CountryCode(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsCustomerInterface(): void
    {
        $this->assertTrue($this->countryCode->supports(new Customer(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->countryCode->supports(new Customer()));
    }

    public function testItAddsCountryCode(): void
    {
        $data = [];

        $this->countryCode->add(CustomerFactory::createPrefilled(), $data, 'json', $this->getContext());

        $this->assertArrayHasKey('countryCode', $data);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'countryCode',
            ],
        ];
    }
}
