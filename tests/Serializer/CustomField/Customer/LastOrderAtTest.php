<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Customer;

use App\Entity\Customer\Customer;
use App\Serializer\CustomField\Customer\LastOrderAt;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\CustomerFactory;
use App\Tests\Util\Factory\OrderFactory;

final class LastOrderAtTest extends AbstractCustomFieldTest
{
    private LastOrderAt $lastOrderAt;

    protected function setUp(): void
    {
        parent::setUp();

        $this->lastOrderAt = new LastOrderAt(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsCustomerInterface(): void
    {
        $this->assertTrue($this->lastOrderAt->supports(new Customer(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->lastOrderAt->supports(new Customer()));
    }

    public function testItAddsCountryCode(): void
    {
        $data = [];

        $customer = CustomerFactory::createPrefilled();

        $customer->addOrder(OrderFactory::createPrefilled());

        $this->lastOrderAt->add($customer, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('lastOrderAt', $data);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'lastOrderAt',
            ],
        ];
    }
}
