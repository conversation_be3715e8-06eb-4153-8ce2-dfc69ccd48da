<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Customer;

use App\Entity\Customer\Customer;
use App\Serializer\CustomField\Customer\MarketingSubscriptionUuid;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\CustomerFactory;

final class MarketingSubscriptionUuidTest extends AbstractCustomFieldTest
{
    private MarketingSubscriptionUuid $marketingSubscriptionUuid;

    protected function setUp(): void
    {
        parent::setUp();

        $this->marketingSubscriptionUuid = new MarketingSubscriptionUuid(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsCustomerInterface(): void
    {
        $this->assertTrue($this->marketingSubscriptionUuid->supports(new Customer(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->marketingSubscriptionUuid->supports(new Customer()));
    }

    public function testItAddsMarketingSubscriptionUuid(): void
    {
        $data = [];

        $customer = CustomerFactory::createPrefilled();

        $this->marketingSubscriptionUuid->add($customer, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('marketingSubscriptionUuid', $data);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'marketingSubscriptionUuid',
            ],
        ];
    }
}
