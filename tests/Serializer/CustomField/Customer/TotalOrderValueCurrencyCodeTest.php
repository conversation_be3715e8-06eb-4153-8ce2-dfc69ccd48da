<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Customer;

use App\Entity\Customer\Customer;
use App\Serializer\CustomField\Customer\TotalOrderValueCurrencyCode;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\CustomerFactory;
use App\Tests\Util\Factory\OrderFactory;

final class TotalOrderValueCurrencyCodeTest extends AbstractCustomFieldTest
{
    private TotalOrderValueCurrencyCode $totalOrderValueCurrencyCode;

    protected function setUp(): void
    {
        parent::setUp();

        $this->totalOrderValueCurrencyCode = new TotalOrderValueCurrencyCode(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsCustomerInterface(): void
    {
        $this->assertTrue($this->totalOrderValueCurrencyCode->supports(new Customer(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->totalOrderValueCurrencyCode->supports(new Customer()));
    }

    public function testItAddsTotalOrderValueCurrencyCode(): void
    {
        $data = [];

        $customer = CustomerFactory::createPrefilled();

        $customer->addOrder(OrderFactory::createPrefilled());

        $this->totalOrderValueCurrencyCode->add($customer, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('totalOrderValueCurrencyCode', $data);
        $this->assertSame('EUR', $data['totalOrderValueCurrencyCode']);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'totalOrderValueCurrencyCode',
            ],
        ];
    }
}
