<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Customer;

use App\Entity\Customer\Customer;
use App\Repository\OrderRepositoryInterface;
use App\Serializer\CustomField\Customer\TotalOrderValue;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\CustomerFactory;
use PHPUnit\Framework\MockObject\MockObject;

final class TotalOrderValueTest extends AbstractCustomFieldTest
{
    private TotalOrderValue $totalOrderValue;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var OrderRepositoryInterface&MockObject $orderRepository */
        $orderRepository = $this->createMock(OrderRepositoryInterface::class);
        $orderRepository->method('getTotalOrderValue')->willReturn(20000);

        $this->totalOrderValue = new TotalOrderValue(
            $this->normalizer,
            $this->propertyAccessor,
            $orderRepository,
        );
    }

    public function testItSupportsCustomerInterface(): void
    {
        $this->assertTrue($this->totalOrderValue->supports(new Customer(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->totalOrderValue->supports(new Customer()));
    }

    public function testItAddsTotalOrderValue(): void
    {
        $data = [];

        $customer = CustomerFactory::createPrefilled();

        $this->totalOrderValue->add($customer, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('totalOrderValue', $data);
        $this->assertSame(20000, $data['totalOrderValue']);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'totalOrderValue',
            ],
        ];
    }
}
