<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Customer;

use App\Entity\Customer\Customer;
use App\Repository\OrderRepositoryInterface;
use App\Serializer\CustomField\Customer\TotalOrderCount;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\CustomerFactory;
use PHPUnit\Framework\MockObject\MockObject;

final class TotalOrderCountTest extends AbstractCustomFieldTest
{
    private TotalOrderCount $totalOrderCount;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var OrderRepositoryInterface&MockObject $orderRepository */
        $orderRepository = $this->createMock(OrderRepositoryInterface::class);
        $orderRepository->method('countOrders')->willReturn(12);

        $this->totalOrderCount = new TotalOrderCount(
            $this->normalizer,
            $this->propertyAccessor,
            $orderRepository,
        );
    }

    public function testItSupportsCustomerInterface(): void
    {
        $this->assertTrue($this->totalOrderCount->supports(new Customer(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->totalOrderCount->supports(new Customer()));
    }

    public function testItAddsOrderCount(): void
    {
        $data = [];

        $customer = CustomerFactory::createPrefilled();

        $this->totalOrderCount->add($customer, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('totalOrderCount', $data);
        $this->assertSame(12, $data['totalOrderCount']);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'totalOrderCount',
            ],
        ];
    }
}
