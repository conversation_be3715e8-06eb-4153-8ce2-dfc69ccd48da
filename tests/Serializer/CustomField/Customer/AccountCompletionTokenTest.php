<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Customer;

use App\Entity\Customer\Customer;
use App\Entity\User\ShopUser;
use App\Serializer\CustomField\Customer\AccountCompletionToken;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\CustomerFactory;
use DateTime;

final class AccountCompletionTokenTest extends AbstractCustomFieldTest
{
    private const string TOKEN = 'token';

    private AccountCompletionToken $accountCompletionToken;

    protected function setUp(): void
    {
        parent::setUp();

        $this->accountCompletionToken = new AccountCompletionToken(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsCustomerInterface(): void
    {
        $this->assertTrue($this->accountCompletionToken->supports(new Customer(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->accountCompletionToken->supports(new Customer()));
    }

    public function testItDoesntAddTokenWhenDateIsEmpty(): void
    {
        $data = [];

        $user = new ShopUser();
        $user->setPasswordResetToken(self::TOKEN);
        /** @var Customer $customer */
        $customer = CustomerFactory::createPrefilled();
        $customer->setUser($user);

        $this->accountCompletionToken->add($customer, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('accountCompletionToken', $data);
        $this->assertNull($data['accountCompletionToken']);
    }

    public function testItDoesntAddTokenWhenTokenIsEmpty(): void
    {
        $data = [];

        $user = new ShopUser();
        $user->setPasswordResetToken(null);
        $user->setAccountCompletionTokenRequestedAt(new DateTime());
        /** @var Customer $customer */
        $customer = CustomerFactory::createPrefilled();
        $customer->setUser($user);

        $this->accountCompletionToken->add($customer, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('accountCompletionToken', $data);
        $this->assertNull($data['accountCompletionToken']);
    }

    public function testItAddsTokenWhenDateAndTokenIsFilled(): void
    {
        $data = [];

        $user = new ShopUser();
        $user->setPasswordResetToken(self::TOKEN);
        $user->setAccountCompletionTokenRequestedAt(new DateTime());
        /** @var Customer $customer */
        $customer = CustomerFactory::createPrefilled();
        $customer->setUser($user);

        $this->accountCompletionToken->add($customer, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('accountCompletionToken', $data);
        $this->assertSame(self::TOKEN, $data['accountCompletionToken']);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'accountCompletionToken',
            ],
        ];
    }
}
