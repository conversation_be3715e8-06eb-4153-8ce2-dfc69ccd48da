<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Customer;

use App\Entity\Customer\Customer;
use App\Serializer\CustomField\MarketingSubscription\EmbeddedInCustomerAsWebhook\OptInDirectMail;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\CustomerFactory;

final class OptInDirectMailTest extends AbstractCustomFieldTest
{
    private OptInDirectMail $optInDirectMail;

    protected function setUp(): void
    {
        parent::setUp();

        $this->optInDirectMail = new OptInDirectMail(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsCustomerInterface(): void
    {
        $this->assertTrue($this->optInDirectMail->supports(new Customer(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->optInDirectMail->supports(new Customer()));
    }

    public function testItAddsOptInDirectMail(): void
    {
        $data = [];

        $customer = CustomerFactory::createPrefilled();

        $this->optInDirectMail->add($customer, $data, 'json', $this->getContext());

        $this->assertArrayHasKey('optInDirectMail', $data);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'optInDirectMail',
            ],
        ];
    }
}
