<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\ProductVariant;

use App\Entity\Product\Product;
use App\Entity\Product\ProductVariant;
use App\Serializer\CustomField\Product\Util\ProductAttributesUtil;
use App\Serializer\CustomField\ProductVariant\ProductAttributes;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\ProductFactory;

final class ProductAttributesTest extends AbstractCustomFieldTest
{
    private const string LOCALE_CODE = 'nl';

    private const string PRODUCT_ATTRIBUTE_CODE = 'type';

    private ProductAttributes $productAttributes;

    public function setUp(): void
    {
        parent::setUp();

        $this->productAttributes = new ProductAttributes(
            new ProductAttributesUtil($this->propertyAccessor)
        );
    }

    public function testItSupportsProductVariantInterface(): void
    {
        $this->assertTrue($this->productAttributes->supports(new ProductVariant(), $this->getContext()));
    }

    public function testItDoesNotSupportProductInterface(): void
    {
        $this->assertFalse($this->productAttributes->supports(new Product(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->productAttributes->supports(new Product(), []));
    }

    public function testItDoesNotAddProductAttributesIfProductHasNoAttributes(): void
    {
        $product = new Product();
        $productVariant = new ProductVariant();
        $product->addVariant($productVariant);

        $data = [];

        $this->productAttributes->add($productVariant, $data, 'json', $this->getContext());

        $this->assertArrayNotHasKey('productAttributes', $data);
    }

    public function testItDoesNotAddProductAttributesIfNotInContext(): void
    {
        $this->propertyAccessor->method('getValue')->willReturn(null);

        $product = ProductFactory::createWithValues('Test Product', 'test-product', self::LOCALE_CODE);
        ProductFactory::addType($product, self::PRODUCT_ATTRIBUTE_CODE);

        $productVariant = new ProductVariant();
        $product->addVariant($productVariant);

        $data = [];

        $this->productAttributes->add($productVariant, $data, 'json', []);

        $this->assertArrayNotHasKey('productAttributes', $data);
    }

    public function testItAddsProductAttributes(): void
    {
        $this->propertyAccessor->method('getValue')->willReturn([self::PRODUCT_ATTRIBUTE_CODE]);

        $product = ProductFactory::createWithValues('Test Product', 'test-product', self::LOCALE_CODE);
        ProductFactory::addType($product, self::PRODUCT_ATTRIBUTE_CODE);

        $productVariant = new ProductVariant();
        $product->addVariant($productVariant);

        $data = [];

        $this->productAttributes->add($productVariant, $data, 'json', $this->getContext());

        $expected = [
            'productAttributes' => [
                self::PRODUCT_ATTRIBUTE_CODE => self::PRODUCT_ATTRIBUTE_CODE,
            ],
        ];

        $this->assertEquals($expected, $data);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'productAttributes' => [
                    self::PRODUCT_ATTRIBUTE_CODE,
                ],
            ],
        ];
    }
}
