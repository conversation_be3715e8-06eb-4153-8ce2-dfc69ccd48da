<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\ProductVariant;

use App\Entity\Product\Product;
use App\Entity\Product\ProductTranslation;
use App\Entity\Product\ProductVariant;
use App\Serializer\CustomField\ProductVariant\Image;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\ProductFactory;
use App\Tests\Util\Factory\TranslationFactory;
use Sylius\Component\Core\Model\ImageInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

final class ImageTest extends AbstractCustomFieldTest
{
    private const string LOCALE = 'nl';

    private const array LOCALES = ['nl', 'en', 'fr', 'de'];

    private const string IMAGE_PATH_FORMAT = 'https://path.to.image.%s.jpg';

    private Image $image;

    public function setUp(): void
    {
        parent::setUp();

        $this->normalizer = $this->createMock(NormalizerInterface::class);
        $this->normalizer->method('normalize')
            ->willReturnCallback(
                fn (ImageInterface $image) => [
                    'path' => $image->getPath(),
                ],
            );

        $this->image = new Image(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsProductVariantInterface(): void
    {
        $this->assertTrue($this->image->supports(new ProductVariant(), $this->getContext()));
    }

    public function testItDoesNotSupportProductInterface(): void
    {
        $this->assertFalse($this->image->supports(new Product(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->image->supports(new Product(), []));
    }

    public function testItAddsImage(): void
    {
        $product = ProductFactory::createWithValues('Test product', 'test-product', self::LOCALE);
        $productVariant = ProductFactory::addVariant($product);

        foreach (self::LOCALES as $locale) {
            TranslationFactory::addTranslation(
                $product,
                ProductTranslation::class,
                $locale,
                [
                    'name' => sprintf('Product name (%s)', $locale),
                    'slug' => sprintf('product-slug-%s', $locale),
                ]
            );
            TranslationFactory::addTranslation(
                $productVariant,
                ProductTranslation::class,
                $locale,
                [
                    'name' => sprintf('Product variant name (%s)', $locale),
                    'slug' => sprintf('product-variant-slug-%s', $locale),
                ]
            );
            ProductFactory::addImage($product, $locale, sprintf(self::IMAGE_PATH_FORMAT, $locale));
        }

        $productVariant->setCurrentLocale(self::LOCALE);

        $data = [];

        $this->image->add($productVariant, $data, 'json', $this->getContext());

        $expected = [
            'image' => [
                'path' => sprintf(self::IMAGE_PATH_FORMAT, self::LOCALE),
            ],
        ];

        $this->assertEquals($expected, $data);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'image' => [
                    'path',
                ],
            ],
        ];
    }
}
