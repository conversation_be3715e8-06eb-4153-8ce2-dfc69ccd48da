<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\ProductVariant;

use App\Entity\Product\Product;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariant;
use App\Serializer\CustomField\ProductVariant\Code;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\ProductFactory;

final class CodeTest extends AbstractCustomFieldTest
{
    private Code $codeCustomField;

    protected function setUp(): void
    {
        parent::setUp();

        $this->codeCustomField = new Code(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsProductVariantWithValidContext(): void
    {
        $this->assertTrue($this->codeCustomField->supports(new ProductVariant(), $this->getContext()));
    }

    public function testItDoesNotSupportProductVariantWithInvalidContext(): void
    {
        $this->assertFalse($this->codeCustomField->supports(new ProductVariant()));
    }

    public function testItDoesNotSupportProductWithValidContext(): void
    {
        $this->assertFalse($this->codeCustomField->supports(new Product(), $this->getContext()));
    }

    public function testItAddsCodeWithoutSupplierAndCountry(): void
    {
        // Arrange
        $data = [];

        $product = new Product();
        ProductFactory::setType($product, ProductType::MEDICATION->value);

        $productVariant = new ProductVariant();
        $productVariant->setProduct($product);
        $productVariant->setCode('96_155_ndsm_apotheek_worldwide');

        // Act
        $this->codeCustomField->add(
            $productVariant,
            $data,
            'json',
            $this->getContext(),
        );

        // Assert
        $this->assertSame([
            'code' => '96_155',
        ], $data);
    }

    private function getContext(): array
    {
        return [
            'context' => 'webhook',
            'attributes' => [
                0 => 'code',
            ],
        ];
    }
}
