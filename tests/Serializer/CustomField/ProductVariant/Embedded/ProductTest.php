<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\ProductVariant\Embedded;

use App\Serializer\CustomField\ProductVariant\Embedded\Product;
use App\Tests\Mocks\Entity\TestProductVariant;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

final class ProductTest extends TestCase
{
    private NormalizerInterface&MockObject $normalizer;
    private PropertyAccessorInterface&MockObject $propertyAccessor;
    private RequestStack&MockObject $requestStack;

    private Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        $this->normalizer = $this->createMock(NormalizerInterface::class);
        $this->propertyAccessor = $this->createMock(PropertyAccessorInterface::class);
        $this->requestStack = $this->createMock(RequestStack::class);

        $this->product = new Product($this->requestStack, $this->normalizer, $this->propertyAccessor);
    }

    public function testAdd(): void
    {
        // Arrange
        $request = new Request();
        $request->query->add(['_embed' => ['product']]);

        $productVariant = new TestProductVariant();
        $this->requestStack->method('getCurrentRequest')->willReturn($request);
        $this->propertyAccessor->method('getValue')->willReturn(['product' => ['taxons']]);
        $this->normalizer->method('normalize')->willReturn(['taxons' => []]);

        // Act
        $data = [];
        $this->product->add($productVariant, $data, null, $this->getContext());

        // Assert
        self::assertSame(['_embedded' => ['product' => ['taxons' => []]]], $data);
    }

    /**
     * @dataProvider  supportsEmbeddedProductDataProvider
     */
    public function testSupportsEmbeddedProduct(?Request $request, array $context, bool $expectedResult): void
    {
        // Arrange
        $productVariant = new TestProductVariant();
        $this->requestStack->method('getCurrentRequest')->willReturn($request);

        // Act
        $result = $this->product->supports($productVariant, $context);

        // Assert
        self::assertSame($expectedResult, $result);
    }

    protected function supportsEmbeddedProductDataProvider(): iterable
    {
        $request = new Request();
        $request->query->add(['_embed' => ['product']]);

        yield 'Supports a valid request' => ['request' => $request, 'context' => $this->getContext(), 'expectedResult' => true];

        yield 'Requires a valid request' => ['request' => null, 'context ' => $this->getContext(), 'expectedResult' => false];

        $request = new Request();
        yield 'Requires the correct a _embed parameter' => ['request' => $request, 'context' => $this->getContext(), 'expectedResult' => false];

        $request = new Request();
        $request->query->add(['_embed' => ['productAssociations']]);
        yield 'Requires the correct _embed parameter' => ['request' => $request, 'context' => $this->getContext(), 'expectedResult' => false];

        $request = new Request();
        $request->query->add(['_embed' => ['product']]);
        $context = ['attributes' => []];
        yield 'Requires a valid _embedded context' => ['request' => $request, 'context' => $context, 'expectedResult' => false];

        $request = new Request();
        $request->query->add(['_embed' => ['product']]);
        $context = ['attributes' => ['_embedded' => []]];
        yield 'Requires a valid product context' => ['request' => $request, 'context' => $context, 'expectedResult' => false];
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                '_embedded' => [
                    'product' => [
                        0 => 'code',
                        1 => 'slug',
                        2 => 'name',
                        'images' => [
                            0 => 'path',
                        ],
                        'mainTaxon' => [
                            0 => 'code',
                            1 => 'slug',
                            2 => 'name',
                        ],
                        'taxons' => [
                            0 => 'code',
                            1 => 'slug',
                            2 => 'name',
                        ],
                    ],
                ],
            ],
        ];
    }
}
