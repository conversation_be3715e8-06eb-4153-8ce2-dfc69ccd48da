<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\ProductVariant;

use App\Entity\Channel\Channel as ChannelEntity;
use App\Entity\Channel\ChannelPricing;
use App\Entity\Currency\Currency;
use App\Entity\Product\Product;
use App\Entity\Product\ProductVariant;
use App\Serializer\CustomField\ProductVariant\Price;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Channel\Context\ChannelContextInterface;
use Sylius\Component\Channel\Context\ChannelNotFoundException;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

final class PriceTest extends TestCase
{
    private Price $price;

    private ChannelContextInterface&MockObject $channelContext;

    public function setUp(): void
    {
        $normalizer = $this->createMock(NormalizerInterface::class);
        $propertyAccessor = $this->createMock(PropertyAccessorInterface::class);
        $this->channelContext = $this->createMock(ChannelContextInterface::class);
        $this->channelContext->method('getChannel')
            ->willReturnCallback(
                function () {
                    $channel = new ChannelEntity();

                    $channel->setName('Test Channel');
                    $channel->setCode('test-channel');
                    $currency = new Currency();
                    $currency->setCode('EUR');
                    $channel->addCurrency($currency);

                    return $channel;
                }
            );

        $this->price = new Price(
            $normalizer,
            $propertyAccessor,
            $this->channelContext,
        );
    }

    public function testItSupportsProductVariantInterface(): void
    {
        $this->assertTrue($this->price->supports(new ProductVariant(), $this->getContext()));
    }

    public function testItDoesNotSupportProductInterface(): void
    {
        $this->assertFalse($this->price->supports(new Product(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->price->supports(new Product(), []));
    }

    public function testItDoesNotAddPriceIfNoChannelIsInContext(): void
    {
        $this->channelContext->method('getChannel')
            ->willThrowException(new ChannelNotFoundException('Channel not found.'));

        $data = [];

        $this->price->add(new ProductVariant(), $data, 'json', $this->getContext());

        $this->assertArrayNotHasKey('price', $data);
    }

    public function testItDoesNotAddPriceIfNoChannelPricingIsAvailable(): void
    {
        $data = [];

        $this->price->add(new ProductVariant(), $data, 'json', $this->getContext());

        $this->assertArrayNotHasKey('price', $data);
    }

    public function testItAddsPrice(): void
    {
        $productVariant = new ProductVariant();
        $channelPricing = new ChannelPricing();
        $channel = $this->channelContext->getChannel();
        $channelPricing->setChannelCode($channel->getCode());
        $channelPricing->setEnabled(true);
        $channelPricing->setPrice(1000);
        $productVariant->addChannelPricing($channelPricing);

        $data = [];

        $this->price->add($productVariant, $data, 'json', $this->getContext());

        $expected = [
            'price' => [
                'amount' => 1000,
                'currency' => 'EUR',
            ],
        ];

        $this->assertEquals($expected, $data);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'price' => [],
            ],
        ];
    }
}
