<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\ProductVariant;

use App\Entity\Product\Product;
use App\Entity\Product\ProductOption;
use App\Entity\Product\ProductOptionValue;
use App\Entity\Product\ProductVariant;
use App\Serializer\CustomField\ProductVariant\OptionValues;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;

final class OptionValuesTest extends AbstractCustomFieldTest
{
    private OptionValues $optionValues;

    public function setUp(): void
    {
        parent::setUp();

        $this->optionValues = new OptionValues(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsProductVariantInterface(): void
    {
        $this->assertTrue($this->optionValues->supports(new ProductVariant(), $this->getContext()));
    }

    public function testItDoesNotSupportProductInterface(): void
    {
        $this->assertFalse($this->optionValues->supports(new Product(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->optionValues->supports(new Product(), []));
    }

    public function testItAddsOptionValues(): void
    {
        $productVariant = new ProductVariant();
        for ($i = 1; $i <= 2; ++$i) {
            $productOptionValue = new ProductOptionValue();
            $productOptionValue->setCode('product-option-value-code-'.$i);
            $productOption = new ProductOption();
            $productOption->setCode('product-option-code-'.$i);
            $productOptionValue->setOption($productOption);
            $productVariant->addOptionValue($productOptionValue);
        }

        $data = [];

        $this->optionValues->add($productVariant, $data, 'json', $this->getContext());

        $expected = [
            'optionValues' => [
                'product-option-code-1' => 'product-option-value-code-1',
                'product-option-code-2' => 'product-option-value-code-2',
            ],
        ];

        $this->assertEquals($expected, $data);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'optionValues' => [],
            ],
        ];
    }
}
