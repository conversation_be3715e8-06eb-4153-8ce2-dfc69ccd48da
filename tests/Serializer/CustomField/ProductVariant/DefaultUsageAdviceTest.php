<?php

declare(strict_types=1);

namespace Serializer\CustomField\ProductVariant;

use App\Entity\Product\ProductVariant;
use App\Entity\Product\ProductVariantTranslation;
use App\Serializer\CustomField\ProductVariant\DefaultUsageAdvice;
use PHPUnit\Framework\TestCase;
use stdClass;
use Sylius\Component\Locale\Context\LocaleContextInterface;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

final class DefaultUsageAdviceTest extends TestCase
{
    private const array CONTEXT = [
        'attributes' => [
            'defaultUsageAdvice',
        ],
    ];
    private DefaultUsageAdvice $customField;

    public function setUp(): void
    {
        $localeContextMock = $this->createMock(LocaleContextInterface::class);
        $localeContextMock->method('getLocaleCode')
            ->willReturn('de');

        $this->customField = new DefaultUsageAdvice(
            $localeContextMock,
            $this->createMock(NormalizerInterface::class),
            $this->createMock(PropertyAccessorInterface::class),
        );
    }

    public function testItSupportsProductVariantInterface(): void
    {
        self::assertTrue($this->customField->supports(new ProductVariant(), self::CONTEXT));
    }

    public function textItDoesNotSupportOtherClass(): void
    {
        self::assertFalse($this->customField->supports(new stdClass(), self::CONTEXT));
    }

    public function provideInvalidContext(): iterable
    {
        yield 'Empty array.' => [[]];

        yield 'Valid attribute name, not in the right place in te array.' => [['defaultUsageAdvice']];

        yield 'Valid attributes key but not as an array.' => [['attributes']];

        yield 'Valid attributes key but with an empty array' => [['attributes' => []]];

        yield 'Valid attributes key but with an array with a wrong attribute name' => [['attributes' => ['something']]];
    }

    /**
     * @dataProvider provideInvalidContext
     */
    public function testItDoesNotSupportInvalidContext(array $context): void
    {
        self::assertFalse($this->customField->supports(new ProductVariant(), $context));
    }

    public function testItDoesNotAddCustomFieldWhenDefaultUsageAdviceIsMissing(): void
    {
        // Arrange
        $productVariantTranslation = new ProductVariantTranslation();
        $productVariantTranslation->setLocale('de');

        $productVariant = new ProductVariant();
        $productVariant->addTranslation($productVariantTranslation);

        $data = [];

        // Act
        $this->customField->add($productVariant, $data, 'json', self::CONTEXT);

        // Assert
        self::assertEmpty($data);
    }

    public function testItAddsCustomField(): void
    {
        // Arrange
        $productVariantTranslation = new ProductVariantTranslation();
        $productVariantTranslation->setLocale('de');
        $productVariantTranslation->setDefaultUsageAdvice('test-default-usage-advice');

        $productVariant = new ProductVariant();
        $productVariant->addTranslation($productVariantTranslation);

        $data = [];

        // Act
        $this->customField->add($productVariant, $data, 'json', self::CONTEXT);

        // Assert
        self::assertEquals([
            'defaultUsageAdvice' => 'test-default-usage-advice',
        ], $data);
    }
}
