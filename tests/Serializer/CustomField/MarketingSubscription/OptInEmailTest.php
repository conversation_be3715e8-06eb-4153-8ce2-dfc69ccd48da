<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\MarketingSubscription;

use App\Serializer\CustomField\MarketingSubscription\OptInEmail;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\MarketingSubscriptionFactory;
use DateTimeInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

final class OptInEmailTest extends AbstractCustomFieldTest
{
    private OptInEmail $optInEmail;

    protected function setUp(): void
    {
        parent::setUp();

        $this->normalizer = $this->createMock(NormalizerInterface::class);
        $this->normalizer->method('normalize')->willReturnCallback(
            function ($data): ?string {
                if ($data instanceof DateTimeInterface) {
                    return $data->format('Y-m-d H:i');
                }

                return null;
            }
        );

        $this->optInEmail = new OptInEmail(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsMarketingSubscriptionInterface(): void
    {
        $marketingSubscription = MarketingSubscriptionFactory::createPrefilled();

        $this->assertTrue($this->optInEmail->supports($marketingSubscription, $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $marketingSubscription = MarketingSubscriptionFactory::createPrefilled();

        $this->assertFalse($this->optInEmail->supports($marketingSubscription));
    }

    public function testItAddsOptInEmail(): void
    {
        $data = [];

        $marketingSubscription = MarketingSubscriptionFactory::createPrefilled();

        $this->optInEmail->add(
            $marketingSubscription,
            $data,
            'json',
            $this->getContext()
        );

        $this->assertSame([
            'optInEmail' => [
                'state' => 'double',
                'subscriptionTypes' => [
                    'service' => true,
                    'productInformation' => true,
                    'promotions' => true,
                ],
                'subscriptionTimeoutUntil' => '2022-03-04 12:00',
                'subscribedAt' => '2022-03-03 12:00',
                'unsubscribedAt' => '2022-03-05 12:00',
                'source' => 'test-opt-in-email-source',
            ],
        ], $data);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'optInEmail' => [
                    'state',
                    'subscriptionTypes' => [
                        'service',
                        'productInformation',
                        'promotions',
                    ],
                    'subscriptionTimeoutUntil',
                    'subscribedAt',
                    'unsubscribedAt',
                    'source',
                ],
            ],
        ];
    }
}
