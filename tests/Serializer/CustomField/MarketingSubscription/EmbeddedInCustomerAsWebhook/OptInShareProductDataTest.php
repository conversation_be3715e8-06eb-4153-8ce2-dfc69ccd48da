<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\MarketingSubscription\EmbeddedInCustomerAsWebhook;

use App\Entity\Customer\Customer;
use App\Entity\MarketingSubscription\MarketingSubscription;
use App\Entity\MarketingSubscription\OptInShareProductData as OptInShareProductDataEntity;
use App\Serializer\CustomField\MarketingSubscription\EmbeddedInCustomerAsWebhook\OptInShareProductData;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use stdClass;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

class OptInShareProductDataTest extends TestCase
{
    private NormalizerInterface&MockObject $normalizerMock;
    private PropertyAccessorInterface&MockObject $propertyAccessorMock;
    private OptInShareProductData $optInShareProductData;

    protected function setUp(): void
    {
        $this->normalizerMock = $this->createMock(NormalizerInterface::class);
        $this->propertyAccessorMock = $this->createMock(PropertyAccessorInterface::class);

        $this->optInShareProductData = new OptInShareProductData(
            $this->normalizerMock,
            $this->propertyAccessorMock
        );
    }

    /**
     * @param array<mixed> $context
     *
     * @dataProvider supportsDataProvider
     */
    public function testSupports(object $object, array $context, bool $expectedResult): void
    {
        $result = $this->optInShareProductData->supports($object, $context);

        $this->assertSame($expectedResult, $result);
    }

    /**
     * @return iterable<string, array{
     *     0: object,
     *     1: array<mixed>,
     *     2: bool
     * }>
     */
    public function supportsDataProvider(): iterable
    {
        $marketingSubscription = $this->createMock(MarketingSubscription::class);
        $customer = $this->createMock(Customer::class);
        $invalidObject = new stdClass();

        $validContext = [
            'attributes' => [
                'optInShareProductData' => [],
            ],
        ];

        $invalidContextNoAttributes = [];
        $invalidContextWrongAttributeType = [
            'attributes' => [
                'optInShareProductData' => 'not_an_array',
            ],
        ];
        $invalidContextMissingKey = [
            'attributes' => [],
        ];

        yield 'supports marketing subscription with valid context' => [
            $marketingSubscription,
            $validContext,
            true,
        ];

        yield 'supports customer with valid context' => [
            $customer,
            $validContext,
            true,
        ];

        yield 'does not support invalid object type' => [
            $invalidObject,
            $validContext,
            false,
        ];

        yield 'does not support when context has no attributes' => [
            $marketingSubscription,
            $invalidContextNoAttributes,
            false,
        ];

        yield 'does not support when optInShareProductData is not array' => [
            $marketingSubscription,
            $invalidContextWrongAttributeType,
            false,
        ];

        yield 'does not support when optInShareProductData key missing' => [
            $marketingSubscription,
            $invalidContextMissingKey,
            false,
        ];
    }

    public function testAddWithInvalidObjectType(): void
    {
        $data = [];
        $object = new stdClass();

        $this->optInShareProductData->add($object, $data);

        $this->assertEmpty($data);
    }

    public function testAdd(): void
    {
        $data = [];
        $optInShareProductDataEntity = new OptInShareProductDataEntity();
        $optInShareProductDataEntity->subscribe();

        $marketingSubscription = $this->createMock(MarketingSubscription::class);
        $marketingSubscription->method('getOptInShareProductData')
            ->willReturn($optInShareProductDataEntity);

        $this->normalizerMock
            ->expects($this->exactly(2))
            ->method('normalize')
            ->willReturnOnConsecutiveCalls(
                '2025-01-01T00:00:00+00:00',
                '2025-01-02T00:00:00+00:00'
            );

        $this->optInShareProductData->add($marketingSubscription, $data);

        $this->assertArrayHasKey('optInShareProductData', $data);
        $this->assertEquals(
            [
                'subscribedAt' => '2025-01-01T00:00:00+00:00',
                'unsubscribedAt' => '2025-01-02T00:00:00+00:00',
                'subscribed' => true,
            ],
            $data['optInShareProductData']
        );
    }

    public function testAddFiltersOutNullValues(): void
    {
        $data = [];
        $optInShareProductDataEntity = new OptInShareProductDataEntity();
        $optInShareProductDataEntity->subscribe();

        $marketingSubscription = $this->createMock(MarketingSubscription::class);
        $marketingSubscription->method('getOptInShareProductData')
            ->willReturn($optInShareProductDataEntity);

        $this->normalizerMock
            ->expects($this->exactly(2))
            ->method('normalize')
            ->willReturnOnConsecutiveCalls(
                '2025-01-01T00:00:00+00:00',
                null
            );

        $this->optInShareProductData->add($marketingSubscription, $data);

        $this->assertArrayHasKey('optInShareProductData', $data);
        $this->assertEquals([
            'subscribed' => true,
            'subscribedAt' => '2025-01-01T00:00:00+00:00',
        ], $data['optInShareProductData']);
    }

    public function testAddWithCustomerWithoutMarketingSubscription(): void
    {
        $data = [];
        $customer = $this->createMock(Customer::class);
        $customer->method('getMarketingSubscription')->willReturn(null);

        $this->optInShareProductData->add($customer, $data);

        $this->assertEmpty($data);
    }
}
