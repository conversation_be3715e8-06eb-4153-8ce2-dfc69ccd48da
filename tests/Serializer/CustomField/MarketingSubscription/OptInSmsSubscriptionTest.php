<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\MarketingSubscription;

use App\Serializer\CustomField\MarketingSubscription\EmbeddedInCustomerAsWebhook\OptInSmsSubscription;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\MarketingSubscriptionFactory;
use DateTimeInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

final class OptInSmsSubscriptionTest extends AbstractCustomFieldTest
{
    private OptInSmsSubscription $optInSmsSubscription;

    protected function setUp(): void
    {
        parent::setUp();

        $this->normalizer = $this->createMock(NormalizerInterface::class);
        $this->normalizer->method('normalize')->willReturnCallback(
            function ($data): ?string {
                if ($data instanceof DateTimeInterface) {
                    return $data->format('Y-m-d H:i');
                }

                return null;
            }
        );

        $this->optInSmsSubscription = new OptInSmsSubscription(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsMarketingSubscriptionInterface(): void
    {
        $marketingSubscription = MarketingSubscriptionFactory::createPrefilled();

        $this->assertTrue($this->optInSmsSubscription->supports($marketingSubscription, $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $marketingSubscription = MarketingSubscriptionFactory::createPrefilled();

        $this->assertFalse($this->optInSmsSubscription->supports($marketingSubscription));
    }

    public function testItAddsOptInSmsSubscription(): void
    {
        $data = [];

        $marketingSubscription = MarketingSubscriptionFactory::createPrefilled();

        $this->optInSmsSubscription->add(
            $marketingSubscription,
            $data,
            'json',
            $this->getContext()
        );

        $this->assertSame([
            'optInSmsSubscription' => [
                'state' => 'single',
                'subscriptionTypes' => [
                    'service' => true,
                ],
            ],
        ], $data);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'optInSmsSubscription' => [
                    'state',
                    'subscriptionTypes' => [
                        'service',
                    ],
                ],
            ],
        ];
    }
}
