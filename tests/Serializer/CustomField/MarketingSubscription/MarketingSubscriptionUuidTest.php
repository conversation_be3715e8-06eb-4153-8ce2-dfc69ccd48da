<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\MarketingSubscription;

use App\Serializer\CustomField\MarketingSubscription\EmbeddedInCustomerAsWebhook\MarketingSubscriptionUuid;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\MarketingSubscriptionFactory;

final class MarketingSubscriptionUuidTest extends AbstractCustomFieldTest
{
    private MarketingSubscriptionUuid $marketingSubscriptionUuid;

    protected function setUp(): void
    {
        parent::setUp();

        $this->marketingSubscriptionUuid = new MarketingSubscriptionUuid(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsMarketingSubscriptionInterface(): void
    {
        $marketingSubscription = MarketingSubscriptionFactory::createPrefilled();

        $this->assertTrue($this->marketingSubscriptionUuid->supports($marketingSubscription, $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $marketingSubscription = MarketingSubscriptionFactory::createPrefilled();

        $this->assertFalse($this->marketingSubscriptionUuid->supports($marketingSubscription));
    }

    public function testItAddsMarketingSubscriptionUuid(): void
    {
        $data = [];

        $this->marketingSubscriptionUuid->add(
            MarketingSubscriptionFactory::createPrefilled(),
            $data,
            'json',
            $this->getContext()
        );

        $this->assertArrayHasKey('marketingSubscriptionUuid', $data);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'marketingSubscriptionUuid',
            ],
        ];
    }
}
