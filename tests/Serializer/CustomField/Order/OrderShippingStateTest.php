<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Order;

use App\Entity\Order\Order;
use App\Serializer\CustomField\Order\OrderShippingState;
use App\StateMachine\OrderFraudCheckStates;
use App\StateMachine\OrderPrescriptionStates;
use App\StateMachine\OrderShippingStates;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\OrderFactory;
use Sylius\Component\Core\OrderShippingStates as SyliusOrderShippingStates;
use Sylius\Component\Order\Model\OrderInterface as SyliusOrderInterface;

final class OrderShippingStateTest extends AbstractCustomFieldTest
{
    private OrderShippingState $orderShippingState;

    protected function setUp(): void
    {
        parent::setUp();

        $this->orderShippingState = new OrderShippingState(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    /**
     * @return iterable<string, array{string, Order}>
     */
    public function orderProvider(): iterable
    {
        $order = OrderFactory::createPrefilled([
            'fraudCheckState' => OrderFraudCheckStates::STATE_READY_FOR_FRAUD_CHECK,
            'shippingState' => OrderShippingStates::STATE_AWAITING_FRAUD_CHECK,
            'prescriptionState' => OrderPrescriptionStates::STATE_AWAITING_FRAUD_CHECK,
        ]);
        yield 'shippingState awaiting_fraud_check shows awaiting_prescription when prescriptionState is awaiting_fraud_check' => [
            OrderShippingStates::STATE_AWAITING_PRESCRIPTION,
            $order,
        ];

        $order = OrderFactory::createPrefilled([
            'fraudCheckState' => OrderFraudCheckStates::STATE_READY_FOR_FRAUD_CHECK,
            'shippingState' => OrderShippingStates::STATE_AWAITING_FRAUD_CHECK,
            'prescriptionState' => OrderPrescriptionStates::STATE_SKIPPED,
        ]);
        yield 'shippingState awaiting_fraud_check shows ready when prescriptionState is not awaiting_fraud_check' => [
            SyliusOrderShippingStates::STATE_READY,
            $order,
        ];

        $order = OrderFactory::createPrefilled([
            'state' => SyliusOrderInterface::STATE_NEW,
            'shippingState' => SyliusOrderShippingStates::STATE_CANCELLED,
        ]);
        yield 'shippingState cancelled shows pending when shippingState is cancelled and order state is not cancelled' => [
            OrderShippingStates::STATE_PENDING,
            $order,
        ];

        $order = OrderFactory::createPrefilled([
            'state' => SyliusOrderInterface::STATE_CANCELLED,
            'shippingState' => SyliusOrderShippingStates::STATE_CANCELLED,
        ]);
        yield 'shippingState cancelled shows cancelled when shippingState is cancelled and order state is cancelled' => [
            SyliusOrderShippingStates::STATE_CANCELLED,
            $order,
        ];

        $order = OrderFactory::createPrefilled(['shippingState' => OrderShippingStates::STATE_PENDING]);
        yield 'shippingState pending shows pending' => [
            OrderShippingStates::STATE_PENDING,
            $order,
        ];
    }

    /**
     * @dataProvider orderProvider
     */
    public function testAdd(
        string $expectedState,
        Order $order,
    ): void {
        $data = [];
        $context = ['attributes' => ['shippingState']];

        // Testing add method
        $this->orderShippingState->add($order, $data, null, $context);
        $this->assertArrayHasKey('shippingState', $data);
        $this->assertEquals($expectedState, $data['shippingState']);
    }

    public function testSupports(): void
    {
        $order = OrderFactory::createPrefilled(['fraudCheckState' => OrderFraudCheckStates::STATE_READY_FOR_FRAUD_CHECK, 'shippingState' => OrderShippingStates::STATE_AWAITING_FRAUD_CHECK, 'prescriptionState' => OrderPrescriptionStates::STATE_AWAITING_FRAUD_CHECK]);
        $this->assertTrue($this->orderShippingState->supports($order, ['attributes' => ['shippingState']]));
        $this->assertFalse($this->orderShippingState->supports($order, []));
    }
}
