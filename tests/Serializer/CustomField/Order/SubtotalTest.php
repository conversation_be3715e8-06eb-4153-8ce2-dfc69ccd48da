<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Order;

use App\Entity\Order\Order;
use App\Serializer\CustomField\Order\Subtotal;
use App\Tests\Mocks\Entity\TestOrder;
use App\Tests\Mocks\Entity\TestOrderItem;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;

final class SubtotalTest extends AbstractCustomFieldTest
{
    private Subtotal $subtotal;

    protected function setUp(): void
    {
        parent::setUp();

        $this->subtotal = new Subtotal(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsOrderInterface(): void
    {
        $this->assertTrue($this->subtotal->supports(new Order(), $this->getContext()));
    }

    public function testItDoesNotSupportInvalidContext(): void
    {
        $this->assertFalse($this->subtotal->supports(new Order()));
    }

    public function testItAddsSubtotal(): void
    {
        $order = new TestOrder();

        $order->setItemsTotal(7000); // Already contains discount
        $order->setTotal(7000); // Already contains discount

        // 2 order items with a unit price of 4000 should result in subtotal of 8000
        for ($i = 0; $i < 2; ++$i) {
            $item = new TestOrderItem();
            $item->setUnitPrice(4000);
            $item->setQuantity(1);
            $order->addItem($item);
        }

        $data = [];
        $this->subtotal->add($order, $data, 'json', $this->getContext());

        $this->assertSame(8000, $data['subtotal']);
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'subtotal',
            ],
        ];
    }
}
