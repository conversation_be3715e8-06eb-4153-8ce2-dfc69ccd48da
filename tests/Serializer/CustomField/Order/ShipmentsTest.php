<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Order;

use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Order\OrderItemUnit;
use App\Serializer\CustomField\Order\Shipments;
use App\Tests\Mocks\Entity\TestShipment;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use stdClass;

final class ShipmentsTest extends AbstractCustomFieldTest
{
    private Shipments $customField;

    protected function setUp(): void
    {
        parent::setUp();

        $this->customField = new Shipments(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    public function testItSupportsOrderWithValidContext(): void
    {
        $this->assertTrue($this->customField->supports(new Order(), $this->getContext()));
    }

    public function testItDoesNotSupportNonOrderWithValidContext(): void
    {
        $this->assertFalse($this->customField->supports(new stdClass(), $this->getContext()));
    }

    /**
     * @dataProvider provideInvalidContext
     */
    public function testItDoesNotSupportOrderWithoutValidContext(array $context): void
    {
        $this->assertFalse($this->customField->supports(new Order(), $context));
    }

    public function testItRemovesShipmentsWithoutUnits(): void
    {
        // Arrange
        $order = new Order();

        $unit = new OrderItemUnit(new OrderItem());

        $shipment1 = new TestShipment();
        $shipment1->setId(1);
        $order->addShipment($shipment1);

        $shipment2 = new TestShipment();
        $shipment2->setId(2);
        $shipment2->addUnit($unit);
        $order->addShipment($shipment2);

        $data = [
            'shipments' => [
                [
                    'id' => 1,
                    'state' => 'cart',
                    // etc
                ],
                [
                    'id' => 2,
                ],
            ],
        ];

        // Act
        $this->customField->add($order, $data, 'json', $this->getContext());

        // Assert
        self::assertCount(2, $order->getShipments());
        self::assertCount(0, $shipment1->getUnits());
        self::assertCount(1, $shipment2->getUnits());
        self::assertCount(1, $data['shipments']);
        self::assertSame(2, reset($data['shipments'])['id']);
    }

    public function provideInvalidContext(): iterable
    {
        yield [[]];

        yield [['attributes' => []]];

        yield [['attributes' => ['shipments']]];

        yield [['attributes' => ['shipments' => true]]];
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'shipments' => [],
            ],
        ];
    }
}
