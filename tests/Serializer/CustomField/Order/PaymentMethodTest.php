<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Order;

use App\Entity\Order\Order;
use App\Entity\Payment\PaymentMethodInterface;
use App\Serializer\CustomField\Order\PaymentMethod;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\PaymentFactory;
use Sylius\Component\Payment\Model\PaymentInterface;

final class PaymentMethodTest extends AbstractCustomFieldTest
{
    private const array SUCCESS_STATES = [
        PaymentInterface::STATE_CART,
        PaymentInterface::STATE_NEW,
        PaymentInterface::STATE_COMPLETED,
        PaymentInterface::STATE_AUTHORIZED,
        PaymentInterface::STATE_PROCESSING,
        PaymentInterface::STATE_FAILED,
    ];

    private const array BACKUP_STATES = [
        PaymentInterface::STATE_CANCELLED,
        PaymentInterface::STATE_UNKNOWN,
    ];

    private PaymentMethod $paymentMethod;

    protected function setUp(): void
    {
        parent::setUp();

        $this->normalizer->method('normalize')
            ->willReturnCallback(
                function (object $object) {
                    self::assertInstanceOf(PaymentMethodInterface::class, $object);

                    return [
                        'name' => $object->getName(),
                        'code' => $object->getCode(),
                    ];
                }
            );

        $this->paymentMethod = new PaymentMethod(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    /**
     * @dataProvider paymentsProvider
     */
    public function testItReturnsTheCorrectMethodName(Order $order, ?string $expectedMethodName, ?string $expectedMethodCode): void
    {
        $data = [];
        $this->paymentMethod->add($order, $data, null, $this->getContext());

        self::assertSame($expectedMethodName, $data['paymentMethod']['name'] ?? null);
        self::assertSame($expectedMethodCode, $data['paymentMethod']['code'] ?? null);
    }

    public function paymentsProvider(): iterable
    {
        foreach (self::SUCCESS_STATES as $state) {
            $payment = PaymentFactory::create(['state' => $state, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'bank transfer', 'code' => 'bank_transfer_dokteronline'])]);
            $order = OrderFactory::createPrefilled(['payments' => [$payment]]);

            $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_CANCELLED, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'iDeal', 'code' => 'ideal_dokteronline'])]);
            $order->addPayment($payment);

            $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_UNKNOWN, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'iDeal', 'code' => 'ideal_dokteronline'])]);
            $order->addPayment($payment);

            $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_REFUNDED, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'iDeal', 'code' => 'ideal_dokteronline'])]);
            $order->addPayment($payment);

            yield sprintf('Fetch most recent success payment method with the state \'%s\'', $state) => ['order' => $order, 'expectedMethodName' => 'bank transfer', 'expectedMethodCode' => 'bank_transfer_dokteronline'];
        }

        foreach (self::BACKUP_STATES as $state) {
            $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_CANCELLED, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'iDeal', 'code' => 'ideal_dokteronline'])]);
            $order = OrderFactory::createPrefilled(['payments' => [$payment]]);

            $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_UNKNOWN, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'iDeal', 'code' => 'ideal_dokteronline'])]);
            $order->addPayment($payment);

            $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_REFUNDED, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'credit card', 'code' => 'creditcard'])]);
            $order->addPayment($payment);

            yield sprintf('Fetch most recent backup payment method with the state \'%s\'', $state) => ['order' => $order, 'expectedMethodName' => 'iDeal', 'expectedMethodCode' => 'ideal_dokteronline'];
        }

        $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_REFUNDED, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'test', 'code' => 'test-code'])]);
        $order = OrderFactory::createPrefilled(['payments' => [$payment]]);

        yield sprintf('Get no payment method when there is no valid payment.') => ['order' => $order, 'expectedMethodName' => null, 'expectedMethodCode' => null];

        $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_COMPLETED]);
        $order = OrderFactory::createPrefilled(['payments' => [$payment]]);

        yield 'Get no payment method when there is no payment method on the payment.' => ['order' => $order, 'expectedMethodName' => null, 'expectedMethodCode' => null];
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'paymentMethod' => [
                    'name',
                    'code',
                ],
            ],
        ];
    }
}
