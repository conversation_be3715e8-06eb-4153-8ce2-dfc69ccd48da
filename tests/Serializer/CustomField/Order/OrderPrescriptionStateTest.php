<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Order;

use App\Serializer\CustomField\Order\OrderPrescriptionState;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\OrderFactory;

class OrderPrescriptionStateTest extends AbstractCustomFieldTest
{
    private OrderPrescriptionState $orderPrescriptionState;

    protected function setUp(): void
    {
        parent::setUp();

        $this->orderPrescriptionState = new OrderPrescriptionState(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    /**
     * @dataProvider providePrescriptionStates
     */
    public function testAdd(string $inputState, string $outputState): void
    {
        // Arrange
        $order = OrderFactory::createPrefilled(
            ['prescriptionState' => $inputState]
        );
        $data = ['prescriptionState' => $inputState];
        $context = ['attributes' => ['prescriptionState']];

        // Act
        $this->orderPrescriptionState->add($order, $data, null, $context);

        // Assert
        $this->assertArrayHasKey('prescriptionState', $data);
        $this->assertEquals($outputState, $data['prescriptionState']);
    }

    /**
     * @return iterable<string, array<array-key, string>>
     */
    public function providePrescriptionStates(): iterable
    {
        yield 'awaiting_fraud_check is converted to ready_for_consult' => [
            'awaiting_fraud_check',
            'ready_for_consult',
        ];

        yield 'awaiting_customer_service is converted to pending' => [
            'awaiting_customer_service',
            'pending',
        ];

        yield 'pending is not converted' => [
            'pending',
            'pending',
        ];
    }

    public function testSupports(): void
    {
        $order = OrderFactory::create();
        $this->assertTrue($this->orderPrescriptionState->supports($order, ['attributes' => ['prescriptionState']]));
        $this->assertFalse($this->orderPrescriptionState->supports($order, []));
    }
}
