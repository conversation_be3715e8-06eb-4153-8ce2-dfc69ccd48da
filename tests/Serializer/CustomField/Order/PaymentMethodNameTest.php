<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Order;

use App\Entity\Order\Order;
use App\Serializer\CustomField\Order\PaymentMethodName;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\PaymentFactory;
use Sylius\Component\Payment\Model\PaymentInterface;

/**
 * @deprecated remove this test when the PaymentMethodName custom field is removed
 */
final class PaymentMethodNameTest extends AbstractCustomFieldTest
{
    private const array SUCCESS_STATES = [
        PaymentInterface::STATE_CART,
        PaymentInterface::STATE_NEW,
        PaymentInterface::STATE_COMPLETED,
        PaymentInterface::STATE_AUTHORIZED,
        PaymentInterface::STATE_PROCESSING,
        PaymentInterface::STATE_FAILED,
    ];

    private const array BACKUP_STATES = [
        PaymentInterface::STATE_CANCELLED,
        PaymentInterface::STATE_UNKNOWN,
    ];

    private PaymentMethodName $paymentMethodName;

    protected function setUp(): void
    {
        parent::setUp();

        $this->paymentMethodName = new PaymentMethodName(
            $this->normalizer,
            $this->propertyAccessor,
        );
    }

    /**
     * @dataProvider paymentsProvider
     */
    public function testItReturnsTheCorrectMethodName(Order $order, ?string $expectedMethodName): void
    {
        $data = [];
        $this->paymentMethodName->add($order, $data, null, $this->getContext());

        self::assertSame($expectedMethodName, $data['paymentMethodName'] ?? null);
    }

    public function paymentsProvider(): iterable
    {
        foreach (self::SUCCESS_STATES as $state) {
            $payment = PaymentFactory::create(['state' => $state, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'bank_transfer_dokteronline'])]);
            $order = OrderFactory::createPrefilled(['payments' => [$payment]]);

            $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_CANCELLED, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'iDeal'])]);
            $order->addPayment($payment);

            $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_UNKNOWN, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'iDeal'])]);
            $order->addPayment($payment);

            $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_REFUNDED, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'iDeal'])]);
            $order->addPayment($payment);

            yield sprintf('Fetch most recent success payment method name with the state \'%s\'', $state) => ['order' => $order, 'expectedMethodName' => 'bank_transfer_dokteronline'];
        }

        foreach (self::BACKUP_STATES as $state) {
            $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_CANCELLED, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'iDeal'])]);
            $order = OrderFactory::createPrefilled(['payments' => [$payment]]);

            $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_UNKNOWN, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'iDeal'])]);
            $order->addPayment($payment);

            $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_REFUNDED, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'credit card'])]);
            $order->addPayment($payment);

            yield sprintf('Fetch most recent backup payment method name with the state \'%s\'', $state) => ['order' => $order, 'expectedMethodName' => 'iDeal'];
        }

        $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_REFUNDED, 'paymentMethod' => PaymentFactory::createPaymentMethod(['name' => 'test'])]);
        $order = OrderFactory::createPrefilled(['payments' => [$payment]]);

        yield sprintf('Get no payment method when there is no valid payment.') => ['order' => $order, 'expectedMethodName' => null];

        $payment = PaymentFactory::create(['state' => PaymentInterface::STATE_COMPLETED]);
        $order = OrderFactory::createPrefilled(['payments' => [$payment]]);

        yield 'Get no payment method when there is no payment method on the payment.' => ['order' => $order, 'expectedMethodName' => null];
    }

    private function getContext(): array
    {
        return [
            'attributes' => [
                'paymentMethodName',
            ],
        ];
    }
}
