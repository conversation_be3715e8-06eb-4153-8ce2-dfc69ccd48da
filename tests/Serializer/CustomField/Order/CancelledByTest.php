<?php

declare(strict_types=1);

namespace App\Tests\Serializer\CustomField\Order;

use App\Entity\Order\Cancellation;
use App\Entity\Order\Enum\CancellationBy;
use App\Entity\Order\Order;
use App\Serializer\CustomField\Order\CancelledBy;
use App\Tests\Serializer\CustomField\AbstractCustomFieldTest;

final class CancelledByTest extends AbstractCustomFieldTest
{
    private CancelledBy $cancelledBy;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cancelledBy = new CancelledBy($this->normalizer, $this->propertyAccessor);
    }

    public function testAddWithValidOrderAddsProperty(): void
    {
        // Arrange
        $data = [];
        $order = new Order();
        $order->setCancellation(new Cancellation(CancellationBy::CUSTOMER_SERVICE, 'Out of stock.'));

        // Act
        $this->cancelledBy->add($order, $data, null, $this->getContext());

        // Assert
        self::assertSame('customer-service', $data['cancelledBy'] ?? null);
    }

    public function testAddWithInvalidOrderDoesNotAddProperty(): void
    {
        // Arrange
        $data = [];
        $order = new Order();

        // Act
        $this->cancelledBy->add($order, $data, null, $this->getContext());

        // Assert
        self::assertEmpty($data);
    }

    public function testSupportsWithValidContextReturnsTrue(): void
    {
        self::assertTrue($this->cancelledBy->supports(new Order(), $this->getContext()));
    }

    /**
     * @dataProvider provideInvalidContext
     */
    public function testSupportsWithInvalidContextReturnsFalse(array $context): void
    {
        self::assertFalse($this->cancelledBy->supports(new Order(), $context));
    }

    protected function provideInvalidContext(): iterable
    {
        yield 'Empty attributes array' => [[
            'context' => 'webhook',
            'attributes' => [],
        ]];

        yield 'Invalid context name' => [[
            'context' => 'api',
            'attributes' => [
                'cancelledBy',
            ],
        ]];
    }

    private function getContext(): array
    {
        return [
            'context' => 'webhook',
            'attributes' => [
                'cancelledBy',
            ],
        ];
    }
}
