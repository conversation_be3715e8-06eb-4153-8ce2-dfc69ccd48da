<?php

declare(strict_types=1);

namespace App\Tests\Serializer\Prepr;

use App\Entity\Addressing\Country;
use App\Entity\Channel\Channel;
use App\Entity\Channel\ChannelPricing;
use App\Entity\Currency\Currency;
use App\Entity\Product\Product;
use App\Entity\Product\ProductVariant;
use App\Repository\ChannelPricingRepositoryInterface;
use App\Repository\ChannelRepositoryInterface;
use App\Serializer\Prepr\ProductList;
use ArrayIterator;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Tools\Pagination\Paginator;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use stdClass;
use Sylius\Component\Core\Model\ImageInterface;
use Sylius\Component\Core\Model\ProductTranslationInterface;
use Sylius\Component\Locale\Context\LocaleContextInterface;

class ProductListTest extends TestCase
{
    private ChannelRepositoryInterface&MockObject $channelRepository;
    private ChannelPricingRepositoryInterface&MockObject $channelPricingRepository;
    private LocaleContextInterface&MockObject $localeContext;

    protected function setUp(): void
    {
        $this->channelRepository = $this->createMock(ChannelRepositoryInterface::class);
        $this->channelPricingRepository = $this->createMock(ChannelPricingRepositoryInterface::class);
        $this->localeContext = $this->createMock(LocaleContextInterface::class);
    }

    public function testNormalize(): void
    {
        // Arrange
        $country = $this->createMock(Country::class);
        $country->method('getCode')->willReturn('DE');
        $channel = $this->createMock(Channel::class);
        $channel->method('isEnabled')->willReturn(true);
        $channel->method('getCode')->willReturn('dok_de');
        $channel->method('getFirstCountry')->willReturn($country);

        $productTranslation = $this->createMock(ProductTranslationInterface::class);
        $productTranslation->method('getName')->willReturn('Product Name');

        $image = $this->createMock(ImageInterface::class);
        $image->method('getPath')->willReturn('image/path.jpg');

        $currency = $this->createMock(Currency::class);
        $currency->method('getCode')->willReturn('EUR');

        $channelPricing = $this->createMock(ChannelPricing::class);
        $channelPricing->method('getPrice')->willReturn(1000);
        $channelPricing->method('getProductVariant')->willReturn(null);

        $product = $this->createMock(Product::class);
        $product->method('getTranslation')->willReturn($productTranslation);
        $product->method('getCode')->willReturn('product_code');
        $product->method('getType')->willReturn('simple');
        $product->method('isPrescriptionRequired')->willReturn(false);
        $product->method('getCreatedAt')->willReturn(new DateTime());
        $product->method('getUpdatedAt')->willReturn(new DateTime());
        $product->method('getImagesByType')->willReturn(new ArrayCollection([$image]));
        $product->method('getVariants')->willReturn(new ArrayCollection());

        $this->localeContext->method('getLocaleCode')->willReturn('de_de');

        $this->channelRepository->method('findBy')->willReturn([$channel]);
        $this->channelPricingRepository->method('findLowestChannelPricingByProductAndChannelCodes')
            ->willReturn([$channelPricing]);

        $paginator = $this->createMock(Paginator::class);
        $paginator->method('getIterator')->willReturn(new ArrayIterator([$product]));
        $paginator->method('count')->willReturn(1);

        $productList = new ProductList(
            $this->channelRepository,
            $this->channelPricingRepository,
            $this->localeContext,
        );

        // Act
        $result = $productList->normalize($paginator);

        // Assert
        $this->assertArrayHasKey('items', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertCount(1, $result['items']);
    }

    public function testNormalizeAndMostRecentUpdatedAtLogic(): void
    {
        // Arrange
        $country = $this->createMock(Country::class);
        $country->method('getCode')->willReturn('DE');
        $channel = $this->createMock(Channel::class);
        $channel->method('isEnabled')->willReturn(true);
        $channel->method('getCode')->willReturn('dok_de');
        $channel->method('getFirstCountry')->willReturn($country);

        $productTranslation = $this->createMock(ProductTranslationInterface::class);
        $productTranslation->method('getName')->willReturn('Product Name');

        $image = $this->createMock(ImageInterface::class);
        $image->method('getPath')->willReturn('image/path.jpg');

        $product = $this->createMock(Product::class);
        $product->method('getTranslation')->willReturn($productTranslation);
        $product->method('getCode')->willReturn('product_code');
        $product->method('getType')->willReturn('simple');
        $product->method('isPrescriptionRequired')->willReturn(false);
        $product->method('getCreatedAt')->willReturn(new DateTime('2023-04-01'));
        $product->method('getUpdatedAt')->willReturn(new DateTime('2023-05-01'));
        $product->method('getImagesByType')->willReturn(new ArrayCollection([$image]));
        $product->method('getVariants')->willReturn(new ArrayCollection());

        $productVariant1 = $this->createMock(ProductVariant::class);
        $productVariant1->method('getUpdatedAt')->willReturn(new DateTime('2023-04-15'));
        $channelPricing1 = $this->createMock(ChannelPricing::class);
        $channelPricing1->method('getProductVariant')->willReturn($productVariant1);

        $productVariant2 = $this->createMock(ProductVariant::class);
        $productVariant2->method('getUpdatedAt')->willReturn(new DateTime('2023-06-01'));
        $channelPricing2 = $this->createMock(ChannelPricing::class);
        $channelPricing2->method('getProductVariant')->willReturn($productVariant2); // Latest update

        // Mock locale context
        $this->localeContext->method('getLocaleCode')->willReturn('de_de');

        // Mock repository responses
        $this->channelRepository->method('findBy')->willReturn([$channel]);
        $this->channelPricingRepository->method('findLowestChannelPricingByProductAndChannelCodes')
            ->willReturn([$channelPricing1, $channelPricing2]);

        // Mock paginator
        $paginator = $this->createMock(Paginator::class);
        $paginator->method('getIterator')->willReturn(new ArrayIterator([$product]));
        $paginator->method('count')->willReturn(1);

        $productList = new ProductList(
            $this->channelRepository,
            $this->channelPricingRepository,
            $this->localeContext,
        );

        // Act
        $result = $productList->normalize($paginator);

        // Assert
        $this->assertArrayHasKey('items', $result);
        $this->assertCount(1, $result['items']);

        $item = $result['items'][0];
        $expectedChangedOn = '2023-06-01T00:00:00+00:00'; // Expected most recent update
        $this->assertEquals($expectedChangedOn, $item['changed_on']);
    }

    public function testSupportsNormalizationTrue(): void
    {
        // Arrange
        $product = $this->createMock(Product::class);

        $paginator = $this->createMock(Paginator::class);
        $paginator->method('getIterator')->willReturn(new ArrayIterator([$product]));

        $productList = new ProductList(
            $this->channelRepository,
            $this->channelPricingRepository,
            $this->localeContext,
        );

        // Act
        $result = $productList->supportsNormalization(
            $paginator,
            'json',
            [
                'attributes' => ['items' => true],
                'responseContentType' => 'application/prepr+json',
            ]
        );

        // Assert
        $this->assertTrue($result);
    }

    public function testSupportsNormalizationFalseForInvalidObject(): void
    {
        // Arrange
        $productList = new ProductList(
            $this->channelRepository,
            $this->channelPricingRepository,
            $this->localeContext,
        );

        // Act
        $result = $productList->supportsNormalization(
            /** @phpstan-ignore-next-line */
            new stdClass(),
            'json',
            ['responseContentType' => 'application/prepr+json']
        );

        // Assert
        $this->assertFalse($result);
    }
}
