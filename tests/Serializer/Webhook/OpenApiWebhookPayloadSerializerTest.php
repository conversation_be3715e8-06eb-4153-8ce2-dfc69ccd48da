<?php

declare(strict_types=1);

namespace App\Tests\Serializer\Webhook;

use App\CanopyDeploy\Payload\CustomerAsPasswordResetPayload;
use App\CanopyDeploy\Payload\CustomerAsWebhookPayload;
use App\CanopyDeploy\Payload\OrderAsWebhookPayload;
use App\CanopyDeploy\Payload\OrderItemAsRemoveWebhookPayload;
use App\Entity\Order\Cancellation;
use App\Entity\Order\Enum\CancellationBy;
use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Order\PreferredOrderItem;
use App\Serializer\ObjectNormalizer;
use App\Serializer\SerializerContext;
use App\Serializer\Webhook\OpenApiWebhookPayloadSerializerInterface;
use App\Serializer\Webhook\WebhookPayloadInterface;
use App\Tests\Util\ChannelContext\TestChannelContext;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\CustomerFactory;
use App\Tests\Util\Factory\MarketingSubscriptionFactory;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\OrderItemFactory;
use App\Tests\Util\Factory\ProductVariantFactory;
use App\Tests\Util\Factory\TaxonFactory;
use App\Tests\Util\JsonSchema\Constraints\Factory;
use JsonSchema\Validator;
use Nijens\OpenapiBundle\Json\JsonPointer;
use Nijens\OpenapiBundle\Json\SchemaLoaderInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;

final class OpenApiWebhookPayloadSerializerTest extends KernelTestCase
{
    private OpenApiWebhookPayloadSerializerInterface $serializer;
    private SchemaLoaderInterface $schemaLoader;
    private Validator $validator;

    protected function setUp(): void
    {
        /** @var OpenApiWebhookPayloadSerializerInterface $serializer */
        $serializer = self::getContainer()->get(OpenApiWebhookPayloadSerializerInterface::class);
        $this->serializer = $serializer;

        /** @var SchemaLoaderInterface $schemaLoader */
        $schemaLoader = self::getContainer()->get('nijens_openapi.json.schema_loader');
        $this->schemaLoader = $schemaLoader;

        /** @var TestChannelContext $channelContext */
        $channelContext = self::getContainer()->get(TestChannelContext::class);
        $channelContext->setChannel(ChannelFactory::createPrefilled());

        // Setting request so \Sylius\Bundle\ApiBundle\Serializer\ProductImageNormalizer has a request in requestStack
        /** @var RequestStack $requestStack */
        $requestStack = self::getContainer()->get(RequestStack::class);
        $requestStack->push(new Request());

        $this->validator = new Validator(new Factory());

        parent::setUp();
    }

    /**
     * @dataProvider webhookPayloadProvider
     */
    public function testWebhooks(
        WebhookPayloadInterface $webhookPayload,
        string $webhookName,
        array $expectedErrors,
    ): void {
        // Arrange: Serialize webhook payload
        $serializedData = $this->serializer->serialize(
            $webhookPayload,
            'json',
            [
                // Make sure all properties are in json even if they are null for testing.
                AbstractObjectNormalizer::SKIP_NULL_VALUES => false,
                ObjectNormalizer::CONTEXT => SerializerContext::WEBHOOK->value,
            ]
        );

        // Arrange: Load schema
        /** @var string $projectDir */
        $projectDir = self::getContainer()->getParameter('kernel.project_dir');
        /** @var string $openapiSpecFilePath */
        $openapiSpecFilePath = realpath($projectDir.'/config/openapi.yaml');
        $schema = $this->schemaLoader->load($openapiSpecFilePath);
        $jsonPointer = new JsonPointer($schema);

        // Arrange: Get schema of webhook content
        $pointer = sprintf('/webhooks/%s/post/requestBody/content/application~1json/schema', $webhookName);
        $jsonSchema = $jsonPointer->get($pointer);

        // Act: Validate
        $decodedSerializedData = json_decode($serializedData, false, 512, JSON_THROW_ON_ERROR);
        $this->validator->validate($decodedSerializedData, $jsonSchema);

        // Assert: Expected errors
        $this->assertEquals(
            $expectedErrors,
            $this->validator->getErrors(),
        );
    }

    public function webhookPayloadProvider(): iterable
    {
        yield 'Create or update customer as webhook payload for marketing subscription' => [
            new CustomerAsWebhookPayload('MarketingSubscriptionWasCreated', MarketingSubscriptionFactory::createPrefilled()),
            'CreateOrUpdateCustomerWithMarketingSubscription',
            [],
        ];

        yield 'Create or update customer as webhook payload with invalid event name' => [
            new CustomerAsWebhookPayload('InvalidEventName', MarketingSubscriptionFactory::createPrefilled()),
            'CreateOrUpdateCustomerWithMarketingSubscription',
            [
                [
                    'property' => 'eventName',
                    'pointer' => '/eventName',
                    'message' => 'Does not have a value in the enumeration ["MarketingSubscriptionWasCreated","MarketingSubscriptionWasUpdated","EmailOptInWasRequested","CustomerWasCreated","CustomerWasUpdated","PasswordWasChanged"]',
                    'constraint' => 'enum',
                    'context' => 1,
                    'enum' => [
                        'MarketingSubscriptionWasCreated',
                        'MarketingSubscriptionWasUpdated',
                        'EmailOptInWasRequested',
                        'CustomerWasCreated',
                        'CustomerWasUpdated',
                        'PasswordWasChanged',
                    ],
                ],
            ],
        ];

        yield 'Create or update customer as webhook payload for customer' => [
            new CustomerAsWebhookPayload('MarketingSubscriptionWasUpdated', CustomerFactory::createPrefilled()),
            'CreateOrUpdateCustomerWithMarketingSubscription',
            [],
        ];

        yield 'Customer as password reset' => [
            new CustomerAsPasswordResetPayload(
                'PasswordResetWasRequested',
                CustomerFactory::createPrefilled(),
                ChannelFactory::createPrefilled()
            ),
            'PasswordReset',
            [],
        ];

        yield 'Create or update order as webhook payload' => [
            new OrderAsWebhookPayload('OrderWasCreated', $this->createOrder()),
            'CreateOrUpdateCartOrOrder',
            $this->getExpectedErrorsForOrderAsWebhookPayload(),
        ];

        yield 'Order item as remove' => [
            new OrderItemAsRemoveWebhookPayload('ItemWasRemovedFromOrder', OrderItemFactory::createPrefilled()),
            'RemoveItemFromCartOrOrder',
            [],
        ];
    }

    private function getExpectedErrorsForOrderAsWebhookPayload(): array
    {
        // @todo remove after fixing https://mv-jira-1.atlassian.net/browse/DV-5352
        $expectedErrors = [];
        for ($i = 0; $i <= 1; ++$i) {
            $expectedErrors[] = [
                'property' => sprintf('order.items[%d].previouslyPrescribed', $i),
                'pointer' => sprintf('/order/items/%d/previouslyPrescribed', $i),
                'message' => 'The property previouslyPrescribed is required',
                'constraint' => 'required',
                'context' => 1,
            ];
            $expectedErrors[] = [
                'property' => sprintf('order.items[%d].previouslyPrescribed', $i),
                'pointer' => sprintf('/order/items/%d/previouslyPrescribed', $i),
                'message' => 'Does not have a value in the enumeration ["Yes","No"]',
                'constraint' => 'enum',
                'context' => 1,
                'enum' => ['Yes', 'No'],
            ];
            $expectedErrors[] = [
                'property' => sprintf('order.items[%d].previouslyPrescribedIntervalDays', $i),
                'pointer' => sprintf('/order/items/%d/previouslyPrescribedIntervalDays', $i),
                'message' => 'The property previouslyPrescribedIntervalDays is required',
                'constraint' => 'required',
                'context' => 1,
            ];
            $expectedErrors[] = [
                'property' => sprintf('order.items[%d]', $i),
                'pointer' => sprintf('/order/items/%d', $i),
                'message' => 'Failed to match all schemas',
                'constraint' => 'allOf',
                'context' => 1,
            ];
        }

        // @todo remove after fixing https://mv-jira-1.atlassian.net/browse/DV-5351
        $expectedErrors[] = [
            'property' => 'order.orderPromotions',
            'pointer' => '/order/orderPromotions',
            'message' => 'The property orderPromotions is required',
            'constraint' => 'required',
            'context' => 1,
        ];
        $expectedErrors[] = [
            'property' => 'order',
            'pointer' => '/order',
            'message' => 'Failed to match all schemas',
            'constraint' => 'allOf',
            'context' => 1,
        ];

        return $expectedErrors;
    }

    private function createOrder(): Order
    {
        $order = OrderFactory::createPrefilled();

        $order->setCancellation(new Cancellation(CancellationBy::DOCTOR, 'Medication is declined.'));

        /** @var OrderItem $item */
        foreach ($order->getItems() as $item) {
            // Make sure all items are linked to a supplier
            $item->setVariant(ProductVariantFactory::createPrefilled());

            $product = $item->getVariant()?->getProduct();
            $product->setMainTaxon(TaxonFactory::createPrefilled());

            $parentItem = OrderItemFactory::createPrefilled();
            $parentItem->setVariant(ProductVariantFactory::createPrefilled());
            $item->setParentOrderItem($parentItem);

            $preferredItem = new PreferredOrderItem($parentItem, ProductVariantFactory::createPrefilled());
            $preferredItem->setUsageAdvice('test-usage-advice');
            $parentItem->addPreferredItem($preferredItem);
        }

        return $order;
    }
}
