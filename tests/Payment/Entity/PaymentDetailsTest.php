<?php

declare(strict_types=1);

namespace App\Tests\Payment\Entity;

use App\Entity\Payment\Payment;
use App\Payment\Entity\PaymentDetails;
use PHPUnit\Framework\TestCase;

final class PaymentDetailsTest extends TestCase
{
    public function testAddPaymentUrl(): void
    {
        // Arrange
        $payment = new Payment();

        // Act
        PaymentDetails::addPaymentUrl($payment, 'http://mollie.localhost/payment/1234567890');

        // Assert
        self::assertSame('http://mollie.localhost/payment/1234567890', PaymentDetails::getPaymentUrl($payment));
    }

    public function testGetPaymentUrl(): void
    {
        // Arrange
        $payment = new Payment();
        PaymentDetails::addPaymentUrl($payment, 'http://mollie.localhost/payment/1234567890');

        // Act
        $paymentUrl = PaymentDetails::getPaymentUrl($payment);

        // Assert
        self::assertSame('http://mollie.localhost/payment/1234567890', $paymentUrl);
    }
}
