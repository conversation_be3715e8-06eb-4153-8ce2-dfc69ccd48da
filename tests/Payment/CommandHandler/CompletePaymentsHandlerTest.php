<?php

declare(strict_types=1);

namespace App\Tests\Payment\CommandHandler;

use App\Auditing\AuditLoggerInterface;
use App\Auditing\OrderMessage;
use App\Entity\Order\Order;
use App\Entity\Payment\GatewayConfig;
use App\Entity\Payment\Payment;
use App\Entity\Payment\PaymentMethod;
use App\Mollie\MolliePaymentDetails;
use App\Payment\Command\CompletePayments;
use App\Payment\CommandHandler\CompletePaymentsHandler;
use App\Tests\Mocks\Entity\TestOrder;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\PaymentFactory;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Payum\Core\Exception\RequestNotSupportedException;
use Payum\Core\GatewayInterface;
use Payum\Core\Model\Token;
use Payum\Core\Payum;
use Payum\Core\Security\GenericTokenFactoryInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use RuntimeException;
use SM\Factory\FactoryInterface as StateMachineFactoryInterface;
use SM\StateMachine\StateMachineInterface;
use Sylius\Component\Payment\Model\PaymentInterface as SyliusPaymentInterface;

final class CompletePaymentsHandlerTest extends TestCase
{
    private StateMachineFactoryInterface&MockObject $stateMachineFactoryMock;
    private Payum&MockObject $payumMock;
    private GatewayInterface&MockObject $gatewayMock;
    private CompletePaymentsHandler $completePaymentsHandler;
    /** @var AuditLoggerInterface<Order>&MockObject */
    private AuditLoggerInterface&MockObject $auditLogger;
    private EntityManagerInterface&MockObject $entityManager;
    private LoggerInterface&MockObject $logger;

    protected function setUp(): void
    {
        $this->stateMachineFactoryMock = $this->createMock(StateMachineFactoryInterface::class);
        $this->payumMock = $this->createMock(Payum::class);

        $this->gatewayMock = $this->createMock(GatewayInterface::class);
        $this->payumMock
            ->method('getGateway')
            ->willReturn($this->gatewayMock);

        $payumToken = new Token();
        $payumToken->setTargetUrl('https://dokteronline.commerce.ehvg.dev/payment/notify/test_payum_token');

        $tokenFactoryMock = $this->createMock(GenericTokenFactoryInterface::class);
        $this->payumMock->method('getTokenFactory')
            ->willReturn($tokenFactoryMock);

        $tokenFactoryMock->method('createToken')
            ->willReturn($payumToken);

        $this->auditLogger = $this->createMock(AuditLoggerInterface::class);
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->completePaymentsHandler = new CompletePaymentsHandler(
            $this->auditLogger,
            $this->entityManager,
            $this->logger,
            $this->payumMock,
            $this->stateMachineFactoryMock,
        );
    }

    public function testInvalidPaymentServiceUuidOnlyCompletesCheckoutPayment(): void
    {
        // Arrange
        $payment = new Payment();
        $payment->setState(SyliusPaymentInterface::STATE_AUTHORIZED);
        MolliePaymentDetails::addPaymentId($payment, 'test-123');

        $gatewayConfig = new GatewayConfig();
        $gatewayConfig->setGatewayName('checkout-service-payment-gateway');

        $paymentMethod = new PaymentMethod();
        $paymentMethod->setGatewayConfig($gatewayConfig);
        $payment->setMethod($paymentMethod);

        $order = new TestOrder();
        $order->setId(1337);
        $order->addPayment($payment);

        $orderRepository = $this->createMock(EntityRepository::class);
        $this->entityManager->method('getRepository')
            ->with(Order::class)
            ->willReturn($orderRepository);
        $orderRepository->method('find')
            ->with($order->getId())
            ->willReturn($order);

        $stateMachineMock = $this->createMock(StateMachineInterface::class);
        $stateMachineMock->method('can')
            ->willReturn(true);

        $this->stateMachineFactoryMock->method('get')
            ->willReturn($stateMachineMock);

        // Assert
        $this->payumMock->expects(self::once())
            ->method('getGateway');

        $stateMachineMock->expects(self::once())
            ->method('apply')
            ->with('complete');

        $this->auditLogger
            ->expects($this->once())
            ->method('log')
            ->with(
                $order,
                OrderMessage::SUCCESSFUL_CAPTURE_PAYMENT,
                ['paymentId' => null]
            );

        // Act
        ($this->completePaymentsHandler)(new CompletePayments($order->getId()));
    }

    /**
     * @dataProvider completePaymentsObjectProvider
     */
    public function testCanCompletePaymentSuccessfully(
        CompletePayments $completePayments,
        callable $expectedAuditLoggerCallback,
    ): void {
        // arrange
        $order = $this->createOrder();

        $orderRepository = $this->createMock(EntityRepository::class);
        $this->entityManager->method('getRepository')
            ->with(Order::class)
            ->willReturn($orderRepository);
        $orderRepository->method('find')
            ->with($order->getId())
            ->willReturn($order);

        // assert
        $this->gatewayMock->expects(self::once())
            ->method('execute');

        $expectedAuditLoggerCallback($this->auditLogger, $order);

        // act
        ($this->completePaymentsHandler)($completePayments);
    }

    /**
     * @return iterable<string, array{
     *     0: CompletePayments,
     *     1: callable(AuditLoggerInterface<Order>&MockObject $auditLoggerMock, Order $order): void
     * }>
     */
    public function completePaymentsObjectProvider(): iterable
    {
        yield 'complete payments without delay logs message without delay' => [
            new CompletePayments(1337, null),
            function (AuditLoggerInterface&MockObject $auditLoggerMock, Order $order) {
                $auditLoggerMock
                    ->expects($this->once())
                    ->method('log')
                    ->with(
                        $order,
                        OrderMessage::SUCCESSFUL_CAPTURE_PAYMENT,
                        ['paymentId' => null]
                    );
            },
        ];

        yield 'complete payments with delay logs message with delay' => [
            new CompletePayments(1337, '6 days'),
            function (AuditLoggerInterface&MockObject $auditLoggerMock, Order $order) {
                $auditLoggerMock
                    ->expects($this->once())
                    ->method('log')
                    ->with(
                        $order,
                        OrderMessage::SUCCESSFUL_CAPTURE_PAYMENT_DELAYED,
                        [
                            'paymentId' => null,
                            'delayPeriod' => '6 days',
                        ],
                    );
            },
        ];
    }

    public function testWontCompletePaymentsWhenNoReferenceIsSet(): void
    {
        // arrange
        $order = OrderFactory::createPrefilled(
            [
                'payments' => PaymentFactory::create([
                    'amount' => 100,
                    'state' => 'completed',
                    'createdAt' => new DateTimeImmutable('2022-01-01 13:00:00'),
                    'updatedAt' => new DateTimeImmutable('2022-01-01 14:00:00'),
                    'currencyCode' => 'EUR',
                    'paymentMethod' => PaymentFactory::createPaymentMethod([
                        'name' => 'iDeal',
                        'code' => 'ideal_dokteronline',
                        'createdAt' => new DateTimeImmutable('2022-01-01 13:00:00'),
                        'updatedAt' => new DateTimeImmutable('2022-01-01 14:00:00'),
                    ]),
                ]),
            ]
        );

        $orderRepository = $this->createMock(EntityRepository::class);
        $this->entityManager->method('getRepository')
            ->with(Order::class)
            ->willReturn($orderRepository);
        $orderRepository->method('find')
            ->with($order->getId())
            ->willReturn($order);

        // assert
        $this->gatewayMock->expects(self::never())
            ->method('execute');

        // act
        ($this->completePaymentsHandler)(new CompletePayments($order->getId()));
    }

    public function testItHandlesUnsupportedCaptureActions(): void
    {
        // arrange
        $order = $this->createOrder();

        $orderRepository = $this->createMock(EntityRepository::class);
        $this->entityManager->method('getRepository')
            ->with(Order::class)
            ->willReturn($orderRepository);
        $orderRepository->method('find')
            ->with($order->getId())
            ->willReturn($order);

        // assert
        $this->gatewayMock->expects(self::once())
            ->method('execute')
            ->willThrowException(new RequestNotSupportedException());

        $this->auditLogger->expects(self::never())
            ->method('log');

        $this->logger->expects(self::never())
            ->method('critical');

        // act
        ($this->completePaymentsHandler)(new CompletePayments($order->getId()));
    }

    public function testItLogsUnexpectedException(): void
    {
        // arrange
        $order = $this->createOrder();

        $orderRepository = $this->createMock(EntityRepository::class);
        $this->entityManager->method('getRepository')
            ->with(Order::class)
            ->willReturn($orderRepository);
        $orderRepository->method('find')
            ->with($order->getId())
            ->willReturn($order);

        // assert
        $this->gatewayMock->expects(self::once())
            ->method('execute')
            ->willThrowException(new RuntimeException());

        $this->auditLogger
            ->expects($this->once())
            ->method('log')
            ->with(
                $order,
                OrderMessage::COULD_NOT_CAPTURE_PAYMENT,
                ['paymentId' => null]
            );

        $this->logger->expects(self::once())
            ->method('critical');

        $this->expectException(RuntimeException::class);

        // act
        ($this->completePaymentsHandler)(new CompletePayments($order->getId()));
    }

    private function createOrder(): Order
    {
        $order = OrderFactory::createPrefilled();
        $order->getLastPayment()?->setState('authorized');

        return $order;
    }
}
