<?php

declare(strict_types=1);

namespace App\Tests\Payment\CommandHandler;

use App\Entity\Payment\GatewayConfig;
use App\Entity\Payment\Payment;
use App\Entity\Payment\PaymentMethod;
use App\Entity\Payment\PaymentMethodGatewayConfig;
use App\Payment\Command\CancelAuthorizedPayment;
use App\Payment\CommandHandler\CancelAuthorizedPaymentHandler;
use App\Payum\Request\CancelAuthorization;
use App\Repository\PaymentRepository;
use Payum\Core\Exception\RequestNotSupportedException;
use Payum\Core\GatewayInterface;
use Payum\Core\Payum;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use RuntimeException;

final class CancelAuthorizedPaymentHandlerTest extends TestCase
{
    private PaymentRepository&MockObject $paymentRepository;
    private Payum&MockObject $payum;
    private LoggerInterface&MockObject $logger;
    private CancelAuthorizedPaymentHandler $handler;

    protected function setUp(): void
    {
        // arrange
        $this->paymentRepository = $this->createMock(PaymentRepository::class);
        $this->payum = $this->createMock(Payum::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->handler = new CancelAuthorizedPaymentHandler(
            $this->paymentRepository,
            $this->payum,
            $this->logger
        );
    }

    public function testSkipsWhenPaymentIsNotFound(): void
    {
        // arrange
        $this->paymentRepository
            ->method('find')
            ->willReturn(null);

        $command = new CancelAuthorizedPayment(1337);

        // act
        $this->handler->__invoke($command);

        // assert
        $this->assertTrue(true); // no exception thrown
    }

    public function testSuccessfullyCancelsAuthorizedPayment(): void
    {
        // arrange
        $payment = $this->createPayment('gateway1');

        $this->paymentRepository
            ->method('find')
            ->willReturn($payment);

        $gateway = $this->createMock(GatewayInterface::class);
        $gateway
            ->expects(self::once())
            ->method('execute')
            ->with(self::isInstanceOf(CancelAuthorization::class));

        $this->payum
            ->method('getGateway')
            ->with('gateway1')
            ->willReturn($gateway);

        $command = new CancelAuthorizedPayment(1337);

        // act
        $this->handler->__invoke($command);

        // assert
        // expectations on gateway mock are enough
    }

    public function testRequestNotSupportedIsIgnored(): void
    {
        // arrange
        $payment = $this->createPayment('gateway2');

        $this->paymentRepository
            ->method('find')
            ->willReturn($payment);

        $gateway = $this->createMock(GatewayInterface::class);
        $gateway
            ->method('execute')
            ->willThrowException(new RequestNotSupportedException());

        $this->payum
            ->method('getGateway')
            ->with('gateway2')
            ->willReturn($gateway);

        $command = new CancelAuthorizedPayment(1337);

        // act
        $this->handler->__invoke($command);

        // assert
        $this->assertTrue(true); // no exception thrown
    }

    public function testUnexpectedExceptionIsLoggedAndRethrown(): void
    {
        // arrange
        $payment = $this->createPayment('gateway3');

        $this->paymentRepository
            ->method('find')
            ->willReturn($payment);

        $exception = new RuntimeException('Failure');

        $gateway = $this->createMock(GatewayInterface::class);
        $gateway
            ->method('execute')
            ->willThrowException($exception);

        $this->payum
            ->method('getGateway')
            ->with('gateway3')
            ->willReturn($gateway);

        $this->logger
            ->expects(self::once())
            ->method('critical')
            ->with(
                $this->stringContains('Failure'),
                $this->arrayHasKey('tags')
            );

        $handler = new CancelAuthorizedPaymentHandler(
            $this->paymentRepository,
            $this->payum,
            $this->logger
        );

        $command = new CancelAuthorizedPayment(1337);

        // assert
        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Failure');

        // act
        $handler->__invoke($command);
    }

    private function createPayment(string $gatewayName): Payment
    {
        $gatewayConfig = $this->createStub(GatewayConfig::class);
        $gatewayConfig
            ->method('getGatewayName')
            ->willReturn($gatewayName);

        $paymentMethod = $this->createStub(PaymentMethod::class);
        $paymentMethod
            ->method('getGatewayConfig')
            ->willReturn($gatewayConfig);

        $paymentMethodGatewayConfig = $this->createStub(PaymentMethodGatewayConfig::class);
        $paymentMethodGatewayConfig->method('getGatewayConfig')->willReturn($gatewayConfig);

        $payment = $this->createMock(Payment::class);
        $payment
            ->method('getPaymentMethodGatewayConfig')
            ->willReturn($paymentMethodGatewayConfig);

        return $payment;
    }
}
