<?php

declare(strict_types=1);

namespace App\Tests\Payment\Dispatcher;

use App\Entity\Order\Order;
use App\Entity\Payment\Collection\PaymentCollection;
use App\Entity\Payment\Payment;
use App\Payment\Command\CompletePayments;
use App\Payment\Dispatcher\CompletePaymentsDispatcher;
use DateMalformedIntervalStringException;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DelayStamp;

final class CompletePaymentsDispatcherTest extends TestCase
{
    private MessageBusInterface&MockObject $messageBusMock;

    private CompletePaymentsDispatcher $dispatcher;

    protected function setUp(): void
    {
        $this->messageBusMock = $this->createMock(MessageBusInterface::class);
        $this->dispatcher = new CompletePaymentsDispatcher($this->messageBusMock, '6 days');
    }

    public function testItDispatchesCompletePaymentsWhenAuthorizedPaymentExists(): void
    {
        $payment = $this->createMock(Payment::class);
        $payment->method('isAuthorized')
            ->willReturn(true);

        $order = $this->createMock(Order::class);
        $order->method('getId')
            ->willReturn(1337);
        $order->method('getPayments')
            ->willReturn(new PaymentCollection([$payment]));

        $this->messageBusMock
            ->expects(self::once())
            ->method('dispatch')
            ->with(
                self::callback(static function (CompletePayments $message) use ($order): bool {
                    self::assertSame($message->getOrderId(), $order->getId());
                    self::assertNull($message->delayPeriod);

                    return true;
                }),
                self::callback(static function (array $stamps): bool {
                    self::assertEmpty($stamps);

                    return true;
                })
            )
            ->willReturn(new Envelope(new CompletePayments($order)));

        ($this->dispatcher)($order);
    }

    public function testItDispatchesWithDelayStampWhenDelayed(): void
    {
        $payment = $this->createMock(Payment::class);
        $payment->method('isAuthorized')
            ->willReturn(true);

        $order = $this->createMock(Order::class);
        $order->method('getId')
            ->willReturn(1337);
        $order->method('getPayments')
            ->willReturn(new PaymentCollection([$payment]));

        $this->messageBusMock
            ->expects(self::once())
            ->method('dispatch')
            ->with(
                self::callback(function (CompletePayments $message) use ($order): bool {
                    self::assertSame($message->getOrderId(), $order->getId());
                    self::assertSame($message->delayPeriod, '6 days');

                    return true;
                }),
                self::callback(static function (array $stamps): bool {
                    self::assertContainsOnlyInstancesOf(DelayStamp::class, $stamps);

                    return true;
                })
            )
            ->willReturn(new Envelope(new CompletePayments($order, '6 days')));

        ($this->dispatcher)($order, true);
    }

    public function testItDoesNothingWhenNoAuthorizedPaymentsExist(): void
    {
        $payment = $this->createMock(Payment::class);
        $payment->method('isAuthorized')
            ->willReturn(false);

        $order = $this->createMock(Order::class);
        $order->method('getPayments')
            ->willReturn(new PaymentCollection([$payment]));

        $this->messageBusMock
            ->expects(self::never())
            ->method('dispatch');

        ($this->dispatcher)($order, true);
    }

    public function testItThrowsWhenDelayStringIsInvalid(): void
    {
        $payment = $this->createMock(Payment::class);
        $payment->method('isAuthorized')
            ->willReturn(true);

        $order = $this->createMock(Order::class);
        $order->method('getPayments')
            ->willReturn(new PaymentCollection([$payment]));

        $this->expectException(DateMalformedIntervalStringException::class);

        (new CompletePaymentsDispatcher($this->messageBusMock, '6 daays'))($order, true);
    }
}
