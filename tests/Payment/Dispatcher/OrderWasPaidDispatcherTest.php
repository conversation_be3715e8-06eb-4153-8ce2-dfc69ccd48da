<?php

declare(strict_types=1);

namespace App\Tests\Payment\Dispatcher;

use App\Entity\Order\Order;
use App\Event\Enum\OrderEventName;
use App\Payment\Dispatcher\OrderWasPaidDispatcher;
use App\StateMachine\Callback\DispatchOrderEvent;
use App\StateMachine\OrderPaymentStates;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class OrderWasPaidDispatcherTest extends TestCase
{
    private readonly DispatchOrderEvent&MockObject $dispatchOrderEventMock;
    private readonly OrderWasPaidDispatcher $orderWasPaidDispatcher;

    protected function setUp(): void
    {
        $this->dispatchOrderEventMock = $this->createMock(DispatchOrderEvent::class);
        $this->orderWasPaidDispatcher = new OrderWasPaidDispatcher($this->dispatchOrderEventMock);
    }

    /**
     * @dataProvider orderProvider
     */
    public function testInvokeSetsEventName(
        Order $order,
        OrderEventName $eventName,
    ): void {
        $this->dispatchOrderEventMock
            ->expects(self::once())
            ->method('__invoke')
            ->with($order, $eventName->name);

        ($this->orderWasPaidDispatcher)($order);
    }

    /**
     * @return iterable<string, array{0: Order, 1: OrderEventName}>
     */
    public static function orderProvider(): iterable
    {
        $order = new Order();
        $order->setPaymentState(OrderPaymentStates::STATE_AUTHORIZED);

        yield sprintf('authorized state returns %s event', OrderEventName::OrderWasPaidAfterAuthorization->name) => [
            $order,
            OrderEventName::OrderWasPaidAfterAuthorization,
        ];

        $order = new Order();
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);

        yield sprintf('non-authorized state returns %s event', OrderEventName::OrderWasPaid->name) => [
            $order,
            OrderEventName::OrderWasPaid,
        ];
    }
}
