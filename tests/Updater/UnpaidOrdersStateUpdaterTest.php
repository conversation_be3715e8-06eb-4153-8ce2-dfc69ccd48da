<?php

declare(strict_types=1);

namespace App\Tests\Updater;

use App\Entity\Order\Cancellation;
use App\Entity\Order\Enum\CancellationBy;
use App\Entity\Order\Order;
use App\Repository\OrderRepositoryInterface;
use App\Updater\UnpaidOrdersStateUpdater;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use SM\Factory\Factory;
use SM\SMException;
use SM\StateMachine\StateMachine;
use Sylius\Component\Order\OrderTransitions;

class UnpaidOrdersStateUpdaterTest extends TestCase
{
    private const string EXPIRATION_PERIOD = '2 days';

    /**
     * @var OrderRepositoryInterface|MockObject
     */
    private OrderRepositoryInterface $orderRepositoryMock;

    /**
     * @var Factory|MockObject
     */
    private Factory $stateMachineFactoryMock;

    /**
     * @var LoggerInterface|MockObject
     */
    private LoggerInterface $logger;

    /**
     * @var StateMachine|MockObject
     */
    private StateMachine $stateMachineMock;

    private UnpaidOrdersStateUpdater $unpaidOrdersStateUpdater;

    private Order $order;

    protected function setUp(): void
    {
        $this->orderRepositoryMock = $this->createMock(OrderRepositoryInterface::class);
        $this->stateMachineFactoryMock = $this->createMock(Factory::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->unpaidOrdersStateUpdater = new UnpaidOrdersStateUpdater(
            $this->orderRepositoryMock,
            $this->stateMachineFactoryMock,
            $this->logger
        );

        $this->stateMachineMock = $this->createMock(StateMachine::class);

        $this->order = $this->createOrder();
    }

    public function testItCanCancelUnpaidOrders(): void
    {
        $this->orderRepositoryMock->expects($this->once())
            ->method('findOrdersUnpaidSince')
            ->willReturn([$this->order]);

        $this->stateMachineFactoryMock->expects($this->once())
            ->method('get')
            ->with($this->order, OrderTransitions::GRAPH)
            ->willReturn($this->stateMachineMock);

        $this->stateMachineMock->expects($this->once())
            ->method('apply')
            ->with(OrderTransitions::TRANSITION_CANCEL)
            ->willReturn(true);

        $this->unpaidOrdersStateUpdater->cancel(self::EXPIRATION_PERIOD);

        self::assertEquals(
            new Cancellation(CancellationBy::SYSTEM, 'payment_expired'),
            $this->order->getCancellation()
        );
    }

    public function testLoggerPrintsErrorWhenOrderCancellationFails(): void
    {
        $this->orderRepositoryMock->expects($this->once())
            ->method('findOrdersUnpaidSince')
            ->willReturn([$this->order]);

        $this->stateMachineFactoryMock->expects($this->once())
            ->method('get')
            ->with($this->order, OrderTransitions::GRAPH)
            ->willReturn($this->stateMachineMock);

        $exception = new SMException('Exception message.');

        $this->stateMachineMock->expects($this->once())
            ->method('apply')
            ->with(OrderTransitions::TRANSITION_CANCEL)
            ->willThrowException($exception);

        $this->logger->expects($this->once())
            ->method('error')
            ->with(
                sprintf('An error occurred while cancelling unpaid order with token %s', $this->order->getTokenValue()),
                ['exception' => $exception, 'message' => $exception->getMessage()]
            );

        $this->unpaidOrdersStateUpdater->cancel(self::EXPIRATION_PERIOD);

        self::assertNull($this->order->getCancellation());
    }

    private function createOrder(): Order
    {
        $order = new Order();
        $order->setTokenValue('token123');

        return $order;
    }
}
