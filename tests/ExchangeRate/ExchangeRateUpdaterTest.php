<?php

declare(strict_types=1);

namespace App\Tests\ExchangeRate;

use App\Entity\Currency\Currency;
use App\Entity\Currency\ExchangeRate;
use App\ExchangeRate\ExchangeRateUpdater;
use ArrayIterator;
use DateTime;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Persistence\ObjectRepository;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpClient\MockHttpClient;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\HttpFoundation\Response;

class ExchangeRateUpdaterTest extends TestCase
{
    private ExchangeRateUpdater $exchangeRateUpdater;

    /** @var ObjectManager|MockObject */
    private ObjectManager $objectManagerMock;

    private ArrayIterator $mockResponses;

    protected function setUp(): void
    {
        $this->objectManagerMock = $this->createMock(ObjectManager::class);
        $this->mockResponses = new ArrayIterator();
        $mockHttpClient = new MockHttpClient(
            $this->mockResponses,
            'http://exchange-rates.provider.localhost'
        );

        $this->exchangeRateUpdater = new ExchangeRateUpdater(
            $this->objectManagerMock,
            $mockHttpClient,
        );
    }

    public function testCanLoadExchangeRates(): void
    {
        $EUR = $this->createCurrency('EUR');
        $GBP = $this->createCurrency('GBP');
        $CHF = $this->createCurrency('CHF');

        $this->mockResponses->append($this->createResponse());

        $objectRepositoryMock = $this->createMock(ObjectRepository::class);

        $this->objectManagerMock->expects($this->exactly(6))
            ->method('getRepository')
            ->withConsecutive(
                [Currency::class],
                [Currency::class],
                [Currency::class],
                [ExchangeRate::class],
            )
            ->willReturn($objectRepositoryMock);

        $objectRepositoryMock->expects($this->once())
            ->method('findAll')
            ->willReturn([]);

        $objectRepositoryMock->expects($this->exactly(5))
            ->method('findOneBy')
            ->withConsecutive(
                [['code' => 'EUR']],
                [['code' => 'GBP']],
                [['sourceCurrency' => 'EUR', 'targetCurrency' => 'GBP']],
                [['code' => 'CHF']],
                [['sourceCurrency' => 'EUR', 'targetCurrency' => 'CHF']],
            )
            ->willReturnOnConsecutiveCalls(
                $EUR,
                $GBP,
                null,
                $CHF,
                null,
            );

        $expectedGBPRate = $this->createExchangeRate($EUR, $GBP, 0.854977);
        $expectedCHFRate = $this->createExchangeRate($EUR, $CHF, 1.091954);

        $this->objectManagerMock->expects($this->exactly(2))
            ->method('persist')
            ->withConsecutive(
                [$this->callback(function (ExchangeRate $exchangeRate) use ($expectedGBPRate) {
                    return self::assertExchangeRateWithUpdatedTime($exchangeRate, $expectedGBPRate);
                })],
                [$this->callback(function (ExchangeRate $exchangeRate) use ($expectedCHFRate) {
                    return self::assertExchangeRateWithUpdatedTime($exchangeRate, $expectedCHFRate);
                })]
            );

        $this->objectManagerMock->expects($this->once())
            ->method('flush');

        $this->exchangeRateUpdater->updateExchangeRates();
    }

    public function testCanUpdateExchangeRates(): void
    {
        $EUR = $this->createCurrency('EUR');
        $GBP = $this->createCurrency('GBP');
        $CHF = $this->createCurrency('CHF');

        $expectedGBPRate = $this->createExchangeRate($EUR, $GBP, 0.854977);
        $expectedCHFRate = $this->createExchangeRate($EUR, $CHF, 1.091954);
        $expectedGBPRate->setCreatedAt(new DateTime());
        $expectedGBPRate->setUpdatedAt(new DateTime());

        $this->mockResponses->append($this->createResponse());

        $objectRepositoryMock = $this->createMock(ObjectRepository::class);

        $this->objectManagerMock->expects($this->exactly(6))
            ->method('getRepository')
            ->withConsecutive(
                [Currency::class],
                [Currency::class],
                [Currency::class],
                [ExchangeRate::class],
            )
            ->willReturn($objectRepositoryMock);

        $objectRepositoryMock->expects($this->once())
            ->method('findAll')
            ->willReturn([]);

        $objectRepositoryMock->expects($this->exactly(5))
            ->method('findOneBy')
            ->withConsecutive(
                [['code' => 'EUR']],
                [['code' => 'GBP']],
                [['sourceCurrency' => 'EUR', 'targetCurrency' => 'GBP']],
                [['code' => 'CHF']],
                [['sourceCurrency' => 'EUR', 'targetCurrency' => 'CHF']],
            )
            ->willReturnOnConsecutiveCalls(
                $EUR,
                $GBP,
                $expectedGBPRate,
                $CHF,
                null,
            );

        $this->objectManagerMock->expects($this->exactly(2))
            ->method('persist')
            ->withConsecutive(
                [$this->callback(function (ExchangeRate $exchangeRate) use ($expectedGBPRate) {
                    $expectedGBPRate->setUpdatedAt($exchangeRate->getUpdatedAt());
                    self::assertEquals($exchangeRate, $expectedGBPRate);

                    return true;
                })],
                [$this->callback(function (ExchangeRate $exchangeRate) use ($expectedCHFRate) {
                    return self::assertExchangeRateWithUpdatedTime($exchangeRate, $expectedCHFRate);
                })]
            );

        $this->objectManagerMock->expects($this->once())
            ->method('flush');

        $this->exchangeRateUpdater->updateExchangeRates();
    }

    public function testCanNotUpdateExchangeRateIfResponseDoesNotContainRates(): void
    {
        $response = [
            'success' => false,
            'error' => [
                'code' => 101,
                'type' => 'missing_access_key',
                'info' => 'You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]',
            ],
        ];

        $mockResponse = new MockResponse(
            json_encode($response),
            [
                'http_code' => Response::HTTP_OK,
            ]
        );

        $this->mockResponses->append($mockResponse);

        $objectRepositoryMock = $this->createMock(ObjectRepository::class);

        $this->objectManagerMock->expects($this->once())
            ->method('getRepository')
            ->with(Currency::class)
            ->willReturn($objectRepositoryMock);

        $objectRepositoryMock->expects($this->once())
            ->method('findAll')
            ->willReturn([]);

        $this->expectExceptionMessage('You have not supplied an API Access Key. [Required format: access_key=YOUR_ACCESS_KEY]');

        $this->exchangeRateUpdater->updateExchangeRates();
    }

    private function assertExchangeRateWithUpdatedTime(ExchangeRate $exchangeRate, ExchangeRate $expectedExchangeRate): bool
    {
        $expectedExchangeRate->setCreatedAt($exchangeRate->getCreatedAt());
        $expectedExchangeRate->setUpdatedAt($exchangeRate->getUpdatedAt());

        self::assertEquals($exchangeRate, $expectedExchangeRate);

        return true;
    }

    private function createExchangeRate(Currency $sourceCurrency, Currency $targetCurrency, float $rate): ExchangeRate
    {
        $exchangeRate = new ExchangeRate();
        $exchangeRate->setSourceCurrency($sourceCurrency);
        $exchangeRate->setTargetCurrency($targetCurrency);
        $exchangeRate->setRatio($rate);

        return $exchangeRate;
    }

    private function createCurrency(string $code): Currency
    {
        $currency = new Currency();
        $currency->setCode($code);

        return $currency;
    }

    private function createResponse(): MockResponse
    {
        $response = [
            'success' => true,
            'timestamp' => 1625662444,
            'base' => 'EUR',
            'date' => '2021-07-07',
            'rates' => [
                'GBP' => 0.854977,
                'CHF' => 1.091954,
            ],
        ];

        return new MockResponse(
            json_encode($response),
            [
                'http_code' => Response::HTTP_OK,
            ]
        );
    }
}
