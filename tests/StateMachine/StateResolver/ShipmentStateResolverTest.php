<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\StateResolver;

use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Order\OrderItemUnit;
use App\Entity\Product\ProductVariant;
use App\Entity\Shipping\Shipment;
use App\StateMachine\OrderFraudCheckStates;
use App\StateMachine\OrderPrescriptionStates;
use App\StateMachine\ShipmentTransitions;
use App\StateMachine\StateResolver\ShipmentStateResolver;
use Sylius\Component\Core\OrderPaymentStates;
use Sylius\Component\Order\Model\OrderInterface as BaseOrderInterface;
use Sylius\Component\Shipping\ShipmentTransitions as SyliusShipmentTransitions;

final class ShipmentStateResolverTest extends AbstractStateResolverTest
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->stateResolver = new ShipmentStateResolver($this->stateMachineFactoryStub);
    }

    public function provideOrder(): iterable
    {
        $order = $this->createOrderWithOneShipment();
        $order->setFraudCheckState(OrderFraudCheckStates::STATE_ALLOWED);
        $order->setState(BaseOrderInterface::STATE_CANCELLED);

        yield 'Order that has state cancelled' => [SyliusShipmentTransitions::TRANSITION_CANCEL, $order];

        $order = $this->createOrderWithOneShipment();
        $order->setFraudCheckState(OrderFraudCheckStates::STATE_ALLOWED);
        $order->setPaymentState(OrderPaymentStates::STATE_AWAITING_PAYMENT);

        yield 'Order that has paymentState awaiting_payment' => [SyliusShipmentTransitions::TRANSITION_CREATE, $order];

        $order = $this->createOrderWithOneShipment();
        $order->setFraudCheckState(OrderFraudCheckStates::STATE_ALLOWED);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);

        yield 'Order with paymentState paid and prescriptionState approved' => [ShipmentTransitions::TRANSITION_READY, $order];

        $order = $this->createOrderWithOneShipment();
        $order->setFraudCheckState(OrderFraudCheckStates::STATE_APPROVED);
        $order->setPaymentState(OrderPaymentStates::STATE_AUTHORIZED);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);

        yield 'Order with paymentState authorized and prescriptionState approved' => [ShipmentTransitions::TRANSITION_READY, $order];

        $order = $this->createOrderWithOneShipment();
        $order->setFraudCheckState(OrderFraudCheckStates::STATE_SKIPPED);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_SKIPPED);

        yield 'Order with paymentState paid and prescriptionState skipped' => [ShipmentTransitions::TRANSITION_READY, $order];

        $order = $this->createOrderWithOneShipment();
        $order->setFraudCheckState(OrderFraudCheckStates::STATE_ALLOWED);
        $order->setPaymentState(OrderPaymentStates::STATE_AUTHORIZED);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_SKIPPED);

        yield 'Order with paymentState authorized and prescriptionState skipped' => [ShipmentTransitions::TRANSITION_READY, $order];

        $order = $this->createOrderWithOneShipment();
        $order->setFraudCheckState(OrderFraudCheckStates::STATE_ALLOWED);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);

        yield 'Order with paymentState paid and one prescriptionRequired unit shipment' => [ShipmentTransitions::TRANSITION_AWAIT_PRESCRIPTION, $order];

        $order = $this->createOrderWithOneShipment();
        $order->setFraudCheckState(OrderFraudCheckStates::STATE_ALLOWED);
        $order->setPaymentState(OrderPaymentStates::STATE_AUTHORIZED);

        yield 'Order with paymentState authorized and one prescriptionRequired unit shipment' => [ShipmentTransitions::TRANSITION_AWAIT_PRESCRIPTION, $order];

        $order = $this->createOrderWithOneShipment();
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setFraudCheckState(OrderFraudCheckStates::STATE_ALLOWED);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_DECLINED);

        yield 'Order with paymentState paid and prescriptionState declined' => [SyliusShipmentTransitions::TRANSITION_CANCEL, $order];

        $order = $this->createOrderWithOneShipment();
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setFraudCheckState(OrderFraudCheckStates::STATE_ALLOWED);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);
        $order->setFollowUpOrder($this->createOrderWithOneShipment());

        yield 'Order that has follow up order with paymentState paid and prescriptionState approved' => [SyliusShipmentTransitions::TRANSITION_SHIP, $order];

        $order = $this->createOrderWithOneShipment();
        $order->setPaymentState(OrderPaymentStates::STATE_PARTIALLY_PAID);
        $order->setFraudCheckState(OrderFraudCheckStates::STATE_ALLOWED);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);

        yield 'Order with paymentState partially paid and prescription state approved' => [ShipmentTransitions::TRANSITION_AWAIT_ADDITIONAL_PAYMENT, $order];

        $order = $this->createOrderWithOneShipment();
        $order->setPaymentState(OrderPaymentStates::STATE_PARTIALLY_PAID);
        $order->setFraudCheckState(OrderFraudCheckStates::STATE_CART);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);
        yield 'Order with paymentState partially paid and fraud check state' => [ShipmentTransitions::TRANSITION_AWAIT_ADDITIONAL_PAYMENT, $order];

        $order = $this->createOrderWithOneShipment();
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setFraudCheckState(OrderFraudCheckStates::STATE_CART);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);
        yield 'Order with paymentState paid and fraud check state' => [ShipmentTransitions::TRANSITION_FRAUD_CHECK, $order];
    }

    private function createOrderWithOneShipment(): Order
    {
        $productVariant = new ProductVariant();
        $productVariant->setPrescriptionRequired(true);

        $orderItem = new OrderItem();
        $orderItem->setVariant($productVariant);

        $orderItemUnit = new OrderItemUnit($orderItem);

        $shipment = new Shipment();
        $shipment->addUnit($orderItemUnit);

        $order = new Order();
        $order->addItem($orderItem);
        $order->addShipment($shipment);

        return $order;
    }
}
