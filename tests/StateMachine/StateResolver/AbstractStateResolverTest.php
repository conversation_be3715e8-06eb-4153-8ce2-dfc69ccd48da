<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\StateResolver;

use App\Entity\Order\Order;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\MockObject\Stub;
use PHPUnit\Framework\TestCase;
use SM\Factory\FactoryInterface;
use SM\StateMachine\StateMachineInterface;
use Sylius\Component\Order\StateResolver\StateResolverInterface;

abstract class AbstractStateResolverTest extends TestCase
{
    protected StateResolverInterface $stateResolver;

    protected FactoryInterface&Stub $stateMachineFactoryStub;
    protected StateMachineInterface&MockObject $stateMachineMock;

    protected function setUp(): void
    {
        $this->stateMachineMock = $this->createMock(StateMachineInterface::class);

        $this->stateMachineFactoryStub = $this->createStub(FactoryInterface::class);
        $this->stateMachineFactoryStub->method('get')
            ->willReturn($this->stateMachineMock);
    }

    /**
     * @dataProvider provideOrder
     */
    public function testResolveAppliesCorrectTransition(
        ?string $expectedTransition,
        Order $order,
    ): void {
        $this->stateMachineMock->expects(is_string($expectedTransition) ? self::once() : self::never())
            ->method('can')
            ->with($expectedTransition)
            ->willReturn(true);
        $this->stateMachineMock->expects(is_string($expectedTransition) ? self::once() : self::never())
            ->method('apply')
            ->with($expectedTransition);

        $this->stateResolver->resolve($order);
    }

    /**
     * @return iterable<string, array{0: string, 1: Order}>
     */
    abstract public function provideOrder(): iterable;
}
