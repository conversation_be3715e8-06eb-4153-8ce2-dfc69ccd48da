<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\StateResolver;

use App\Entity\Order\Order;
use App\Entity\Payment\Payment;
use App\Entity\Payment\PaymentMethod;
use App\Entity\Refund\RefundPayment;
use App\StateMachine\StateResolver\OrderPaymentStateResolver;
use App\Tests\Mocks\Entity\TestOrder;
use Sylius\Component\Core\OrderPaymentTransitions;

final class OrderPaymentStateResolverTest extends AbstractStateResolverTest
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->stateResolver = new OrderPaymentStateResolver($this->stateMachineFactoryStub);
    }

    public function provideOrder(): iterable
    {
        $order = $this->createOrder(100);
        $this->addPaymentToOrder($order, Payment::STATE_COMPLETED, 100);
        $this->addRefundPaymentToOrder($order, Payment::STATE_COMPLETED, 100);
        yield 'is refunded' => [OrderPaymentTransitions::TRANSITION_REFUND, $order];

        $order = $this->createOrder(100);
        $this->addPaymentToOrder($order, Payment::STATE_COMPLETED, 100);
        yield 'is paid' => [OrderPaymentTransitions::TRANSITION_PAY, $order];

        $order = $this->createOrder(100);
        $this->addPaymentToOrder($order, Payment::STATE_AUTHORIZED, 100);
        yield 'is authorized' => [OrderPaymentTransitions::TRANSITION_AUTHORIZE, $order];

        $order = $this->createOrder(100);
        $this->addPaymentToOrder($order, Payment::STATE_COMPLETED, 50);
        yield 'is partially paid' => [OrderPaymentTransitions::TRANSITION_PARTIALLY_PAY, $order];

        $order = $this->createOrder(100);
        $this->addPaymentToOrder($order, Payment::STATE_AUTHORIZED, 50);
        yield 'is partially authorized' => [OrderPaymentTransitions::TRANSITION_PARTIALLY_AUTHORIZE, $order];

        $order = $this->createOrder(100);
        $order->setState(Order::STATE_CANCELLED);
        yield 'is cancelled' => [OrderPaymentTransitions::TRANSITION_CANCEL, $order];

        $order = $this->createOrder(100);
        yield 'default' => [OrderPaymentTransitions::TRANSITION_REQUEST_PAYMENT, $order];
    }

    private function createOrder(int $orderTotal): Order
    {
        $order = new TestOrder();
        $order->setTotal($orderTotal);

        return $order;
    }

    private function addPaymentToOrder(Order $order, string $state, int $amount): void
    {
        $payment = new Payment();
        $payment->setAmount($amount);
        $payment->setState($state);

        $order->addPayment($payment);
    }

    private function addRefundPaymentToOrder(Order $order, string $state, int $amount): void
    {
        $order->addRefundPayment(
            new RefundPayment($order, $amount, 'EUR', $state, new PaymentMethod())
        );
    }
}
