<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Guard;

use App\Entity\Addressing\Address;
use App\Entity\Channel\Channel;
use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Order\PreferredOrderItem;
use App\Entity\Payment\Payment;
use App\Entity\Payment\PaymentMethod;
use App\Entity\Product\Product;
use App\Entity\Product\ProductAttribute;
use App\Entity\Product\ProductAttributeValue;
use App\Entity\Product\ProductInterface;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariant;
use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShippingMethod;
use App\Entity\TermQuestion\TermQuestion;
use App\Repository\TermQuestionRepository;
use App\StateMachine\Guard\OrderCheckoutStateGuard;
use App\StateMachine\OrderCheckoutStates;
use App\Tests\Mocks\AnamnesisServiceClient;
use App\Tests\Mocks\Entity\TestOrder;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Ramsey\Uuid\Uuid;
use Sylius\Component\Core\Repository\ProductRepositoryInterface;

final class OrderCheckoutStateGuardTest extends TestCase
{
    private OrderCheckoutStateGuard $guard;

    private TermQuestionRepository&MockObject $termQuestionRepositoryMock;

    /** @var ProductRepositoryInterface<ProductInterface>&MockObject */
    private ProductRepositoryInterface&MockObject $productRepositoryMock;

    protected function setUp(): void
    {
        $this->termQuestionRepositoryMock = $this->createMock(TermQuestionRepository::class);
        $this->productRepositoryMock = $this->createMock(ProductRepositoryInterface::class);

        $this->guard = new OrderCheckoutStateGuard(
            new AnamnesisServiceClient(),
            $this->termQuestionRepositoryMock,
            $this->productRepositoryMock,
            $this->createStub(LoggerInterface::class),
        );
    }

    /**
     * @dataProvider provideCanSkipMedicalQuestionnaire
     */
    public function testCanSkipMedicalQuestionnaire(Order $order): void
    {
        self::assertTrue($this->guard->canSkipMedicalQuestionnaire($order));
    }

    public function provideCanSkipMedicalQuestionnaire(): iterable
    {
        $order = new Order();

        $this->createOrderItemForOrder($order, false);

        yield 'Order with 1 orderItem with 1 variant with prescriptionRequired=false' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanSkipMedicalQuestionnaire
     */
    public function testInvalidCanSkipMedicalQuestionnaire(Order $order): void
    {
        self::assertFalse($this->guard->canSkipMedicalQuestionnaire($order));
    }

    public function provideInvalidCanSkipMedicalQuestionnaire(): iterable
    {
        $order = new Order();

        $this->createOrderItemForOrder($order, true);

        yield 'Order with 1 orderItem with 1 variant with prescriptionRequired=true' => [$order];

        $order = new Order();

        $this->createOrderItemForOrder($order, false);
        $this->createOrderItemForOrder($order, true);

        yield 'Order with 1 orderItem with prescriptionRequired=false and 1 orderItem with prescriptionRequired=true' => [$order];

        $order = new Order();

        yield 'Order with no orderItem(s)' => [$order];
    }

    /**
     * @dataProvider provideCanCompleteMedicalQuestionnaire
     */
    public function testCanCompleteMedicalQuestionnaire(Order $order): void
    {
        self::assertTrue($this->guard->canCompleteMedicalQuestionnaire($order));
    }

    public function provideCanCompleteMedicalQuestionnaire(): iterable
    {
        $order = new TestOrder();
        $order->setMedicalQuestionnaire(Uuid::fromString('69e5a697-00fb-4087-bf9e-880a6bcf58e4'));

        yield 'Order with a medicalQuestionnaire UUID that is validated' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanCompleteMedicalQuestionnaire
     */
    public function testInvalidCanCompleteMedicalQuestionnaire(Order $order): void
    {
        self::assertFalse($this->guard->canCompleteMedicalQuestionnaire($order));
    }

    public function provideInvalidCanCompleteMedicalQuestionnaire(): iterable
    {
        $order = new Order();

        yield 'Order without medicalQuestionnaire UUID' => [$order];

        $order = new Order();
        $order->setMedicalQuestionnaire(Uuid::uuid4());

        yield 'Order with medicalQuestionnaire UUID that is not finished' => [$order];
    }

    /**
     * @dataProvider provideCanSelectPreferredProducts
     */
    public function testCanSelectPreferredProducts(Order $order): void
    {
        self::assertTrue($this->guard->canSelectPreferredProducts($order));
    }

    public function provideCanSelectPreferredProducts(): iterable
    {
        $order = $this->createOrderWithChannel(false);

        $orderItem = $this->createOrderItemForOrder($order, true);
        $this->addProductTypeToOrderItem($orderItem, ProductType::CONSULT);
        $orderItem->addPreferredItem(new PreferredOrderItem(
            $orderItem,
            new ProductVariant(),
        ));

        yield 'Order with 1 consult product with a preferred variant' => [$order];

        $order = $this->createOrderWithChannel(false);

        $orderItem = $this->createOrderItemForOrder($order, true);
        $this->addProductTypeToOrderItem($orderItem, ProductType::MEDICATION);

        $orderItem = $this->createOrderItemForOrder($order, true);
        $this->addProductTypeToOrderItem($orderItem, ProductType::MEDICATION);

        yield 'Order with only medication products' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanSelectPreferredProducts
     */
    public function testInvalidCanSelectPreferredProducts(Order $order): void
    {
        self::assertFalse($this->guard->canSelectPreferredProducts($order));
    }

    public function provideInvalidCanSelectPreferredProducts(): iterable
    {
        $order = $this->createOrderWithChannel(true);

        yield 'Order with a channel that can addPrescriptionMedicationDirectlyToCart' => [$order];

        $order = $this->createOrderWithChannel(false);

        yield 'Order without orderItems' => [$order];

        $order = $this->createOrderWithChannel(false);

        $orderItem = $this->createOrderItemForOrder($order, true);
        $this->addProductTypeToOrderItem($orderItem, ProductType::CONSULT);

        yield 'Order with 1 consult product without preferred variant' => [$order];

        $order = $this->createOrderWithChannel(false);

        $orderItem = $this->createOrderItemForOrder($order, true);
        $this->addProductTypeToOrderItem($orderItem, ProductType::CONSULT);
        $orderItem->addPreferredItem(new PreferredOrderItem(
            $orderItem,
            new ProductVariant(),
        ));

        $orderItem = $this->createOrderItemForOrder($order, true);
        $this->addProductTypeToOrderItem($orderItem, ProductType::CONSULT);

        yield 'Order with 1 consult product with a preferred variant and 1 consult product without a preferred variant' => [$order];
    }

    /**
     * @dataProvider provideCanSelectDeliveryService
     */
    public function testCanSelectDeliveryService(Order $order): void
    {
        $this->productRepositoryMock->method('findOneByChannelAndCode')
            ->willReturn(new Product());

        self::assertTrue($this->guard->canSelectDeliveryService($order));
    }

    public function provideCanSelectDeliveryService(): iterable
    {
        $order = $this->createOrderWithChannel(true);
        $orderItem = $this->createOrderItemForOrder($order, false);
        $orderItem->getProduct()?->setCode(ProductInterface::SERVICE_BLUECLINIC_CODE);

        yield 'Order with an orderItem that is a delivery product' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanSelectDeliveryService
     */
    public function testInvalidCanSelectDeliveryService(
        Order $order,
        bool $channelHasDeliveryProducts,
    ): void {
        if ($channelHasDeliveryProducts) {
            $this->productRepositoryMock->method('findOneByChannelAndCode')
                ->willReturn(new Product());
        }

        self::assertFalse($this->guard->canSelectDeliveryService($order));
    }

    public function provideInvalidCanSelectDeliveryService(): iterable
    {
        $order = $this->createOrderWithChannel(false);

        yield 'Channel from order does not have delivery products' => [$order, false];

        yield 'Order with no orderItems' => [$order, true];

        $this->createOrderItemForOrder($order, true);

        yield 'Order with no delivery product order items' => [$order, true];
    }

    /**
     * @dataProvider provideCanAddress
     */
    public function testCanAddress(Order $order): void
    {
        self::assertTrue($this->guard->canAddress($order));
    }

    public function provideCanAddress(): iterable
    {
        $order = new Order();
        $order->setCheckoutState(OrderCheckoutStates::STATE_DELIVERY_SERVICE_SELECTED);
        $order->setShippingAddress(new Address());
        $order->setBillingAddress(new Address());

        yield 'Order with a shipping and a billing address' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanAddress
     */
    public function testInvalidCanAddress(Order $order): void
    {
        self::assertFalse($this->guard->canAddress($order));
    }

    public function provideInvalidCanAddress(): iterable
    {
        $order = new Order();

        yield 'Order with no addresses' => [$order];

        $order = new Order();
        $order->setShippingAddress(new Address());

        yield 'Order with only a shipping address' => [$order];

        $order = new Order();
        $order->setBillingAddress(new Address());

        yield 'Order with only a billing address' => [$order];
    }

    /**
     * @dataProvider provideCanSelectShipping
     */
    public function testCanSelectShipping(Order $order): void
    {
        self::assertTrue($this->guard->canSelectShipping($order));
    }

    public function provideCanSelectShipping(): iterable
    {
        $shippingMethod = new ShippingMethod();

        $shipment = new Shipment();
        $shipment->setMethod($shippingMethod);

        $order = new Order();
        $order->addShipment($shipment);

        yield 'Order with 1 shipment with a method selected' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanSelectShipping
     */
    public function testInvalidCanSelectShipping(Order $order): void
    {
        self::assertFalse($this->guard->canSelectShipping($order));
    }

    public function provideInvalidCanSelectShipping(): iterable
    {
        $order = new Order();

        yield 'Order with no shipments' => [$order];

        $order = new Order();
        $order->addShipment(new Shipment());

        yield 'Order with 1 shipment with no method selected' => [$order];
    }

    /**
     * @dataProvider provideCanSkipShipping
     */
    public function testCanSkipShipping(Order $order): void
    {
        self::assertTrue($this->guard->canSkipShipping($order));
    }

    public function provideCanSkipShipping(): iterable
    {
        $channel = new Channel();
        $channel->setSkippingShippingStepAllowed(true);

        $order = new Order();
        $order->setChannel($channel);

        yield 'Order with channel that can skip shipping method step' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanSkipShipping
     */
    public function testInvalidCanSkipShipping(Order $order): void
    {
        self::assertFalse($this->guard->canSkipShipping($order));
    }

    public function provideInvalidCanSkipShipping(): iterable
    {
        $channel = new Channel();
        $channel->setCode('dok_gb');

        $order = new Order();
        $order->setChannel($channel);

        yield 'Order with a channel that cannot skip shipping method step' => [$order];
    }

    /**
     * @dataProvider provideCanSelectPayment
     */
    public function testCanSelectPayment(Order $order): void
    {
        self::assertTrue($this->guard->canSelectPayment($order));
    }

    public function provideCanSelectPayment(): iterable
    {
        $paymentMethod = new PaymentMethod();

        $payment = new Payment();
        $payment->setMethod($paymentMethod);

        $order = new Order();
        $order->addPayment($payment);

        yield 'Order with 1 payment that has a payment method' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanSelectPayment
     */
    public function testInvalidSelectPayment(Order $order): void
    {
        self::assertFalse($this->guard->canSelectPayment($order));
    }

    public function provideInvalidCanSelectPayment(): iterable
    {
        $order = new Order();

        yield 'Order without payments' => [$order];

        $payment = new Payment();

        $order = new Order();
        $order->addPayment($payment);

        yield 'Order with 1 payment that does not have a payment method' => [$order];
    }

    /**
     * @dataProvider provideCanSkipPayment
     */
    public function testCanSkipPayment(Order $order): void
    {
        self::assertTrue($this->guard->canSkipPayment($order));
    }

    public function provideCanSkipPayment(): iterable
    {
        $channel = new Channel();
        $channel->setSkippingPaymentStepAllowed(true);

        $order = new Order();
        $order->setChannel($channel);

        yield 'Order with channel that can skip payment method step' => [$order];
    }

    /**
     * @dataProvider provideInvalidOrderForChannelForOrderAllowsSkippingPaymentMethodStep
     */
    public function testInvalidCanSkipPayment(
        Order $order,
    ): void {
        self::assertFalse($this->guard->canSkipPayment($order));
    }

    public function provideInvalidOrderForChannelForOrderAllowsSkippingPaymentMethodStep(): iterable
    {
        $channel = new Channel();
        $channel->setCode('dok_gb');

        $order = new Order();
        $order->setChannel($channel);

        yield 'Order with a channel that cannot skip payment method step' => [$order];
    }

    /**
     * @dataProvider provideCanComplete
     */
    public function testCanComplete(Order $order): void
    {
        $termQuestion = new TermQuestion();
        $termQuestion->setCode('test');
        $this->termQuestionRepositoryMock->method('findAllByOrder')
            ->willReturn([$termQuestion]);

        self::assertTrue($this->guard->canComplete($order));
    }

    public function provideCanComplete(): iterable
    {
        $productVariant = new ProductVariant();
        $productVariant->setPrescriptionRequired(true);

        $orderItem = new OrderItem();
        $orderItem->setVariant($productVariant);

        $order = new Order();
        $order->setCheckoutTermsAnswers(['test' => 'test answer']);
        $order->setMedicalQuestionnaire(Uuid::fromString('69e5a697-00fb-4087-bf9e-880a6bcf58e4'));
        $order->addItem($orderItem);

        yield 'Order (RX) with a valid checkoutTermsAnswer and medicalQuestionnaire' => [$order];

        $order = new Order();
        $order->setCheckoutTermsAnswers(['test' => 'test answer']);

        yield 'Order (OTC) with a valid checkoutTermsAnswer and no medicalQuestionnaire' => [$order];

        $followUpOrder = new Order();
        $followUpOrder->setParentOrder(new Order());
        $followUpOrder->setCheckoutTermsAnswers(['test' => 'test answer']);

        yield 'Follow up order with valid checkoutTermsAnswer without a medicalQuestionnaire' => [$followUpOrder];
    }

    /**
     * @dataProvider provideInvalidCanComplete
     */
    public function testInvalidCanComplete(Order $order): void
    {
        $termQuestion = new TermQuestion();
        $termQuestion->setCode('test');
        $this->termQuestionRepositoryMock->method('findAllByOrder')
            ->willReturn([$termQuestion]);

        self::assertFalse($this->guard->canComplete($order));
    }

    public function provideInvalidCanComplete(): iterable
    {
        $order = new Order();

        yield 'Order without any checkoutTermsAnswers and no medicalQuestionnaire' => [$order];

        $order = new Order();
        $order->setCheckoutTermsAnswers(['invalid' => 'test answer']);

        yield 'Order with an invalid checkoutTermsAnswer and no medicalQuestionnaire' => [$order];

        $order = new Order();
        $order->setMedicalQuestionnaire(Uuid::uuid4());

        yield 'Order without any checkoutTermsAnswers and invalid medicalQuestionnaire' => [$order];

        $order = new Order();
        $order->setCheckoutTermsAnswers(['invalid' => 'test answer']);
        $order->setMedicalQuestionnaire(Uuid::uuid4());

        yield 'Order with an invalid checkoutTermsAnswer and invalid medicalQuestionnaire' => [$order];
    }

    private function createOrderItemForOrder(
        Order $order,
        bool $prescriptionRequired,
    ): OrderItem {
        $productVariant = new ProductVariant();
        $productVariant->setPrescriptionRequired($prescriptionRequired);

        $product = new Product();
        $productVariant->setProduct($product);

        $orderItem = new OrderItem();
        $orderItem->setVariant($productVariant);

        $order->addItem($orderItem);

        return $orderItem;
    }

    private function createOrderWithChannel(
        bool $addPrescriptionMedicationDirectlyToCart,
    ): Order {
        $channel = new Channel();
        $channel->setCode('dok_gb');
        $channel->setAddPrescriptionMedicationDirectlyToCart($addPrescriptionMedicationDirectlyToCart);

        $order = new Order();
        $order->setChannel($channel);

        return $order;
    }

    private function addProductTypeToOrderItem(
        OrderItem $orderItem,
        ProductType $productType,
    ): void {
        /** @var ProductInterface $product */
        $product = $orderItem->getProduct();

        $productAttribute = new ProductAttribute();
        $productAttribute->setCode(ProductAttribute::ATTRIBUTE_CODE_TYPE);
        $productAttribute->setStorageType('json');

        $typeAttributeValue = new ProductAttributeValue();
        $typeAttributeValue->setAttribute($productAttribute);
        $typeAttributeValue->setValue([$productType->value]);

        $product->addAttribute($typeAttributeValue);
    }
}
