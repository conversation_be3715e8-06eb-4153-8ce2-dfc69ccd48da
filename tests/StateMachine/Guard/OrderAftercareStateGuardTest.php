<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Guard;

use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Product\ProductVariant;
use App\Entity\Shipping\Shipment;
use App\StateMachine\Guard\OrderAftercareStateGuard;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Sylius\Component\Core\OrderShippingStates;
use Sylius\Component\Shipping\Model\ShipmentInterface;

final class OrderAftercareStateGuardTest extends TestCase
{
    private OrderAftercareStateGuard $guard;

    protected function setUp(): void
    {
        $this->guard = new OrderAftercareStateGuard(
            $this->createMock(LoggerInterface::class)
        );
    }

    /**
     * @dataProvider provideCanCreate
     */
    public function testCanCreate(Order $order, $expectedResult): void
    {
        self::assertEquals($expectedResult, $this->guard->canCreate($order));
    }

    public function provideCanCreate(): iterable
    {
        $productVariant = new ProductVariant();
        $productVariant->setPrescriptionRequired(true);

        $orderItem = new OrderItem();
        $orderItem->setVariant($productVariant);

        $order = new Order();
        $order->addItem($orderItem);

        yield 'order with prescription required variant' => [$order, true];

        $order = new Order();

        yield 'order without prescription required variant' => [$order, false];
    }

    /**
     * @dataProvider provideCanSkip
     */
    public function testCanSkip(Order $order, $expectedResult): void
    {
        self::assertEquals($expectedResult, $this->guard->canSkip($order));
    }

    public function provideCanSkip(): iterable
    {
        $order = new Order();
        $order->setParentOrder(new Order());

        yield 'order with parent order' => [$order, true];

        $productVariant = new ProductVariant();
        $productVariant->setPrescriptionRequired(false);

        $orderItem = new OrderItem();
        $orderItem->setVariant($productVariant);

        $order = new Order();
        $order->addItem($orderItem);

        yield 'order without prescription required variant' => [$order, true];

        $productVariant->setPrescriptionRequired(true);
        $orderItem->setVariant($productVariant);

        $order = new Order();
        $order->addItem($orderItem);

        yield 'order with prescription required variant' => [$order, false];
    }

    /**
     * @dataProvider provideCanOpen
     */
    public function testCanOpen(Order $order, $expectedResult): void
    {
        self::assertEquals($expectedResult, $this->guard->canOpen($order));
    }

    public function provideCanOpen(): iterable
    {
        $order = new Order();
        $shipment = new Shipment();
        $shipment->setState(ShipmentInterface::STATE_SHIPPED);
        $order->addShipment($shipment);

        yield 'order with shipped shipment' => [$order, true];

        $order = new Order();
        $order->setShippingState(OrderShippingStates::STATE_READY);

        yield 'order with shipping state ready' => [$order, false];
    }
}
