<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Guard;

use App\Entity\Order\Order;
use App\Entity\Shipping\Shipment;
use App\StateMachine\Guard\OrderShippingStateGuard;
use App\StateMachine\OrderShippingStates;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\OrderItemFactory;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Sylius\Component\Core\OrderShippingStates as BaseOrderShippingStates;

final class OrderShippingStateGuardTest extends TestCase
{
    private OrderShippingStateGuard $guard;

    protected function setUp(): void
    {
        $this->guard = new OrderShippingStateGuard($this->createStub(LoggerInterface::class));
    }

    /**
     * @dataProvider provideCanCreate
     */
    public function testCanCreate(Order $order): void
    {
        self::assertTrue($this->guard->canCreate($order));
    }

    public function provideCanCreate(): iterable
    {
        $order = new Order();
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_AWAITING_PAYMENT);

        yield 'Order with 1 shipment that is awaiting_payment' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_AWAITING_PAYMENT, 5);

        yield 'Order with 5 shipments that are awaiting_payment' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanCreate
     */
    public function testInvalidCanCreate(Order $order): void
    {
        self::assertFalse($this->guard->canCreate($order));
    }

    public function provideInvalidCanCreate(): iterable
    {
        $order = new Order();

        yield 'Order with no shipments' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_AWAITING_PAYMENT);
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CANCELLED);

        yield 'Order with 1 shipment that is awaiting_payment and 1 shipment that is not awaiting_payment' => [$order];
    }

    /**
     * @dataProvider provideCanCancel
     */
    public function testCanCancel(Order $order): void
    {
        self::assertTrue($this->guard->canCancel($order));
    }

    public function provideCanCancel(): iterable
    {
        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CANCELLED);

        yield 'Order with 1 shipment that is cancelled' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CANCELLED, 5);

        yield 'Order with 5 shipments that are cancelled' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanCancel
     */
    public function testInvalidCanCancel(Order $order): void
    {
        self::assertFalse($this->guard->canCancel($order));
    }

    public function provideInvalidCanCancel(): iterable
    {
        $order = new Order();

        yield 'Order no shipments' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CANCELLED, 5);
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_READY);

        yield 'Order with 5 shipments that are cancelled and 1 shipment that is not cancelled' => [$order];
    }

    /**
     * @dataProvider provideValidOrderForCanReady
     */
    public function testCanReady(Order $order): void
    {
        self::assertTrue($this->guard->canReady($order));
    }

    public function provideValidOrderForCanReady(): iterable
    {
        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_READY);

        yield 'Order with 1 shipment that is ready' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_READY, 5);

        yield 'Order with 5 shipments that are ready' => [$order];
    }

    /**
     * @dataProvider provideInvalidOrderForTestInvalidCanReady
     */
    public function testsInvalidCanReady(Order $order): void
    {
        self::assertFalse($this->guard->canReady($order));
    }

    public function provideInvalidOrderForTestInvalidCanReady(): iterable
    {
        $order = new Order();

        yield 'Order with no shipments' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CANCELLED);

        yield 'Order 1 shipment that is not ready' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CANCELLED, 10);

        yield 'Order 10 shipment that are not ready' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_READY, 10);
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CANCELLED);

        yield 'Order 10 shipment that are ready but 1 shipment that is not ready' => [$order];
    }

    /**
     * @dataProvider provideCanAwaitPrescription
     */
    public function testCanAwaitPrescription(Order $order): void
    {
        self::assertTrue($this->guard->canAwaitPrescription($order));
    }

    public function provideCanAwaitPrescription(): iterable
    {
        $order = new Order();
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_AWAITING_PRESCRIPTION);

        yield 'Order with 1 shipment that is awaiting_prescription' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_AWAITING_PRESCRIPTION, 10);

        yield 'Order with 10 shipments that are awaiting_prescription' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanAwaitPrescription
     */
    public function testInvalidCanAwaitPrescription(Order $order): void
    {
        self::assertFalse($this->guard->canAwaitPrescription($order));
    }

    public function provideInvalidCanAwaitPrescription(): iterable
    {
        $order = new Order();

        yield 'Order with no shipments' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CART);

        yield 'Order with 1 shipment not awaiting_prescription' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CART);
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_AWAITING_PRESCRIPTION);

        yield 'Order with 1 shipment awaiting_prescription and one not awaiting_prescription' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CART);
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_AWAITING_PRESCRIPTION, 10);

        yield 'Order with 10 shipment awaiting_prescription and one not awaiting_prescription' => [$order];
    }

    /**
     * @dataProvider provideCanNotifySupplier
     */
    public function testCanNotifySupplier(Order $order): void
    {
        self::assertTrue($this->guard->canNotifySupplier($order));
    }

    public function provideCanNotifySupplier(): iterable
    {
        $order = new Order();
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_PENDING);

        yield 'Order with 1 shipment that is pending' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_PENDING, 5);

        yield 'Order with 5 shipments that are pending' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanNotifySupplier
     */
    public function testInvalidCanNotifySupplier(Order $order): void
    {
        self::assertFalse($this->guard->canNotifySupplier($order));
    }

    public function provideInvalidCanNotifySupplier(): iterable
    {
        $order = new Order();

        yield 'Order with no shipments' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CART);

        yield 'Order with 1 shipment not pending' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CART);
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_PENDING);

        yield 'Order with 1 shipment pending and one not pending' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CART);
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_PENDING, 10);

        yield 'Order with 10 shipment pending and one not pending' => [$order];
    }

    /**
     * @dataProvider provideCanShip
     */
    public function testCanShip(Order $order): void
    {
        self::assertTrue($this->guard->canShip($order));
    }

    public function provideCanShip(): iterable
    {
        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_SHIPPED);

        yield 'Order with 1 shipment that is shipped' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_SHIPPED, 5);

        yield 'Order with 5 shipments that are shipped' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanShip
     */
    public function testInvalidCanShip(Order $order): void
    {
        self::assertFalse($this->guard->canShip($order));
    }

    public function provideInvalidCanShip(): iterable
    {
        $order = new Order();

        yield 'Order with no shipments' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CART);

        yield 'Order with 1 shipment not shipped' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_SHIPPED);
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_AWAITING_PRESCRIPTION);

        yield 'Order with 1 shipment shipped and one not shipped' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CART);
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_SHIPPED, 10);

        yield 'Order with 10 shipment shipped and one not shipped' => [$order];
    }

    /**
     * @dataProvider provideCanMarkForReshipment
     */
    public function testCanMarkForReshipment(Order $order): void
    {
        self::assertTrue($this->guard->canMarkForReshipment($order));
    }

    public function provideCanMarkForReshipment(): iterable
    {
        $order = new Order();
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_PENDING);

        yield 'Order with 1 shipment that is pending' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_PENDING);
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_RETURNED);

        yield 'Order with 1 shipment that is pending, 1 returned' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_PENDING);
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_RETURNED);
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CANCELLED);

        yield 'Order with 1 shipment that is pending, 1 returned, 1 cancelled' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_PENDING, 5);

        yield 'Order with 5 shipments that are pending' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanMarkForReshipment
     */
    public function testInvalidCanMarkForReshipment(Order $order): void
    {
        self::assertFalse($this->guard->canMarkForReshipment($order));
    }

    public function provideInvalidCanMarkForReshipment(): iterable
    {
        $order = new Order();

        yield 'Order with no shipments' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CART);

        yield 'Order with 1 shipment not pending' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CART);
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_PENDING);

        yield 'Order with 1 shipment pending and one not pending' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CART);
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_PENDING, 10);

        yield 'Order with 10 shipments pending and one not pending' => [$order];
    }

    /**
     * @dataProvider provideCanReturn
     */
    public function testCanReturn(Order $order): void
    {
        self::assertTrue($this->guard->canReturn($order));
    }

    public function provideCanReturn(): iterable
    {
        $order = new Order();
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_RETURNED);

        yield 'Order with 1 shipment that is returned' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_RETURNED, 5);

        yield 'Order with 5 shipments that are returned' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanReturn
     */
    public function testInvalidCanReturn(Order $order): void
    {
        self::assertFalse($this->guard->canReturn($order));
    }

    public function provideInvalidCanReturn(): iterable
    {
        $order = new Order();

        yield 'Order with no shipments' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CART);

        yield 'Order with 1 shipment not returned' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CART);
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_RETURNED);

        yield 'Order with 1 shipment returned and one not returned' => [$order];

        $order = new Order();
        $this->createShipmentsWithStatus($order, BaseOrderShippingStates::STATE_CART);
        $this->createShipmentsWithStatus($order, OrderShippingStates::STATE_RETURNED, 10);

        yield 'Order with 10 shipment returned and one not returned' => [$order];
    }

    private function createShipmentsWithStatus(
        Order $order,
        string $shipmentState,
        int $count = 1,
    ): void {
        for ($i = 0; $i < $count; ++$i) {
            $shipment = new Shipment();
            $shipment->setState($shipmentState);

            $order->addShipment($shipment);

            $orderItem = OrderItemFactory::createPrefilled(['order' => $order]);
            $unit = OrderFactory::createOrderItemUnit([
                'orderItem' => $orderItem,
                'shipment' => $shipment,
            ]);

            $shipment->addUnit($unit);
        }
    }
}
