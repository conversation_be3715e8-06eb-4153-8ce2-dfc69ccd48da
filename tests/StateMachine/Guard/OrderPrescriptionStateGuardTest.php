<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Guard;

use App\Entity\Channel\Channel;
use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Order\PreferredOrderItem;
use App\Entity\Product\Product;
use App\Entity\Product\ProductInterface;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariant;
use App\Entity\Supplier\SupplierDoctor;
use App\Repository\SupplierDoctorRepository;
use App\Repository\SupplierDoctorRepositoryInterface;
use App\StateMachine\Guard\OrderPrescriptionStateGuard;
use App\Tests\Util\Factory\ProductFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Sylius\Component\Core\OrderPaymentStates;

final class OrderPrescriptionStateGuardTest extends TestCase
{
    private SupplierDoctorRepositoryInterface&MockObject $supplierDoctorRepository;
    private OrderPrescriptionStateGuard $guard;

    protected function setUp(): void
    {
        $this->supplierDoctorRepository = $this->createMock(SupplierDoctorRepository::class);

        $this->supplierDoctorRepository
            ->method('findOneBy')
            ->willReturnCallback(static function (array $criteria) {
                if (!isset($criteria['registrationNumber'])) {
                    return null;
                }

                if (!in_array($criteria['registrationNumber'], ['**********', 'MCI418956'], true)) {
                    return null;
                }

                $supplierDoctor = new SupplierDoctor();
                $supplierDoctor->setRegistrationNumber($criteria['registrationNumber']);

                return $supplierDoctor;
            });

        $this->guard = new OrderPrescriptionStateGuard($this->createStub(LoggerInterface::class), $this->supplierDoctorRepository);
    }

    /**
     * @dataProvider provideCanOrder
     */
    public function testCanOrder(Order $order): void
    {
        self::assertTrue($this->guard->canOrder($order));
    }

    public function provideCanOrder(): iterable
    {
        $productVariant = new ProductVariant();
        $productVariant->setPrescriptionRequired(true);

        $orderItem = new OrderItem();
        $orderItem->setVariant($productVariant);

        $order = new Order();
        $order->addItem($orderItem);

        yield 'Order with productVariant prescriptionRequired=true' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanOrder
     */
    public function testInvalidCanOrder(Order $order): void
    {
        self::assertFalse($this->guard->canOrder($order));
    }

    public function provideInvalidCanOrder(): iterable
    {
        $productVariant = new ProductVariant();
        $productVariant->setPrescriptionRequired(false);

        $orderItem = new OrderItem();
        $orderItem->setVariant($productVariant);

        $order = new Order();
        $order->addItem($orderItem);

        yield 'Order with productVariant prescriptionRequired=false' => [$order];
    }

    /**
     * @dataProvider provideValidOrderForCanReady
     */
    public function testCanReadyReturnsTrueForValidOrder(Order $order): void
    {
        self::assertTrue($this->guard->canReady($order));
    }

    public function provideValidOrderForCanReady(): iterable
    {
        $order = new Order();
        $order->setPaymentState(OrderPaymentStates::STATE_AUTHORIZED);

        yield 'Order with paymentState authorized' => [$order];

        $order = new Order();
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);

        yield 'Order with paymentState paid' => [$order];
    }

    /**
     * @dataProvider provideInvalidOrderForCanReady
     */
    public function testCanReadyThrowsExceptionForInvalidOrder(Order $order): void
    {
        self::assertFalse($this->guard->canReady($order));
    }

    public function provideInvalidOrderForCanReady(): iterable
    {
        $order = new Order();
        $order->setPaymentState(OrderPaymentStates::STATE_AWAITING_PAYMENT);

        yield 'Order with paymentState awaiting_payment' => [$order];

        $order = new Order();
        $order->setPaymentState(OrderPaymentStates::STATE_CANCELLED);

        yield 'Order with paymentState cancelled' => [$order];

        $order = new Order();
        $order->setPaymentState(OrderPaymentStates::STATE_REFUNDED);

        yield 'Order with paymentState refunded' => [$order];

        $order = new Order();
        $order->setPaymentState(OrderPaymentStates::STATE_PARTIALLY_PAID);

        yield 'Order with paymentState partially_paid' => [$order];

        $order = new Order();
        $order->setPaymentState(OrderPaymentStates::STATE_PARTIALLY_AUTHORIZED);

        yield 'Order with paymentState partially_authorized' => [$order];

        $order = new Order();
        $order->setPaymentState(OrderPaymentStates::STATE_PARTIALLY_REFUNDED);

        yield 'Order with paymentState partially_refunded' => [$order];
    }

    /**
     * @dataProvider provideCanFollowUpOrder
     */
    public function testCanFollowUpOrder(Order $order): void
    {
        self::assertTrue($this->guard->canFollowUpOrder($order));
    }

    public function provideCanFollowUpOrder(): iterable
    {
        $order = new Order();
        $followUpOrder = new Order();

        $order->setFollowUpOrder($followUpOrder);

        yield 'Order that is an follow-up order' => [$followUpOrder];
    }

    /**
     * @dataProvider provideInvalidCanFollowUpOrder
     */
    public function testInvalidCanFollowUpOrder(Order $order): void
    {
        self::assertFalse($this->guard->canFollowUpOrder($order));
    }

    public function provideInvalidCanFollowUpOrder(): iterable
    {
        $order = new Order();

        yield 'Regular order' => [$order];

        $order = new Order();
        $followUpOrder = new Order();

        $order->setFollowUpOrder($followUpOrder);

        yield 'Regular order with an follow-up order as child' => [$order];
    }

    /**
     * @dataProvider provideCanApprove
     */
    public function testCanApprove(Order $order): void
    {
        self::assertTrue($this->guard->canApprove($order));
    }

    public function provideCanApprove(): iterable
    {
        $medicationChannel = new Channel();
        $medicationChannel->setAddPrescriptionMedicationDirectlyToCart(true);

        $consultChannel = new Channel();
        $consultChannel->setAddPrescriptionMedicationDirectlyToCart(false);

        $productVariant = $this->createProductVariant(true, ProductType::MEDICATION);
        $orderItem = $this->createOrderItem($productVariant, 'usageAdvice');

        $order = new Order();
        $order->setChannel($medicationChannel);
        $order->addItem($orderItem);

        yield 'Order in medication channel with productVariant prescriptionRequired=true and usageAdvice filled' => [$order];

        $productVariant = $this->createProductVariant(false, ProductType::MEDICATION);
        $orderItem = $this->createOrderItem($productVariant, null);

        $order = new Order();
        $order->setChannel($medicationChannel);
        $order->addItem($orderItem);

        yield 'Order in medication channel with productVariant no prescription required and no usage advice' => [$order];

        $productVariant = $this->createProductVariant(true, ProductType::CONSULT);
        $orderItem = $this->createOrderItem($productVariant);

        $productVariantMedication = $this->createProductVariant(true, ProductType::MEDICATION);
        $childOrderItem = $this->createOrderItem($productVariantMedication, 'usageAdvice');
        $orderItem->addChildItem($childOrderItem);

        $order = new Order();
        $order->setChannel($medicationChannel);
        $order->addItem($orderItem);
        yield 'Order in medication channel with one child order item with usage advice' => [$order];

        $productVariant = $this->createProductVariant(true, ProductType::MEDICATION);
        $orderItem = $this->createOrderItem($productVariant, 'usageAdvice');

        $order = new Order();
        $order->setChannel($consultChannel);
        $order->addItem($orderItem);

        yield 'Order in consult channel with productVariant prescriptionRequired=true and usageAdvice filled' => [$order];

        $productVariant = $this->createProductVariant(false, ProductType::MEDICATION);
        $orderItem = $this->createOrderItem($productVariant, null);

        $order = new Order();
        $order->setChannel($consultChannel);
        $order->addItem($orderItem);

        yield 'Order in consult channel with productVariant no prescription required and no usage advice' => [$order];

        $productVariant = $this->createProductVariant(true, ProductType::CONSULT);
        $orderItem = $this->createOrderItem($productVariant);

        $preferredItem = $this->createProductVariant(true, ProductType::MEDICATION);
        $preferredOrderItem = new PreferredOrderItem($orderItem, $preferredItem, 1, 'usageadvice');
        $orderItem->addPreferredItem($preferredOrderItem);

        $order = new Order();
        $order->setChannel($consultChannel);
        $order->addItem($orderItem);
        yield 'Order in consult channel with preferred order item with usage advice' => [$order];

        $medicationChannel = new Channel();
        $medicationChannel->setAddPrescriptionMedicationDirectlyToCart(true);

        $consultChannel = new Channel();
        $consultChannel->setAddPrescriptionMedicationDirectlyToCart(false);

        $order = new Order();
        $order->setDoctorName('test doctor');
        $order->setDoctorRegistrationNumber('**********');
        $order->setChannel($medicationChannel);

        $productVariant = $this->createProductVariant(true, ProductType::MEDICATION);
        $orderItem = $this->createOrderItem($productVariant, 'usageAdvice');
        $order->addItem($orderItem);

        $productVariant = $this->createProductVariant(true, ProductType::SERVICE);
        $productVariant->getProduct()->setCode(ProductInterface::SERVICE_PRESCRIPTION_CODE);
        $orderItem = $this->createOrderItem($productVariant, 'usageAdvice');
        $order->addItem($orderItem);

        yield 'Order with service prescription requires a valid doctor' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanApprove
     */
    public function testInvalidCanApprove(Order $order): void
    {
        self::assertFalse($this->guard->canApprove($order));
    }

    public function provideInvalidCanApprove(): iterable
    {
        $medicationChannel = new Channel();
        $medicationChannel->setAddPrescriptionMedicationDirectlyToCart(true);

        $consultChannel = new Channel();
        $consultChannel->setAddPrescriptionMedicationDirectlyToCart(false);

        $product = new Product();
        $productVariant = new ProductVariant();
        $productVariant->setPrescriptionRequired(true);
        $productVariant->setProduct($product);

        $orderItem = new OrderItem();
        $orderItem->setVariant($productVariant);
        $orderItem->setUsageAdvice(null);

        $order = new Order();
        $order->setChannel($medicationChannel);
        $order->addItem($orderItem);

        yield 'Order in medication channel with productVariant prescriptionRequired=true and usageAdvice is not filled' => [$order];

        $productVariant = $this->createProductVariant(true, ProductType::CONSULT);
        $orderItem = $this->createOrderItem($productVariant);

        $childVariant = $this->createProductVariant(true, ProductType::MEDICATION);
        $childOrderItem = $this->createOrderItem($childVariant);
        $orderItem->addChildItem($childOrderItem);

        $order = new Order();
        $order->setChannel($medicationChannel);
        $order->addItem($orderItem);
        yield 'Order in medication channel with consult and child variants where usageAdvice is not filled' => [$order];

        $productVariant = $this->createProductVariant(true, ProductType::CONSULT);
        $orderItem = $this->createOrderItem($productVariant);

        $order = new Order();
        $order->setChannel($medicationChannel);
        $order->addItem($orderItem);
        yield 'Order in medication channel with consult and no preferred variant(s)' => [$order];

        $product = new Product();
        $productVariant = new ProductVariant();
        $productVariant->setPrescriptionRequired(true);
        $productVariant->setProduct($product);

        $orderItem = new OrderItem();
        $orderItem->setVariant($productVariant);
        $orderItem->setUsageAdvice(null);

        $order = new Order();
        $order->setChannel($consultChannel);
        $order->addItem($orderItem);

        yield 'Order in consult channel with productVariant prescriptionRequired=true and usageAdvice is not filled' => [$order];

        $productVariant = $this->createProductVariant(true, ProductType::CONSULT);
        $orderItem = $this->createOrderItem($productVariant);

        $preferredVariant = $this->createProductVariant(true, ProductType::MEDICATION);

        $preferredOrderItem = new PreferredOrderItem($orderItem, $preferredVariant);
        $orderItem->addPreferredItem($preferredOrderItem);

        $order = new Order();
        $order->setChannel($consultChannel);
        $order->addItem($orderItem);
        yield 'Order in consult channel with consult and preferred variants where usageAdvice is not filled' => [$order];

        $productVariant = $this->createProductVariant(true, ProductType::CONSULT);
        $orderItem = $this->createOrderItem($productVariant);

        $order = new Order();
        $order->setChannel($consultChannel);
        $order->addItem($orderItem);
        yield 'Order in consult channel with consult and no preferred variant(s)' => [$order];

        $medicationChannel = new Channel();
        $medicationChannel->setAddPrescriptionMedicationDirectlyToCart(true);

        $consultChannel = new Channel();
        $consultChannel->setAddPrescriptionMedicationDirectlyToCart(false);

        $order = new Order();
        $order->setDoctorName('test doctor');
        $order->setDoctorRegistrationNumber(null);
        $order->setChannel($medicationChannel);

        $productVariant = $this->createProductVariant(true, ProductType::MEDICATION);
        $orderItem = $this->createOrderItem($productVariant, 'usageAdvice');
        $order->addItem($orderItem);

        $productVariant = $this->createProductVariant(true, ProductType::SERVICE);
        $productVariant->getProduct()->setCode(ProductInterface::SERVICE_PRESCRIPTION_CODE);
        $orderItem = $this->createOrderItem($productVariant, 'usageAdvice');
        $order->addItem($orderItem);

        yield 'Order with service prescription requires a valid doctor' => [$order];
    }

    /**
     * @dataProvider provideCanSkip
     */
    public function testCanSkip(Order $order): void
    {
        self::assertTrue($this->guard->canSkip($order));
    }

    public function provideCanSkip(): iterable
    {
        $productVariant = new ProductVariant();
        $productVariant->setPrescriptionRequired(false);

        $orderItem = new OrderItem();
        $orderItem->setVariant($productVariant);

        $order = new Order();
        $order->addItem($orderItem);

        yield 'Order with productVariant only prescriptionRequired=false' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanSkip
     */
    public function testInvalidCanSkip(Order $order): void
    {
        self::assertFalse($this->guard->canSkip($order));
    }

    public function provideInvalidCanSkip(): iterable
    {
        $productVariant = new ProductVariant();
        $productVariant->setPrescriptionRequired(true);

        $orderItem = new OrderItem();
        $orderItem->setVariant($productVariant);

        $order = new Order();
        $order->addItem($orderItem);

        yield 'Order with productVariant only prescriptionRequired=true' => [$order];

        $productVariant = new ProductVariant();
        $productVariant->setPrescriptionRequired(true);

        $orderItem = new OrderItem();
        $orderItem->setVariant($productVariant);

        $order = new Order();
        $order->addItem($orderItem);

        $productVariant = new ProductVariant();
        $productVariant->setPrescriptionRequired(false);

        $orderItem = new OrderItem();
        $orderItem->setVariant($productVariant);

        $order->addItem($orderItem);

        yield 'Order with productVariant prescriptionRequired=true and productVariant prescriptionRequired=false' => [$order];
    }

    private function createProductVariant(bool $prescriptionRequired, ProductType $productType): ProductVariant
    {
        $product = new Product();
        ProductFactory::setType($product, $productType->value);
        $productVariant = new ProductVariant();
        $productVariant->setPrescriptionRequired($prescriptionRequired);
        $productVariant->setProduct($product);

        return $productVariant;
    }

    private function createOrderItem(ProductVariant $productVariant, ?string $usageAdvice = null): OrderItem
    {
        $orderItem = new OrderItem();
        $orderItem->setVariant($productVariant);
        $orderItem->setUsageAdvice($usageAdvice);

        return $orderItem;
    }
}
