<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Guard;

use App\Entity\Order\Order;
use App\Entity\Payment\Payment;
use App\Entity\Refund\RefundPayment;
use App\StateMachine\Guard\OrderPaymentStateGuard;
use App\Tests\Mocks\Entity\TestOrder;
use App\Tests\Util\Factory\PaymentFactory;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Sylius\Component\Payment\Model\PaymentInterface;

final class OrderPaymentStateGuardTest extends TestCase
{
    private OrderPaymentStateGuard $guard;

    protected function setUp(): void
    {
        $this->guard = new OrderPaymentStateGuard($this->createStub(LoggerInterface::class));
    }

    /**
     * @dataProvider provideCanPartiallyAuthorize
     */
    public function testCanPartiallyAuthorize(Order $order): void
    {
        self::assertTrue($this->guard->canPartiallyAuthorize($order));
    }

    public function provideCanPartiallyAuthorize(): iterable
    {
        $order = new TestOrder();
        $order->setTotal(300);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_AUTHORIZED, 200);

        yield 'Order with total 200 with 1 authorized payment with amount 100' => [$order];

        $order = new TestOrder();
        $order->setTotal(400);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_AUTHORIZED, 200);
        $this->createPaymentForOrder($order, PaymentInterface::STATE_AUTHORIZED, 150);

        yield 'Order with total 400 with 1 authorized payment with amount 200 and 1 with amount 150' => [$order];

        $order = new TestOrder();
        $order->setTotal(100);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_AUTHORIZED, 150);
        $this->createPaymentForOrder($order, PaymentInterface::STATE_COMPLETED, 150);

        yield 'Order with an authorized and completed payment' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanPartiallyAuthorize
     */
    public function testInvalidCanPartiallyAuthorize(Order $order): void
    {
        self::assertFalse($this->guard->canPartiallyAuthorize($order));
    }

    public function provideInvalidCanPartiallyAuthorize(): iterable
    {
        $order = new Order();

        $this->createPaymentForOrder($order, PaymentInterface::STATE_COMPLETED);

        yield 'Order with 1 completed payment' => [$order];

        $order = new TestOrder();
        $order->setTotal(100);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_AUTHORIZED, 0);

        yield 'Order with 0 authorized amount' => [$order];
    }

    /**
     * @dataProvider provideCanAuthorize
     */
    public function testCanAuthorize(Order $order): void
    {
        self::assertTrue($this->guard->canAuthorize($order));
    }

    public function provideCanAuthorize(): iterable
    {
        $order = new TestOrder();
        $order->setTotal(100);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_AUTHORIZED);

        yield 'Order with amount 100 and 1 authorized payment with amount 100' => [$order];

        $order = new TestOrder();
        $order->setTotal(100);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_AUTHORIZED);
        $this->createPaymentForOrder($order, PaymentInterface::STATE_AUTHORIZED);

        yield 'Order with amount 200 and 2 authorized payments with amount 100' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanAuthorize
     */
    public function testInvalidCanAuthorize(Order $order): void
    {
        self::assertFalse($this->guard->canAuthorize($order));
    }

    public function provideInvalidCanAuthorize(): iterable
    {
        $order = new TestOrder();
        $order->setTotal(100);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_NEW);

        yield 'Order with amount 100 and 1 completed payment with amount 100' => [$order];
    }

    /**
     * @dataProvider provideCanPartiallyPay
     */
    public function testCanPartiallyPay(Order $order): void
    {
        self::assertTrue($this->guard->canPartiallyPay($order));
    }

    public function provideCanPartiallyPay(): iterable
    {
        $order = new TestOrder();
        $order->setTotal(200);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_CART);
        $this->createPaymentForOrder($order, PaymentInterface::STATE_COMPLETED);

        yield 'Order with amount 100 with 1 completed payment with amount 100 and 1 cart payment with amount 100' => [$order];

        $order = new TestOrder();
        $order->setTotal(150);

        // Original payment
        $this->createPaymentForOrder($order, PaymentInterface::STATE_COMPLETED, 200);

        // Refund
        $refund = new RefundPayment(
            $order,
            100,
            'EUR',
            RefundPayment::STATE_COMPLETED,
            PaymentFactory::createPaymentMethod([])
        );
        $order->addRefundPayment($refund);

        // Reship product
        $this->createPaymentForOrder($order, PaymentInterface::STATE_CART, 50);

        yield 'Order with amount 150 with 1 completed payment with amount 200, 1 refund with amount 100 and 1 cart payment with amount 50' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanPartiallyPay
     */
    public function testInvalidCanPartiallyPay(Order $order): void
    {
        self::assertFalse($this->guard->canPartiallyPay($order));
    }

    public function provideInvalidCanPartiallyPay(): iterable
    {
        $order = new Order();

        $this->createPaymentForOrder($order, PaymentInterface::STATE_AUTHORIZED);

        yield 'Order with 1 authorized payment' => [$order];

        $order = new Order();

        $this->createPaymentForOrder($order, PaymentInterface::STATE_CANCELLED);

        yield 'Order with 1 cancelled payment' => [$order];

        $order = new TestOrder();
        $order->setTotal(100);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_COMPLETED);
        $this->createPaymentForOrder($order, PaymentInterface::STATE_COMPLETED);

        yield 'Order total 100 with 2 completed payments with amount 100' => [$order];
    }

    /**
     * @dataProvider provideCanPay
     */
    public function testCanPay(Order $order): void
    {
        self::assertTrue($this->guard->canPay($order));
    }

    public function provideCanPay(): iterable
    {
        $order = new TestOrder();
        $order->setTotal(100);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_COMPLETED);

        yield 'Order with total 100 and 1 completed payment with amount 100' => [$order];

        $order = new TestOrder();
        $order->setTotal(200);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_COMPLETED);
        $this->createPaymentForOrder($order, PaymentInterface::STATE_COMPLETED);

        yield 'Order with total 200 and 2 completed payments with amount 100' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanPay
     */
    public function testInvalidCanPay(Order $order): void
    {
        self::assertFalse($this->guard->canPay($order));
    }

    public function provideInvalidCanPay(): iterable
    {
        $order = new TestOrder();
        $order->setTotal(100);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_CART, 50);
        $this->createPaymentForOrder($order, PaymentInterface::STATE_COMPLETED, 50);

        yield 'Order with total 100 with 1 completed payment with amount 50 and 1 cart payment with amount 50' => [$order];
    }

    /**
     * @dataProvider provideCanRefund
     */
    public function testCanRefund(Order $order): void
    {
        self::assertTrue($this->guard->canRefund($order));
    }

    public function provideCanRefund(): iterable
    {
        $order = new TestOrder();
        $order->setTotal(100);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_REFUNDED);

        yield 'Order with total 100 and 1 refunded payment with amount 100' => [$order];

        $order = new TestOrder();
        $order->setTotal(100);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_REFUNDED, 50);
        $this->createPaymentForOrder($order, PaymentInterface::STATE_REFUNDED, 50);

        yield 'Order with total 100 and 2 refunded payments with amount 50' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanRefund
     */
    public function testInvalidCanRefund(Order $order): void
    {
        self::assertFalse($this->guard->canRefund($order));
    }

    public function provideInvalidCanRefund(): iterable
    {
        $order = new TestOrder();
        $order->setTotal(100);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_COMPLETED);
        $this->createPaymentForOrder($order, PaymentInterface::STATE_REFUNDED, 50);

        yield 'Order with total 100 and 1 completed payment with amount 100 and 1 refunded payment with 50' => [$order];
    }

    /**
     * @dataProvider provideCanCancel
     */
    public function testCanCancel(Order $order): void
    {
        self::assertTrue($this->guard->canCancel($order));
    }

    public function provideCanCancel(): iterable
    {
        $order = new TestOrder();
        $order->setTotal(100);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_CANCELLED);

        yield 'Order with total 100 and 1 cancelled payment with amount 100' => [$order];

        $order = new TestOrder();
        $order->setTotal(100);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_CANCELLED, 200);

        yield 'Order with total 100 and 1 cancelled payment with amount 200' => [$order];

        $order = new TestOrder();
        $order->setTotal(200);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_CANCELLED);
        $this->createPaymentForOrder($order, PaymentInterface::STATE_CANCELLED);

        yield 'Order with total 200 and 2 cancelled payments with amount 100' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanCancel
     */
    public function testInvalidCanCancel(Order $order): void
    {
        self::assertFalse($this->guard->canCancel($order));
    }

    public function provideInvalidCanCancel(): iterable
    {
        $order = new TestOrder();
        $order->setTotal(100);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_CANCELLED, 50);
        $this->createPaymentForOrder($order, PaymentInterface::STATE_CART);

        yield 'Order with total 100 and 1 cancelled payment with amount 50 and 1 cart payment with amount 100' => [$order];

        $order = new TestOrder();
        $order->setTotal(100);

        $this->createPaymentForOrder($order, PaymentInterface::STATE_CART);

        yield 'Order with total 100 and 1 cart payment with amount 100' => [$order];
    }

    /**
     * @dataProvider provideCanRequestPayment
     */
    public function testCanRequestPayment(Order $order): void
    {
        self::assertTrue($this->guard->canRequestPayment($order));
    }

    public function provideCanRequestPayment(): iterable
    {
        $order = new Order();

        $this->createPaymentForOrder($order, PaymentInterface::STATE_NEW);

        yield 'Order with 1 new payment' => [$order];

        $order = new Order();

        $this->createPaymentForOrder($order, PaymentInterface::STATE_NEW);
        $this->createPaymentForOrder($order, PaymentInterface::STATE_NEW);

        yield 'Order with 2 new payments' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanRequestPayment
     */
    public function testInvalidCanRequestPayment(Order $order): void
    {
        self::assertFalse($this->guard->canRequestPayment($order));
    }

    public function provideInvalidCanRequestPayment(): iterable
    {
        $order = new Order();

        $this->createPaymentForOrder($order, PaymentInterface::STATE_NEW);
        $this->createPaymentForOrder($order, PaymentInterface::STATE_CART);

        yield 'Order with 1 new payment and 1 cart payment' => [$order];
    }

    private function createPaymentForOrder(
        Order $order,
        string $state,
        int $amount = 100,
    ): void {
        $payment = new Payment();
        $payment->setState($state);
        $payment->setAmount($amount);

        $order->addPayment($payment);
    }
}
