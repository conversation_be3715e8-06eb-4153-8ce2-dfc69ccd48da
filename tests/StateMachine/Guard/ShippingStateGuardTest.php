<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Guard;

use App\Entity\Order\Order;
use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShipmentInterface;
use App\StateMachine\Guard\ShippingStateGuard;
use App\StateMachine\OrderPrescriptionStates;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Sylius\Component\Core\OrderPaymentStates;
use Sylius\Component\Order\Model\OrderInterface as SyliusOrderInterface;

final class ShippingStateGuardTest extends TestCase
{
    private ShippingStateGuard $guard;

    protected function setUp(): void
    {
        $this->guard = new ShippingStateGuard($this->createStub(LoggerInterface::class));
    }

    /**
     * @dataProvider provideCanCreateOrder
     */
    public function testCanCreate(ShipmentInterface $shipment): void
    {
        self::assertTrue($this->guard->canCreate($shipment));
    }

    public function provideCanCreateOrder(): iterable
    {
        $order = new Order();
        $order->setState(SyliusOrderInterface::STATE_NEW);
        $order->setPaymentState(OrderPaymentStates::STATE_AWAITING_PAYMENT);

        $shipment = new Shipment();
        $shipment->setOrder($order);

        yield 'Order with state new and payment state cart' => [$shipment];
    }

    /**
     * @dataProvider provideInvalidCanCreateOrders
     */
    public function testInvalidCanCreate(ShipmentInterface $shipment): void
    {
        self::assertFalse($this->guard->canCreate($shipment));
    }

    public function provideInvalidCanCreateOrders(): iterable
    {
        $order = new Order();
        $order->setState(SyliusOrderInterface::STATE_CANCELLED);
        $order->setPaymentState(OrderPaymentStates::STATE_CART);

        $shipment = new Shipment();
        $shipment->setOrder($order);

        yield 'Order with state cancelled and payment state cart' => [$shipment];

        $order = new Order();
        $order->setState(SyliusOrderInterface::STATE_CANCELLED);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);

        $shipment = new Shipment();
        $shipment->setOrder($order);

        yield 'Order with state cancelled and payment state paid' => [$shipment];
    }

    /**
     * @dataProvider provideValidOrderForCanReady
     */
    public function testCanReady(ShipmentInterface $shipment): void
    {
        self::assertTrue($this->guard->canReady($shipment));
    }

    public function provideValidOrderForCanReady(): iterable
    {
        $order = new Order();
        $order->setState(SyliusOrderInterface::STATE_NEW);
        $order->setPaymentState(OrderPaymentStates::STATE_AUTHORIZED);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);

        $shipment = new Shipment();
        $shipment->setOrder($order);

        yield 'Order with state new and payment state authorized and prescription approved' => [$shipment];

        $order->setPaymentState(OrderPaymentStates::STATE_PAID);

        yield 'Order with state new and payment state paid and prescription approved' => [$shipment];

        $order->setPaymentState(OrderPaymentStates::STATE_AUTHORIZED);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_SKIPPED);

        yield 'Order with state new and payment state authorized and prescription skipped' => [$shipment];

        $order->setPaymentState(OrderPaymentStates::STATE_PAID);

        yield 'Order with state new and payment state paid and prescription skipped' => [$shipment];
    }

    /**
     * @dataProvider provideInvalidCanReady
     */
    public function testsInvalidCanReady(ShipmentInterface $shipment): void
    {
        self::assertFalse($this->guard->canReady($shipment));
    }

    public function provideInvalidCanReady(): iterable
    {
        $order = new Order();
        $order->setState(SyliusOrderInterface::STATE_CANCELLED);

        $shipment = new Shipment();
        $shipment->setOrder($order);

        yield 'Order with state cancelled' => [$shipment];

        $order = new Order();
        $order->setState(SyliusOrderInterface::STATE_NEW);
        $order->setPaymentState(OrderPaymentStates::STATE_AWAITING_PAYMENT);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);

        $shipment = new Shipment();
        $shipment->setOrder($order);

        yield 'Order with state new and payment state awaiting and prescription approved' => [$shipment];
    }

    /**
     * @dataProvider provideCanAwaitPrescription
     */
    public function testCanAwaitPrescription(ShipmentInterface $shipment): void
    {
        self::assertTrue($this->guard->canAwaitPrescription($shipment));
    }

    public function provideCanAwaitPrescription(): iterable
    {
        $order = new Order();
        $shipment = new Shipment();
        $shipment->setOrder($order);

        $orderState = SyliusOrderInterface::STATE_NEW;
        $paymentStates = [OrderPaymentStates::STATE_AUTHORIZED, OrderPaymentStates::STATE_PAID];
        $prescriptionStates = [OrderPrescriptionStates::STATE_AWAITING_PAYMENT, OrderPrescriptionStates::STATE_READY_FOR_CONSULT];

        $order->setState($orderState);
        foreach ($paymentStates as $paymentState) {
            $order->setPaymentState($paymentState);

            foreach ($prescriptionStates as $prescriptionState) {
                $order->setPrescriptionState($prescriptionState);

                yield "state $orderState, paymentState $paymentState and prescriptionState $prescriptionState" => [$shipment];
            }
        }
    }

    /**
     * @dataProvider provideInvalidCanAwaitPrescription
     */
    public function testInvalidCanAwaitPrescription(ShipmentInterface $shipment): void
    {
        self::assertFalse($this->guard->canAwaitPrescription($shipment));
    }

    public function provideInvalidCanAwaitPrescription(): iterable
    {
        $order = new Order();

        $shipment = new Shipment();
        $shipment->setOrder($order);

        yield 'Order with orderState, paymentState and prescriptionState cart' => [$shipment];

        $order = new Order();
        $order->setState(SyliusOrderInterface::STATE_CANCELLED);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);

        $shipment = new Shipment();
        $shipment->setOrder($order);

        yield 'Order with orderState cancelled, paymentState paid and prescriptionState approved' => [$shipment];

        $order = new Order();
        $order->setState(SyliusOrderInterface::STATE_NEW);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_APPROVED);

        $shipment = new Shipment();
        $shipment->setOrder($order);

        yield 'Order with orderState new, paymentState paid and prescriptionState approved' => [$shipment];

        $order = new Order();
        $order->setState(SyliusOrderInterface::STATE_NEW);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_DECLINED);

        $shipment = new Shipment();
        $shipment->setOrder($order);

        yield 'Order with orderState new, paymentState paid and prescriptionState declined' => [$shipment];

        $order = new Order();
        $order->setState(SyliusOrderInterface::STATE_NEW);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_SKIPPED);

        $shipment = new Shipment();
        $shipment->setOrder($order);

        yield 'Order with orderState new, paymentState paid and prescriptionState skipped' => [$shipment];

        $order = new Order();
        $order->setState(SyliusOrderInterface::STATE_CANCELLED);
        $order->setPaymentState(OrderPaymentStates::STATE_PAID);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_AWAITING_PAYMENT);

        $shipment = new Shipment();
        $shipment->setOrder($order);

        yield 'Order with orderState cancelled, paymentState paid and prescriptionState new' => [$shipment];
    }
}
