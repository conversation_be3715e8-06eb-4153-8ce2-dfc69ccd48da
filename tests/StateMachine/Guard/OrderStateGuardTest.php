<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Guard;

use App\Entity\Order\Order;
use App\Entity\Shipping\ShipmentInterface;
use App\StateMachine\Guard\OrderStateGuard;
use App\StateMachine\OrderShippingStates;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\OrderItemFactory;
use App\Tests\Util\Factory\OrderItemRefundFactory;
use App\Tests\Util\Factory\OrderItemUnitFactory;
use App\Tests\Util\Factory\ShipmentFactory;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Sylius\Component\Core\OrderCheckoutStates as BaseOrderCheckoutStates;
use Sylius\Component\Core\OrderShippingStates as BaseOrderShippingStates;
use Sylius\Component\Shipping\Model\ShipmentInterface as SyliusShipmentInterface;

final class OrderStateGuardTest extends TestCase
{
    private const array INVALID_SHIPMENT_STATES_FOR_CANCEL = [
        SyliusShipmentInterface::STATE_SHIPPED,
        SyliusShipmentInterface::STATE_READY,
        ShipmentInterface::STATE_PENDING,
        ShipmentInterface::STATE_PROCESSING,
        ShipmentInterface::STATE_RETURNED,
    ];

    private const array VALID_SHIPMENT_STATES_FOR_CANCEL = [
        SyliusShipmentInterface::STATE_CART,
        ShipmentInterface::STATE_AWAITING_PAYMENT,
        ShipmentInterface::STATE_AWAITING_FRAUD_CHECK,
        ShipmentInterface::STATE_AWAITING_PRESCRIPTION,
        SyliusShipmentInterface::STATE_CANCELLED,
    ];
    private OrderStateGuard $guard;

    protected function setUp(): void
    {
        $this->guard = new OrderStateGuard($this->createStub(LoggerInterface::class));
    }

    /**
     * @dataProvider provideCanCompleteCheckout
     */
    public function testCanCompleteCheckout(Order $order): void
    {
        self::assertTrue($this->guard->canCompleteCheckout($order));
    }

    public function provideCanCompleteCheckout(): iterable
    {
        $order = new Order();
        $order->setCheckoutState(BaseOrderCheckoutStates::STATE_COMPLETED);

        yield 'Order with checkoutState completed' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanCompleteCheckout
     */
    public function testInvalidCanCompleteCheckout(Order $order): void
    {
        self::assertFalse($this->guard->canCompleteCheckout($order));
    }

    public function provideInvalidCanCompleteCheckout(): iterable
    {
        $order = new Order();
        $order->setCheckoutState(BaseOrderCheckoutStates::STATE_CART);

        yield 'Order with checkoutState cart' => [$order];

        $order = new Order();
        $order->setCheckoutState(BaseOrderCheckoutStates::STATE_SHIPPING_SELECTED);

        yield 'Order with checkoutState shipping_selected' => [$order];

        $order = new Order();
        $order->setCheckoutState(BaseOrderCheckoutStates::STATE_ADDRESSED);

        yield 'Order with checkoutState addressed' => [$order];

        $order = new Order();
        $order->setCheckoutState(BaseOrderCheckoutStates::STATE_PAYMENT_SELECTED);

        yield 'Order with checkoutState payment_selected' => [$order];
    }

    /**
     * @dataProvider provideCanFulfill
     */
    public function testCanFulfill(Order $order): void
    {
        self::assertTrue($this->guard->canFulfill($order));
    }

    /**
     * @return iterable<string, array{0: Order}>
     */
    public function provideCanFulfill(): iterable
    {
        $order = new Order();
        $order->setShippingState(BaseOrderShippingStates::STATE_SHIPPED);

        yield 'Order with shippingState shipped' => [$order];

        $order = $this->createOrderWithCountOrderItemsAndShipments(
            [
                SyliusShipmentInterface::STATE_SHIPPED => false,
                SyliusShipmentInterface::STATE_CANCELLED => false,
                ShipmentInterface::STATE_RETURNED => false,
            ]
        );
        $order->setShippingState(BaseOrderShippingStates::STATE_PARTIALLY_SHIPPED);

        yield 'Order with shippingState partially_shipped' => [$order];

        $order = $this->createOrderWithCountOrderItemsAndShipments(
            [
                SyliusShipmentInterface::STATE_SHIPPED => true,
            ]
        );

        yield 'Order with 1 orderItem that is shipped' => [$order];

        $order = $this->createOrderWithCountOrderItemsAndShipments(
            [
                ShipmentInterface::STATE_RETURNED => false,
            ]
        );

        yield 'Order with 1 orderItem that is returned' => [$order];

        $order = $this->createOrderWithCountOrderItemsAndShipments(
            [
                SyliusShipmentInterface::STATE_CANCELLED => true,
            ]
        );

        yield 'Order with 1 orderItem that is refunded' => [$order];
    }

    /**
     * @dataProvider provideInvalidCanFulfill
     */
    public function testInvalidCanFulfill(Order $order): void
    {
        self::assertFalse($this->guard->canFulfill($order));
    }

    /**
     * @return iterable<string, array{0: Order}>
     */
    public function provideInvalidCanFulfill(): iterable
    {
        $invalidShippingStates = [
            BaseOrderShippingStates::STATE_CART,
            BaseOrderShippingStates::STATE_READY,
            BaseOrderShippingStates::STATE_CANCELLED,
            OrderShippingStates::STATE_AWAITING_PAYMENT,
            OrderShippingStates::STATE_AWAITING_PRESCRIPTION,
            OrderShippingStates::STATE_PENDING,
            OrderShippingStates::STATE_RETURNED,
        ];

        foreach ($invalidShippingStates as $invalidShippingState) {
            $order = OrderFactory::create();
            $order->setShippingState($invalidShippingState);

            yield "Order with shippingState {$invalidShippingState}" => [$order];
        }

        yield 'Order with no orderItems' => [OrderFactory::create()];

        $order = $this->createOrderWithCountOrderItemsAndShipments(
            [
                SyliusShipmentInterface::STATE_SHIPPED => false,
                ShipmentInterface::STATE_PENDING => false,
            ]
        );

        yield 'Order with 1 orderItem that is shipped and 1 pending' => [$order];

        $order = $this->createOrderWithCountOrderItemsAndShipments(
            [
                SyliusShipmentInterface::STATE_SHIPPED => false,
                SyliusShipmentInterface::STATE_CANCELLED => false,
                ShipmentInterface::STATE_RETURNED => false,
                ShipmentInterface::STATE_PENDING => false,
            ]
        );
        $order->setShippingState(BaseOrderShippingStates::STATE_PARTIALLY_SHIPPED);

        yield 'Order with shippingState partially_shipped must not have a pending shipment' => [$order];

        $order = $this->createOrderWithCountOrderItemsAndShipments(
            [
                SyliusShipmentInterface::STATE_SHIPPED => false,
                SyliusShipmentInterface::STATE_CANCELLED => false,
                ShipmentInterface::STATE_RETURNED => false,
                SyliusShipmentInterface::STATE_READY => false,
            ]
        );
        $order->setShippingState(BaseOrderShippingStates::STATE_PARTIALLY_SHIPPED);

        yield 'Order with shippingState partially_shipped must not have a ready shipment' => [$order];

        $order = $this->createOrderWithCountOrderItemsAndShipments(
            [
                SyliusShipmentInterface::STATE_SHIPPED => false,
                SyliusShipmentInterface::STATE_CANCELLED => false,
                ShipmentInterface::STATE_RETURNED => false,
                ShipmentInterface::STATE_PROCESSING => false,
            ]
        );
        $order->setShippingState(BaseOrderShippingStates::STATE_PARTIALLY_SHIPPED);

        yield 'Order with shippingState partially_shipped must not have a processing shipment' => [$order];

        $order = $this->createOrderWithCountOrderItemsAndShipments(
            [
                ShipmentInterface::STATE_RETURNED => false,
                ShipmentInterface::STATE_PENDING => false,
            ]
        );

        yield 'Order with 1 orderItem that is returned and 1 pending' => [$order];

        $order = $this->createOrderWithCountOrderItemsAndShipments(
            [
                SyliusShipmentInterface::STATE_CANCELLED => true,
                ShipmentInterface::STATE_PENDING => false,
            ]
        );

        yield 'Order with 1 orderItem that is refunded and 1 pending' => [$order];
    }

    /**
     * @dataProvider provideCanCancel
     */
    public function testCanCancel(Order $order): void
    {
        self::assertTrue($this->guard->canCancel($order));
    }

    /**
     * @return iterable<string, array{Order}>
     */
    public function provideCanCancel(): iterable
    {
        $order = OrderFactory::create();

        yield 'Order with no shipments.' => [$order];

        foreach (self::VALID_SHIPMENT_STATES_FOR_CANCEL as $validState) {
            $order = OrderFactory::create();
            $this->createShipmentForOrder($order, $validState);

            yield "Order with 1 shipment that is state {$validState}." => [$order];
        }
    }

    /**
     * @dataProvider provideInvalidCanCancel
     */
    public function testInvalidCanCancel(Order $order): void
    {
        self::assertFalse($this->guard->canCancel($order));
    }

    /**
     * @return iterable<string, array{Order}>
     */
    public function provideInvalidCanCancel(): iterable
    {
        foreach (self::INVALID_SHIPMENT_STATES_FOR_CANCEL as $invalidState) {
            $order = OrderFactory::create();
            $this->createShipmentForOrder($order, $invalidState);

            yield "Order with 1 shipment that has state {$invalidState}." => [$order];
        }
    }

    /**
     * @param array<string, bool> $orderItemShipmentStates
     */
    private function createOrderWithCountOrderItemsAndShipments(array $orderItemShipmentStates): Order
    {
        $order = OrderFactory::create();

        foreach ($orderItemShipmentStates as $orderItemShipmentState => $isRefunded) {
            $orderItem = OrderItemFactory::create();

            if ($isRefunded) {
                $orderItem->setOrderItemRefund(
                    OrderItemRefundFactory::create(
                        [
                            'orderItem' => $orderItem,
                        ]
                    )
                );
            }

            $orderItemUnit = OrderItemUnitFactory::create(
                [
                    'orderItem' => $orderItem,
                ]
            );
            $orderItemUnit->setShipment(
                ShipmentFactory::create(
                    [
                        'state' => $orderItemShipmentState,
                    ]
                )
            );

            $order->addItem($orderItem);
        }

        return $order;
    }

    private function createShipmentForOrder(
        Order $order,
        string $shipmentState,
        int $countUnits = 1,
    ): void {
        $units = [];

        for ($i = 0; $i < $countUnits; ++$i) {
            $units[] = OrderItemUnitFactory::create(
                [
                    'orderItem' => OrderItemFactory::create(),
                ]
            );
        }

        ShipmentFactory::create(
            [
                'order' => $order,
                'state' => $shipmentState,
                'units' => $units,
            ]
        );
    }
}
