<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Callback;

use App\Entity\Currency\ExchangeRate;
use App\Entity\Order\Order;
use App\Entity\Payment\Payment;
use App\StateMachine\Callback\AddExchangeRateToPayment;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Currency\Repository\ExchangeRateRepositoryInterface;

final class AddExchangeRateToPaymentTest extends TestCase
{
    /** @var ExchangeRateRepositoryInterface<ExchangeRate>&MockObject */
    private ExchangeRateRepositoryInterface&MockObject $exchangeRateRepository;

    private AddExchangeRateToPayment $addExchangeRateToPayment;

    protected function setUp(): void
    {
        $this->exchangeRateRepository = $this->createMock(ExchangeRateRepositoryInterface::class);

        $this->addExchangeRateToPayment = new AddExchangeRateToPayment(
            $this->createStub(EntityManagerInterface::class),
            $this->exchangeRateRepository,
        );
    }

    /**
     * @dataProvider provideExchangeRateForTestInvokeSetsExchangeRate
     */
    public function testInvokeSetsExchangeRate(
        float $expectedRate,
        float $actualRate,
        string $actualCurrencyCode,
    ): void {
        $order = new Order();
        $order->setCurrencyCode($actualCurrencyCode);

        $payment = new Payment();
        $payment->setOrder($order);

        $exchangeRate = new ExchangeRate();
        $exchangeRate->setRatio($actualRate);

        $this->exchangeRateRepository->method('findOneWithCurrencyPair')
            ->with('EUR', $actualCurrencyCode)
            ->willReturn($exchangeRate);

        // Act
        ($this->addExchangeRateToPayment)($payment);

        // Assert
        self::assertSame($expectedRate, $payment->getExchangeRate());
    }

    /**
     * @return iterable<string, array{0: float, 1: float, 2: string}>
     */
    public function provideExchangeRateForTestInvokeSetsExchangeRate(): iterable
    {
        yield 'ExchangeRate from EUR to SEK should return actual exchange rate.' => [
            11.27308,
            11.27308,
            'SEK',
        ];

        yield 'ExchangeRate from EUR to EUR should always return 1.' => [
            1.00,
            2.00, // This checks wether the EUR to EUR should always return 1.00 even if the exchangeRateRepository returns 2.00
            'EUR',
        ];
    }
}
