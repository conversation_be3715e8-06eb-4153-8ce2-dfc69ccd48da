<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Callback;

use App\Entity\Order\Order;
use App\Event\Enum\OrderEventName;
use App\Event\OrderEvent;
use App\Messenger\Event\EventTransport;
use App\StateMachine\Callback\DispatchOrderEvent;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use RuntimeException;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DispatchAfterCurrentBusStamp;

class DispatchOrderEventTest extends TestCase
{
    private MessageBusInterface&MockObject $messageBus;
    private DispatchOrderEvent $dispatchOrderEvent;

    public function setUp(): void
    {
        $this->messageBus = $this->createMock(MessageBusInterface::class);
        $this->dispatchOrderEvent = new DispatchOrderEvent($this->messageBus);
    }

    /**
     * @dataProvider orderEventNameProvider
     */
    public function testInvokeDispatchesOrderEvent(string $eventName): void
    {
        $order = new Order();

        $event = new EventTransport(new OrderEvent(OrderEventName::fromName($eventName), $order), $eventName);
        $this->messageBus->expects($this->once())
            ->method('dispatch')
            ->with($event)
            ->willReturn(new Envelope($event, [new DispatchAfterCurrentBusStamp()]));

        ($this->dispatchOrderEvent)($order, $eventName);
    }

    public function orderEventNameProvider(): iterable
    {
        $orderEvents = [
            'CartWasCreated',
            'CartWasUpdated',
            'OrderWasCreated',
            'OrderWasUpdated',
            'OrderPaymentWasRequested',
            'OrderWasPaid',
            'OrderWasPaidWithAuthorization',
            'OrderWasPaidAfterAuthorization',
            'OrderWasCancelled',
            'OrderWasFulfilled',
            'OrderPrescriptionResponseWasRequestedByDoctor',
            'OrderPrescriptionWasPrescribedByDoctor',
            'OrderPrescriptionWasDeclinedByDoctor',
            'OrderShipmentWasRegisteredAtSupplier',
            'OrderShipmentWasRecalledBySupplier',
            'OrderShipmentWasReturnedToSupplier',
            'OrderShipmentWasCancelledBySupplier',
            'OrderShipmentWasSentBySupplier',
            'OrderAftercareDoctorHasResponded',
        ];

        foreach ($orderEvents as $eventName) {
            yield $eventName => [$eventName];
        }
    }

    public function testInvokeThrowsRuntimeExceptionOnUnknownOrderEventName(): void
    {
        $order = new Order();

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('UnknownOrderEvent is not a valid backing value for enum App\Event\Enum\OrderEventName');

        $this->dispatchOrderEvent->__invoke($order, 'UnknownOrderEvent');
    }
}
