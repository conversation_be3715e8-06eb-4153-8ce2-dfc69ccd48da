<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Callback;

use App\Entity\Order\Order;
use App\Entity\Payment\Collection\PaymentCollection;
use App\Entity\Payment\Payment;
use App\Payment\Command\CancelAuthorizedPayment;
use App\StateMachine\Callback\CancelAuthorizedPayments;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

final class CancelAuthorizedPaymentsTest extends TestCase
{
    private MessageBusInterface&MockObject $messageBus;
    private CancelAuthorizedPayments $cancelAuthorizedPayments;

    protected function setUp(): void
    {
        $this->messageBus = $this->createMock(MessageBusInterface::class);
        $this->cancelAuthorizedPayments = new CancelAuthorizedPayments($this->messageBus);
    }

    public function testCancelAuthorizedPayments(): void
    {
        // Arrange
        $order = $this->createMock(Order::class);
        $authorizedPayment = $this->createMock(Payment::class);
        $authorizedPayment->method('getState')->willReturn('authorized');
        $authorizedPayment->method('getId')->willReturn(1);

        $nonAuthorizedPayment = $this->createMock(Payment::class);
        $nonAuthorizedPayment->method('getState')->willReturn('some_other_state');

        $order->method('getPayments')->willReturn(new PaymentCollection([$authorizedPayment, $nonAuthorizedPayment]));

        // Expect the CancelAuthorizedPayment command to be dispatched once for the authorized payment
        $this->messageBus
            ->expects($this->once())
            ->method('dispatch')
            ->with($this->callback(static function (CancelAuthorizedPayment $command) use ($authorizedPayment): bool {
                return $command->paymentId === $authorizedPayment->getId();
            }))
            ->willReturnCallback(static function (CancelAuthorizedPayment $command): Envelope {
                return new Envelope($command);
            });

        // Act
        ($this->cancelAuthorizedPayments)($order);
    }
}
