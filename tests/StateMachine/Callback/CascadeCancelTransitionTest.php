<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Callback;

use App\Entity\Order\Order;
use App\Entity\Payment\Payment;
use App\Entity\Shipping\Shipment;
use App\StateMachine\Callback\CascadeCancelTransition;
use App\StateMachine\OrderAftercareTransitions;
use App\StateMachine\OrderFraudCheckTransitions;
use App\StateMachine\OrderPrescriptionStates;
use App\StateMachine\OrderPrescriptionTransitions;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use SM\Factory\FactoryInterface;
use SM\StateMachine\StateMachineInterface;
use Sylius\Component\Order\StateResolver\StateResolverInterface;
use Sylius\Component\Payment\Model\PaymentInterface;
use Sylius\Component\Payment\PaymentTransitions;
use Sylius\Component\Shipping\Model\ShipmentInterface;
use Sylius\Component\Shipping\ShipmentTransitions;

final class CascadeCancelTransitionTest extends TestCase
{
    private FactoryInterface&MockObject $stateMachineFactoryMock;
    private EntityManagerInterface&MockObject $entityManagerMock;
    private StateResolverInterface&MockObject $orderPaymentStateResolverMock;
    private StateResolverInterface&MockObject $orderShippingStateResolverMock;

    private CascadeCancelTransition $cascadeCancelTransition;

    public function setUp(): void
    {
        $this->stateMachineFactoryMock = $this->createMock(FactoryInterface::class);
        $this->entityManagerMock = $this->createMock(EntityManagerInterface::class);
        $this->orderPaymentStateResolverMock = $this->createMock(StateResolverInterface::class);
        $this->orderShippingStateResolverMock = $this->createMock(StateResolverInterface::class);

        $this->cascadeCancelTransition = new CascadeCancelTransition(
            $this->stateMachineFactoryMock,
            $this->entityManagerMock,
            $this->orderPaymentStateResolverMock,
            $this->orderShippingStateResolverMock,
        );
    }

    public function testCanCascadeCancelTransition(): void
    {
        $payment = new Payment();
        $payment->setState(PaymentInterface::STATE_CART);

        $shipment = new Shipment();
        $shipment->setState(ShipmentInterface::STATE_CART);

        $order = new Order();
        $order->addPayment($payment);
        $order->addShipment($shipment);
        $order->setPrescriptionState(OrderPrescriptionStates::STATE_CART);

        $paymentStateMachineMock = $this->createMock(StateMachineInterface::class);
        $orderPrescriptionStateMachineMock = $this->createMock(StateMachineInterface::class);
        $orderFraudCheckStateMachineMock = $this->createMock(StateMachineInterface::class);
        $orderAftercareStateMachineMock = $this->createMock(StateMachineInterface::class);
        $shipmentStateMachineMock = $this->createMock(StateMachineInterface::class);

        $this->stateMachineFactoryMock
            ->expects(self::exactly(5))
            ->method('get')
            ->withConsecutive(
                [self::isInstanceOf(PaymentInterface::class), PaymentTransitions::GRAPH],
                [self::isInstanceOf(Order::class), OrderPrescriptionTransitions::GRAPH],
                [self::isInstanceOf(Order::class), OrderAftercareTransitions::GRAPH],
                [self::isInstanceOf(ShipmentInterface::class), ShipmentTransitions::GRAPH],
                [self::isInstanceOf(Order::class), OrderFraudCheckTransitions::GRAPH],
            )
            ->willReturnOnConsecutiveCalls(
                $paymentStateMachineMock,
                $orderPrescriptionStateMachineMock,
                $orderAftercareStateMachineMock,
                $shipmentStateMachineMock,
                $orderFraudCheckStateMachineMock,
            );

        $paymentStateMachineMock->expects(self::once())
            ->method('apply')
            ->with(PaymentTransitions::TRANSITION_CANCEL, true);

        $orderPrescriptionStateMachineMock->expects(self::once())
            ->method('apply')
            ->with(OrderPrescriptionTransitions::TRANSITION_CANCEL, true);

        $orderAftercareStateMachineMock->expects(self::once())
            ->method('apply')
            ->with(OrderAftercareTransitions::TRANSITION_CANCEL, true);

        $shipmentStateMachineMock->expects(self::once())
            ->method('apply')
            ->with(ShipmentTransitions::TRANSITION_CANCEL, true);

        $this->orderPaymentStateResolverMock->expects(self::once())
            ->method('resolve')
            ->with($order);

        $this->orderShippingStateResolverMock->expects(self::once())
            ->method('resolve')
            ->with($order);

        $orderAftercareStateMachineMock->expects(self::once())
            ->method('apply')
            ->with(OrderFraudCheckTransitions::TRANSITION_CANCEL, true);

        $this->entityManagerMock->expects(self::once())
            ->method('flush');

        ($this->cascadeCancelTransition)($order);
    }
}
