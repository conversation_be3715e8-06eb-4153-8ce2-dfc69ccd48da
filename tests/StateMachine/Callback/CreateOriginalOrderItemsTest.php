<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Callback;

use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Order\OrderItemUnit;
use App\Entity\Order\OriginalOrderItem;
use App\Entity\Order\PreferredOrderItem;
use App\Entity\Product\Product;
use App\Entity\Product\ProductVariant;
use App\StateMachine\Callback\CreateOriginalOrderItems;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class CreateOriginalOrderItemsTest extends TestCase
{
    private EntityManagerInterface&MockObject $entityManagerMock;

    private CreateOriginalOrderItems $stateMachineCallback;

    public function setUp(): void
    {
        $this->entityManagerMock = $this->createMock(EntityManagerInterface::class);

        $this->stateMachineCallback = new CreateOriginalOrderItems($this->entityManagerMock);
    }

    public function testInvokeWithEmptyItemsDoesNoEntityManagerPersistCalls(): void
    {
        $orderStub = $this->createStub(Order::class);
        $orderStub->method('getItems')
            ->willReturn(new ArrayCollection());

        $this->entityManagerMock
            ->expects(self::never())
            ->method('persist');

        ($this->stateMachineCallback)($orderStub);
    }

    public function testInvokeWithOnlyParentItemsCreatesOriginalParentItems(): void
    {
        $order = $this->createTestOrderWithParentItemsOnly();

        $this->entityManagerMock
            ->expects(self::once())
            ->method('persist')
            ->with(self::isInstanceOf(OriginalOrderItem::class));
        $this->entityManagerMock
            ->expects(self::once())
            ->method('flush');

        ($this->stateMachineCallback)($order);
    }

    public function testInvokeWithChildAndParentItemsCreatesOriginalParentAndOriginalChildItems(): void
    {
        $order = $this->createTestOrderWithParentAndChildOrderItems();

        $this->entityManagerMock
            ->expects(self::exactly(2))
            ->method('persist')
            ->with(self::isInstanceOf(OriginalOrderItem::class));
        $this->entityManagerMock
            ->expects(self::once())
            ->method('flush');

        ($this->stateMachineCallback)($order);
    }

    private function createTestOrderWithParentItemsOnly(): Order
    {
        $product = new Product();
        $product->setCode('viagra');
        $product->setCurrentLocale('en');
        $product->setName('Viagra');
        $product->setSlug('viagra');

        $productVariant = new ProductVariant();
        $productVariant->setCode('1_1_viagra_supplier_worldwide');
        $productVariant->setProduct($product);

        $orderItem = new OrderItem();
        $orderItem->addUnit(new OrderItemUnit($orderItem));
        $orderItem->setProductName('viagra');
        $orderItem->setUsageAdvice('Max of one per day');
        $orderItem->setVariant($productVariant);
        $orderItem->setVariantName('Viagra 100mg 10 caps.');

        $order = new Order();
        $order->addItem($orderItem);
        $order->setCurrencyCode('EUR');
        $order->setLocaleCode('en');

        return $order;
    }

    private function createTestOrderWithParentAndChildOrderItems(): Order
    {
        $consultProduct = new Product();
        $consultProduct->setCode('consult_erectile_dysfunction');
        $consultProduct->setCurrentLocale('en');
        $consultProduct->setName('Consult for Erectile Dysfunction');
        $consultProduct->setSlug('consult-for-erectile-dysfunction');

        $consultVariant = new ProductVariant();
        $consultVariant->setCode('consult_erectile_dysfunction');
        $consultVariant->setProduct($consultProduct);

        $product = new Product();
        $product->setCode('viagra');
        $product->setCurrentLocale('en');
        $product->setName('Viagra');
        $product->setSlug('viagra');

        $productVariant = new ProductVariant();
        $productVariant->setCode('1_1_viagra_supplier_worldwide');
        $productVariant->setProduct($product);

        $parentItem = new OrderItem();
        $parentItem->addUnit(new OrderItemUnit($parentItem));
        $parentItem->setProductName('consult_erectile_dysfunction');
        $parentItem->setVariant($consultVariant);
        $parentItem->setVariantName('Consult for Erectile Dysfunction');

        $preferredOrderItem = new PreferredOrderItem($parentItem, $productVariant);
        $parentItem->addPreferredItem($preferredOrderItem);

        $childItem = new OrderItem();
        $childItem->addUnit(new OrderItemUnit($childItem));
        $childItem->setProductName('Viagra');
        $childItem->setUsageAdvice('Max one per day.');
        $childItem->setVariant($productVariant);
        $childItem->setVariantName('Viagra 100mg 10 caps.');
        $childItem->setParentOrderItem($parentItem);

        $order = new Order();
        $order->addItem($childItem);
        $order->addItem($parentItem);
        $order->setCurrencyCode('EUR');
        $order->setLocaleCode('en');
        $order->setTokenValue('1337');

        return $order;
    }
}
