<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Callback;

use App\Entity\Shipping\Shipment;
use App\StateMachine\Callback\SetSupplierShipmentReference;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class SetSupplierShipmentReferenceTest extends TestCase
{
    private EntityManagerInterface&MockObject $entityManagerMock;
    private SetSupplierShipmentReference $setSupplierShipmentReference;

    public function setUp(): void
    {
        $this->entityManagerMock = $this->createMock(EntityManagerInterface::class);

        $this->setSupplierShipmentReference = new SetSupplierShipmentReference(
            $this->entityManagerMock
        );
    }

    public function testInvokeTerminatesWhenSupplierShipmentReferenceIsSet(): void
    {
        // Arrange
        $shipment = new Shipment();
        $shipment->setSupplierShipmentReference('0b5c4e64-be5b-43fe-aa3f-b9d21052ae35');

        // Assert
        $this->entityManagerMock
            ->expects(self::never())
            ->method('flush');

        // Act
        ($this->setSupplierShipmentReference)($shipment);
    }

    public function testInvokeSetsShipmentReferenceOnShipment(): void
    {
        // Arrange
        $shipment = new Shipment();

        // Assert
        $this->entityManagerMock
            ->expects(self::once())
            ->method('flush');

        // Act
        ($this->setSupplierShipmentReference)($shipment);

        self::assertIsString($shipment->getSupplierShipmentReference());
        self::assertNotEmpty($shipment->getSupplierShipmentReference());
    }
}
