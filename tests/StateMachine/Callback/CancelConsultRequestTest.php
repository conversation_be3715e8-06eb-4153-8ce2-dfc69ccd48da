<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Callback;

use App\Entity\Order\Order;
use App\StateMachine\Callback\ConsultSystem\CancelConsultRequest;
use DateTime;
use PHPUnit\Framework\TestCase;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;
use SuperBrave\ConsultSystemClient\ClientInterface;
use SuperBrave\ConsultSystemClient\Model\Address;
use SuperBrave\ConsultSystemClient\Model\ConsultRequest;
use SuperBrave\ConsultSystemClient\Model\Customer;
use SuperBrave\ConsultSystemClient\Model\Doctor;
use SuperBrave\ConsultSystemClient\Model\Enum\ConsultRequestType;
use SuperBrave\ConsultSystemClient\Model\Enum\Gender;
use SuperBrave\ConsultSystemClient\Model\Enum\LastMessageInteractionState;
use SuperBrave\ConsultSystemClient\Model\Enum\State;
use SuperBrave\ConsultSystemClient\Model\Store;
use Symfony\Component\HttpClient\Exception\ClientException;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\ConflictHttpException;

final class CancelConsultRequestTest extends TestCase
{
    public function testItCanCancelConsultRequest(): void
    {
        $expectedUuid = '00d5fb3a-da5f-4222-be4d-604c29fc0e67';
        $order = $this->createMock(Order::class);
        $order->expects(self::once())
            ->method('getConsultSystemReference')
            ->willReturn(Uuid::fromString($expectedUuid));

        $consultSystemClient = $this->createMock(ClientInterface::class);
        $consultRequest = $this->getConsultRequest(State::New, Uuid::fromString($expectedUuid));
        $consultSystemClient
            ->expects(self::once())
            ->method('getConsultRequest')
            ->willReturn($consultRequest);

        $consultSystemClient
            ->expects(self::once())
            ->method('cancelConsultRequest')
            ->with($expectedUuid);

        $cancelConsultRequest = new CancelConsultRequest($consultSystemClient);
        $cancelConsultRequest($order);
    }

    public function testItDoesNothingWithoutConsultRequestReference(): void
    {
        $order = $this->createMock(Order::class);
        $order->expects(self::once())
            ->method('getConsultSystemReference')
            ->willReturn(null);

        $consultSystemClient = $this->createMock(ClientInterface::class);
        $consultSystemClient
            ->expects(self::never())
            ->method('getConsultRequest');

        $consultSystemClient
            ->expects(self::never())
            ->method('cancelConsultRequest');

        $cancelConsultRequest = new CancelConsultRequest($consultSystemClient);
        $cancelConsultRequest($order);
    }

    /**
     * @dataProvider stateProvider
     */
    public function testItSkipsConsultRequestWhenHavingSpecificStateInConsultSystem(State $state): void
    {
        $reference = Uuid::uuid4();
        $order = $this->createMock(Order::class);
        $order->expects(self::once())
            ->method('getConsultSystemReference')
            ->willReturn($reference);

        $consultSystemClient = $this->createMock(ClientInterface::class);

        $consultRequest = $this->getConsultRequest($state, $reference);
        $consultSystemClient
            ->expects(self::once())
            ->method('getConsultRequest')
            ->willReturn($consultRequest);

        $consultSystemClient
            ->expects(self::never())
            ->method('cancelConsultRequest')
            ->with($reference);

        $cancelConsultRequest = new CancelConsultRequest($consultSystemClient);
        $cancelConsultRequest($order);
    }

    public function testThrowsConflictExceptionWhenClientReturnsInvalidResponse(): void
    {
        $order = $this->createMock(Order::class);
        $consultSystemClient = $this->createMock(ClientInterface::class);
        $response = new MockResponse(info: ['http_code' => Response::HTTP_CONFLICT]);

        $order->expects(self::once())
            ->method('getConsultSystemReference')
            ->willReturn(Uuid::fromString('00d5fb3a-da5f-4222-be4d-604c29fc0e67'));

        $consultRequest = $this->getConsultRequest(
            State::New,
            Uuid::fromString('00d5fb3a-da5f-4222-be4d-604c29fc0e67')
        );
        $consultSystemClient
            ->expects(self::once())
            ->method('getConsultRequest')
            ->willReturn($consultRequest);

        $consultSystemClient
            ->expects(self::once())
            ->method('cancelConsultRequest')
            ->willThrowException(new ClientException($response));

        $this->expectException(ConflictHttpException::class);
        $this->expectExceptionMessage('The order could not be cancelled');

        $cancelConsultRequest = new CancelConsultRequest($consultSystemClient);
        $cancelConsultRequest($order);
    }

    protected function stateProvider(): iterable
    {
        yield [State::Approved];
        yield [State::Declined];
        yield [State::Cancelled];
    }

    private function getConsultRequest(State $state, UuidInterface $reference): ConsultRequest
    {
        $address = new Address(
            'address',
            'postalCode',
            'city',
            'country'
        );

        $consultRequest = new ConsultRequest(
            'reference',
            ConsultRequestType::consult,
            'https://questionnaire-url',
            new Store('name', 'https://notify-url'),
            new Customer(
                'reference',
                'firstName',
                'lastName',
                new DateTime(),
                Gender::Male,
                'nl',
                $address,
                $address,
            ),
            [],
            $state,
            LastMessageInteractionState::None,
            new Doctor(Uuid::uuid4()->toString(), 'name', '12345'),
            new DateTime(),
            new DateTime(),
            $reference->toString()
        );

        return $consultRequest;
    }
}
