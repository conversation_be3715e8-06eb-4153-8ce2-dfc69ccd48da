<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Callback;

use App\Entity\Shipping\Shipment;
use App\StateMachine\Callback\SetDispatchedToSupplierAt;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class SetDispatchedToSupplierAtTest extends TestCase
{
    private EntityManagerInterface&MockObject $entityManager;

    protected function setUp(): void
    {
        parent::setUp();
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
    }

    public function testInvokeWithValidShipment(): void
    {
        $shipment = new Shipment();
        $shipment->setSupplierShipmentReference('SUP123');

        $this->entityManager
            ->expects(self::once())
            ->method('flush');

        $setDispatchedToSupplierAt = new SetDispatchedToSupplierAt($this->entityManager);
        $setDispatchedToSupplierAt($shipment);

        $this->assertInstanceOf(DateTimeImmutable::class, $shipment->getDispatchedToSupplierAt());
    }

    public function testInvokeWithEmptyShipmentReference(): void
    {
        $shipment = new Shipment();

        $setDispatchedToSupplierAt = new SetDispatchedToSupplierAt($this->entityManager);
        $setDispatchedToSupplierAt($shipment);

        $this->assertNull($shipment->getDispatchedToSupplierAt(), 'No supplierReference means that the supplier is not notified.');
    }
}
