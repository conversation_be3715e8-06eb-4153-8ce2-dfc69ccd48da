<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Callback;

use App\CommunicationSystem\Message\SyncOrderMessage;
use App\CommunicationSystem\OrderStateValidator;
use App\Entity\Order\Order;
use App\StateMachine\Callback\SynchronizeOrderInCommunicationSystem;
use App\StateMachine\OrderPrescriptionStates;
use App\StateMachine\OrderShippingStates;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Order\Model\OrderInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

class SynchronizeOrderInCommunicationSystemTest extends TestCase
{
    private OrderStateValidator $orderStateValidator;

    protected function setUp(): void
    {
        $this->orderStateValidator = new OrderStateValidator();
    }

    /**
     * @dataProvider provideAllowedStates
     */
    public function testInvokeDispatchesMessageForAllowedStates(
        string $state,
        string $shippingState,
        string $prescriptionState,
    ): void {
        $messageBus = $this->createMock(MessageBusInterface::class);
        $order = $this->createMock(Order::class);

        $order->method('getState')->willReturn($state);
        $order->method('getShippingState')->willReturn($shippingState);
        $order->method('getPrescriptionState')->willReturn($prescriptionState);
        $order->method('getId')->willReturn(123);

        $message = new SyncOrderMessage(123);
        $envelope = new Envelope($message);

        $messageBus->expects($this->once())
            ->method('dispatch')
            ->with(
                $this->callback(function ($message) {
                    return $message instanceof SyncOrderMessage && $message->orderId === 123;
                })
            )
            ->willReturn($envelope);

        $callback = new SynchronizeOrderInCommunicationSystem($messageBus, $this->orderStateValidator);
        $callback($order);
    }

    /**
     * @dataProvider provideDisallowedStates
     */
    public function testInvokeDoesNotDispatchMessageForDisallowedStates(
        string $state,
        string $shippingState,
        string $prescriptionState,
    ): void {
        $messageBus = $this->createMock(MessageBusInterface::class);
        $order = $this->createMock(Order::class);

        $order->method('getState')->willReturn($state);
        $order->method('getShippingState')->willReturn($shippingState);
        $order->method('getPrescriptionState')->willReturn($prescriptionState);

        $messageBus->expects($this->never())->method('dispatch');

        $callback = new SynchronizeOrderInCommunicationSystem($messageBus, $this->orderStateValidator);
        $callback($order);
    }

    /**
     * @return array<int,array<int,string>>
     */
    public function provideAllowedStates(): array
    {
        return [
            [OrderInterface::STATE_NEW, OrderShippingStates::STATE_PENDING, OrderPrescriptionStates::STATE_SKIPPED],
            [OrderInterface::STATE_FULFILLED, OrderShippingStates::STATE_READY, OrderPrescriptionStates::STATE_DOCTOR_UNAVAILABLE],
            [OrderInterface::STATE_CANCELLED, OrderShippingStates::STATE_AWAITING_PRESCRIPTION, OrderPrescriptionStates::STATE_PENDING],
            [OrderInterface::STATE_NEW, OrderShippingStates::STATE_PARTIALLY_SHIPPED, OrderPrescriptionStates::STATE_WAITING_FOR_RESPONSE],
            [OrderInterface::STATE_NEW, OrderShippingStates::STATE_RETURNED, OrderPrescriptionStates::STATE_APPROVED],
            [OrderInterface::STATE_NEW, OrderShippingStates::STATE_PARTIALLY_RETURNED, OrderPrescriptionStates::STATE_DECLINED],
            [OrderInterface::STATE_NEW, OrderShippingStates::STATE_SHIPPED, OrderPrescriptionStates::STATE_CANCELLED],
            [OrderInterface::STATE_NEW, OrderShippingStates::STATE_CANCELLED, OrderPrescriptionStates::STATE_SKIPPED],
            [OrderInterface::STATE_NEW, OrderShippingStates::STATE_SHIPPED, OrderPrescriptionStates::STATE_READY_FOR_CONSULT],
        ];
    }

    /**
     * @return array<int,array<int,string>>
     */
    public function provideDisallowedStates(): array
    {
        return [
            [OrderInterface::STATE_CART, OrderShippingStates::STATE_SHIPPED, OrderPrescriptionStates::STATE_SKIPPED],
            [OrderInterface::STATE_NEW, OrderShippingStates::STATE_CART, OrderPrescriptionStates::STATE_SKIPPED],
            [OrderInterface::STATE_NEW, OrderShippingStates::STATE_AWAITING_PAYMENT, OrderPrescriptionStates::STATE_SKIPPED],
            [OrderInterface::STATE_NEW, OrderShippingStates::STATE_AWAITING_FRAUD_CHECK, OrderPrescriptionStates::STATE_SKIPPED],
            [OrderInterface::STATE_NEW, OrderShippingStates::STATE_SHIPPED, OrderPrescriptionStates::STATE_CART],
            [OrderInterface::STATE_NEW, OrderShippingStates::STATE_SHIPPED, OrderPrescriptionStates::STATE_AWAITING_PAYMENT],
            [OrderInterface::STATE_NEW, OrderShippingStates::STATE_SHIPPED, OrderPrescriptionStates::STATE_AWAITING_FRAUD_CHECK],
            [OrderInterface::STATE_NEW, OrderShippingStates::STATE_SHIPPED, OrderPrescriptionStates::STATE_AWAITING_CUSTOMER_SERVICE],
        ];
    }
}
