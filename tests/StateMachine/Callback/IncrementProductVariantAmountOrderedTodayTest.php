<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Callback;

use App\Entity\Order\Order;
use App\Entity\Product\ProductVariant;
use App\Repository\ProductVariantRepositoryInterface;
use App\StateMachine\Callback\IncrementProductVariantAmountOrderedToday;
use App\Tests\Mocks\Entity\TestOrderItem;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class IncrementProductVariantAmountOrderedTodayTest extends TestCase
{
    private IncrementProductVariantAmountOrderedToday $callback;

    private ProductVariantRepositoryInterface&MockObject $productVariantRepositoryMock;

    protected function setUp(): void
    {
        $this->productVariantRepositoryMock = $this->createMock(ProductVariantRepositoryInterface::class);

        $this->callback = new IncrementProductVariantAmountOrderedToday($this->productVariantRepositoryMock);
    }

    public function testInvokeIncrementsCounter(): void
    {
        // Arrange
        $quantity = 2;

        $productVariant = new ProductVariant();

        $orderItem = new TestOrderItem();
        $orderItem->setVariant($productVariant);
        $orderItem->setQuantity($quantity);

        $order = new Order();
        $order->addItem($orderItem);

        // Assert
        $this->productVariantRepositoryMock->expects(self::once())
            ->method('incrementAmountOrderedToday')
            ->with($productVariant, $quantity);

        // Act
        ($this->callback)($order);
    }
}
