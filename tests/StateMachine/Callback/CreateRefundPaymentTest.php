<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\Callback;

use App\Entity\Order\Cancellation;
use App\Entity\Order\Enum\CancellationBy;
use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Payment\Payment;
use App\Entity\Product\ProductType;
use App\Entity\Refund\Enum\RefundPaymentReason;
use App\Entity\Refund\RefundPayment;
use App\Entity\Refund\RefundPaymentInterface;
use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShipmentInterface;
use App\Refund\Calculator\RefundableAmountByShipmentsCalculator;
use App\Refund\Calculator\RefundAmountCalculator;
use App\Refund\Factory\CreateOrderItemRefundsInterface;
use App\Refund\Factory\CreateRefundPaymentFactory;
use App\Refund\Factory\CreateRefundPaymentFactoryInterface;
use App\StateMachine\Callback\CreateRefundPayment;
use App\StateMachine\RefundPaymentTransitions;
use App\StateMachine\StateApplier\RefundPaymentStateApplierInterface;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\OrderItemFactory;
use App\Tests\Util\Factory\PaymentFactory;
use App\Tests\Util\Factory\ProductVariantFactory;
use App\Tests\Util\Factory\RefundPaymentFactory;
use App\Tests\Util\Factory\ShipmentFactory;
use App\Tests\Util\Factory\SupplierFactory;
use ArrayIterator;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Sylius\Component\Core\Model\PaymentMethodInterface;
use Sylius\Component\Payment\Model\PaymentInterface;
use Sylius\RefundPlugin\Factory\RefundPaymentFactoryInterface;
use Symfony\Component\Messenger\MessageBus;
use Symfony\Component\Messenger\TraceableMessageBus;

final class CreateRefundPaymentTest extends TestCase
{
    private const int PAYMENT_METHOD_IDEAL = 10;
    private const string ORDER_ID = 'DO00001';

    private TraceableMessageBus $messageBus;
    private ?PaymentMethodInterface $paymentMethod = null;
    private RefundableAmountByShipmentsCalculator $refundableAmountCalculator;
    private RefundAmountCalculator $refundAmountCalculator;
    private CreateOrderItemRefundsInterface&MockObject $createOrderItemRefunds;
    private EntityManagerInterface&MockObject $entityManager;

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->messageBus = new TraceableMessageBus(new MessageBus());
        $this->refundableAmountCalculator = new RefundableAmountByShipmentsCalculator();
        $this->refundAmountCalculator = new RefundAmountCalculator($this->refundableAmountCalculator);
        $this->createOrderItemRefunds = $this->createMock(CreateOrderItemRefundsInterface::class);
    }

    /**
     * @regression() test for https://mv-jira-1.atlassian.net/browse/DV-4948
     */
    public function testRefundWontReturnTooMuch(): void
    {
        $order = $this->getOrderWithPayments(
            [
                // Create 3 previous order items with a total of 14000
                'previous' => [1500, 2500, 10000],
                // Current value of the order is 2500
                'current' => [1000, 1500],
                // The customer already paid 3100
                'paid' => [3100],
                // The customer already got a refund of 100
                'refunds' => [RefundPaymentInterface::STATE_COMPLETED => 50, RefundPaymentInterface::STATE_NEW => 50],
            ]
        );
        $shipment = $this->setUpRefundables(3100);
        $order->addShipment($shipment[0]);

        $refundPaymentFactory = $this->getRefundPaymentFactory($order, 500);

        $createRefundPayment = new CreateRefundPaymentFactory(
            $refundPaymentFactory,
            $this->entityManager,
            $this->createMock(RefundPaymentStateApplierInterface::class),
            $this->refundAmountCalculator,
        );

        $createRefundPayment = new CreateRefundPayment(
            $createRefundPayment,
            $this->createOrderItemRefunds
        );

        $this->createOrderItemRefunds->expects(self::once())
            ->method('createOrderItemRefunds');

        $createRefundPayment->__invoke($order, CreateRefundPaymentFactoryInterface::REASON_CANCELLED);
    }

    /**
     * @regression() test for https://mv-jira-1.atlassian.net/browse/DV-7355
     */
    public function testOnlyOneOpenRefund(): void
    {
        $shipments = $this->setUpRefundables(3000);

        $order = $this->getOrderWithPayments(
            [
                // Create 3 previous order items with a total of 14000
                'previous' => [1500, 2500, 10000],
                // Current value of the order is 2500
                'current' => [1000, 1500],
                // The customer already paid 3100
                'paid' => [3100],
                // The customer already got a refundPayment of 100
                'refunds' => [RefundPaymentInterface::STATE_COMPLETED => 50, RefundPaymentInterface::STATE_NEW => 50],
            ],
            $shipments
        );

        $refundPaymentFactory = $this->getRefundPaymentFactory($order, 550);
        $refundStateApplier = $this->createMock(RefundPaymentStateApplierInterface::class);
        $refundStateApplier->expects(self::once())
            ->method('apply')
            ->with($order->getRefundPayments()->last(), RefundPaymentTransitions::TRANSITION_DECLINE)
            ->willReturnCallback(
                static function (RefundPayment $refundPayment) {
                    $refundPayment->setState(RefundPaymentInterface::STATE_DECLINED);
                }
            );

        $createRefundPayment = new CreateRefundPaymentFactory(
            $refundPaymentFactory,
            $this->entityManager,
            $refundStateApplier,
            $this->refundAmountCalculator,
        );

        $createRefundPaymentCallback = new CreateRefundPayment(
            $createRefundPayment,
            $this->createOrderItemRefunds
        );

        $this->createOrderItemRefunds->expects(self::never())
            ->method('createOrderItemRefunds');

        $createRefundPaymentCallback->__invoke($order, CreateRefundPaymentFactoryInterface::REASON_OVERPAID);

        $dispatchedMessages = $this->messageBus->getDispatchedMessages();
        self::assertCount(0, $dispatchedMessages);
        self::assertCount(3, $order->getRefundPayments());

        /** @var RefundPayment $refundPayment */
        $refundPayment = $order->getRefundPayments()
            ->filterInState(RefundPaymentInterface::STATE_COMPLETED)
            ->first();
        self::assertSame(50, $refundPayment->getAmount());

        /** @var RefundPayment $refundPayment */
        $refundPayment = $order->getRefundPayments()
            ->filterInState(RefundPaymentInterface::STATE_DECLINED)
            ->first();
        self::assertSame(50, $refundPayment->getAmount());

        /** @var RefundPayment $refundPayment */
        $refundPayment = $order->getRefundPayments()
            ->filterInState(RefundPaymentInterface::STATE_NEW)
            ->first();
        self::assertSame(550, $refundPayment->getAmount());
    }

    /**
     * @param array{result: int, orders: array{paid:array<int>, current:array<int>}} $orderAmounts
     * @dataProvider provideDifferentOrderAmounts
     */
    public function testDifferentAmountsWillReturnCorrectAmount(array $orderAmounts): void
    {
        $order = $this->getOrderWithPayments($orderAmounts['orders']);
        $shipment = $this->setUpRefundables($orderAmounts['result']);
        $order->addShipment($shipment[0]);
        $order->setCancellation(new Cancellation(CancellationBy::CUSTOMER_SERVICE, 'out_of_stock'));
        $refundPaymentFactory = $this->getRefundPaymentFactory($order, $orderAmounts['result']);

        $createRefundPayment = new CreateRefundPaymentFactory(
            $refundPaymentFactory,
            $this->entityManager,
            $this->createMock(RefundPaymentStateApplierInterface::class),
            $this->refundAmountCalculator,
        );

        $createRefundPaymentCallback = new CreateRefundPayment(
            $createRefundPayment,
            $this->createOrderItemRefunds
        );

        $this->createOrderItemRefunds->expects(self::once())
            ->method('createOrderItemRefunds');

        $createRefundPaymentCallback->__invoke($order, CreateRefundPaymentFactoryInterface::REASON_CANCELLED);
    }

    public function testRefundWillNotBeCreatedAmountIsLessThanPaid(): void
    {
        $orders = [
            'paid' => [],
            'current' => [5200, 1500],
        ];
        $shipments = $this->setUpRefundables(array_sum($orders['paid']));
        $order = $this->getOrderWithPayments($orders, $shipments);
        $refundPaymentFactory = $this->getRefundPaymentFactory($order, 0);

        $createRefundPayment = new CreateRefundPaymentFactory(
            $refundPaymentFactory,
            $this->entityManager,
            $this->createMock(RefundPaymentStateApplierInterface::class),
            $this->refundAmountCalculator,
        );

        $createRefundPaymentCallback = new CreateRefundPayment(
            $createRefundPayment,
            $this->createOrderItemRefunds
        );

        $this->createOrderItemRefunds->expects(self::never())
            ->method('createOrderItemRefunds');

        $createRefundPaymentCallback->__invoke($order, CreateRefundPaymentFactoryInterface::REASON_OVERPAID);
    }

    public function testRefundWillNotBeCreatedWhenThereIsNoPaymentMethod(): void
    {
        $createRefundPayment = new CreateRefundPaymentFactory(
            $this->createMock(RefundPaymentFactoryInterface::class),
            $this->entityManager,
            $this->createMock(RefundPaymentStateApplierInterface::class),
            $this->refundAmountCalculator,
        );

        $createRefundPaymentCallback = new CreateRefundPayment(
            $createRefundPayment,
            $this->createOrderItemRefunds
        );

        $this->createOrderItemRefunds->expects(self::never())
            ->method('createOrderItemRefunds');

        $order = $this->getOrderWithPayments([]);

        $createRefundPaymentCallback->__invoke($order, CreateRefundPaymentFactoryInterface::REASON_OVERPAID);
    }

    /**
     * @dataProvider partiallyReturnedOrdersProvider
     * @param array<array-key, ShipmentInterface> $shipments
     * @param array<array-key, array{totalOrderAmount: int, expectedRefund: int, shipments: array<ShipmentInterface>,
     * items: array<OrderItem>}> $orderItems
     */
    public function testAmountRefundedPartiallyReturnedOrders(
        int $totalOrderAmount,
        int $expectedRefund,
        array $shipments,
        array $orderItems,
    ): void {
        /** @var Order $order */
        $order = OrderFactory::create(
            [
                'id' => 1337,
                'channel' => ChannelFactory::createPrefilled(['multipleShipmentsAllowed' => true]),
                'number' => self::ORDER_ID,
                'payments' => [],
                'shipments' => $shipments,
                'items' => $orderItems,
            ]
        );

        $payment = new Payment();
        $payment->setState(PaymentInterface::STATE_COMPLETED);
        $payment->setAmount($totalOrderAmount);
        $payment->setMethod($this->getPaymentMethod());
        $order->addPayment($payment);

        $refundPaymentFactory = $this->getRefundPaymentFactory(
            $order,
            $this->refundableAmountCalculator->getRefundableAmount($order)
        );

        $createRefundPayment = new CreateRefundPaymentFactory(
            $refundPaymentFactory,
            $this->entityManager,
            $this->createMock(RefundPaymentStateApplierInterface::class),
            $this->refundAmountCalculator,
        );

        $createRefundPaymentCallback = new CreateRefundPayment(
            $createRefundPayment,
            $this->createOrderItemRefunds
        );

        $this->createOrderItemRefunds->expects(self::once())
            ->method('createOrderItemRefunds');

        $createRefundPaymentCallback->__invoke($order, CreateRefundPaymentFactoryInterface::REASON_CANCELLED);
    }

    /**
     * @dataProvider cancelledOrdersProvider
     * @param array<array-key, ShipmentInterface> $shipments
     * @param array<array-key, array{totalOrderAmount: int, expectedRefund: int, shipments: array<ShipmentInterface>,
     * items: array<OrderItem>}> $orderItems
     */
    public function testAmountRefundedCancelledOrders(
        int $totalOrderAmount,
        int $expectedRefund,
        array $shipments,
        array $orderItems,
    ): void {
        /** @var Order $order */
        $order = OrderFactory::create(
            [
                'id' => 1337,
                'channel' => ChannelFactory::createPrefilled(['multipleShipmentsAllowed' => true]),
                'number' => self::ORDER_ID,
                'payments' => [],
                'shipments' => $shipments,
                'items' => $orderItems,
            ]
        );

        $payment = new Payment();
        $payment->setState(PaymentInterface::STATE_COMPLETED);
        $payment->setAmount($totalOrderAmount);
        $payment->setMethod($this->getPaymentMethod());
        $order->addPayment($payment);

        $refundPaymentFactory = $this->getRefundPaymentFactory(
            $order,
            $expectedRefund,
            RefundPaymentReason::ORDER_CANCELLED
        );

        $createRefundPayment = new CreateRefundPaymentFactory(
            $refundPaymentFactory,
            $this->entityManager,
            $this->createMock(RefundPaymentStateApplierInterface::class),
            $this->refundAmountCalculator,
        );

        $createRefundPaymentCallback = new CreateRefundPayment(
            $createRefundPayment,
            $this->createOrderItemRefunds
        );

        $this->createOrderItemRefunds->expects(self::once())
            ->method('createOrderItemRefunds');

        $createRefundPaymentCallback->__invoke($order, CreateRefundPaymentFactoryInterface::REASON_CANCELLED);
    }

    /**
     * @return ArrayIterator<array-key, array{totalOrderAmount: int, expectedRefund: int, shipments: array<ShipmentInterface>, items: array<OrderItem>}>
     */
    public function partiallyReturnedOrdersProvider(): iterable
    {
        $orderItems = [];
        $orderItems[] = $this->setupItem(0, ProductType::CONSULT);
        $orderItems[] = $this->setupItem(4000, ProductType::MEDICATION);
        $orderItems[] = $this->setupItem(0, ProductType::CONSULT);
        $orderItems[] = $this->setupItem(10000, ProductType::MEDICATION);
        $orderItems[] = $this->setupItem(1495, ProductType::SERVICE);

        $shipments = [];

        $shipment = $this->setShipment('shipped');
        $shipment->addUnit(
            OrderFactory::createOrderItemUnit(
                [
                    'orderItem' => $orderItems[0],
                    'shipment' => $shipment,
                ]
            )
        );

        $medicineOrderItemUnit = OrderFactory::createOrderItemUnit(
            [
                'orderItem' => $orderItems[1],
                'shipment' => $shipment,
            ]
        );

        $shipment->addUnit($medicineOrderItemUnit);

        $shipments[] = $shipment;

        $shipment = $this->setShipment('cancelled');

        $shipment->addUnit(
            OrderFactory::createOrderItemUnit(
                [
                    'orderItem' => $orderItems[2],
                    'shipment' => $shipment,
                ]
            )
        );

        $medicineOrderItem = OrderItemFactory::create(
            [
                'unitPrice' => 10000,
                'unitCostPrice' => 10000,
                'originalUnitPrice' => 10000,
            ]
        );

        $medicineOrderItemUnit = OrderFactory::createOrderItemUnit(
            [
                'orderItem' => $medicineOrderItem,
                'shipment' => $shipment,
            ]
        );

        $shipment->addUnit($medicineOrderItemUnit);

        $shipments[] = $shipment;

        yield 'order with shipped and cancelled shipments and digital product' => [
            'totalOrderAmount' => 15495,
            'expectedRefund' => 10000,
            'shipments' => $shipments,
            'items' => $orderItems,
        ];

        $orderItems = [];
        $orderItems[] = $this->setupItem(0, ProductType::CONSULT);
        $orderItems[] = $this->setupItem(10000, ProductType::MEDICATION);
        $orderItems[] = $this->setupItem(0, ProductType::CONSULT);
        $orderItems[] = $this->setupItem(10000, ProductType::MEDICATION);
        $orderItems[] = $this->setupItem(4000, ProductType::MEDICATION);

        $shipments = [];

        $shipment = $this->setShipment('shipped');
        $shipment->addUnit(
            OrderFactory::createOrderItemUnit(
                [
                    'orderItem' => $orderItems[0],
                    'shipment' => $shipment,
                ]
            )
        );

        $medicineOrderItemUnit = OrderFactory::createOrderItemUnit(
            [
                'orderItem' => $orderItems[4],
                'shipment' => $shipment,
            ]
        );

        $shipment->addUnit($medicineOrderItemUnit);

        $shipments[] = $shipment;

        $shipment = $this->setShipment('returned');

        $shipment->addUnit(
            OrderFactory::createOrderItemUnit(
                [
                    'orderItem' => $orderItems[2],
                    'shipment' => $shipment,
                ]
            )
        );

        $medicineProductVariant = ProductVariantFactory::createPrefilled();

        $medicineOrderItem = OrderItemFactory::create(
            [
                'unitPrice' => 10000,
                'unitCostPrice' => 10000,
                'originalUnitPrice' => 10000,
                'variant' => $medicineProductVariant,
            ]
        );

        $medicineOrderItemUnit = OrderFactory::createOrderItemUnit(
            [
                'orderItem' => $medicineOrderItem,
                'shipment' => $shipment,
            ]
        );

        $shipment->addUnit($medicineOrderItemUnit);

        $shipments[] = $shipment;

        yield 'order with shipped and cancelled shipments, digital product and switched order item' => [
            'totalOrderAmount' => 20000,
            'expectedRefund' => 10000,
            'shipments' => $shipments,
            'items' => $orderItems,
        ];
    }

    /**
     * @return ArrayIterator<array-key, array{totalOrderAmount: int, expectedRefund: int, shipments: array<ShipmentInterface>, items: array<OrderItem>}>
     */
    public function cancelledOrdersProvider(): iterable
    {
        $orderItems = [];
        $orderItems[] = $this->setupItem(0, ProductType::CONSULT);
        $orderItems[] = $this->setupItem(4000, ProductType::MEDICATION);
        $orderItems[] = $this->setupItem(1495, ProductType::SERVICE);

        $shipments = [];

        $shipment = $this->setShipment('returned');
        $shipments[] = $shipment;

        $shipment = $this->setShipment('cancelled');

        $shipment->addUnit(
            OrderFactory::createOrderItemUnit(
                [
                    'orderItem' => $orderItems[0],
                    'shipment' => $shipment,
                ]
            )
        );

        $medicineOrderItemUnit = OrderFactory::createOrderItemUnit(
            [
                'orderItem' => $orderItems[1],
                'shipment' => $shipment,
            ]
        );

        $shipment->addUnit($medicineOrderItemUnit);

        $shipments[] = $shipment;

        yield 'order with returned and cancelled shipments and digital product' => [
            'totalOrderAmount' => 5495,
            'expectedRefund' => 4000,
            'shipments' => $shipments,
            'items' => $orderItems,
        ];

        $orderItems = [];
        $orderItems[] = $this->setupItem(0, ProductType::CONSULT);
        $orderItems[] = $this->setupItem(4000, ProductType::MEDICATION);

        $shipment = $this->setShipment('cancelled');

        $shipment->addUnit(
            OrderFactory::createOrderItemUnit(
                [
                    'orderItem' => $orderItems[0],
                    'shipment' => $shipment,
                ]
            )
        );

        $medicineOrderItemUnit = OrderFactory::createOrderItemUnit(
            [
                'orderItem' => $orderItems[1],
                'shipment' => $shipment,
            ]
        );

        $shipment->addUnit($medicineOrderItemUnit);

        $shipments[] = $shipment;

        yield 'cancelled order without digital product' => [
            'totalOrderAmount' => 4000,
            'expectedRefund' => 4000,
            'shipments' => $shipments,
            'items' => $orderItems,
        ];

        $orderItems = [];
        $orderItems[] = $this->setupItem(0, ProductType::CONSULT);
        $orderItems[] = $this->setupItem(4000, ProductType::MEDICATION);

        $shipment = $this->setShipment('cancelled');

        $shipment->addUnit(
            OrderFactory::createOrderItemUnit(
                [
                    'orderItem' => $orderItems[0],
                    'shipment' => $shipment,
                ]
            )
        );

        $medicineOrderItemUnit = OrderFactory::createOrderItemUnit(
            [
                'orderItem' => $orderItems[1],
                'shipment' => $shipment,
            ]
        );

        $shipment->addUnit($medicineOrderItemUnit);

        $shipments[] = $shipment;

        yield 'cancelled order with digital product' => [
            'totalOrderAmount' => 4000,
            'expectedRefund' => 4000,
            'shipments' => $shipments,
            'items' => $orderItems,
        ];
    }

    /**
     * @return array<array<array{result: int, orders: array{paid:array<int>, current:array<int>}}>>
     */
    public function provideDifferentOrderAmounts(): array
    {
        return [
            [
                [
                    'result' => 1500,
                    'orders' => [
                        'paid' => [1500, 2500],
                        'current' => [1000, 1500],
                    ],
                ],
            ],
            [
                [
                    'result' => 1000,
                    'orders' => [
                        'paid' => [5000, 7000],
                        'current' => [6000, 5000],
                    ],
                ],
            ],
            [
                [
                    'result' => 4755,
                    'orders' => [
                        'paid' => [1200, 8555],
                        'current' => [5000],
                    ],
                ],
            ],
        ];
    }

    public function getPaymentMethod(): PaymentMethodInterface
    {
        if ($this->paymentMethod === null) {
            $this->paymentMethod = PaymentFactory::createPaymentMethod(['id' => self::PAYMENT_METHOD_IDEAL]);
        }

        return $this->paymentMethod;
    }

    /**
     * @param array<mixed> $orderAmounts
     * @param array<ShipmentInterface> $shipments
     */
    private function getOrderWithPayments(array $orderAmounts, array $shipments = []): Order
    {
        $currentItems = new ArrayCollection();
        foreach ($orderAmounts['current'] ?? [] as $orderAmount) {
            $currentItems->add($this->getOrderItem($orderAmount));
        }

        /** @var Order $order */
        $order = OrderFactory::createPrefilled(
            [
                'id' => 1337,
                'channel' => ChannelFactory::createPrefilled(),
                'number' => self::ORDER_ID,
                'items' => $currentItems,
                'payments' => [],
                'shipments' => $shipments,
            ]
        );

        if (empty($orderAmounts)) {
            return $order;
        }

        $previousItems = new ArrayCollection();
        foreach ($orderAmounts['previous'] ?? [] as $orderAmount) {
            $previousItems->add($this->getOrderItem($orderAmount));
        }

        foreach ($orderAmounts['paid'] ?? [] as $paidAmount) {
            $payment = new Payment();
            $payment->setState(PaymentInterface::STATE_COMPLETED);
            $payment->setAmount($paidAmount);
            $payment->setMethod($this->getPaymentMethod());
            $order->addPayment($payment);
        }

        foreach ($orderAmounts['refunds'] ?? [] as $state => $refundAmount) {
            $state = is_string($state) ? $state : RefundPaymentInterface::STATE_COMPLETED;
            $refund = new RefundPayment(
                $order,
                $refundAmount,
                'EUR',
                $state,
                $this->getPaymentMethod()
            );

            $order->addRefundPayment($refund);
        }

        return $order;
    }

    private function getRefundPaymentFactory(
        Order $order,
        int $amount = 1500,
        RefundPaymentReason $reason = RefundPaymentReason::DIFFERENCE_PAID_AMOUNT,
    ): RefundPaymentFactoryInterface&MockObject {
        $refundPaymentFactory = $this->createMock(RefundPaymentFactoryInterface::class);
        if ($amount <= 0) {
            return $refundPaymentFactory;
        }

        $refundPayment = $this->getRefundPayment($amount, $order, $reason);
        $refundPaymentFactory
            ->expects($this->once())
            ->method('createWithData')
            ->willReturn($refundPayment);

        return $refundPaymentFactory;
    }

    private function getOrderItem(int $total): OrderItem
    {
        return OrderItemFactory::createPrefilled(['total' => $total]);
    }

    private function getRefundPayment(
        int $amount,
        Order $order,
        RefundPaymentReason $reason = RefundPaymentReason::DIFFERENCE_PAID_AMOUNT,
    ): RefundPayment {
        $refundPayment = RefundPaymentFactory::create(
            [
                'amount' => $amount,
                'state' => RefundPayment::STATE_NEW,
                'order' => $order,
            ]
        );
        $refundPayment->setReason($reason);

        return $refundPayment;
    }

    private function setShipment(string $state): Shipment
    {
        $supplier = SupplierFactory::create(
            [
                'name' => 'Test Supplier',
            ]
        );
        $shipment = ShipmentFactory::create(
            [
                'state' => $state,
                'supplier' => $supplier,
            ]
        );

        return $shipment;
    }

    /**
     * @return array<ShipmentInterface>
     */
    private function setUpRefundables(int $amount): array
    {
        $orderItems = [];
        $orderItems[] = $this->setupItem($amount, ProductType::MEDICATION);
        $shipment = $this->setShipment('cancelled');
        $shipment->addUnit(
            OrderFactory::createOrderItemUnit(
                [
                    'orderItem' => $orderItems[0],
                    'shipment' => $shipment,
                ]
            )
        );
        $shipments[] = $shipment;

        return $shipments;
    }

    private function setupItem(int $amount, ProductType $type): OrderItem
    {
        return OrderItemFactory::create(
            [
                'id' => random_int(0, 100),
                'unitPrice' => $amount,
                'unitCostPrice' => $amount,
                'originalUnitPrice' => $amount,
                'variant' => ProductVariantFactory::createPrefilled(
                    [
                        'id' => random_int(0, 100),
                        'productData' => [],
                    ],
                    $type
                ),
            ]
        );
    }
}
