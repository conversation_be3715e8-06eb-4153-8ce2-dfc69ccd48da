<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\StateApplier;

use App\Entity\Order\Order;
use App\Entity\Payment\Payment;
use App\StateMachine\StateApplier\PaymentStateApplier;
use App\StateMachine\StateApplier\PaymentStateApplierInterface;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use SM\Factory\FactoryInterface;
use SM\StateMachine\StateMachineInterface;
use Sylius\Component\Payment\PaymentTransitions;

final class PaymentStateApplierTest extends TestCase
{
    private StateMachineInterface&MockObject $stateMachineMock;
    private PaymentStateApplierInterface $paymentStateApplier;

    protected function setUp(): void
    {
        parent::setUp();
        $this->stateMachineMock = $this->createMock(StateMachineInterface::class);

        $stateMachineFactoryMock = $this->createStub(FactoryInterface::class);
        $stateMachineFactoryMock->method('get')
            ->willReturn($this->stateMachineMock);

        $this->paymentStateApplier = new PaymentStateApplier(
            $stateMachineFactoryMock,
            $this->createStub(EntityManagerInterface::class),
        );
    }

    public function testApplyNewState(): void
    {
        $cartPayment = new Payment();

        $completedPayment = new Payment();
        $completedPayment->setState(Payment::STATE_COMPLETED);

        $order = new Order();
        $order->addPayment($cartPayment);
        $order->addPayment($completedPayment);

        $this->stateMachineMock->expects(self::once())
            ->method('apply')
            ->with('create');

        $this->paymentStateApplier->apply($order, Payment::STATE_CART, PaymentTransitions::TRANSITION_CREATE);
    }
}
