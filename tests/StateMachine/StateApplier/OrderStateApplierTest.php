<?php

declare(strict_types=1);

namespace App\Tests\StateMachine\StateApplier;

use App\Entity\Order\Order;
use App\StateMachine\OrderShippingStates;
use App\StateMachine\OrderTransitions;
use App\StateMachine\StateApplier\OrderStateApplier;
use App\Tests\Mocks\Entity\TestOrder;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use SM\Factory\FactoryInterface;
use SM\StateMachine\StateMachineInterface;

final class OrderStateApplierTest extends TestCase
{
    private StateMachineInterface&MockObject $stateMachineMock;
    private OrderStateApplier $orderStateApplier;

    public function setUp(): void
    {
        $this->stateMachineMock = $this->createMock(StateMachineInterface::class);

        $stateMachineFactoryMock = $this->createStub(FactoryInterface::class);
        $stateMachineFactoryMock->method('get')
            ->willReturn($this->stateMachineMock);

        $this->orderStateApplier = new OrderStateApplier(
            $stateMachineFactoryMock,
            $this->createStub(EntityManagerInterface::class),
        );
    }

    /**
     * @dataProvider provideOrderForApplyNewStateAfterTransitionShippingState
     */
    public function testApplyNewStateAfterTransitionShippingState(
        string $expectedTransition,
        Order $order,
    ): void {
        $this->stateMachineMock->expects(self::once())
            ->method('apply')
            ->with($expectedTransition);

        $this->orderStateApplier->applyNewStateAfterTransitionShippingState($order);
    }

    public function provideOrderForApplyNewStateAfterTransitionShippingState(): iterable
    {
        $order = new TestOrder();
        $order->setShippingState(OrderShippingStates::STATE_RETURNED);

        yield 'Order with shipment state returned' => [OrderTransitions::TRANSITION_RETURNED, $order];
    }
}
