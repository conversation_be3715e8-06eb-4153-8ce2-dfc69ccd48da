<?php

declare(strict_types=1);

namespace App\Tests\Channel\Context\RequestBased;

use App\Channel\Context\RequestBased\HeaderBasedRequestResolver;
use App\Entity\Channel\Channel;
use App\Repository\ChannelRepositoryInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;

/**
 * Tests the {@see HeaderBasedRequestResolver}.
 *
 * <AUTHOR> <<EMAIL>>
 */
class HeaderBasedRequestResolverTest extends TestCase
{
    private HeaderBasedRequestResolver $requestResolver;

    private ChannelRepositoryInterface&MockObject $channelRepository;

    protected function setUp(): void
    {
        $this->channelRepository = $this->createMock(ChannelRepositoryInterface::class);

        $this->requestResolver = new HeaderBasedRequestResolver($this->channelRepository);
    }

    public function testCanReturnChannel(): void
    {
        $request = new Request();
        $request->headers->set('Sylius-Channel-Code', 'dok_gb');

        $channel = new Channel();

        $this->channelRepository->method('findOneByCode')
            ->with('dok_gb')
            ->willReturn($channel);

        static::assertSame(
            $channel,
            $this->requestResolver->findChannel($request)
        );
    }

    public function testCannotReturnChannelWhenHeaderNotSet(): void
    {
        $request = new Request();

        $this->channelRepository->expects($this->never())
            ->method('findOneByCode');

        static::assertNull(
            $this->requestResolver->findChannel($request)
        );
    }

    public function testCannotReturnChannelWhenChannelCodeFromHeaderIsNotFoundInChannelRepository(): void
    {
        $request = new Request();
        $request->headers->set('Sylius-Channel-Code', 'dok_gb');

        $this->channelRepository->method('findOneByCode')
            ->with('dok_gb')
            ->willReturn(null);

        static::assertNull(
            $this->requestResolver->findChannel($request)
        );
    }
}
