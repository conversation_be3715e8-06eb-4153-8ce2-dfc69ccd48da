<?php

declare(strict_types=1);

namespace App\Tests\Supplier\Resolver;

use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Order\PreferredOrderItem;
use App\Entity\Product\ProductInterface;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariantInterface;
use App\Entity\Shipping\ShipmentInterface;
use App\Entity\Supplier\Supplier;
use App\Entity\Supplier\SupplierDoctor;
use App\Entity\Supplier\SupplierInterface;
use App\Repository\OrderItemUnitRepositoryInterface;
use App\Repository\SupplierDoctorRepository;
use App\Repository\SupplierDoctorRepositoryInterface;
use App\Supplier\Resolver\Exception\MissingDoctorException;
use App\Supplier\Resolver\Exception\UnresolvableSupplierException;
use App\Supplier\Resolver\SupplierIdentifierResolver;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\CustomerFactory;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\OrderItemFactory;
use App\Tests\Util\Factory\PreferredOrderItemFactory;
use App\Tests\Util\Factory\ProductVariantFactory;
use App\Tests\Util\Factory\ShipmentFactory;
use App\Tests\Util\Factory\SupplierDoctorFactory;
use App\Tests\Util\Factory\SupplierFactory;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Throwable;

class SupplierIdentifierResolverTest extends TestCase
{
    private const string DOCTOR_SUPPLIER_IDENTIFIER = 'doctor-supplier-identifier';
    private const string VARIANT_SUPPLIER_ID = 'variant-supplier-identifier';
    private const string PREFERRED_PRODUCT_SUPPLIER = 'preferred-product-supplier-identifier';
    private const string DOCTOR_REGISTRATION_NUMBER = 'test-registration-number';

    private SupplierDoctorRepositoryInterface&MockObject $supplierDoctorRepository;
    private SupplierIdentifierResolver $supplierIdentifierResolver;
    private OrderItemUnitRepositoryInterface&MockObject $orderItemUnitRepositoryMock;

    protected function setUp(): void
    {
        $this->supplierDoctorRepository = $this->createMock(SupplierDoctorRepository::class);
        $this->orderItemUnitRepositoryMock = $this->createMock(OrderItemUnitRepositoryInterface::class);

        $this->supplierIdentifierResolver = new SupplierIdentifierResolver($this->supplierDoctorRepository, $this->orderItemUnitRepositoryMock);
    }

    /**
     * Scenarios
     * - dok_nl: Supplier is not resolved for prescription service without preferred products
     * - dok_nl: Supplier is not resolved for prescription service with preferred products
     * - dok_nl: Supplier is not resolved for blueclinic service without preferred products
     * - dok_nl: Supplier is not resolved for bleuclinic service with preferred products.
     * - dok_gb: Supplier is not resolved for order without products.
     *
     * @param class-string<Throwable> $exception
     * @dataProvider resolveForShipmentWithoutSupplierDataProvider
     */
    public function testResolveForShipmentWithoutSupplier(Order $order, ShipmentInterface $shipment, string $exception): void
    {
        // arrange
        $this->orderItemUnitRepositoryMock
            ->method('findByShipment')
            ->with($shipment)
            ->willReturn($order->getItemUnits()->toArray());

        // assert
        $this->expectException($exception);

        // act
        $this->supplierIdentifierResolver->resolveForShipment($shipment);
    }

    public function resolveForShipmentWithoutSupplierDataProvider(): iterable
    {
        yield 'dok_nl: Supplier is not resolved for prescription service with preferred products' => $this->providePrescriptionWithPreferredProductsOrder();

        yield 'dok_nl: Supplier is not resolved for prescription service without preferred products' => $this->providePrescriptionWithoutPreferredProductsOrder();

        yield 'dok_nl: Supplier is not resolved for blueclinic service with preferred products' => $this->provideBlueclinicServiceWithPerferredProducts();

        yield 'dok_nl: Supplier is not resolved for blueclinic service without preferred products' => $this->provideBlueclinicServiceWithoutPerferredProducts();

        yield 'dok_gb: Supplier is not resolved for order without products.' => $this->provideDokGbServiceWithoutProducts();
    }

    public function testSupplierIsResolved(): void
    {
        // arrange
        ['order' => $order, 'shipment' => $shipment, 'expectedSupplier' => $expectedSupplier] = $this->provideSupplierIsResolved();

        $this->orderItemUnitRepositoryMock
            ->method('findByShipment')
            ->with($shipment)
            ->willReturn($order->getItemUnits()->toArray());

        // act
        $result = $this->supplierIdentifierResolver->resolveForShipment($shipment);

        // assert
        self::assertInstanceOf(SupplierInterface::class, $result);
        self::assertEquals($expectedSupplier, $result->getIdentifier());
    }

    public function testSupplierIsResolvedForPrescriptionServiceWithDoctor(): void
    {
        // arrange
        ['order' => $order, 'shipment' => $shipment, 'expectedSupplier' => $expectedSupplier] = $this->provideSupplierIsResolvedForPrescriptionServiceWithDoctor();

        $this->supplierDoctorRepository
            ->method('findOneBy')
            ->with(['registrationNumber' => $order->getDoctorRegistrationNumber()])
            ->willReturn(SupplierDoctorFactory::create());

        $this->orderItemUnitRepositoryMock
            ->method('findByShipment')
            ->with($shipment)
            ->willReturn($order->getItemUnits()->toArray());

        // act
        $result = $this->supplierIdentifierResolver->resolveForShipment($shipment);

        // assert
        self::assertInstanceOf(SupplierInterface::class, $result);
        self::assertEquals($expectedSupplier, $result->getIdentifier());
    }

    public function testResolveForOrderWithServiceProduct(): void
    {
        // Arrange
        $supplier = new Supplier();
        $supplier->setIdentifier(self::DOCTOR_SUPPLIER_IDENTIFIER);
        $doctor = new SupplierDoctor();
        $doctor->setRegistrationNumber(self::DOCTOR_REGISTRATION_NUMBER);
        $doctor->setSupplier($supplier);
        $order = $this->createMock(Order::class);
        $order->expects($this->once())->method('hasServiceProduct')
            ->with('service_prescription')
            ->willReturn(true);
        $order->expects($this->once())->method('getDoctorRegistrationNumber')
            ->willReturn(self::DOCTOR_REGISTRATION_NUMBER);
        $this->supplierDoctorRepository->expects($this->once())->method('findOneBy')
            ->with(['registrationNumber' => self::DOCTOR_REGISTRATION_NUMBER])
            ->willReturn($doctor);

        // Act
        $result = $this->supplierIdentifierResolver->resolveForOrder($order);

        // Assert
        $this->assertSame(self::DOCTOR_SUPPLIER_IDENTIFIER, $result->getIdentifier());
    }

    public function testResolveForOrderWithoutServiceProduct(): void
    {
        // Arrange
        $supplier = new Supplier();
        $supplier->setIdentifier(self::VARIANT_SUPPLIER_ID);
        $productVariant = $this->createMock(ProductVariantInterface::class);
        $productVariant->expects($this->once())->method('getSupplier')
            ->willReturn($supplier);
        $orderItem = $this->createMock(OrderItem::class);
        $orderItem->expects($this->once())->method('getVariant')
            ->willReturn($productVariant);
        $order = $this->createMock(Order::class);
        $order->expects($this->once())->method('hasServiceProduct')
            ->with('service_prescription')
            ->willReturn(false);
        $order->expects($this->once())->method('getItems')
            ->willReturn(new ArrayCollection([$orderItem]));

        // Act
        $result = $this->supplierIdentifierResolver->resolveForOrder($order);

        // Assert
        $this->assertSame(self::VARIANT_SUPPLIER_ID, $result->getIdentifier());
    }

    public function testResolveForOrderWithoutServiceProductWithPreferredOrderItem(): void
    {
        // Arrange
        $supplier = new Supplier();
        $supplier->setIdentifier(self::PREFERRED_PRODUCT_SUPPLIER);
        $productVariant = $this->createMock(ProductVariantInterface::class);
        $productVariant->expects($this->once())->method('getSupplier')
            ->willReturn($supplier);
        $preferredOrderItem = $this->createMock(PreferredOrderItem::class);
        $preferredOrderItem->expects($this->once())->method('getVariant')
            ->willReturn($productVariant);
        $orderItem = $this->createMock(OrderItem::class);
        $orderItem->expects($this->once())->method('getPreferredItems')
            ->willReturn(new ArrayCollection([$preferredOrderItem]));
        $order = $this->createMock(Order::class);
        $order->expects($this->once())->method('hasServiceProduct')
            ->with('service_prescription')
            ->willReturn(false);
        $order->expects($this->once())->method('getItems')
            ->willReturn(new ArrayCollection([$orderItem]));

        // Act
        $result = $this->supplierIdentifierResolver->resolveForOrder($order);

        // Assert
        $this->assertSame(self::PREFERRED_PRODUCT_SUPPLIER, $result->getIdentifier());
    }

    public function testItThrowsExceptionForDoctorWithoutRegistrationNumber(): void
    {
        // Arrange
        $order = $this->createMock(Order::class);
        $order->expects($this->once())->method('hasServiceProduct')
            ->with('service_prescription')
            ->willReturn(true);
        $order->expects($this->once())->method('getDoctorRegistrationNumber')
            ->willReturn('');

        // Act / Assert
        $this->expectException(MissingDoctorException::class);
        $this->supplierIdentifierResolver->resolveForOrder($order);
    }

    public function testItThrowsExceptionForDoctorNotLinkedToSupplier(): void
    {
        // Arrange
        $order = $this->createMock(Order::class);
        $order->expects($this->once())->method('hasServiceProduct')
            ->with('service_prescription')
            ->willReturn(true);
        $order->expects($this->once())->method('getDoctorRegistrationNumber')
            ->willReturn(self::DOCTOR_REGISTRATION_NUMBER);
        $this->supplierDoctorRepository->expects($this->once())->method('findOneBy')
            ->with(['registrationNumber' => self::DOCTOR_REGISTRATION_NUMBER])
            ->willReturn(null);

        // Act / Assert
        $this->expectException(MissingDoctorException::class);
        $this->supplierIdentifierResolver->resolveForOrder($order);
    }

    public function testItThrowsExceptionForDoctorLinkedToDisabledSupplier(): void
    {
        // Arrange
        $order = $this->createMock(Order::class);
        $order->expects($this->once())->method('hasServiceProduct')
            ->with('service_prescription')
            ->willReturn(true);
        $order->expects($this->once())->method('getDoctorRegistrationNumber')
            ->willReturn(self::DOCTOR_REGISTRATION_NUMBER);
        $this->supplierDoctorRepository->expects($this->once())->method('findOneBy')
            ->with(['registrationNumber' => self::DOCTOR_REGISTRATION_NUMBER])
            ->willReturn(SupplierDoctorFactory::create([
                'supplier' => SupplierFactory::createPrefilled([
                    'enabled' => false,
                    'identifier' => 'test-doctor-supplier-identifier',
                ]),
            ]));

        // Assert
        $this->expectException(UnresolvableSupplierException::class);
        $this->expectExceptionMessage('Supplier with identifier "test-doctor-supplier-identifier" is disabled.');

        // Act
        $this->supplierIdentifierResolver->resolveForOrder($order);
    }

    public function testItThrowsExceptionForOrderWithoutItems(): void
    {
        // Arrange
        $order = $this->createMock(Order::class);
        $order->expects($this->once())->method('hasServiceProduct')
            ->with('service_prescription')
            ->willReturn(false);
        $order->expects($this->once())->method('getItems')
            ->willReturn(new ArrayCollection());

        // Act / Assert
        $this->expectException(UnresolvableSupplierException::class);
        $this->supplierIdentifierResolver->resolveForOrder($order);
    }

    private function provideDokGbServiceWithoutProducts(): array
    {
        $shipment = ShipmentFactory::create([
            'id' => 1337,
            'state' => 'new',
            'shippingMethod' => ShipmentFactory::createShippingMethod([
                'name' => 'test-shipping-method',
            ]),
            'supplierShipmentReference' => 'test-supplier-shipment-reference',
        ]);

        $consultProductVariant = ProductVariantFactory::createPrefilled(['id' => 1], ProductType::CONSULT);
        $consultOrderItem = OrderItemFactory::create(['id' => 1, 'variant' => $consultProductVariant]);
        $consultOrderItem->addUnit(
            OrderFactory::createOrderItemUnit([
                'orderItem' => $consultOrderItem,
                'shipment' => $shipment,
            ])
        );

        $order = OrderFactory::createPrefilled([
            'shipment' => $shipment,
            'items' => [$consultOrderItem],
            'doctorName' => null,
            'doctorRegistrationNumber' => null,
            'channel' => ChannelFactory::create([
                'id' => 1,
                'code' => 'dok_gb',
                'addPrescriptionMedicationDirectlyToCart' => true,
            ]),
        ]);
        $shipment = $order->getShipments()->first();

        return ['order' => $order, 'shipment' => $shipment, 'exception' => UnresolvableSupplierException::class];
    }

    private function provideBlueclinicServiceWithoutPerferredProducts(): array
    {
        $shipment = ShipmentFactory::create([
            'id' => 1337,
            'state' => 'new',
            'shippingMethod' => ShipmentFactory::createShippingMethod([
                'name' => 'test-shipping-method',
            ]),
            'supplierShipmentReference' => 'test-supplier-shipment-reference',
        ]);

        $consultProductVariant = ProductVariantFactory::createPrefilled(['id' => 1], ProductType::CONSULT);
        $consultOrderItem = OrderItemFactory::create(['id' => 1, 'variant' => $consultProductVariant]);
        $consultOrderItem->addUnit(
            OrderFactory::createOrderItemUnit([
                'orderItem' => $consultOrderItem,
                'shipment' => $shipment,
            ])
        );

        $order = OrderFactory::createPrefilled([
            'shipment' => $shipment,
            'items' => [$consultOrderItem],
            'doctorName' => null,
            'doctorRegistrationNumber' => null,
        ]);
        $shipment = $order->getShipments()->first();

        return ['order' => $order, 'shipment' => $shipment, 'exception' => UnresolvableSupplierException::class];
    }

    private function provideBlueclinicServiceWithPerferredProducts(): array
    {
        $shipment = ShipmentFactory::create([
            'id' => 1337,
            'state' => 'new',
            'shippingMethod' => ShipmentFactory::createShippingMethod([
                'name' => 'test-shipping-method',
            ]),
            'supplierShipmentReference' => 'test-supplier-shipment-reference',
        ]);

        $consultProductVariant = ProductVariantFactory::createPrefilled(['id' => 1], ProductType::CONSULT);
        $consultOrderItem = OrderItemFactory::create(['id' => 1, 'variant' => $consultProductVariant]);
        $consultOrderItem->addUnit(
            OrderFactory::createOrderItemUnit([
                'orderItem' => $consultOrderItem,
                'shipment' => $shipment,
            ])
        );

        $medicineProductVariant = ProductVariantFactory::createPrefilled(['id' => 2]);
        $medicinePreferredOrderItem = PreferredOrderItemFactory::createPrefilled(['orderItem' => $consultOrderItem, 'variant' => $medicineProductVariant]);
        $consultOrderItem->addPreferredItem($medicinePreferredOrderItem);

        $blueclinicOrderItemServiceVariant = ProductVariantFactory::createPrefilled(
            [
                'id' => 1,
                'code' => ProductInterface::SERVICE_BLUECLINIC_CODE,
                'productData' => ['code' => ProductInterface::SERVICE_BLUECLINIC_CODE],
            ],
            ProductType::SERVICE
        );
        $blueclinicOrderItem = OrderItemFactory::create(['id' => 1, 'variant' => $blueclinicOrderItemServiceVariant]);
        $blueclinicOrderItem->addUnit(
            OrderFactory::createOrderItemUnit([
                'orderItem' => $blueclinicOrderItem,
                'shipment' => $shipment,
            ])
        );

        $order = OrderFactory::createPrefilled([
            'shipment' => $shipment,
            'items' => [$consultOrderItem, $blueclinicOrderItem],
            'doctorName' => null,
            'doctorRegistrationNumber' => null,
        ]);
        $shipment = $order->getShipments()->first();

        return ['order' => $order, 'shipment' => $shipment, 'exception' => UnresolvableSupplierException::class];
    }

    private function providePrescriptionWithPreferredProductsOrder(): array
    {
        $shipment = ShipmentFactory::create([
            'id' => 1336,
            'state' => 'new',
            'shippingMethod' => ShipmentFactory::createShippingMethod([
                'name' => 'test-shipping-method',
            ]),
            'supplierShipmentReference' => 'test-supplier-shipment-reference',
        ]);

        $consultProductVariant = ProductVariantFactory::createPrefilled(['id' => 1], ProductType::CONSULT);
        $consultOrderItem = OrderItemFactory::create(['id' => 1, 'variant' => $consultProductVariant]);
        $consultOrderItemUnit = OrderFactory::createOrderItemUnit([
            'orderItem' => $consultOrderItem,
            'shipment' => $shipment,
        ]);
        $shipment->addUnit($consultOrderItemUnit);
        $consultOrderItem->addUnit($consultOrderItemUnit);

        $medicineProductVariant = ProductVariantFactory::createPrefilled(['id' => 2]);
        $medicinePreferredOrderItem = PreferredOrderItemFactory::createPrefilled(['orderItem' => $consultOrderItem, 'variant' => $medicineProductVariant]);
        $consultOrderItem->addPreferredItem($medicinePreferredOrderItem);

        $prescriptionServiceVariant = ProductVariantFactory::createPrefilled(
            [
                'id' => 2,
                'code' => ProductInterface::SERVICE_PRESCRIPTION_CODE,
                'productData' => ['code' => ProductInterface::SERVICE_PRESCRIPTION_CODE],
                'supplier' => SupplierFactory::createPrefilled(),
            ],
            ProductType::SERVICE
        );
        $prescriptionOrderItem = OrderItemFactory::create(['id' => 1, 'variant' => $prescriptionServiceVariant]);
        $prescriptionOrderItemUnit = OrderFactory::createOrderItemUnit([
            'orderItem' => $prescriptionOrderItem,
            'shipment' => $shipment,
        ]);
        $shipment->addUnit($consultOrderItemUnit);
        $prescriptionOrderItem->addUnit($prescriptionOrderItemUnit);

        $order = OrderFactory::createPrefilled([
            'shipment' => $shipment,
            'items' => [$consultOrderItem, $prescriptionOrderItem],
            'doctorName' => null,
            'doctorRegistrationNumber' => null,
        ]);
        $shipment = $order->getShipments()->first();

        return ['order' => $order, 'shipment' => $shipment, 'exception' => MissingDoctorException::class];
    }

    private function providePrescriptionWithoutPreferredProductsOrder(): array
    {
        $shipment = ShipmentFactory::create([
            'id' => 1338,
            'state' => 'new',
            'shippingMethod' => ShipmentFactory::createShippingMethod([
                'name' => 'test-shipping-method',
            ]),
            'supplierShipmentReference' => 'test-supplier-shipment-reference',
        ]);

        $consultProductVariant = ProductVariantFactory::createPrefilled(['id' => 2], ProductType::CONSULT);
        $consultOrderItem = OrderItemFactory::create(['id' => 2, 'variant' => $consultProductVariant]);
        $consultOrderItemUnit = OrderFactory::createOrderItemUnit([
            'orderItem' => $consultOrderItem,
            'shipment' => $shipment,
        ]);
        $shipment->addUnit($consultOrderItemUnit);
        $consultOrderItem->addUnit($consultOrderItemUnit);

        $prescriptionServiceVariant = ProductVariantFactory::createPrefilled(
            [
                'id' => 2,
                'code' => ProductInterface::SERVICE_PRESCRIPTION_CODE,
                'productData' => ['code' => ProductInterface::SERVICE_PRESCRIPTION_CODE],
            ],
            ProductType::SERVICE
        );
        $prescriptionOrderItem = OrderItemFactory::create(['id' => 2, 'variant' => $prescriptionServiceVariant]);
        $prescriptionOrderItemUnit = OrderFactory::createOrderItemUnit([
            'orderItem' => $prescriptionOrderItem,
            'shipment' => $shipment,
        ]);
        $shipment->addUnit($prescriptionOrderItemUnit);
        $prescriptionOrderItem->addUnit($prescriptionOrderItemUnit);

        $order = OrderFactory::createPrefilled([
            'shipment' => $shipment,
            'items' => [$consultOrderItem, $prescriptionOrderItem],
            'doctorName' => null,
            'doctorRegistrationNumber' => null,
        ]);
        $shipment = $order->getShipments()->first();

        return ['order' => $order, 'shipment' => $shipment, 'exception' => MissingDoctorException::class];
    }

    private function provideSupplierIsResolvedForPrescriptionServiceWithDoctor(): array
    {
        $shipment = ShipmentFactory::create([
            'id' => 1337,
            'state' => 'new',
            'shippingMethod' => ShipmentFactory::createShippingMethod([
                'name' => 'test-shipping-method',
            ]),
            'supplierShipmentReference' => 'test-supplier-shipment-reference',
        ]);

        $consultProductVariant = ProductVariantFactory::createPrefilled(['id' => 1], ProductType::CONSULT);
        $consultOrderItem = OrderItemFactory::create(['id' => 1, 'variant' => $consultProductVariant]);
        $consultOrderItemUnit = OrderFactory::createOrderItemUnit([
            'orderItem' => $consultOrderItem,
            'shipment' => $shipment,
        ]);
        $shipment->addUnit($consultOrderItemUnit);
        $consultOrderItem->addUnit(
            $consultOrderItemUnit
        );

        $prescriptionServiceVariant = ProductVariantFactory::createPrefilled(
            [
                'id' => 1,
                'code' => ProductInterface::SERVICE_PRESCRIPTION_CODE,
                'productData' => ['code' => ProductInterface::SERVICE_PRESCRIPTION_CODE],
                'supplier' => SupplierFactory::createPrefilled(),
            ],
            ProductType::SERVICE
        );

        $prescriptionOrderItem = OrderItemFactory::create(['id' => 1, 'variant' => $prescriptionServiceVariant]);
        $prescriptionOrderItemUnit = OrderFactory::createOrderItemUnit([
            'orderItem' => $prescriptionOrderItem,
            'shipment' => $shipment,
        ]);

        $shipment->addUnit($prescriptionOrderItemUnit);

        $prescriptionOrderItem->addUnit(
            $prescriptionOrderItemUnit
        );

        $order = OrderFactory::createPrefilled([
            'shipment' => $shipment,
            'items' => [$consultOrderItem, $prescriptionOrderItem],
        ]);
        $shipment = $order->getShipments()->first();

        return ['order' => $order, 'shipment' => $shipment, 'expectedSupplier' => 'test-supplier'];
    }

    private function provideSupplierIsResolved(): array
    {
        $shipment = ShipmentFactory::create([
            'id' => 1337,
            'state' => 'new',
            'shippingMethod' => ShipmentFactory::createShippingMethod([
                'name' => 'test-shipping-method',
            ]),
            'supplierShipmentReference' => 'test-supplier-shipment-reference',
        ]);

        $consultProductVariant = ProductVariantFactory::createPrefilled(
            ['id' => 1],
            ProductType::CONSULT,
            'en',
            'en',
            ['en']
        );

        $consultOrderItem = OrderItemFactory::create([
            'id' => 1,
            'variant' => $consultProductVariant,
        ]);

        $consultOrderItem->addUnit(
            OrderFactory::createOrderItemUnit([
                'orderItem' => $consultOrderItem,
                'shipment' => $shipment,
            ])
        );

        $medicineProductVariant = ProductVariantFactory::createPrefilled(
            ['id' => 2],
            ProductType::MEDICATION,
            'en',
            'en',
            ['en']
        );

        $medicineOrderItem = OrderItemFactory::create([
            'id' => 2,
            'variant' => $medicineProductVariant,
            'parentOrderItem' => $consultOrderItem,
        ]);

        $medicineOrderItem->addUnit(
            OrderFactory::createOrderItemUnit([
                'orderItem' => $medicineOrderItem,
                'shipment' => $shipment,
            ])
        );

        $order = OrderFactory::create([
            'id' => 1337,
            'state' => 'new',
            'checkoutState' => 'completed',
            'paymentState' => 'paid',
            'prescriptionState' => 'awaiting_prescription',
            'shippingState' => 'awaiting_prescription',
            'createdAt' => new DateTimeImmutable('2022-01-01 12:00:00'),
            'updatedAt' => new DateTimeImmutable('2022-01-01 13:00:00'),
            'currencyCode' => 'EUR',
            'number' => 'DO1337',
            'tokenValue' => 'test-token-value',
            'channel' => ChannelFactory::create([
                'id' => 1,
                'code' => 'dok_gb',
            ]),
            'checkoutCompletedAt' => new DateTimeImmutable('2022-01-01 13:00:00'),
            'customer' => CustomerFactory::create([
                'id' => 1,
                'email' => '<EMAIL>',
            ]),
            'localeCode' => 'en',
            'shipments' => [
                $shipment,
            ],
            'items' => [
                $consultOrderItem,
                $medicineOrderItem,
            ],
        ]);

        $shipment = $order->getShipments()->first();

        return ['order' => $order, 'shipment' => $shipment, 'expectedSupplier' => 'test-supplier'];
    }
}
