<?php

declare(strict_types=1);

namespace App\Tests\Supplier\Resolver;

use App\Entity\Product\ProductType;
use App\Resolver\ProductVariant\ProductVariantsResolver;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\OrderItemFactory;
use App\Tests\Util\Factory\PreferredOrderItemFactory;
use App\Tests\Util\Factory\ProductVariantFactory;
use PHPUnit\Framework\TestCase;

final class ProductVariantsResolverTest extends TestCase
{
    private ProductVariantsResolver $productVariantsResolver;

    protected function setUp(): void
    {
        $this->productVariantsResolver = new ProductVariantsResolver();
    }

    public function testItResolvesProductVariants(): void
    {
        // Arrange
        $expectedProductVariant = ProductVariantFactory::createPrefilled();
        $order = OrderFactory::create([
            'channel' => ChannelFactory::createPrefilled(),
            'items' => [
                OrderItemFactory::create([
                    'variant' => $expectedProductVariant,
                ]),
            ],
        ]);

        // Act
        $productVariants = $this->productVariantsResolver->resolve($order);

        // Assert
        $this->assertSame([$expectedProductVariant], $productVariants);
    }

    public function testItResolvesPreferredProductVariants(): void
    {
        // Arrange
        $expectedProductVariant = ProductVariantFactory::createPrefilled();

        $orderItem = OrderItemFactory::create([
            'variant' => ProductVariantFactory::createPrefilled([], ProductType::CONSULT),
        ]);

        $orderItem->addPreferredItem(
            PreferredOrderItemFactory::create([
                'orderItem' => $orderItem,
                'variant' => $expectedProductVariant,
            ])
        );

        $order = OrderFactory::create([
            'channel' => ChannelFactory::createPrefilled(),
            'items' => [$orderItem],
        ]);

        // Act
        $productVariants = $this->productVariantsResolver->resolve($order);

        // Assert
        $this->assertSame([$expectedProductVariant], $productVariants);
    }

    public function testItDoesNotResolveItemsWithoutProductVariants(): void
    {
        // Arrange
        $order = OrderFactory::create([
            'channel' => ChannelFactory::createPrefilled(),
            'items' => [
                OrderItemFactory::create(),
            ],
        ]);

        // Act
        $productVariants = $this->productVariantsResolver->resolve($order);

        // Assert
        $this->assertSame([], $productVariants);
    }

    public function testItDoesNotResolveServiceProducts(): void
    {
        // Arrange
        $order = OrderFactory::create([
            'channel' => ChannelFactory::createPrefilled(),
            'items' => [
                OrderItemFactory::create([
                    'variant' => ProductVariantFactory::createPrefilled([], ProductType::SERVICE),
                ]),
            ],
        ]);

        // Act
        $productVariants = $this->productVariantsResolver->resolve($order);

        // Assert
        $this->assertSame([], $productVariants);
    }
}
