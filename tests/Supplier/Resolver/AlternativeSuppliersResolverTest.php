<?php

declare(strict_types=1);

namespace App\Tests\Supplier\Resolver;

use App\Entity\Product\ProductVariantInterface;
use App\Entity\Supplier\SupplierInterface;
use App\Repository\SupplierRepositoryInterface;
use App\Resolver\ProductVariant\ProductVariantsResolverInterface;
use App\Supplier\Resolver\AlternativeSuppliersResolver;
use App\Supplier\Resolver\AlternativeSuppliersResolverInterface;
use App\Supplier\Resolver\Exception\UnresolvableSupplierException;
use App\Supplier\Resolver\SupplierIdentifierResolverInterface;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\ProductVariantFactory;
use App\Tests\Util\Factory\SupplierFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class AlternativeSuppliersResolverTest extends TestCase
{
    private AlternativeSuppliersResolverInterface $alternativeSuppliersResolver;
    private ProductVariantsResolverInterface&MockObject $productVariantsResolver;
    private SupplierIdentifierResolverInterface&MockObject $supplierIdentifierResolver;
    private SupplierRepositoryInterface&MockObject $supplierRepository;

    protected function setUp(): void
    {
        $this->productVariantsResolver = $this->createMock(ProductVariantsResolverInterface::class);
        $this->supplierIdentifierResolver = $this->createMock(SupplierIdentifierResolverInterface::class);
        $this->supplierRepository = $this->createMock(SupplierRepositoryInterface::class);

        $this->alternativeSuppliersResolver = new AlternativeSuppliersResolver(
            $this->productVariantsResolver,
            $this->supplierIdentifierResolver,
            $this->supplierRepository,
        );
    }

    public function testItGetsAlternativeSuppliersFromOrderItems(): void
    {
        // Arrange
        $order = OrderFactory::createPrefilled();

        $this->supplierIdentifierResolver->method('resolveForOrder')
            ->with($order)
            ->willThrowException(new UnresolvableSupplierException());

        // Act
        $suppliers = $this->alternativeSuppliersResolver->resolve($order);

        // Assert
        $this->assertSame([], $suppliers);
    }

    public function testItDoesntThrowExceptionWithoutSupplierOnOrderItems(): void
    {
        // Arrange
        $order = OrderFactory::createPrefilled();
        $allSuppliers = $this->getAllSuppliers();
        $currentSupplier = $allSuppliers[1];
        $expectedSuppliers = [$allSuppliers[1], $allSuppliers[3]];

        $this->supplierIdentifierResolver->method('resolveForOrder')
            ->with($order)
            ->willReturn($currentSupplier);
        $this->productVariantsResolver->method('resolve')
            ->with($order)
            ->willReturn($this->getProductVariants());
        $this->supplierRepository->method('findAll')
            ->willReturn($allSuppliers);

        // Only Test Supplier 3 is present in all variants
        $this->supplierRepository->method('findSuppliersHavingProductVariantInStock')
            ->willReturnCallback(
                static function (
                    ProductVariantInterface $productVariant,
                ) use ($allSuppliers) {
                    if ($productVariant->getCode() === 'test-code-2') {
                        return [$allSuppliers[3]];
                    }

                    return [$allSuppliers[2], $allSuppliers[3]];
                }
            );

        // Act
        $suppliers = $this->alternativeSuppliersResolver->resolve($order);

        // Assert
        $this->assertSame($expectedSuppliers, array_values($suppliers));
    }

    /**
     * @return array<ProductVariantInterface>
     */
    private function getProductVariants(): array
    {
        $productVariants = [];
        for ($i = 1; $i <= 5; ++$i) {
            $productVariants[] = ProductVariantFactory::createPrefilled([
                'code' => sprintf('test-code-%d', $i),
            ]);
        }

        return $productVariants;
    }

    /**
     * @return array<SupplierInterface>
     */
    private function getAllSuppliers(): array
    {
        $suppliers = [];
        for ($i = 1; $i <= 5; ++$i) {
            $suppliers[$i] = SupplierFactory::create([
                'id' => $i,
                'name' => sprintf('Test Supplier %d', $i),
                'identifier' => sprintf('test-supplier-%d', $i),
            ]);
        }

        return $suppliers;
    }
}
