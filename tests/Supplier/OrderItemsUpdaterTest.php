<?php

declare(strict_types=1);

namespace App\Tests\Supplier;

use App\Entity\Order\OrderItemRefund;
use App\Entity\Supplier\SupplierInterface;
use App\Repository\ProductVariantRepositoryInterface;
use App\Supplier\OrderItemsUpdater;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\OrderItemFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class OrderItemsUpdaterTest extends TestCase
{
    private OrderItemsUpdater $orderItemsUpdater;
    private MockObject&ProductVariantRepositoryInterface $productVariantRepositoryMock;

    protected function setUp(): void
    {
        $this->productVariantRepositoryMock = $this->createMock(ProductVariantRepositoryInterface::class);

        $this->orderItemsUpdater = new OrderItemsUpdater(
            $this->productVariantRepositoryMock,
        );
    }

    public function testUpdateOrderItemsDoesNotUpdateRefundedOrderItems(): void
    {
        // Arrange
        $order = OrderFactory::create([
            'channel' => ChannelFactory::create(),
            'items' => [
                OrderItemFactory::create([
                    'orderItemRefund' => $this->createStub(OrderItemRefund::class),
                ]),
            ],
        ]);

        // Assert
        $this->productVariantRepositoryMock->expects(self::never())
            ->method('findOneByCodePrefixAndSupplier');

        // Act
        $this->orderItemsUpdater->updateOrderItems(
            $order,
            $order->getItems(),
            $this->createStub(SupplierInterface::class),
        );
    }
}
