<?php

declare(strict_types=1);

namespace App\Tests\Supplier\MessageHandler;

use App\Entity\Shipping\Shipment;
use App\Supplier\Message\CancelSupplierServiceShipment;
use App\Supplier\MessageHandler\CancelSupplierServiceShipmentHandler;
use App\Supplier\SupplierServiceShipmentExistsCheckerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Superbrave\PharmacyServiceClient\ClientInterface;
use Sylius\Component\Core\Repository\ShipmentRepositoryInterface;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;

class CancelSupplierServiceShipmentHandlerTest extends TestCase
{
    private const string SUPPLIER_SERVICE_UUID = 'aa-bb-cc-dd';

    /** @var ClientInterface|MockObject */
    private $clientMock;

    /** @var ShipmentRepositoryInterface<Shipment>&MockObject */
    private ShipmentRepositoryInterface&MockObject $shipmentRepositoryMock;
    private SupplierServiceShipmentExistsCheckerInterface&MockObject $supplierServiceShipmentExistsCheckerMock;

    private CancelSupplierServiceShipmentHandler $handler;

    protected function setUp(): void
    {
        $this->clientMock = $this->createMock(ClientInterface::class);
        $this->shipmentRepositoryMock = $this->createMock(ShipmentRepositoryInterface::class);
        $this->supplierServiceShipmentExistsCheckerMock = $this->createMock(SupplierServiceShipmentExistsCheckerInterface::class);

        $this->handler = new CancelSupplierServiceShipmentHandler(
            $this->clientMock,
            $this->shipmentRepositoryMock,
            $this->createMock(LoggerInterface::class),
            $this->supplierServiceShipmentExistsCheckerMock,
        );
    }

    public function testItCancelsSupplierServiceShipmentWithReference(): void
    {
        $shipmentMock = $this->createConfiguredMock(Shipment::class, [
            'getId' => 1,
            'getSupplierShipmentReference' => self::SUPPLIER_SERVICE_UUID,
        ]);

        $this->shipmentRepositoryMock
            ->expects($this->once())
            ->method('find')
            ->willReturn($shipmentMock);

        $this->supplierServiceShipmentExistsCheckerMock
            ->expects($this->once())
            ->method('exists')
            ->with($shipmentMock)
            ->willReturn(true);

        $this->clientMock->expects($this->once())
            ->method('cancelOrder')
            ->with(self::SUPPLIER_SERVICE_UUID);

        ($this->handler)(new CancelSupplierServiceShipment($shipmentMock));
    }

    public function testItThrowsAnErrorWhenSupplierServiceReferenceIsNotSetOnShipment(): void
    {
        $shipmentMock = $this->createConfiguredMock(Shipment::class, [
            'getId' => 1,
            'getSupplierShipmentReference' => self::SUPPLIER_SERVICE_UUID,
        ]);

        $this->shipmentRepositoryMock
            ->expects($this->once())
            ->method('find')
            ->willReturn($shipmentMock);

        $this->supplierServiceShipmentExistsCheckerMock
            ->expects($this->once())
            ->method('exists')
            ->with($shipmentMock)
            ->willReturn(false);

        $this->expectException(UnrecoverableMessageHandlingException::class);
        $this->expectExceptionMessage(
            'Unable to cancel supplier service shipment for shipment with id: 1, shipment was not found.'
        );

        ($this->handler)(new CancelSupplierServiceShipment($shipmentMock));
    }
}
