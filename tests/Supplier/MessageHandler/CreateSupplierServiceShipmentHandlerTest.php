<?php

declare(strict_types=1);

namespace App\Tests\Supplier\MessageHandler;

use App\Entity\Addressing\Address;
use App\Entity\BusinessUnit\BusinessUnit;
use App\Entity\Channel\Channel;
use App\Entity\Channel\ChannelPricing;
use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Order\OrderItemUnit;
use App\Entity\Product\Product;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariant;
use App\Entity\Product\ProductVariantTranslation;
use App\Entity\Shipping\Shipment;
use App\Entity\Supplier\Supplier;
use App\OrderProcessing\Shipment\Message\CancelShipment;
use App\Payment\Command\CompletePayments;
use App\StateMachine\Callback\SetSupplierShipmentReference;
use App\Supplier\CreateSupplierServiceShipmentDispatcherInterface;
use App\Supplier\Exception\ConvertToSupplierServiceDtoException;
use App\Supplier\Message\CreateSupplierServiceShipment;
use App\Supplier\MessageHandler\CreateSupplierServiceShipmentHandler;
use App\Supplier\SupplierServiceShipmentCreatorInterface;
use App\Supplier\SupplierServiceShipmentExistsCheckerInterface;
use App\Tests\Mocks\Entity\TestChannel;
use App\Tests\Mocks\Entity\TestCustomer;
use App\Tests\Mocks\Entity\TestOrder;
use App\Tests\Mocks\Entity\TestOrderItem;
use App\Tests\Util\Factory\BusinessUnitFactory;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\PaymentFactory;
use App\Tests\Util\Factory\ProductFactory;
use App\Tests\Util\Factory\ShipmentFactory;
use DateTimeImmutable;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use SM\Factory\FactoryInterface;
use SM\StateMachine\StateMachineInterface;
use stdClass;
use Superbrave\PharmacyServiceClient\Model\Response\Order as SupplierServiceOrderResponse;
use Sylius\Component\Core\Repository\ShipmentRepositoryInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Exception\RecoverableMessageHandlingException;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Messenger\MessageBusInterface;

class CreateSupplierServiceShipmentHandlerTest extends TestCase
{
    public const string CHANNEL_CODE = 'channel_code';
    private const string TEST_DATE = '01-01-2022';

    private const string TEST_LOCALE = 'en';

    private const string SUPPLIER_SERVICE_UUID = 'aa-bb-cc-dd';

    private const string TEST_SUPPLIER = 'test-supplier';

    /** @var ShipmentRepositoryInterface<Shipment>&MockObject */
    private ShipmentRepositoryInterface&MockObject $shipmentRepositoryMock;

    private CreateSupplierServiceShipmentHandler $handler;

    private StateMachineInterface&MockObject $stateMachineMock;

    private MessageBusInterface&MockObject $messageBusMock;

    private SupplierServiceShipmentCreatorInterface&MockObject $supplierServiceShipmentCreator;

    private CreateSupplierServiceShipmentDispatcherInterface&MockObject $createSupplierServiceShipmentDispatcherMock;

    private SupplierServiceShipmentExistsCheckerInterface&MockObject $supplierServiceShipmentExistsChecker;
    private SetSupplierShipmentReference $setSupplierShipmentReference;

    protected function setUp(): void
    {
        $this->shipmentRepositoryMock = $this->createMock(ShipmentRepositoryInterface::class);
        $this->stateMachineMock = $this->createMock(StateMachineInterface::class);
        $this->messageBusMock = $this->createMock(MessageBusInterface::class);
        $this->supplierServiceShipmentCreator = $this->createMock(SupplierServiceShipmentCreatorInterface::class);
        $this->createSupplierServiceShipmentDispatcherMock = $this->createMock(CreateSupplierServiceShipmentDispatcherInterface::class);
        $this->supplierServiceShipmentExistsChecker = $this->createMock(SupplierServiceShipmentExistsCheckerInterface::class);
        $this->setSupplierShipmentReference = new SetSupplierShipmentReference(
            $this->createMock(EntityManagerInterface::class)
        );

        $factoryMock = $this->createMock(FactoryInterface::class);
        $factoryMock->method('get')
            ->willReturn($this->stateMachineMock);

        $entityManagerMock = $this->createMock(EntityManager::class);
        $entityManagerMock->method('getConnection')->willReturn($this->createMock(Connection::class));

        $this->handler = new CreateSupplierServiceShipmentHandler(
            $this->messageBusMock,
            $this->shipmentRepositoryMock,
            $entityManagerMock,
            $factoryMock,
            $this->supplierServiceShipmentCreator,
            $this->createSupplierServiceShipmentDispatcherMock,
            $this->supplierServiceShipmentExistsChecker,
            $this->setSupplierShipmentReference,
        );
    }

    public function testItSendsCorrectShipmentData(): void
    {
        $shipment = $this->createShipment();
        $shipmentMock = $this->createMock(Shipment::class);
        $shipmentMock->expects($this->once())
            ->method('getId')
            ->willReturn(1337);

        $this->shipmentRepositoryMock->expects($this->once())
            ->method('find')
            ->with(1337)
            ->willReturn($shipment);

        $supplierServiceOrderResponse = $this->createMock(SupplierServiceOrderResponse::class);
        $supplierServiceOrderResponse->method('getUuid')->willReturn(self::SUPPLIER_SERVICE_UUID);

        $this->supplierServiceShipmentCreator->method('create')->willReturn($supplierServiceOrderResponse);

        /** @var Order $order */
        $order = $shipment->getOrder();
        $order->setPaymentState('paid');
        $order->getLastPayment()?->setState('authorized');

        $this->messageBusMock->expects($this->never())
            ->method('dispatch')
            ->with(new CompletePayments($order->getId()))
            ->willReturn(new Envelope(new stdClass()));

        $this->stateMachineMock->expects($this->once())
            ->method('can')
            ->willReturn(true);

        $this->stateMachineMock->expects($this->once())
            ->method('apply')
            ->with('notify_supplier');

        $this->createSupplierServiceShipmentDispatcherMock->method('isAllowed')
            ->willReturn(true);

        ($this->handler)(new CreateSupplierServiceShipment($shipmentMock));

        self::assertEquals(self::SUPPLIER_SERVICE_UUID, $shipment->getSupplierShipmentReference());
    }

    public function testItThrowsAnErrorWhenOrderInformationIsIncomplete(): void
    {
        // Arrange
        $shipmentMock = $this->createMock(Shipment::class);
        $shipmentMock
            ->method('getId')
            ->willReturn(1337);

        $businessUnit = new BusinessUnit();
        $businessUnit->setCompanyName('Test-Business-Unit');

        $channel = new Channel();
        $channel->setBusinessUnit($businessUnit);

        $order = new TestOrder();
        $order->setId(1337);
        $order->setChannel($channel);
        $order->setPaymentState('paid');
        $payment = PaymentFactory::create([
            'details' => ['uuid' => '0a9885f8-27de-46cd-84ac-11e89b319823',
                'state' => 'authorized',
            ]]);
        $order->addPayment($payment);

        $this->stateMachineMock->expects($this->once())
            ->method('can')
            ->willReturn(true);

        $this->messageBusMock->expects($this->never())
            ->method('dispatch')
            ->with(new CompletePayments($order->getId()))
            ->willReturn(new Envelope(new stdClass()));

        $shipmentMock
            ->method('getOrder')
            ->willReturn($order);

        $this->shipmentRepositoryMock->expects($this->once())
            ->method('find')
            ->with(1337)
            ->willReturn($shipmentMock);

        $this->supplierServiceShipmentCreator->method('create')->willThrowException(new ConvertToSupplierServiceDtoException('Unable to create supplier service shipment for shipment with id: 1337'));

        $this->expectException(UnrecoverableMessageHandlingException::class);
        $this->expectExceptionMessage('Unable to create supplier service shipment for shipment with id: 1337');

        $this->stateMachineMock->expects($this->never())
            ->method('apply');

        $this->createSupplierServiceShipmentDispatcherMock->method('isAllowed')
            ->willReturn(true);

        ($this->handler)(new CreateSupplierServiceShipment($shipmentMock));
    }

    public function testOrderWithBlueClinicServiceWillNotSend(): void
    {
        $shipmentMock = $this->createMock(Shipment::class);
        $shipmentMock
            ->method('getId')
            ->willReturn(1337);

        $order = $this->createOrderWithBlueclinicService();
        $channel = $this->createChannelWithMedicationChannel();

        $order->setChannel($channel);
        $shipmentMock->method('getOrder')
            ->willReturn($order);

        $this->shipmentRepositoryMock->expects($this->once())
            ->method('find')
            ->with(1337)
            ->willReturn($shipmentMock);

        $shipmentMock->expects($this->once())
            ->method('setSupplierShipmentReference');

        $this->messageBusMock->method('dispatch')
            ->willReturn(new Envelope(new stdClass()));

        $this->createSupplierServiceShipmentDispatcherMock->method('isAllowed')
            ->willReturn(true);

        ($this->handler)(new CreateSupplierServiceShipment($shipmentMock));
    }

    public function testDispatcherNotAllowedWillNotSend(): void
    {
        // Arrange
        $shipment = ShipmentFactory::create([
            'id' => 1337,
            'order' => OrderFactory::create([
                'channel' => ChannelFactory::create([
                    'businessUnit' => BusinessUnitFactory::create([
                        'companyName' => 'TestDOK',
                    ]),
                ]),
            ]),
        ]);

        $this->shipmentRepositoryMock->method('find')
            ->willReturn($shipment);

        $this->createSupplierServiceShipmentDispatcherMock->method('isAllowed')
            ->willReturn(false);

        // Assert
        $this->messageBusMock->expects(self::never())
            ->method('dispatch');

        $this->expectException(UnrecoverableMessageHandlingException::class);
        $this->expectExceptionMessage("Shipment with id '1337' is not allowed to be sent to the supplier service.");

        // Act
        ($this->handler)(new CreateSupplierServiceShipment($shipment));
    }

    public function testShipmentWithSupplierShipmentExistsWillNotSend(): void
    {
        // Arrange
        $shipmentMock = $this->createMock(Shipment::class);
        $shipmentMock->method('getId')->willReturn(1337);
        $shipmentMock->method('getSupplierShipmentReference')->willReturn('test-supplier-order-reference');

        $channel = $this->createChannelWithMedicationChannel();

        $order = new TestOrder();
        $order->setPaymentState('paid');
        $order->addShipment($shipmentMock);
        $order->setChannel($channel);
        $order->setTokenValue('test-token-value');

        $shipmentMock->method('getOrder')
            ->willReturn($order);

        $this->shipmentRepositoryMock->method('find')
            ->with(1337)
            ->willReturn($shipmentMock);

        // Assert
        $shipmentMock->expects($this->never())->method('setSupplierShipmentReference');
        $this->expectException(UnrecoverableMessageHandlingException::class);

        $this->supplierServiceShipmentExistsChecker
            ->expects($this->once())
            ->method('exists')
            ->with($shipmentMock)
            ->willReturn(true);

        $this->expectExceptionMessage(sprintf(
            "Skipping sending shipment for order with tokenValue '%s' to the supplier service, it already has a shipment reference '%s'.",
            $order->getTokenValue(),
            $shipmentMock->getSupplierShipmentReference(),
        ));

        $this->createSupplierServiceShipmentDispatcherMock->method('isAllowed')
            ->willReturn(true);

        // Act
        ($this->handler)(new CreateSupplierServiceShipment($shipmentMock));
    }

    public function testUnpaidOrderWillNotSend(): void
    {
        // Arrange
        $shipmentMock = $this->createMock(Shipment::class);
        $shipmentMock->method('getId')->willReturn(1337);
        $shipmentMock->method('getSupplierShipmentReference')->willReturn(null);

        $channel = $this->createChannelWithMedicationChannel();

        $order = new TestOrder();
        $order->setPaymentState('awaiting_payment');
        $order->addShipment($shipmentMock);
        $order->setChannel($channel);
        $order->setTokenValue('test-token-value');

        $shipmentMock->method('getOrder')
            ->willReturn($order);

        $this->shipmentRepositoryMock->method('find')
            ->with(1337)
            ->willReturn($shipmentMock);

        $this->createSupplierServiceShipmentDispatcherMock->method('isAllowed')
            ->willReturn(true);

        // Assert
        $shipmentMock->expects($this->once())->method('setSupplierShipmentReference');

        $this->supplierServiceShipmentExistsChecker
            ->expects($this->once())
            ->method('exists')
            ->with($shipmentMock)
            ->willReturn(false);

        $this->expectException(RecoverableMessageHandlingException::class);
        $this->expectExceptionMessage(sprintf(
            "Skipping sending order with tokenValue '%s' to the supplier service, it's paymentState is not paid or authorized.",
            $order->getTokenValue(),
        ));

        // Act
        ($this->handler)(new CreateSupplierServiceShipment($shipmentMock));
    }

    public function testOrderWithWrongShipmentStateWillNotSend(): void
    {
        // Arrange
        $shipmentMock = $this->createMock(Shipment::class);
        $shipmentMock->method('getId')->willReturn(1337);
        $shipmentMock->method('getSupplierShipmentReference')->willReturn(null);
        $shipmentMock->method('getState')->willReturn('awaiting_prescription');

        $channel = $this->createChannelWithMedicationChannel();

        $order = new TestOrder();
        $order->setPaymentState('authorized');
        $order->addShipment($shipmentMock);
        $order->setChannel($channel);
        $order->setTokenValue('test-token-value');

        $shipmentMock->method('getOrder')
            ->willReturn($order);

        $this->shipmentRepositoryMock->method('find')
            ->with(1337)
            ->willReturn($shipmentMock);

        $this->stateMachineMock->expects($this->once())
            ->method('can')
            ->willReturn(false);

        $this->createSupplierServiceShipmentDispatcherMock->method('isAllowed')
            ->willReturn(true);

        // Assert
        $shipmentMock->expects($this->once())->method('setSupplierShipmentReference');
        $this->expectException(RecoverableMessageHandlingException::class);
        $this->expectExceptionMessage(sprintf(
            "Unable to apply transition shipment with state 'awaiting_prescription' using transition 'notify_supplier' for order with tokenValue '%s'",
            $order->getTokenValue(),
        ));

        // Act
        ($this->handler)(new CreateSupplierServiceShipment($shipmentMock));
    }

    public function testOrderWithOutOfStockItemsWillNotSend(): void
    {
        // Arrange
        $channel = $this->createChannelWithMedicationChannel();

        $order = new TestOrder();
        $order->setPaymentState('paid');
        $order->setChannel($channel);
        $order->setTokenValue('test-token-value');

        $shipmentMock = $this->createMock(Shipment::class);
        $shipmentMock->method('getId')->willReturn(1337);
        $shipmentMock->method('hasOutOfStockItems')->willReturn(true);
        $shipmentMock->method('getOrder')->willReturn($order);

        $this->shipmentRepositoryMock->method('find')
            ->with(1337)
            ->willReturn($shipmentMock);

        $this->stateMachineMock->expects($this->once())
            ->method('can')
            ->willReturn(true);

        $cancelShipmentMessage = new CancelShipment($shipmentMock, 'Shipment cancelled by system because of out of stock items.');

        // Assert: Make sure the supplier shipment reference is not set on shipment
        // and the CancelShipment message is dispatched
        $shipmentMock->expects($this->once())->method('setSupplierShipmentReference');
        $this->messageBusMock->expects($this->once())
            ->method('dispatch')
            ->with($cancelShipmentMessage)
            ->willReturn(new Envelope($cancelShipmentMessage));

        $this->createSupplierServiceShipmentDispatcherMock->method('isAllowed')
            ->willReturn(true);

        // Act
        ($this->handler)(new CreateSupplierServiceShipment($shipmentMock));
    }

    private function createShipment(): Shipment
    {
        $medicationOrderItem = $this->createOrderItem();
        $medicationOrderItemUnit = new OrderItemUnit($medicationOrderItem);

        $consultOrderItem = $this->createOrderItem(ProductType::CONSULT);
        $consultOrderItemUnit = new OrderItemUnit($consultOrderItem);

        $serviceOrderItem = $this->createOrderItem(ProductType::SERVICE);
        $serviceOrderItemUnit = new OrderItemUnit($consultOrderItem);

        $order = new TestOrder();
        $order->setId(1337);
        $order->setCreatedAt(new DateTimeImmutable(self::TEST_DATE));
        $order->setBillingAddress($this->createAddress());
        $order->setShippingAddress($this->createAddress());
        $order->setCustomer($this->createCustomer());
        $order->setLocaleCode(self::TEST_LOCALE);
        $order->setChannel($this->createChannel());

        $order->addItem($medicationOrderItem);
        $order->addItem($consultOrderItem);
        $order->addItem($serviceOrderItem);

        $payment = PaymentFactory::create([
            'details' => ['uuid' => '0a9885f8-27de-46cd-84ac-11e89b319823',
                'state' => 'authorized',
            ]]);
        $order->addPayment($payment);

        $shipment = new Shipment();
        $shipment->setOrder($order);

        $shipment->addUnit($medicationOrderItemUnit);
        $shipment->addUnit($consultOrderItemUnit);
        $shipment->addUnit($serviceOrderItemUnit);

        $order->addShipment($shipment);

        return $shipment;
    }

    private function createCustomer(): TestCustomer
    {
        $customer = new TestCustomer();
        $customer->setId(1337);
        $customer->setCreatedAt(new DateTimeImmutable(self::TEST_DATE));
        $customer->setFirstName('Hans');
        $customer->setLastName('de Bever');
        $customer->setEmail('<EMAIL>');
        $customer->setPhoneNumber('06123456789');
        $customer->setBirthday(new DateTimeImmutable('01-01-1990'));
        $customer->setGender('m');

        return $customer;
    }

    private function createAddress(): Address
    {
        $address = new Address();
        $address->setCreatedAt(new DateTimeImmutable(self::TEST_DATE));
        $address->setCountryCode('gb');
        $address->setCity('Old Gastove');
        $address->setStreet('Bucketblock 1');
        $address->setPostcode('1234 AB');

        return $address;
    }

    private function createOrderItem(ProductType $productType = ProductType::MEDICATION): OrderItem
    {
        $supplier = new Supplier();
        $supplier->setIdentifier(self::TEST_SUPPLIER);

        $translation = new ProductVariantTranslation();
        $translation->setLocale(self::TEST_LOCALE);
        $translation->setName('test-variant');
        $translation->setLeaflet('https://downloads.dokteronline.com/leaflets/nl/test-leaflet.pdf');

        $product = new Product();
        ProductFactory::setType($product, $productType->value);

        $variant = new ProductVariant();
        $variant->addTranslation($translation);
        $variant->setCode('test-variant');
        $variant->setCurrentLocale(self::TEST_LOCALE);
        $variant->setCreatedAt(new DateTimeImmutable(self::TEST_DATE));
        $variant->setSupplier($supplier);
        $variant->setProduct($product);

        $channelPricing = new ChannelPricing();
        $channelPricing->setEnabled(true);
        $channelPricing->setProductVariant($variant);
        $channelPricing->setChannelCode(self::CHANNEL_CODE);

        $variant->addChannelPricing($channelPricing);

        $item = new TestOrderItem();
        $item->setId(1234);
        $item->setUsageAdvice('Do not feed to the children');
        $item->setVariant($variant);

        return $item;
    }

    private function createBlueclinicOrderItem(): OrderItem
    {
        $supplier = new Supplier();
        $supplier->setIdentifier(self::TEST_SUPPLIER);

        $translation = new ProductVariantTranslation();
        $translation->setLocale(self::TEST_LOCALE);
        $translation->setName('service_blueclinic');
        $translation->setLeaflet('https://downloads.dokteronline.com/leaflets/nl/test-leaflet.pdf');

        $product = new Product();
        $product->setCode('service_blueclinic');
        $product->setEnabled(true);
        $product->setCurrentLocale('nl');
        $product->setFallbackLocale('nl');
        ProductFactory::setType($product, ProductType::SERVICE->value);

        $variant = new ProductVariant();
        $variant->addTranslation($translation);
        $variant->setCode('service_blueclinic');
        $variant->setCurrentLocale(self::TEST_LOCALE);
        $variant->setCreatedAt(new DateTimeImmutable(self::TEST_DATE));
        $variant->setSupplier($supplier);
        $variant->setProduct($product);

        $item = new TestOrderItem();
        $item->setId(1234);
        $item->setUsageAdvice('Do not feed to the children');
        $item->setVariant($variant);

        return $item;
    }

    private function createChannel(): Channel
    {
        $channel = new Channel();
        $channel->setCode(self::CHANNEL_CODE);

        $businessUnit = new BusinessUnit();
        $businessUnit->setCompanyName('My-store');

        $channel->setBusinessUnit($businessUnit);

        return $channel;
    }

    private function createChannelWithMedicationChannel(): TestChannel
    {
        $channel = new TestChannel();
        $channel->setAddPrescriptionMedicationDirectlyToCart(false);
        $channel->setCode(self::CHANNEL_CODE);

        $medicationChannel = new TestChannel();
        $medicationChannel->setAddPrescriptionMedicationDirectlyToCart(true);
        $channel->setMedicationChannel($medicationChannel);

        $businessUnit = new BusinessUnit();
        $businessUnit->setCompanyName('My-store');

        $channel->setBusinessUnit($businessUnit);

        return $channel;
    }

    private function createOrderWithBlueclinicService(): TestOrder
    {
        $orderItem = $this->createBlueclinicOrderItem();

        $order = new TestOrder();
        $order->setId(12);
        $order->setLocaleCode('nl');
        $order->addItem($orderItem);

        return $order;
    }
}
