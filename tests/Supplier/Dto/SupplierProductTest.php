<?php

declare(strict_types=1);

namespace App\Tests\Supplier\Dto;

use App\Entity\Product\ProductVariant;
use App\Entity\Product\ProductVariantTranslation;
use App\Supplier\Dto\SupplierProduct;
use PHPUnit\Framework\TestCase;

class SupplierProductTest extends TestCase
{
    private const string LOCALE = 'en';

    private const string VARIANT_CODE = 'test-variant';

    private const string VARIANT_NAME = 'Test variant';

    public function testItCorrectlyConvertsEntityToDto(): void
    {
        $translation = new ProductVariantTranslation();
        $translation->setLocale(self::LOCALE);
        $translation->setName(self::VARIANT_NAME);
        $translation->setLeaflet('https://downloads.dokteronline.com/leaflets/nl/test-leaflet.pdf');

        $productVariant = new ProductVariant();
        $productVariant->setCode(self::VARIANT_CODE);
        $productVariant->addTranslation($translation);
        $productVariant->setSupplierVariantCode('12345');
        $productVariant->setSupplierVariantName('Test variant');

        $expectedSupplierProduct = new SupplierProduct('12345', 'Test variant');

        self::assertEquals($expectedSupplierProduct, SupplierProduct::fromEntity($productVariant));
    }
}
