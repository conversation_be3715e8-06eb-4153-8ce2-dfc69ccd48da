<?php

declare(strict_types=1);

namespace App\Tests\Supplier\Dto;

use App\Entity\Order\OrderItem;
use App\Entity\Order\OrderItemUnit;
use App\Entity\Product\ProductVariant;
use App\Entity\Product\ProductVariantTranslation;
use App\Supplier\Dto\OrderItem as OrderItemDTO;
use App\Tests\Mocks\Entity\TestOrderItem;
use PHPUnit\Framework\TestCase;
use Superbrave\PharmacyServiceClient\Model\Quantity;
use Superbrave\PharmacyServiceClient\Model\SupplierProduct;

class OrderItemTest extends TestCase
{
    private const string LOCALE = 'en';

    private const string VARIANT_CODE = 'test-variant';

    private const string VARIANT_NAME = 'Test variant';

    private const string USAGE_ADVICE = 'Do not feed to children';

    /**
     * @dataProvider orderItemProvider
     */
    public function testFromEntityReturnsExpectedOrderItem(OrderItem $orderItem, OrderItemDTO $expectedOrderItem): void
    {
        self::assertEquals($expectedOrderItem, OrderItemDTO::fromEntity($orderItem));
    }

    public function orderItemProvider(): iterable
    {
        yield 'With default values.' => [
            $this->createOrderItem(
                $this->createProductVariant()
            ),
            new OrderItemDTO(
                self::VARIANT_CODE,
                new SupplierProduct('12345', 'Test variant'),
                new Quantity(1, 'pack'),
                self::USAGE_ADVICE
            ),
        ];

        yield 'With calculated quantity (1 x 5 = 5).' => [
            $this->createOrderItem(
                $this->createProductVariant(),
                5
            ),
            new OrderItemDTO(
                self::VARIANT_CODE,
                new SupplierProduct('12345', 'Test variant'),
                new Quantity(5, 'pack'),
                self::USAGE_ADVICE
            ),
        ];

        yield 'With calculated quantity (2 x 5 = 10).' => [
            $this->createOrderItem(
                $this->createProductVariant(quantityMultiplier: 2),
                5
            ),
            new OrderItemDTO(
                self::VARIANT_CODE,
                new SupplierProduct('12345', 'Test variant'),
                new Quantity(10, 'pack'),
                self::USAGE_ADVICE
            ),
        ];
    }

    private function createProductVariant(int $quantityMultiplier = 1): ProductVariant
    {
        $translation = new ProductVariantTranslation();
        $translation->setLocale(self::LOCALE);
        $translation->setName(self::VARIANT_NAME);
        $translation->setLeaflet('https://downloads.dokteronline.com/leaflets/nl/test-leaflet.pdf');

        $productVariant = new ProductVariant();
        $productVariant->setCode(self::VARIANT_CODE);
        $productVariant->addTranslation($translation);
        $productVariant->setSupplierVariantCode('12345');
        $productVariant->setSupplierVariantName('Test variant');
        $productVariant->setQuantityMultiplier($quantityMultiplier);

        return $productVariant;
    }

    private function createOrderItem(ProductVariant $productVariant, int $quantity = 1): OrderItem
    {
        $orderItem = new TestOrderItem();
        $orderItem->setId(1337);
        $orderItem->addUnit(new OrderItemUnit($orderItem));
        $orderItem->setQuantity($quantity);
        $orderItem->setVariant($productVariant);
        $orderItem->setUsageAdvice(self::USAGE_ADVICE);

        return $orderItem;
    }
}
