<?php

declare(strict_types=1);

namespace App\Tests\Supplier\Dto;

use App\Entity\Addressing\Address;
use App\Entity\Customer\Customer;
use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Order\OrderItemUnit;
use App\Entity\Product\Product;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariant;
use App\Entity\Product\ProductVariantTranslation;
use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShipmentInterface;
use App\Entity\Supplier\Supplier;
use App\Entity\Supplier\SupplierInterface;
use App\Supplier\Dto\Customer as CustomerDTO;
use App\Supplier\Dto\Order as OrderDTO;
use App\Supplier\Dto\OrderItem as OrderItemDTO;
use App\Tests\Mocks\Entity\TestOrder;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\ProductFactory;
use DateTimeImmutable;
use PHPUnit\Framework\TestCase;
use Ramsey\Uuid\Uuid;
use Superbrave\PharmacyServiceClient\Model\OrderItem as SupplierServiceOrderItem;
use Superbrave\PharmacyServiceClient\Model\Request\Customer as SupplierServiceCustomer;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Webmozart\Assert\Assert;

final class OrderTest extends TestCase
{
    public const string NOTIFY_URL = 'api/notify/orders/%s/shipment/%s';
    private const string TEST_DATE = '01-01-2022';

    private const string TEST_LOCALE = 'nl';

    private const string SUPPLIER_IDENTIFIER = 'supplier-identifier';

    private const null PRESCRIPTION_FILENAME = null;

    private const null USAGE_ADVICE = null;

    private const string CONSULT_SYSTEM_REFERENCE_ORDER_UUID = '4a31a18a-1193-4464-802c-80d2c3866a38';
    private const string CONSULT_SYSTEM_REFERENCE_FOLLOW_UP_ORDER_UUID = '4a31a18a-1193-4464-802c-80d2c3866a39';

    private ?SupplierInterface $testSupplier = null;

    public function testItCorrectlyConvertsEntityToDto(): void
    {
        $expectedOrderDTO = new OrderDTO(
            'DO7331',
            self::SUPPLIER_IDENTIFIER,
            $this->createSupplierServiceOrderItems(),
            $this->createSupplierServiceCustomer(),
            sprintf(self::NOTIFY_URL, '1337', ''),
            self::PRESCRIPTION_FILENAME,
            null,
            self::CONSULT_SYSTEM_REFERENCE_ORDER_UUID,
        );

        $urlGeneratorMock = $this->createMock(UrlGeneratorInterface::class);
        $urlGeneratorMock->expects($this->once())
            ->method('generate')
            ->with('api_custom_notify_callback')
            ->willReturn(sprintf(self::NOTIFY_URL, '1337', ''));

        self::assertEquals(
            $expectedOrderDTO,
            OrderDTO::fromEntity($this->createShipment(), $urlGeneratorMock, self::SUPPLIER_IDENTIFIER)
        );
    }

    public function testItCorrectlyConvertsEntityToDtoForFollowUpOrder(): void
    {
        $expectedOrderDTO = new OrderDTO(
            'DO7331',
            self::SUPPLIER_IDENTIFIER,
            $this->createSupplierServiceOrderItems(),
            $this->createSupplierServiceCustomer(),
            sprintf(self::NOTIFY_URL, '1337', ''),
            self::PRESCRIPTION_FILENAME,
            'BC000003231',
            self::CONSULT_SYSTEM_REFERENCE_FOLLOW_UP_ORDER_UUID,
        );

        $urlGeneratorMock = $this->createMock(UrlGeneratorInterface::class);
        $urlGeneratorMock->expects($this->once())
            ->method('generate')
            ->with('api_custom_notify_callback')
            ->willReturn(sprintf(self::NOTIFY_URL, '1337', ''));

        self::assertEquals(
            $expectedOrderDTO,
            OrderDTO::fromEntity($this->createShipment(true), $urlGeneratorMock, self::SUPPLIER_IDENTIFIER)
        );
    }

    public function testItCorrectlyConvertsEntityWithMultipleShipmentsToDto(): void
    {
        // Arrange
        $order = OrderFactory::createPrefilled();
        $shipment = $order->getShipments()->first();
        Assert::isInstanceOf($shipment, Shipment::class);

        $expectedOrderDTO = new OrderDTO(
            'DO1337',
            $shipment->getSupplier()?->getIdentifier() ?? 'test-supplier',
            $this->createSupplierServiceOrderItems($order, $shipment->getSupplierFromUnits()),
            $this->createSupplierServiceCustomer($order, $shipment->getSupplierFromUnits()),
            sprintf(self::NOTIFY_URL, '1337', ''),
            self::PRESCRIPTION_FILENAME,
            null,
            self::CONSULT_SYSTEM_REFERENCE_ORDER_UUID,
        );

        $urlGeneratorMock = $this->createMock(UrlGeneratorInterface::class);
        $urlGeneratorMock->expects($this->once())
            ->method('generate')
            ->with('api_custom_notify_callback')
            ->willReturn(sprintf(self::NOTIFY_URL, '1337', ''));

        // Act
        $orderDto = OrderDTO::fromEntity($shipment, $urlGeneratorMock, self::SUPPLIER_IDENTIFIER);

        // Assert

        // Make sure we have 3 shipments
        self::assertCount(3, $order->getShipments());

        $shipmentOne = $order->getShipments()->get(1);
        $shipmentTwo = $order->getShipments()->get(2);
        self::assertInstanceOf(Shipment::class, $shipmentOne);
        self::assertInstanceOf(Shipment::class, $shipmentTwo);
        // Make sure both shipments have the same supplier
        self::assertEquals($shipment->getSupplierFromUnits(), $shipmentOne->getSupplierFromUnits());
        self::assertEquals($shipment->getSupplierFromUnits(), $shipmentTwo->getSupplierFromUnits());
        // Make sure that the order has a total of 4 order items (first shipment has 2, the others has 1 each)
        self::assertCount(4, $order->getItems());
        // Make sure the shipment has 2 order items (1 consult, 1 medication)
        self::assertCount(2, $shipment->getOrderItems());
        // Make sure the shipment has 1 medication
        self::assertCount(1, $shipmentTwo->getOrderItems());

        self::assertEquals(
            $expectedOrderDTO,
            $orderDto
        );
    }

    private function createShipment(
        bool $createFollowUpOrder = false,
        ?Order $order = null,
        ?SupplierInterface $supplier = null,
    ): Shipment {
        $orderItem = $this->createOrderItem($supplier);
        $orderItemUnit = new OrderItemUnit($orderItem);

        if ($createFollowUpOrder) {
            $parentOrder = new TestOrder();
            $parentOrder->setNumber('BC000003231');
            $parentOrder->setConsultSystemReference(
                Uuid::fromString(self::CONSULT_SYSTEM_REFERENCE_FOLLOW_UP_ORDER_UUID)
            );
        }

        if (!$order instanceof Order) {
            $order = new TestOrder();
            $order->setId(1337);
            $order->setNumber('DO7331');
            $order->addItem($orderItem);
            $order->setCreatedAt(new DateTimeImmutable(self::TEST_DATE));
            $order->setBillingAddress($this->createAddress());
            $order->setShippingAddress($this->createAddress());
            $order->setCustomer($this->createCustomer());
            $order->setLocaleCode(self::TEST_LOCALE);
        }

        if ($createFollowUpOrder) {
            $order->setParentOrder($parentOrder);
        } else {
            $order->setConsultSystemReference(Uuid::fromString(self::CONSULT_SYSTEM_REFERENCE_ORDER_UUID));
        }

        $order->addItem($orderItem);
        $shipment = new Shipment();
        $shipment->setOrder($order);
        $order->addShipment($shipment);
        $shipment->addUnit($orderItemUnit);

        return $shipment;
    }

    private function createCustomer(): Customer
    {
        $customer = new Customer();
        $customer->setId(1);
        $customer->setCreatedAt(new DateTimeImmutable(self::TEST_DATE));
        $customer->setFirstName('John');
        $customer->setLastName('Doe');
        $customer->setEmail('<EMAIL>');
        $customer->setPhoneNumber('076123456789');
        $customer->setBirthday(new DateTimeImmutable('1982-03-15T12'));
        $customer->setGender('F');

        return $customer;
    }

    private function createAddress(): Address
    {
        $address = new Address();
        $address->setCreatedAt(new DateTimeImmutable(self::TEST_DATE));
        $address->setCountryCode('NL');
        $address->setCity('Test City');
        $address->setStreet('Test Street');
        $address->setPostcode('1234AZ');
        $address->setCompany('Test Company');

        return $address;
    }

    private function createOrderItem(?SupplierInterface $supplier = null): OrderItem
    {
        $identifier = $supplier?->getIdentifier();

        if (!$supplier instanceof SupplierInterface) {
            if (!$this->testSupplier instanceof SupplierInterface) {
                $supplier = new Supplier();
                $supplier->setIdentifier($identifier ?? self::SUPPLIER_IDENTIFIER);
                $this->testSupplier = $supplier;
            }

            $supplier = $this->testSupplier;
        }

        $translation = new ProductVariantTranslation();
        $translation->setLocale(self::TEST_LOCALE);
        $translation->setName('7_25_test-variant-code-medication');
        $translation->setLeaflet('https://downloads.dokteronline.com/leaflets/nl/test-leaflet.pdf');

        $product = new Product();
        ProductFactory::setType($product, ProductType::MEDICATION->value);

        $variant = new ProductVariant();
        $variant->addTranslation($translation);
        $variant->setCode('7_25_test-variant-code-medication');
        $variant->setCurrentLocale(self::TEST_LOCALE);
        $variant->setCreatedAt(new DateTimeImmutable(self::TEST_DATE));
        $variant->setSupplier($supplier);
        $variant->setSupplierVariantCode(null);
        $variant->setSupplierVariantName(null);
        $variant->setProduct($product);

        $item = new OrderItem(1234);
        $item->setUsageAdvice(self::USAGE_ADVICE);
        $item->setVariant($variant);

        return $item;
    }

    private function createSupplierServiceOrderItems(
        ?Order $order = null,
        ?SupplierInterface $supplier = null,
    ): array {
        /** @var ShipmentInterface $shipment */
        $shipment = $this->createShipment(order: $order, supplier: $supplier);
        $orderItems = [];
        foreach ($shipment->getOrderItems() as $item) {
            $orderItemDTO = OrderItemDTO::fromEntity($item);
            $orderItems[] = new SupplierServiceOrderItem(
                $orderItemDTO->getReference(),
                $orderItemDTO->getSupplierProduct(),
                $orderItemDTO->getQuantity(),
                $orderItemDTO->getUsageAdvice()
            );
        }

        return $orderItems;
    }

    private function createSupplierServiceCustomer(?Order $order = null, ?SupplierInterface $supplier = null): SupplierServiceCustomer
    {
        /** @var Order $order */
        $order = $this->createShipment(order: $order, supplier: $supplier)->getOrder();
        $customerDTO = CustomerDTO::fromEntity($order);

        return new SupplierServiceCustomer(
            $customerDTO->getReference(),
            $customerDTO->getFirstName(),
            $customerDTO->getLastName(),
            $customerDTO->getEmailAddress(),
            $customerDTO->getPhoneNumber(),
            $customerDTO->getDateOfBirth(),
            $customerDTO->getGenderAtBirth(),
            $customerDTO->getLanguage(),
            $customerDTO->getBillingAddress(),
            $customerDTO->getShippingAddress()
        );
    }
}
