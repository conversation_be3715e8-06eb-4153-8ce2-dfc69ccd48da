<?php

declare(strict_types=1);

namespace App\Tests\Supplier\Dto;

use App\Entity\Addressing\Address as AddressEntity;
use App\Entity\Addressing\ServicePoint as ServicePointEntity;
use App\Entity\Customer\Customer;
use App\Entity\Order\Order;
use App\Supplier\Dto\BillingAddress as BillingAddressDTO;
use App\Supplier\Dto\Customer as CustomerDTO;
use App\Supplier\Dto\ShippingAddress as ShippingAddressDTO;
use App\Tests\Mocks\Entity\TestCustomer;
use DateTimeImmutable;
use PHPUnit\Framework\TestCase;
use Superbrave\PharmacyServiceClient\Model\Address;
use Superbrave\PharmacyServiceClient\Model\ShippingAddress;

class CustomerTest extends TestCase
{
    private const string LOCALE = 'en';

    private const string TEST_DATE = '01-01-2022';

    /**
     * @return iterable<string, array{
     *     0: array{
     *          additionalAddressInformation?: string,
     *          servicePoint?: list<array{
     *              locationId: string,
     *              provider: string
     *          }>
     *     }
     * }>
     */
    public static function provideShippingData(): iterable
    {
        yield 'with additionalAddressInformation and servicePoint' => [
            [
                'additionalAddressInformation' => 'test-additional-address-information',
                'servicePoint' => [
                    ['locationId' => 'XXXX-YY-200', 'provider' => 'DHL'],
                ],
            ],
        ];

        yield 'with servicePoint' => [
            [
                'servicePoint' => [
                    ['locationId' => 'XXXX-YY-200', 'provider' => 'DHL'],
                ],
            ],
        ];

        yield 'with additionalAddressInformation' => [
            [
                'additionalAddressInformation' => 'test-additional-address-information',
            ],
        ];

        yield 'no shipping data' => [[]];
    }

    /**
     * @dataProvider provideShippingData
     *
     * @param array{
     *      additionalAddressInformation?: string,
     *      servicePoint?: list<array{
     *          locationId: string,
     *          provider: string
     *      }>
     * } $shippingData
     */
    public function testItCorrectlyConvertsEntityToDto(array $shippingData): void
    {
        $expectedCustomerDTO = new CustomerDTO(
            '1337',
            'Hans',
            'de Bever',
            '<EMAIL>',
            '06123456789',
            new DateTimeImmutable('01-01-1990'),
            'm',
            self::LOCALE,
            $this->createSupplierServiceBillingAddress(),
            $this->createSupplierServiceShippingAddress($shippingData)
        );

        self::assertEquals(
            $expectedCustomerDTO,
            CustomerDTO::fromEntity($this->createOrder(shippingData: $shippingData))
        );
    }

    /**
     * @dataProvider phoneNumberProvider
     *
     * @param array{phoneNumber: string|null} $customerData
     * @param array{phoneNumber: string|null} $shippingData
     * @param array{phoneNumber: string|null} $billingData
     */
    public function testItCorrectlySetsPhoneNumber(
        array $customerData,
        array $shippingData,
        array $billingData,
        string $expectedPhoneNumber,
    ): void {
        $expectedCustomerDTO = new CustomerDTO(
            '1337',
            'Hans',
            'de Bever',
            '<EMAIL>',
            $expectedPhoneNumber,
            new DateTimeImmutable('01-01-1990'),
            'm',
            self::LOCALE,
            $this->createSupplierServiceBillingAddress(),
            $this->createSupplierServiceShippingAddress()
        );

        self::assertEquals(
            $expectedCustomerDTO,
            CustomerDTO::fromEntity($this->createOrder($customerData, $shippingData, $billingData))
        );
    }

    /**
     * @return iterable<array{
     *     customer: array{phoneNumber: string|null},
     *     shippingAddress: array{phoneNumber: string|null},
     *     billingAddress: array{phoneNumber: string|null},
     *     expectedPhoneNumber: string|null,
     * }>
     */
    public function phoneNumberProvider(): iterable
    {
        yield 'Sets the phone number through the customer.' => [
            'customer' => ['phoneNumber' => '**********'],
            'shippingAddress' => ['phoneNumber' => null],
            'billingAddress' => ['phoneNumber' => null],
            'expectedPhoneNumber' => '**********',
        ];

        yield 'Sets the phone number through the shipping address.' => [
            'customer' => ['phoneNumber' => null],
            'shippingAddress' => ['phoneNumber' => '**********'],
            'billingAddress' => ['phoneNumber' => null],
            'expectedPhoneNumber' => '**********',
        ];

        yield 'Sets the phone number through the billing address.' => [
            'customer' => ['phoneNumber' => null],
            'shippingAddress' => ['phoneNumber' => null],
            'billingAddress' => ['phoneNumber' => '**********'],
            'expectedPhoneNumber' => '**********',
        ];

        yield 'Gives priority to the shipping phone number.' => [
            'customer' => ['phoneNumber' => '**********'],
            'shippingAddress' => ['phoneNumber' => '**********'],
            'billingAddress' => ['phoneNumber' => '**********'],
            'expectedPhoneNumber' => '**********',
        ];

        yield 'Gives priority to the billing phone number when shipping is empty.' => [
            'customer' => ['phoneNumber' => '**********'],
            'shippingAddress' => ['phoneNumber' => null],
            'billingAddress' => ['phoneNumber' => '**********'],
            'expectedPhoneNumber' => '**********',
        ];
    }

    /**
     * @param array<mixed> $customerData
     * @param array<mixed> $shippingData
     * @param array<mixed> $billingData
     */
    private function createOrder(array $customerData = [], array $shippingData = [], array $billingData = []): Order
    {
        $order = new Order();
        $order->setLocaleCode(self::LOCALE);
        $order->setCustomer($this->createCustomer($customerData));
        $order->setBillingAddress($this->createBillingAddressEntity($billingData));
        $order->setShippingAddress($this->createShippingAddressEntity($shippingData));

        return $order;
    }

    /**
     * @param array{phoneNumber?:string} $data
     */
    private function createCustomer(array $data = []): Customer
    {
        $customer = new TestCustomer();
        $customer->setId(1337);
        $customer->setCreatedAt(new DateTimeImmutable(self::TEST_DATE));
        $customer->setFirstName('Hans');
        $customer->setLastName('de Bever');
        $customer->setEmail('<EMAIL>');
        $customer->setPhoneNumber($data['phoneNumber'] ?? '06123456789');
        $customer->setBirthday(new DateTimeImmutable('01-01-1990'));
        $customer->setGender('m');

        return $customer;
    }

    /**
     * @param array{phoneNumber?: string} $data
     */
    private function createBillingAddressEntity(array $data = []): AddressEntity
    {
        $address = new AddressEntity();
        $address->setCreatedAt(new DateTimeImmutable(self::TEST_DATE));
        $address->setCountryCode('gb');
        $address->setCity('Old Gastove');
        $address->setStreet('Bucketblock 1');
        $address->setPostcode('1234 AB');
        $address->setPhoneNumber($data['phoneNumber'] ?? null);

        return $address;
    }

    /**
     * @param array{
     *     phoneNumber?: string,
     *     additionalAddressInformation?: string,
     *     servicePoint?: list<array{
     *        locationId: string,
     *        provider: string,
     *     }>
     * } $data
     */
    private function createShippingAddressEntity(array $data = []): AddressEntity
    {
        $address = new AddressEntity();
        $address->setCreatedAt(new DateTimeImmutable(self::TEST_DATE));
        $address->setCountryCode('gb');
        $address->setCity('Old Gastove');
        $address->setStreet('Bucketblock 1');
        $address->setPostcode('1234 AB');
        $address->setPhoneNumber($data['phoneNumber'] ?? null);
        $address->setAdditionalAddressInformation($data['additionalAddressInformation'] ?? null);

        if (isset($data['servicePoint'])) {
            $address->setServicePoint(
                new ServicePointEntity($data['servicePoint'])
            );
        }

        return $address;
    }

    /**
     * @param array{
     *     phoneNumber?: string,
     * } $data
     */
    private function createSupplierServiceBillingAddress(array $data = []): Address
    {
        $addressDTO = BillingAddressDTO::fromEntity($this->createBillingAddressEntity($data));

        return $addressDTO->toSupplierServiceAddress();
    }

    /**
     * @param array{
     *     phoneNumber?: string,
     *     additionalAddressInformation?: string,
     *     servicePoint?: list<array{
     *        locationId: string,
     *        provider: string,
     *     }>
     * } $data
     */
    private function createSupplierServiceShippingAddress(array $data = []): ShippingAddress
    {
        $addressDTO = ShippingAddressDTO::fromEntity($this->createShippingAddressEntity($data));

        return $addressDTO->toSupplierServiceShippingAddress();
    }
}
