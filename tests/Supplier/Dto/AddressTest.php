<?php

declare(strict_types=1);

namespace App\Tests\Supplier\Dto;

use App\Entity\Addressing\Address as AddressEntity;
use App\Entity\Addressing\ServicePoint as ServicePointEntity;
use App\Supplier\Dto\ShippingAddress;
use PHPUnit\Framework\TestCase;

class AddressTest extends TestCase
{
    public function testItCorrectlyConvertsEntityToDto(): void
    {
        // Arrange
        $address = new AddressEntity();
        $address->setFirstName('Hans');
        $address->setLastName('de Bever');
        $address->setStreet('Bucketblock 1');
        $address->setCity('Old Gastove');
        $address->setPostcode('1234 AB');
        $address->setCountryCode('NL');
        $address->setCompany('SuperBrave');
        $address->setAdditionalAddressInformation('Extra field');
        $address->setServicePoint(
            new ServicePointEntity([
                [
                    'locationId' => '888-92-NL',
                    'provider' => 'DHL Service Point',
                ],
            ])
        );

        $expectedAddressDTO = new ShippingAddress(
            address: 'Bucketblock 1',
            postalCode: '1234 AB',
            city: 'Old Gastove',
            country: 'NL',
            companyName: 'SuperBrave',
            additionalAddressInformation: 'Extra field',
            servicePoint: new ServicePointEntity([['locationId' => '888-92-NL', 'provider' => 'DHL Service Point']]),
        );

        // Act
        $shippingAddressDTO = ShippingAddress::fromEntity($address);

        // Assert
        self::assertEquals($expectedAddressDTO, $shippingAddressDTO);
    }
}
