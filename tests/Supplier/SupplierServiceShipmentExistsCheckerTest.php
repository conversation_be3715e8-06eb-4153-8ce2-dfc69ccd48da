<?php

declare(strict_types=1);

namespace App\Tests\Supplier;

use App\Entity\Shipping\Shipment;
use App\Supplier\SupplierServiceShipmentExistsChecker;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Superbrave\PharmacyServiceClient\ClientInterface;

class SupplierServiceShipmentExistsCheckerTest extends TestCase
{
    private ClientInterface&MockObject $client;
    private SupplierServiceShipmentExistsChecker $supplierServiceShipmentExistsChecker;

    public function setUp(): void
    {
        $this->client = $this->createMock(ClientInterface::class);

        $this->supplierServiceShipmentExistsChecker = new SupplierServiceShipmentExistsChecker($this->client);
    }

    public function testExistsReturnsTrueWhenNoExceptionIsThrown(): void
    {
        $supplierShipmentReference = '1deca121-62fb-4628-861b-d1fcdf07d783';

        // Arrange
        $shipment = new Shipment();
        $shipment->setSupplierShipmentReference($supplierShipmentReference);

        $this->client
            ->method('getOrder')
            ->with($supplierShipmentReference);

        // Act
        $exists = $this->supplierServiceShipmentExistsChecker->exists($shipment);

        // Assert
        self::assertTrue($exists);
    }

    public function testExistsReturnsFalseAnExceptionIsThrown(): void
    {
        $supplierShipmentReference = '1deca121-62fb-4628-861b-d1fcdf07d783';

        // Arrange
        $shipment = new Shipment();

        $this->client
            ->method('getOrder')
            ->with($supplierShipmentReference);

        // Act
        $exists = $this->supplierServiceShipmentExistsChecker->exists($shipment);

        // Assert
        self::assertFalse($exists);
    }

    public function testExistsReturnsCachedShipmentValue(): void
    {
        $supplierShipmentReference = '1deca121-62fb-4628-861b-d1fcdf07d783';

        // Arrange
        $shipment = new Shipment();
        $shipment->setSupplierShipmentReference($supplierShipmentReference);

        $this->client
            ->expects(self::once())
            ->method('getOrder')
            ->with($supplierShipmentReference);

        self::assertTrue($this->supplierServiceShipmentExistsChecker->exists($shipment));

        // Act & Assert
        self::assertTrue($this->supplierServiceShipmentExistsChecker->exists($shipment));
    }
}
