<?php

declare(strict_types=1);

namespace App\Tests\Supplier\StateMachine\StateResolver;

use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShipmentInterface;
use App\StateMachine\OrderShippingStates;
use App\StateMachine\ShipmentTransitions;
use App\Supplier\StateMachine\StateResolver\ShipmentStateResolverBasedOnSupplierOrderStatus;
use App\Tests\Mocks\Entity\TestOrder;
use PHPUnit\Framework\TestCase;
use SM\Factory\FactoryInterface;
use SM\StateMachine\StateMachineInterface;
use Superbrave\PharmacyServiceClient\Model\Response\Order as SupplierOrder;
use Sylius\Component\Core\OrderShippingStates as SyliusOrderShippingState;
use Sylius\Component\Order\Model\OrderInterface;
use Sylius\Component\Shipping\Model\ShipmentInterface as SyliusShipmentInterface;
use S<PERSON><PERSON>\Component\Shipping\ShipmentTransitions as SyliusShipmentTransitions;

class ShipmentStateResolverBasedOnSupplierOrderStatusTest extends TestCase
{
    protected function setUp(): void
    {
        $this->stateMachineMock = $this->createMock(StateMachineInterface::class);
        $stateMachineFactoryStub = $this->createStub(FactoryInterface::class);
        $stateMachineFactoryStub->method('get')
            ->willReturn($this->stateMachineMock);

        $this->resolver = new ShipmentStateResolverBasedOnSupplierOrderStatus($stateMachineFactoryStub);
    }

    /**
     * @dataProvider provideOrderShipmentStatus
     */
    public function testResolveAppliesCorrectTransition(
        string $expectedTransition,
        string $orderShipmentStatus,
        ShipmentInterface $shipment,
    ): void {
        $orderShipment = $this->createMock(SupplierOrder::class);
        $orderShipment->expects(self::once())
            ->method('getStatus')
            ->willReturn($orderShipmentStatus);

        $this->stateMachineMock->expects(self::once())
            ->method('can')
            ->with($expectedTransition)
            ->willReturn(true);
        $this->stateMachineMock->expects(self::once())
            ->method('apply')
            ->with($expectedTransition);

        $this->resolver->resolve($shipment, $orderShipment);
    }

    public function provideOrderShipmentStatus(): iterable
    {
        $orderShipmentStatus = $this->createShipment(
            OrderInterface::STATE_NEW,
            OrderShippingStates::STATE_PENDING,
            ShipmentInterface::STATE_PENDING
        );

        yield 'order with 1 shipment that is pending results in processed' => [
            ShipmentTransitions::TRANSITION_PROCESS,
            SupplierOrder::STATUS_PROCESSING,
            $orderShipmentStatus,
        ];

        $orderShipmentStatus = $this->createShipment(
            OrderInterface::STATE_NEW,
            OrderShippingStates::STATE_PENDING,
            ShipmentInterface::STATE_PENDING
        );

        yield 'order with 1 shipment that is pending results in shipped' => [
            SyliusShipmentTransitions::TRANSITION_SHIP,
            SupplierOrder::STATUS_SHIPPED,
            $orderShipmentStatus,
        ];

        $orderShipmentStatus = $this->createShipment(
            OrderInterface::STATE_NEW,
            OrderShippingStates::STATE_PENDING,
            ShipmentInterface::STATE_PENDING
        );

        yield 'order with 1 shipment that is pending results in cancelled' => [
            ShipmentTransitions::TRANSITION_CANCEL_BY_SUPPLIER,
            SupplierOrder::STATUS_CANCELLED,
            $orderShipmentStatus,
        ];

        $orderShipmentStatus = $this->createShipment(
            OrderInterface::STATE_FULFILLED,
            SyliusOrderShippingState::STATE_SHIPPED,
            SyliusShipmentInterface::STATE_SHIPPED
        );

        yield 'order with 1 shipment that is shipped results in returned' => [
            ShipmentTransitions::TRANSITION_RETURN,
            SupplierOrder::STATUS_RETURNED,
            $orderShipmentStatus,
        ];

        $orderShipmentStatus = $this->createShipment(
            OrderInterface::STATE_FULFILLED,
            SyliusOrderShippingState::STATE_SHIPPED,
            SyliusShipmentInterface::STATE_SHIPPED
        );

        yield 'order with 1 shipment that is shipped results in pending' => [
            ShipmentTransitions::TRANSITION_RECALL_TO_PENDING,
            SupplierOrder::STATUS_RESHIP,
            $orderShipmentStatus,
        ];

        $orderShipmentStatus = $this->createShipment(
            OrderInterface::STATE_FULFILLED,
            SyliusOrderShippingState::STATE_SHIPPED,
            SyliusShipmentInterface::STATE_SHIPPED
        );

        yield 'order with 1 shipment that is shipped results in processing' => [
            ShipmentTransitions::TRANSITION_RECALL_TO_PROCESSING,
            SupplierOrder::STATUS_PROCESSING,
            $orderShipmentStatus,
        ];

        $orderShipmentStatus = $this->createShipment(
            OrderInterface::STATE_FULFILLED,
            SyliusOrderShippingState::STATE_SHIPPED,
            SyliusShipmentInterface::STATE_SHIPPED
        );

        yield 'order with 1 shipment that is shipped results in shipped' => [
            SyliusShipmentTransitions::TRANSITION_SHIP,
            SupplierOrder::STATUS_SHIPPED,
            $orderShipmentStatus,
        ];

        $orderShipmentStatus = $this->createShipment(
            OrderInterface::STATE_NEW,
            SyliusOrderShippingState::STATE_CANCELLED,
            SyliusShipmentInterface::STATE_CANCELLED
        );

        yield 'order with 1 shipment that is cancelled results in cancelled' => [
            ShipmentTransitions::TRANSITION_CANCEL_BY_SUPPLIER,
            SupplierOrder::STATUS_CANCELLED,
            $orderShipmentStatus,
        ];

        $orderShipmentStatus = $this->createShipment(
            OrderInterface::STATE_NEW,
            OrderShippingStates::STATE_PENDING,
            ShipmentInterface::STATE_PROCESSING
        );

        yield 'order with 1 shipment that is processing results in pending' => [
            ShipmentTransitions::TRANSITION_UNMARK_FROM_PROCESSING,
            SupplierOrder::STATUS_NEW,
            $orderShipmentStatus,
        ];
    }

    private function createShipment(
        string $orderState,
        string $orderShippingState,
        string $shipmentState,
    ): ShipmentInterface {
        $order = new TestOrder();
        $order->setTokenValue('08s02i1k_');
        $order->setState($orderState);
        $order->setShippingState($orderShippingState);

        $shipment = new Shipment();
        $shipment->setState($shipmentState);

        $order->addShipment($shipment);

        return $shipment;
    }
}
