<?php

declare(strict_types=1);

namespace App\Tests\Supplier\StateMachine\StateApplier\Order;

use App\Entity\Order\Order;
use App\Supplier\StateMachine\StateApplier\Order\AdminOrderCancelApplier;
use App\Supplier\StateMachine\StateApplier\Order\CannotCancelOrderException;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\TestCase;
use SM\Factory\FactoryInterface;
use SM\StateMachine\StateMachineInterface;
use Sylius\Component\Order\OrderTransitions as SyliusOrderTransitions;

final class AdminOrderCancelApplierTest extends TestCase
{
    public function testFulfillOrderWhenPossible(): void
    {
        // Arrange & Assert
        $stateMachine = $this->createMock(StateMachineInterface::class);
        $stateMachine->method('can')->willReturnOnConsecutiveCalls(true, true, false);
        $stateMachine->expects($this->once())->method('apply')->with(SyliusOrderTransitions::TRANSITION_FULFILL);

        $factory = $this->createMock(FactoryInterface::class);
        $factory->method('get')->willReturn($stateMachine);

        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->once())->method('flush');

        $order = $this->createMock(Order::class);
        $order->expects($this->once())->method('setCancellation');

        $applier = new AdminOrderCancelApplier($factory, $entityManager);

        // Act
        $applier->apply($order, 'reason');
    }

    public function testCancelsOrderWhenPossible(): void
    {
        // Arrange & Assert
        $stateMachine = $this->createMock(StateMachineInterface::class);
        $stateMachine->method('can')->willReturnOnConsecutiveCalls(false, true, false);
        $stateMachine->expects($this->once())->method('apply')->with(SyliusOrderTransitions::TRANSITION_CANCEL);

        $factory = $this->createMock(FactoryInterface::class);
        $factory->method('get')->willReturn($stateMachine);

        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->once())->method('flush');

        $order = $this->createMock(Order::class);
        $order->expects($this->once())->method('setCancellation');

        $applier = new AdminOrderCancelApplier($factory, $entityManager);

        // Act
        $applier->apply($order, 'reason');
    }

    public function testThrowsExceptionWhenOrderCannotBeCancelled(): void
    {
        // Arrange
        $stateMachine = $this->createMock(StateMachineInterface::class);
        $stateMachine->method('can')->willReturn(false);

        $factory = $this->createMock(FactoryInterface::class);
        $factory->method('get')->willReturn($stateMachine);

        $entityManager = $this->createMock(EntityManagerInterface::class);

        $order = $this->createMock(Order::class);

        $applier = new AdminOrderCancelApplier($factory, $entityManager);

        // Act & Assert
        $this->expectException(CannotCancelOrderException::class);
        $applier->apply($order, 'reason');
    }

    public function testThrowsExceptionWhenReasonIsEmpty(): void
    {
        // Arrange
        $stateMachine = $this->createMock(StateMachineInterface::class);
        $stateMachine->method('can')->willReturn(true);

        $factory = $this->createMock(FactoryInterface::class);
        $factory->method('get')->willReturn($stateMachine);

        $entityManager = $this->createMock(EntityManagerInterface::class);

        $order = $this->createMock(Order::class);

        $applier = new AdminOrderCancelApplier($factory, $entityManager);

        // Act & Assert
        $this->expectException(CannotCancelOrderException::class);
        $applier->apply($order, '');
    }
}
