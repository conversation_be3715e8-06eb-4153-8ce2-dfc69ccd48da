<?php

declare(strict_types=1);

namespace App\Tests\Supplier\StateMachine\Callback;

use App\Entity\Shipping\Shipment;
use App\Supplier\CreateSupplierServiceShipmentDispatcherInterface;
use App\Supplier\StateMachine\Callback\CreateShipmentInSupplierService;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class CreateShipmentInSupplierServiceTest extends TestCase
{
    private CreateShipmentInSupplierService $createShipmentInSupplierService;

    private CreateSupplierServiceShipmentDispatcherInterface&MockObject $createSupplierServiceShipmentDispatcherMock;

    protected function setUp(): void
    {
        $this->createSupplierServiceShipmentDispatcherMock = $this->createMock(CreateSupplierServiceShipmentDispatcherInterface::class);

        $this->createShipmentInSupplierService = new CreateShipmentInSupplierService(
            $this->createSupplierServiceShipmentDispatcherMock,
        );
    }

    public function testInvokeCallsDispatch(): void
    {
        // Arrange
        $shipment = new Shipment();

        // Assert
        $this->createSupplierServiceShipmentDispatcherMock->expects(self::once())
            ->method('dispatch')
            ->with($shipment);

        // Act
        ($this->createShipmentInSupplierService)($shipment);
    }
}
