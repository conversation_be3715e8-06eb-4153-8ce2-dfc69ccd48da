<?php

declare(strict_types=1);

namespace App\Tests\Supplier\StateMachine\Callback;

use App\Entity\Shipping\Shipment;
use App\Supplier\Message\CancelSupplierServiceShipment;
use App\Supplier\StateMachine\Callback\CancelShipmentInSupplierService;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Messenger\MessageBus;
use Symfony\Component\Messenger\TraceableMessageBus;

class CancelShipmentInSupplierServiceTest extends TestCase
{
    public function testItPutsCancelSupplierServiceShipmentMessageOnTheBus(): void
    {
        $messageBus = new TraceableMessageBus(new MessageBus());
        $createShipmentOnReady = new CancelShipmentInSupplierService($messageBus);

        $shipmentMock = $this->createMock(Shipment::class);
        $shipmentMock->expects($this->once())
            ->method('getId')
            ->willReturn(1);

        ($createShipmentOnReady)($shipmentMock);

        $messages = $messageBus->getDispatchedMessages();

        self::assertCount(1, $messages);
        self::assertInstanceOf(CancelSupplierServiceShipment::class, $messages[0]['message']);
        self::assertSame(1, $messages[0]['message']->getShipmentId());
    }
}
