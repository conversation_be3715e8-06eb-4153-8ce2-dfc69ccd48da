<?php

declare(strict_types=1);

namespace App\Tests\Supplier;

use App\Entity\Channel\Channel;
use App\Entity\Order\Order;
use App\Entity\Order\OrderItemUnit;
use App\Entity\Product\ProductType;
use App\Entity\Product\ValueObject\Attributes;
use App\Entity\Shipping\Shipment;
use App\Entity\Shipping\ShipmentInterface;
use App\Entity\Supplier\SupplierInterface;
use App\Exception\InvalidStateException;
use App\Repository\ProductVariantRepositoryInterface;
use App\StateMachine\OrderShippingStates;
use App\Supplier\CreateSupplierServiceShipmentDispatcherInterface;
use App\Supplier\Exception\InvalidOrderItemException;
use App\Supplier\OrderItemCollection;
use App\Supplier\OrderItemsUpdater;
use App\Supplier\SupplierShipmentItemsUpdater;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\OrderItemFactory;
use App\Tests\Util\Factory\ProductVariantFactory;
use App\Tests\Util\Factory\ShipmentFactory;
use App\Tests\Util\Factory\SupplierFactory;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\MockObject\Rule\InvocationOrder;
use PHPUnit\Framework\TestCase;
use Sylius\Resource\Factory\Factory;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;
use Webmozart\Assert\InvalidArgumentException;

final class SupplierShipmentItemsUpdaterTest extends TestCase
{
    public const string SUPPLIER_MY_OWN_CHEMIST = 'my-own-chemist';
    public const string SUPPLIER_PRIME_PHARMACY = 'prime-pharmacy';
    public const string SUPPLIER_APOTHEEK_CULEMBORG = 'apotheek-culemborg';
    public const string SUPPLIER_APOTHEEK_BAD_NIEUWESCHANS = 'apotheek-bad-nieuweschans';

    private SupplierShipmentItemsUpdater $supplierShipmentItemsUpdater;
    private ProductVariantRepositoryInterface&MockObject $productVariantRepository;
    private EventDispatcherInterface&MockObject $eventDispatcher;
    private CreateSupplierServiceShipmentDispatcherInterface&MockObject $createSupplierServiceShipmentDispatcher;

    protected function setUp(): void
    {
        $this->productVariantRepository = $this->createMock(ProductVariantRepositoryInterface::class);
        $this->eventDispatcher = $this->createMock(EventDispatcherInterface::class);
        $this->createSupplierServiceShipmentDispatcher = $this->createMock(CreateSupplierServiceShipmentDispatcherInterface::class);

        $this->supplierShipmentItemsUpdater = new SupplierShipmentItemsUpdater(
            $this->createMock(EntityManagerInterface::class),
            $this->eventDispatcher,
            $this->createSupplierServiceShipmentDispatcher,
            new Factory(Shipment::class),
            new OrderItemsUpdater($this->productVariantRepository)
        );
    }

    public function testMultipleShipmentsOnChannelAreAllowed(): void
    {
        // Arrange
        $order = OrderFactory::create(['channel' => ChannelFactory::createPrefilled(['multipleShipmentsAllowed' => false])]);

        // Assert
        $this->expectException(InvalidArgumentException::class);

        // Act
        $this->supplierShipmentItemsUpdater->updateSupplierOnOrderItems($order, new OrderItemCollection(OrderItemFactory::createPrefilled()), SupplierFactory::createPrefilled());
    }

    public function testItCanSwitchFromSupplierToAShippedSupplierForReadyShippingOrder(): void
    {
        // Arrange
        $supplierCulemborg = SupplierFactory::createPrefilled([
            'name' => 'Apotheek Culemborg BV',
            'identifier' => self::SUPPLIER_APOTHEEK_CULEMBORG,
        ]);
        $supplierPrime = SupplierFactory::createPrefilled([
            'name' => 'Prime-Pharmacy',
            'identifier' => self::SUPPLIER_PRIME_PHARMACY,
        ]);
        $supplierChemist = SupplierFactory::createPrefilled([
            'name' => 'MyOwnChemist',
            'identifier' => self::SUPPLIER_MY_OWN_CHEMIST,
        ]);

        $order = OrderFactory::create([
            'state' => OrderShippingStates::STATE_PARTIALLY_SHIPPED,
            'channel' => ChannelFactory::createPrefilled(['multipleShipmentsAllowed' => true]),
        ]);

        $order->addShipment($this->createShipmentWithOrderItems($order, $supplierCulemborg, state: ShipmentInterface::STATE_SHIPPED));
        $order->addShipment($this->createShipmentWithOrderItems($order, $supplierPrime, numberOfItems: 2, state: ShipmentInterface::STATE_CANCELLED));
        $order->addShipment($this->createShipmentWithOrderItems($order, $supplierChemist, state: ShipmentInterface::STATE_PROCESSING));

        $this->successFullyFindAlternativeProductVariants($supplierCulemborg, self::exactly(2));
        $this->createSupplierServiceShipmentDispatcher->expects(self::once())->method('dispatch');

        $switchOrderItems = $this->getOrderItemsToSwitchBySuppliers($order, [self::SUPPLIER_PRIME_PHARMACY]);

        $this->eventDispatcher->expects(self::once())->method('dispatch');

        // Act
        $this->supplierShipmentItemsUpdater->updateSupplierOnOrderItems($order, $switchOrderItems, $supplierCulemborg);

        // Assert
        self::assertCount(4, $order->getShipments());

        $shipment = $order->getShipments()[0];
        self::assertInstanceOf(ShipmentInterface::class, $shipment);
        self::assertEquals($supplierCulemborg, $shipment->getSupplierFromUnits());
        self::assertEquals($supplierCulemborg, $shipment->getSupplier());
        self::assertCount(1, $shipment->getUnits());
        self::assertEquals(ShipmentInterface::STATE_SHIPPED, $shipment->getState());

        $shipment = $order->getShipments()[1];
        self::assertInstanceOf(ShipmentInterface::class, $shipment);
        self::assertNull($shipment->getSupplierFromUnits());
        self::assertEquals($supplierPrime, $shipment->getSupplier());
        self::assertCount(0, $shipment->getUnits());
        self::assertEquals(ShipmentInterface::STATE_CANCELLED, $shipment->getState());

        $shipment = $order->getShipments()[2];
        self::assertInstanceOf(ShipmentInterface::class, $shipment);
        self::assertEquals($supplierChemist, $shipment->getSupplierFromUnits());
        self::assertEquals($supplierChemist, $shipment->getSupplier());
        self::assertCount(1, $shipment->getUnits());
        self::assertEquals(ShipmentInterface::STATE_PROCESSING, $shipment->getState());

        $shipment = $order->getShipments()[3];
        self::assertInstanceOf(ShipmentInterface::class, $shipment);
        self::assertEquals($supplierCulemborg, $shipment->getSupplierFromUnits());
        self::assertEquals($supplierCulemborg, $shipment->getSupplier());
        self::assertCount(2, $shipment->getUnits());
        self::assertEquals(ShipmentInterface::STATE_READY, $shipment->getState());
    }

    public function testItCanSwitchFromSupplierForReadyShippingOrder(): void
    {
        // Arrange
        $supplierBad = SupplierFactory::createPrefilled([
            'name' => 'Apotheek Bad Nieuweschans BV',
            'identifier' => self::SUPPLIER_APOTHEEK_BAD_NIEUWESCHANS,
        ]);
        $supplierCulemborg = SupplierFactory::createPrefilled([
            'name' => 'Apotheek Culemborg BV',
            'identifier' => self::SUPPLIER_APOTHEEK_CULEMBORG,
        ]);
        $supplierPrime = SupplierFactory::createPrefilled([
            'name' => 'Prime-Pharmacy',
            'identifier' => self::SUPPLIER_PRIME_PHARMACY,
        ]);
        $supplierChemist = SupplierFactory::createPrefilled([
            'name' => 'MyOwnChemist',
            'identifier' => self::SUPPLIER_MY_OWN_CHEMIST,
        ]);

        $order = OrderFactory::create([
            'state' => OrderShippingStates::STATE_PARTIALLY_SHIPPED,
            'channel' => ChannelFactory::createPrefilled(['multipleShipmentsAllowed' => true]),
        ]);

        $order->addShipment($this->createShipmentWithOrderItems($order, $supplierCulemborg, state: ShipmentInterface::STATE_SHIPPED));
        $order->addShipment($this->createShipmentWithOrderItems($order, $supplierPrime, numberOfItems: 2, state: ShipmentInterface::STATE_CANCELLED));
        $order->addShipment($this->createShipmentWithOrderItems($order, $supplierChemist, state: ShipmentInterface::STATE_PROCESSING));

        $this->successFullyFindAlternativeProductVariants($supplierBad, self::exactly(2));
        $this->createSupplierServiceShipmentDispatcher->expects(self::once())->method('dispatch');

        $switchOrderItems = $this->getOrderItemsToSwitchBySuppliers($order, [self::SUPPLIER_PRIME_PHARMACY]);

        $this->eventDispatcher->expects(self::once())->method('dispatch');

        // Act
        $this->supplierShipmentItemsUpdater->updateSupplierOnOrderItems($order, $switchOrderItems, $supplierBad);

        // Assert
        self::assertCount(4, $order->getShipments());

        $shipment = $order->getShipments()[0];
        self::assertInstanceOf(ShipmentInterface::class, $shipment);
        self::assertEquals($supplierCulemborg, $shipment->getSupplierFromUnits());
        self::assertEquals($supplierCulemborg, $shipment->getSupplier());
        self::assertCount(1, $shipment->getUnits());
        self::assertEquals(ShipmentInterface::STATE_SHIPPED, $shipment->getState());

        $shipment = $order->getShipments()[1];
        self::assertInstanceOf(ShipmentInterface::class, $shipment);
        self::assertNull($shipment->getSupplierFromUnits());
        self::assertEquals($supplierPrime, $shipment->getSupplier());
        self::assertCount(0, $shipment->getUnits());
        self::assertEquals(ShipmentInterface::STATE_CANCELLED, $shipment->getState());

        $shipment = $order->getShipments()[2];
        self::assertInstanceOf(ShipmentInterface::class, $shipment);
        self::assertEquals($supplierChemist, $shipment->getSupplierFromUnits());
        self::assertEquals($supplierChemist, $shipment->getSupplier());
        self::assertCount(1, $shipment->getUnits());
        self::assertEquals(ShipmentInterface::STATE_PROCESSING, $shipment->getState());

        $shipment = $order->getShipments()[3];
        self::assertInstanceOf(ShipmentInterface::class, $shipment);
        self::assertEquals($supplierBad, $shipment->getSupplierFromUnits());
        self::assertEquals($supplierBad, $shipment->getSupplier());
        self::assertCount(2, $shipment->getUnits());
        self::assertEquals(ShipmentInterface::STATE_READY, $shipment->getState());
    }

    public function testItCanSwitchFromSupplierForAwaitingShippingOrder(): void
    {
        // Arrange
        $newSupplier = SupplierFactory::createPrefilled([
            'name' => 'MyOwnChemist',
            'identifier' => self::SUPPLIER_MY_OWN_CHEMIST,
        ]);

        $order = $this->getValidOrderWithMultipleShipments();

        // Get all order items in a flat array from the shipments that belong to the suppliers 'apotheek-bad-nieuweschans' and 'apotheek-culemborg'
        $switchOrderItems = $this->getOrderItemsToSwitchBySuppliers($order);

        $this->successFullyFindAlternativeProductVariants($newSupplier, self::exactly(2));
        $this->createSupplierServiceShipmentDispatcher->expects(self::never())->method('dispatch');

        $this->eventDispatcher->expects(self::exactly(2))->method('dispatch');

        // Act
        $this->supplierShipmentItemsUpdater->updateSupplierOnOrderItems(
            $order,
            $switchOrderItems,
            $newSupplier
        );

        // Assert
        $orderItems = $order->getItems();
        self::assertCount(5, $orderItems);
        self::assertNull($orderItems[0]->getVariant()->getSupplier());
        self::assertEquals(self::SUPPLIER_MY_OWN_CHEMIST, $orderItems[1]->getVariant()->getSupplier()?->getIdentifier());
        self::assertTrue($orderItems[1]->isImmutable());
        self::assertEquals(self::SUPPLIER_MY_OWN_CHEMIST, $orderItems[2]->getVariant()->getSupplier()?->getIdentifier());
        self::assertTrue($orderItems[2]->isImmutable());
        self::assertEquals(self::SUPPLIER_PRIME_PHARMACY, $orderItems[3]->getVariant()->getSupplier()?->getIdentifier());
        self::assertFalse($orderItems[3]->isImmutable());
        self::assertEquals(self::SUPPLIER_PRIME_PHARMACY, $orderItems[4]->getVariant()->getSupplier()?->getIdentifier());
        self::assertFalse($orderItems[4]->isImmutable());

        self::assertCount(2, $order->getShipments());
        /** @var Shipment $firstShipment */
        $firstShipment = $order->getShipments()->first();
        self::assertEquals(self::SUPPLIER_PRIME_PHARMACY, $firstShipment->getSupplierFromUnits()?->getIdentifier());
        self::assertCount(2, $firstShipment->getUnits());
        /** @var Shipment $lastShipment */
        $lastShipment = $order->getShipments()->last();
        self::assertEquals(self::SUPPLIER_MY_OWN_CHEMIST, $lastShipment->getSupplierFromUnits()?->getIdentifier());
        self::assertCount(2, $lastShipment->getUnits());
    }

    public function testItCanPartiallySwitchFromSupplierForAwaitingShippingOrder(): void
    {
        // Arrange
        $newSupplier = SupplierFactory::createPrefilled([
            'name' => 'MyOwnChemist',
            'identifier' => self::SUPPLIER_MY_OWN_CHEMIST,
        ]);

        $order = $this->getValidOrderWithMultipleShipments();

        // Get all order items in a flat array from the shipments that belong to the suppliers 'apotheek-bad-nieuweschans' and 'apotheek-culemborg'
        $switchShipment = $order->getShipments()
            /** @phpstan-ignore-next-line @todo MEET-86-shipment */
            ->filter(static fn (ShipmentInterface $shipment) => $shipment->getSupplierFromUnits()?->getIdentifier() === self::SUPPLIER_PRIME_PHARMACY)
            ->first();

        $switchOrderItems = new OrderItemCollection(
            $switchShipment->getUnits()
                ->map(static fn (OrderItemUnit $unit) => $unit->getOrderItem())
                ->first()
        );

        $this->successFullyFindAlternativeProductVariants($newSupplier, self::exactly(1));
        $this->createSupplierServiceShipmentDispatcher->expects(self::never())->method('dispatch');
        $this->eventDispatcher->expects(self::once())->method('dispatch');

        // Act
        $this->supplierShipmentItemsUpdater->updateSupplierOnOrderItems(
            $order,
            $switchOrderItems,
            $newSupplier
        );

        // Assert
        self::assertCount(4, $order->getShipments());
        foreach (
            [
                self::SUPPLIER_MY_OWN_CHEMIST => 1,
                self::SUPPLIER_PRIME_PHARMACY => 1,
                self::SUPPLIER_APOTHEEK_CULEMBORG => 1,
                self::SUPPLIER_APOTHEEK_BAD_NIEUWESCHANS => 1,
            ] as $requiredSupplier => $unitCount
        ) {
            /** @phpstan-ignore-next-line @todo MEET-86-shipment */
            $shipment = $order->getShipments()->filter(static fn (ShipmentInterface $shipment) => $shipment->getSupplierFromUnits()?->getIdentifier() === $requiredSupplier)->first();
            $supplier = $shipment->getSupplierFromUnits();
            self::assertInstanceOf(SupplierInterface::class, $supplier);
            self::assertEquals($requiredSupplier, $supplier->getIdentifier());
            self::assertCount($unitCount, $shipment->getUnits());
        }
    }

    public function testItCanSwitchFromSupplierToAnExistingShipmentForAwaitingShippingOrder(): void
    {
        // Arrange
        $order = $this->getValidOrderWithMultipleShipments();
        /** @phpstan-ignore-next-line @todo MEET-86-shipment */
        $newSupplier = $order->getShipments()->filter(static fn (ShipmentInterface $shipment) => $shipment->getSupplierFromUnits()?->getIdentifier() === self::SUPPLIER_PRIME_PHARMACY)->first()->getSupplierFromUnits();

        $switchOrderItems = $this->getOrderItemsToSwitchBySuppliers($order);
        $this->successFullyFindAlternativeProductVariants($newSupplier, self::exactly(2));
        $this->createSupplierServiceShipmentDispatcher->expects(self::never())->method('dispatch');
        $this->eventDispatcher->expects(self::exactly(2))->method('dispatch');

        // Act
        $this->supplierShipmentItemsUpdater->updateSupplierOnOrderItems(
            $order,
            $switchOrderItems,
            $newSupplier
        );

        // Assert
        $orderItems = $order->getItems();
        self::assertCount(5, $orderItems);
        self::assertNull($orderItems[0]->getVariant()->getSupplier());
        self::assertEquals(self::SUPPLIER_PRIME_PHARMACY, $orderItems[1]->getVariant()->getSupplier()?->getIdentifier());
        self::assertTrue($orderItems[1]->isImmutable());
        self::assertEquals(self::SUPPLIER_PRIME_PHARMACY, $orderItems[2]->getVariant()->getSupplier()?->getIdentifier());
        self::assertTrue($orderItems[2]->isImmutable());
        self::assertEquals(self::SUPPLIER_PRIME_PHARMACY, $orderItems[3]->getVariant()->getSupplier()?->getIdentifier());
        self::assertFalse($orderItems[3]->isImmutable());
        self::assertEquals(self::SUPPLIER_PRIME_PHARMACY, $orderItems[4]->getVariant()->getSupplier()?->getIdentifier());
        self::assertFalse($orderItems[4]->isImmutable());

        self::assertCount(1, $order->getShipments());
        $firstShipment = $order->getShipments()->first();
        self::assertEquals(self::SUPPLIER_PRIME_PHARMACY, $firstShipment->getSupplierFromUnits()->getIdentifier());
        self::assertCount(4, $firstShipment->getUnits());
    }

    /**
     * @dataProvider dataProviderWhenTheOrderItemShipmentStateIsInvalid
     */
    public function testItFailsWhenTheOrderItemShipmentStateIsInvalid(string $shippingState): void
    {
        // Arrange
        $order = OrderFactory::createPrefilled(
            ['channel' => ChannelFactory::createPrefilled(['multipleShipmentsAllowed' => true])],
            hasShipment: false
        );
        $order->setState($shippingState);
        $shipment = ShipmentFactory::create(['shippingMethod' => ShipmentFactory::createShippingMethod()]);
        foreach ($order->getItemUnits() as $item) {
            $shipment->addUnit($item);
        }

        $order->addShipment($shipment);

        // Assert
        $this->expectException(InvalidStateException::class);

        // Act
        $this->supplierShipmentItemsUpdater->updateSupplierOnOrderItems(
            $order,
            new OrderItemCollection(...$order->getItems()->toArray()),
            SupplierFactory::createPrefilled()
        );
    }

    /**
     * @dataProvider dataProviderWhenAnOrderItemIsInvalid
     */
    public function testItFailsWhenAnOrderItemIsInvalid(Order $order, OrderItemCollection $itemCollection, Exception $exception, ?SupplierInterface $newSupplier = null): void
    {
        // Assert
        $this->expectException($exception::class);
        $this->expectExceptionMessage($exception->getMessage());

        // Act
        $this->supplierShipmentItemsUpdater->updateSupplierOnOrderItems(
            $order,
            $itemCollection,
            $newSupplier ?? SupplierFactory::createPrefilled()
        );
    }

    private function dataProviderWhenTheOrderItemShipmentStateIsInvalid(): iterable
    {
        return [
            [OrderShippingStates::STATE_PROCESSING],
            [OrderShippingStates::STATE_READY],
            [OrderShippingStates::STATE_SHIPPED],
            [OrderShippingStates::STATE_PENDING],
            [OrderShippingStates::STATE_CART],
            [OrderShippingStates::STATE_RETURNED],
        ];
    }

    private function dataProviderWhenAnOrderItemIsInvalid(): iterable
    {
        $invalidShipment = ShipmentFactory::create(['shippingMethod' => ShipmentFactory::createShippingMethod(), 'state' => OrderShippingStates::STATE_AWAITING_PRESCRIPTION]);
        $invalidOrder = OrderFactory::createPrefilled(['channel' => ChannelFactory::createPrefilled(['multipleShipmentsAllowed' => true])], hasShipment: false);
        $invalidOrder->addShipment($invalidShipment);
        $invalidOrder->setShippingState(OrderShippingStates::STATE_AWAITING_PAYMENT);

        $shipment = ShipmentFactory::create(['shippingMethod' => ShipmentFactory::createShippingMethod(), 'state' => OrderShippingStates::STATE_AWAITING_PRESCRIPTION]);
        $order = OrderFactory::createPrefilled(['channel' => ChannelFactory::createPrefilled(['multipleShipmentsAllowed' => true])], hasShipment: false);

        foreach ($order->getItemUnits() as $item) {
            $shipment->addUnit($item);
        }
        $order->addShipment($shipment);
        $order->setShippingState(OrderShippingStates::STATE_AWAITING_PAYMENT);

        $itemCollection = new OrderItemCollection(...$order->getItems()->toArray(), ...$invalidOrder->getItems()->toArray());
        yield 'OrderItem does not belong to the Order' => [
            $order,
            $itemCollection,
            InvalidOrderItemException::createForInvalidOrder(
                $order,
                $invalidOrder->getItems()->first()
            ),
        ];

        $itemCollection = new OrderItemCollection(...$invalidOrder->getItems()->toArray());
        yield 'OrderItem has missing shipment' => [
            $invalidOrder,
            $itemCollection,
            InvalidOrderItemException::createForMissingShipment($invalidOrder->getItems()->first()),
        ];

        // Arrange
        $supplierBad = SupplierFactory::createPrefilled([
            'name' => 'Apotheek Bad Nieuweschans BV',
            'identifier' => self::SUPPLIER_APOTHEEK_BAD_NIEUWESCHANS,
        ]);
        $supplierCulemborg = SupplierFactory::createPrefilled([
            'name' => 'Apotheek Culemborg BV',
            'identifier' => self::SUPPLIER_APOTHEEK_CULEMBORG,
        ]);
        $supplierPrime = SupplierFactory::createPrefilled([
            'name' => 'Prime-Pharmacy',
            'identifier' => self::SUPPLIER_PRIME_PHARMACY,
        ]);
        $supplierChemist = SupplierFactory::createPrefilled([
            'name' => 'MyOwnChemist',
            'identifier' => self::SUPPLIER_MY_OWN_CHEMIST,
        ]);

        $order = OrderFactory::create([
            'state' => OrderShippingStates::STATE_PARTIALLY_SHIPPED,
            'channel' => ChannelFactory::createPrefilled(['multipleShipmentsAllowed' => true]),
        ]);

        $order->addShipment($this->createShipmentWithOrderItems($order, $supplierPrime, numberOfItems: 2, state: ShipmentInterface::STATE_CANCELLED));
        $order->addShipment($this->createShipmentWithOrderItems($order, $supplierCulemborg, state: ShipmentInterface::STATE_SHIPPED));
        $order->addShipment($this->createShipmentWithOrderItems($order, $supplierChemist, state: ShipmentInterface::STATE_PROCESSING));

        yield 'Not all OrderItems to switch are valid' => [
            $order,
            new OrderItemCollection(...$order->getItems()->toArray()),
            new InvalidStateException('shipmentState', [
                ShipmentInterface::STATE_AWAITING_PAYMENT,
                ShipmentInterface::STATE_AWAITING_PRESCRIPTION,
                ShipmentInterface::STATE_CANCELLED,
            ], $shipment),
        ];

        $shipment = ShipmentFactory::create(['shippingMethod' => ShipmentFactory::createShippingMethod(), 'state' => OrderShippingStates::STATE_PENDING]);
        $order = OrderFactory::createPrefilled(['channel' => ChannelFactory::createPrefilled(['multipleShipmentsAllowed' => true])], hasShipment: false);

        foreach ($order->getItemUnits() as $item) {
            $shipment->addUnit($item);
        }
        $order->addShipment($shipment);
        $order->setShippingState(OrderShippingStates::STATE_AWAITING_PAYMENT);

        yield 'OrderItem shipment state is invalid' => [
            $order,
            new OrderItemCollection(...$order->getItems()->toArray()),
            new InvalidStateException('shipmentState', [
                ShipmentInterface::STATE_AWAITING_PAYMENT,
                ShipmentInterface::STATE_AWAITING_PRESCRIPTION,
                ShipmentInterface::STATE_CANCELLED,
            ], $shipment),
        ];
    }

    private function getValidOrderWithMultipleShipments(): Order
    {
        $supplierBad = SupplierFactory::createPrefilled([
            'name' => 'Apotheek Bad Nieuweschans BV',
            'identifier' => self::SUPPLIER_APOTHEEK_BAD_NIEUWESCHANS,
        ]);

        $supplierCulemborg = SupplierFactory::createPrefilled([
            'name' => 'Apotheek Culemborg BV',
            'identifier' => self::SUPPLIER_APOTHEEK_CULEMBORG,
        ]);

        $supplierPrime = SupplierFactory::createPrefilled([
            'name' => 'Prime-Pharmacy',
            'identifier' => self::SUPPLIER_PRIME_PHARMACY,
        ]);

        $order = OrderFactory::createPrefilled(
            [
                'shippingState' => OrderShippingStates::STATE_AWAITING_PAYMENT,
                'channel' => ChannelFactory::createPrefilled(['multipleShipmentsAllowed' => true]),
            ],
            hasShipment: false,
            supplier: $supplierBad
        );

        $shipment = ShipmentFactory::create([
            'shippingMethod' => ShipmentFactory::createShippingMethod(),
            'state' => OrderShippingStates::STATE_AWAITING_PAYMENT,
            'supplier' => $supplierBad,
        ]);

        foreach ($order->getItemUnits() as $item) {
            if (!$item->getOrderItem()->getVariant()->getSupplier()) {
                continue;
            }
            $shipment->addUnit($item);
        }
        $order->addShipment($shipment);

        $order->addShipment($this->createShipmentWithOrderItems($order, $supplierCulemborg));
        $order->addShipment($this->createShipmentWithOrderItems($order, $supplierPrime, numberOfItems: 2));

        return $order;
    }

    private function createShipmentWithOrderItems(Order $order, SupplierInterface $supplier, int $numberOfItems = 1, string $state = ShipmentInterface::STATE_AWAITING_PAYMENT): ShipmentInterface
    {
        $defaultId = crc32($supplier->getIdentifier());
        $shipment = ShipmentFactory::create([
            'id' => $defaultId,
            'shippingMethod' => ShipmentFactory::createShippingMethod(),
            'state' => $state,
            'supplier' => $supplier,
        ]);

        for ($i = 0; $i < $numberOfItems; ++$i) {
            $OtcProductVariant = ProductVariantFactory::createPrefilled(
                [
                    'id' => $defaultId + $i,
                    'code' => "7_5{$i}_".str_replace('-', '_', $supplier->getIdentifier()).'_worldwide',
                    'prescriptionRequired' => false,
                    'productData' => [
                        'code' => '7',
                    ],
                    'attributes' => [
                        Attributes::ATTRIBUTE_Z_INDEX => 'Z00001',
                    ],
                    'supplier' => $supplier,
                ],
                ProductType::MEDICATION,
                $order->getLocaleCode(),
                $order->getLocaleCode(),
                [$order->getLocaleCode()],
            );

            $otcOrderItem = OrderItemFactory::create([
                'id' => $defaultId + $i,
                'unitPrice' => 300,
                'unitCostPrice' => 150,
                'originalUnitPrice' => 300,
                'variant' => $OtcProductVariant,
            ]);

            $otcOrderItemUnit = OrderFactory::createOrderItemUnit([
                'orderItem' => $otcOrderItem,
                'shipment' => $shipment,
            ]);
            $otcOrderItem->addUnit($otcOrderItemUnit);
            $order->addItem($otcOrderItem);
            $shipment->addUnit($otcOrderItemUnit);
        }

        return $shipment;
    }

    private function successFullyFindAlternativeProductVariants(?SupplierInterface $newSupplier, InvocationOrder $exactly): void
    {
        $this->productVariantRepository
            ->expects($exactly)
            ->method('findOneByCodePrefixAndSupplier')
            ->willReturnCallback(static function (
                Channel $channel,
                string $productVariantCodeWithoutSupplierCountry,
                SupplierInterface $supplier,
            ) use ($newSupplier) {
                return ProductVariantFactory::createPrefilled([
                    'code' => $productVariantCodeWithoutSupplierCountry.'_'.str_replace('-', '_', $newSupplier->getIdentifier()).'_worldwide',
                    'supplier' => $newSupplier,
                ]);
            });
    }

    private function getOrderItemsToSwitchBySuppliers(Order $order, array $switchSuppliers = ['apotheek-bad-nieuweschans', 'apotheek-culemborg']): mixed
    {
        // Get all order items in a flat array from the shipments that belong to the suppliers 'apotheek-bad-nieuweschans' and 'apotheek-culemborg'
        $switchOrderItems = array_reduce(
            $order->getShipments()
                /** @phpstan-ignore-next-line @todo MEET-86-shipment */
                ->filter(static fn (ShipmentInterface $shipment) => in_array(
                    $shipment->getSupplierFromUnits()?->getIdentifier(),
                    $switchSuppliers,
                    true
                ))
                ->map(
                    /** @phpstan-ignore-next-line @todo MEET-86-shipment */
                    static fn (ShipmentInterface $shipment) => $shipment->getUnits()
                        ->map(static fn (OrderItemUnit $unit) => $unit->getOrderItem())
                        ->toArray()
                )
                ->toArray(),
            'array_merge',
            []
        );

        return new OrderItemCollection(...$switchOrderItems);
    }
}
