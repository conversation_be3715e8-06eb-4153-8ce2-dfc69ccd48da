<?php

declare(strict_types=1);

namespace App\Tests\Supplier;

use App\Entity\Product\ProductType;
use App\Entity\Shipping\ShipmentInterface;
use App\Supplier\CreateSupplierServiceShipmentDispatcher;
use App\Supplier\Message\CreateSupplierServiceShipment;
use App\Supplier\SupplierServiceShipmentExistsCheckerInterface;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\OrderItemFactory;
use App\Tests\Util\Factory\ProductFactory;
use App\Tests\Util\Factory\ProductVariantFactory;
use App\Tests\Util\Factory\ShipmentFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\MockObject\Stub;
use PHPUnit\Framework\TestCase;
use Ramsey\Uuid\Uuid;
use SM\Factory\FactoryInterface;
use SM\StateMachine\StateMachineInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

final class CreateSupplierServiceShipmentDispatcherTest extends TestCase
{
    private CreateSupplierServiceShipmentDispatcher $createSupplierServiceShipmentDispatcher;

    private MessageBusInterface&MockObject $messageBusMock;
    private FactoryInterface&Stub $statemachineFactoryStub;
    private SupplierServiceShipmentExistsCheckerInterface&MockObject $supplierServiceShipmentExistsChecker;

    protected function setUp(): void
    {
        $this->messageBusMock = $this->createMock(MessageBusInterface::class);
        $this->statemachineFactoryStub = $this->createStub(FactoryInterface::class);
        $this->supplierServiceShipmentExistsChecker = $this->createMock(SupplierServiceShipmentExistsCheckerInterface::class);

        $this->createSupplierServiceShipmentDispatcher = new CreateSupplierServiceShipmentDispatcher(
            $this->messageBusMock,
            $this->statemachineFactoryStub,
            $this->supplierServiceShipmentExistsChecker,
        );
    }

    public function testShipmentThatIsNotAllowedWillNotDispatch(): void
    {
        // Arrange
        $shipment = ShipmentFactory::create([
            'order' => OrderFactory::create([
                'channel' => ChannelFactory::create(),
            ]),
        ]);

        // Assert
        $this->messageBusMock->expects(self::never())
            ->method('dispatch');

        // Act
        $this->createSupplierServiceShipmentDispatcher->dispatch($shipment);
    }

    public function testShipmentForFollowUpOrderIsAllowed(): void
    {
        // Arrange
        $shipment = $this->createShipmentForFollowUpOrder();

        // Act
        $allowed = $this->createSupplierServiceShipmentDispatcher->isAllowed($shipment);

        // Assert
        self::assertTrue($allowed);
    }

    public function testShipmentForFollowUpOrderWillDispatch(): void
    {
        // Arrange
        $shipment = $this->createShipmentForFollowUpOrder();
        $message = new CreateSupplierServiceShipment($shipment);

        // Assert
        $this->messageBusMock->expects(self::once())
            ->method('dispatch')
            ->with($message)
            ->willReturn(new Envelope($message));

        // Act
        $this->createSupplierServiceShipmentDispatcher->dispatch($shipment);
    }

    public function testShipmentWithReferenceButNotTransitionedIsAllowed(): void
    {
        // Arrange
        $shipment = $this->createShipmentWithSupplierReferenceButNotTransitioned();

        $stateMachineMock = $this->createMock(StateMachineInterface::class);
        $this->statemachineFactoryStub->method('get')
            ->willReturn($stateMachineMock);

        $stateMachineMock->expects(self::once())
            ->method('can')
            ->with('notify_supplier')
            ->willReturn(true);

        $this->supplierServiceShipmentExistsChecker
            ->expects(self::once())
            ->method('exists')
            ->with($shipment)
            ->willReturn(true);

        // Act
        $allowed = $this->createSupplierServiceShipmentDispatcher->isAllowed($shipment);

        // Assert
        self::assertTrue($allowed);
    }

    public function testShipmentWithReferenceButNotTransitionedWillDispatch(): void
    {
        // Arrange
        $shipment = $this->createShipmentWithSupplierReferenceButNotTransitioned();
        $message = new CreateSupplierServiceShipment($shipment);

        $stateMachineMock = $this->createMock(StateMachineInterface::class);
        $this->statemachineFactoryStub->method('get')
            ->willReturn($stateMachineMock);

        $stateMachineMock->expects(self::once())
            ->method('can')
            ->with('notify_supplier')
            ->willReturn(true);

        $this->supplierServiceShipmentExistsChecker
            ->expects(self::once())
            ->method('exists')
            ->with($shipment)
            ->willReturn(true);

        // Assert
        $this->messageBusMock->expects(self::once())
            ->method('dispatch')
            ->with($message)
            ->willReturn(new Envelope($message));

        // Act
        $this->createSupplierServiceShipmentDispatcher->dispatch($shipment);
    }

    /**
     * @dataProvider provideShipmentWithValidPaymentStateAndWithoutSupplierReference
     */
    public function testShipmentWithValidPaymentStateAndWithoutSupplierReferenceIsAllowed(
        ShipmentInterface $shipment,
    ): void {
        // Arrange
        $stateMachineMock = $this->createMock(StateMachineInterface::class);
        $this->statemachineFactoryStub->method('get')
            ->willReturn($stateMachineMock);
        $stateMachineMock->expects(self::once())
            ->method('can')
            ->with('notify_supplier')
            ->willReturn(true);

        // Act
        $allowed = $this->createSupplierServiceShipmentDispatcher->isAllowed($shipment);

        // Assert
        self::assertTrue($allowed);
    }

    /**
     * @dataProvider provideShipmentWithValidPaymentStateAndWithoutSupplierReference
     */
    public function testShipmentWithValidPaymentStateAndWithoutSupplierReferenceWillDispatch(
        ShipmentInterface $shipment,
    ): void {
        // Arrange
        $message = new CreateSupplierServiceShipment($shipment);

        $stateMachineMock = $this->createMock(StateMachineInterface::class);
        $this->statemachineFactoryStub->method('get')
            ->willReturn($stateMachineMock);

        $stateMachineMock->expects(self::once())
            ->method('can')
            ->with('notify_supplier')
            ->willReturn(true);

        // Assert
        $this->messageBusMock->expects(self::once())
            ->method('dispatch')
            ->with($message)
            ->willReturn(new Envelope($message));

        // Act
        $this->createSupplierServiceShipmentDispatcher->dispatch($shipment);
    }

    /**
     * @return iterable<array{
     *     0: ShipmentInterface
     * }>
     */
    public function provideShipmentWithValidPaymentStateAndWithoutSupplierReference(): iterable
    {
        yield [
            ShipmentFactory::create([
                'order' => OrderFactory::create([
                    'channel' => ChannelFactory::create(),
                    'paymentState' => 'authorized',
                ]),
            ]),
        ];

        yield [
            ShipmentFactory::create([
                'order' => OrderFactory::create([
                    'channel' => ChannelFactory::create(),
                    'paymentState' => 'paid',
                ]),
            ]),
        ];
    }

    private function createShipmentWithSupplierReferenceButNotTransitioned(): ShipmentInterface
    {
        return ShipmentFactory::create([
            'order' => OrderFactory::create([
                'channel' => ChannelFactory::create(),
            ]),
            'supplierShipmentReference' => Uuid::uuid4()->toString(),
        ]);
    }

    private function createShipmentForFollowUpOrder(): ShipmentInterface
    {
        return ShipmentFactory::create([
            'order' => OrderFactory::create([
                'channel' => ChannelFactory::create([
                    'addPrescriptionMedicationDirectlyToCart' => false,
                    'medicationChannel' => ChannelFactory::create([
                        'addPrescriptionMedicationDirectlyToCart' => true,
                    ]),
                ]),
                'items' => [
                    OrderItemFactory::create([
                        'variant' => ProductVariantFactory::create([
                            'product' => ProductFactory::createPrefilled([
                                'code' => 'service_blueclinic',
                            ], ProductType::SERVICE),
                        ]),
                    ]),
                ],
            ]),
        ]);
    }
}
