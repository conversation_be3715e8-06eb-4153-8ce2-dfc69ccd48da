<?php

declare(strict_types=1);

namespace App\Tests\Supplier;

use App\Entity\Order\Order;
use App\Entity\Shipping\ShipmentInterface;
use App\Entity\Supplier\SupplierInterface;
use App\Supplier\Exception\ConvertToSupplierServiceDtoException;
use App\Supplier\Resolver\SupplierIdentifierResolverInterface;
use App\Supplier\SupplierServiceShipmentCreator;
use App\Tests\Util\Factory\CustomerFactory;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\SupplierFactory;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use RuntimeException;
use Superbrave\PharmacyServiceClient\ClientInterface;
use Superbrave\PharmacyServiceClient\Model\Request\CreateOrder;
use Superbrave\PharmacyServiceClient\Model\Response\Order as SupplierServiceOrder;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

class SupplierServiceShipmentCreatorTest extends TestCase
{
    private const string STORE_NAME = 'test-store-name';

    private ClientInterface&MockObject $supplierServiceClient;
    private LoggerInterface&MockObject $logger;
    private MockObject&UrlGeneratorInterface $router;
    private SupplierIdentifierResolverInterface&MockObject $supplierIdentifier;
    private SupplierServiceShipmentCreator $supplierServiceShipmentCreator;
    private EntityManagerInterface&MockObject $entityManagerMock;

    protected function setUp(): void
    {
        $this->supplierServiceClient = $this->createMock(ClientInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->router = $this->createMock(UrlGeneratorInterface::class);
        $this->router->method('generate')->willReturn('test/route');

        $this->supplierIdentifier = $this->createMock(SupplierIdentifierResolverInterface::class);
        $this->entityManagerMock = $this->createMock(EntityManagerInterface::class);

        $this->supplierServiceShipmentCreator = new SupplierServiceShipmentCreator(
            $this->supplierServiceClient,
            $this->logger,
            $this->router,
            $this->supplierIdentifier,
            $this->entityManagerMock
        );
    }

    public function testItCreatesSupplierShipmentOrder(): void
    {
        // Arrange
        $order = OrderFactory::createPrefilled();
        $customer = CustomerFactory::createPrefilled();
        $order->setCustomer($customer);
        /** @var ShipmentInterface $shipment */
        $shipment = $order->getShipments()->first();
        $supplier = SupplierFactory::create([
            'id' => 1,
            'name' => 'Test supplier',
            'identifier' => 'test-supplier',
        ]);

        $this->supplierIdentifier->method('resolveForShipment')->willReturn($supplier);

        // Act
        $supplierServiceOrder = $this->supplierServiceShipmentCreator->create(
            $order,
            $shipment,
            self::STORE_NAME
        );

        // Assert
        $this->assertInstanceOf(SupplierServiceOrder::class, $supplierServiceOrder);
        $this->assertInstanceOf(SupplierInterface::class, $shipment->getSupplier());
    }

    /**
     * @return iterable<string, array{0:Order}>
     */
    public function provideOrderWithoutPhoneNumber(): iterable
    {
        $order = OrderFactory::createPrefilled([
            'shippingAddressData' => [
                'phoneNumber' => null,
            ],
            'billingAddressData' => [
                'phoneNumber' => 'correct-phone-number',
            ],
        ]);
        $customer = CustomerFactory::createPrefilled([
            'phoneNumber' => 'wrong-phone-number',
        ]);
        $order->setCustomer($customer);

        yield 'Order with phoneNumber=null on shippingAddress' => [$order];

        $order = OrderFactory::createPrefilled([
            'shippingAddressData' => [
                'phoneNumber' => 'correct-phone-number',
            ],
            'billingAddressData' => [
                'phoneNumber' => null,
            ],
        ]);
        $customer = CustomerFactory::createPrefilled([
            'phoneNumber' => 'wrong-phone-number',
        ]);
        $order->setCustomer($customer);

        yield 'Order with phoneNumber=null on billingAddress' => [$order];

        $order = OrderFactory::createPrefilled([
            'shippingAddressData' => [
                'phoneNumber' => null,
            ],
            'billingAddressData' => [
                'phoneNumber' => null,
            ],
        ]);
        $customer = CustomerFactory::createPrefilled([
            'phoneNumber' => 'correct-phone-number',
        ]);
        $order->setCustomer($customer);

        yield 'Order with phoneNumber=null on billingAddress and shippingAddress' => [$order];

        $order = OrderFactory::createPrefilled([
            'shippingAddressData' => [
                'phoneNumber' => '',
            ],
            'billingAddressData' => [
                'phoneNumber' => 'correct-phone-number',
            ],
        ]);
        $customer = CustomerFactory::createPrefilled([
            'phoneNumber' => 'wrong-phone-number',
        ]);
        $order->setCustomer($customer);

        yield 'Order with phoneNumber="" on shippingAddress' => [$order];

        $order = OrderFactory::createPrefilled([
            'shippingAddressData' => [
                'phoneNumber' => 'correct-phone-number',
            ],
            'billingAddressData' => [
                'phoneNumber' => '',
            ],
        ]);
        $customer = CustomerFactory::createPrefilled([
            'phoneNumber' => 'wrong-phone-number',
        ]);
        $order->setCustomer($customer);

        yield 'Order with phoneNumber="" on billingAddress' => [$order];

        $order = OrderFactory::createPrefilled([
            'shippingAddressData' => [
                'phoneNumber' => '',
            ],
            'billingAddressData' => [
                'phoneNumber' => '',
            ],
        ]);
        $customer = CustomerFactory::createPrefilled([
            'phoneNumber' => 'correct-phone-number',
        ]);
        $order->setCustomer($customer);

        yield 'Order with phoneNumber="" on billingAddress and shippingAddress' => [$order];
    }

    /**
     * @dataProvider provideOrderWithoutPhoneNumber
     */
    public function testItCreatesSupplierShipmentOrderWithoutPhoneOnShippingAddress(Order $order): void
    {
        // Arrange
        /** @var ShipmentInterface $shipment */
        $shipment = $order->getShipments()->first();
        $supplier = SupplierFactory::create([
            'id' => 1,
            'name' => 'Test supplier',
            'identifier' => 'test-supplier',
        ]);

        $this->supplierIdentifier->method('resolveForShipment')->willReturn($supplier);

        // Assert
        $this->supplierServiceClient->expects(self::once())
            ->method('createShipmentWithUuid')
            ->with(
                self::callback(static function (string $uuid): bool {
                    self::assertNotEmpty($uuid);

                    return true;
                }),
                self::callback(
                    static fn (CreateOrder $createOrder) => $createOrder->jsonSerialize()['customer']->jsonSerialize()['phoneNumber'] === 'correct-phone-number'
                )
            );

        // Act
        $this->supplierServiceShipmentCreator->create(
            $order,
            $shipment,
            self::STORE_NAME
        );
    }

    public function testItThrowsExceptionIfSupplierShipmentOrderCannotBeCreated(): void
    {
        // Arrange
        $order = OrderFactory::createPrefilled();
        $order->setCustomer(null);
        /** @var ShipmentInterface $shipment */
        $shipment = $order->getShipments()->first();
        $supplier = SupplierFactory::create([
            'id' => 1,
            'name' => 'Test supplier',
            'identifier' => 'test-supplier',
        ]);

        $this->supplierIdentifier->method('resolveForShipment')->willReturn($supplier);

        // Assert
        $this->expectException(ConvertToSupplierServiceDtoException::class);

        // Act
        $this->supplierServiceShipmentCreator->create(
            $order,
            $shipment,
            self::STORE_NAME
        );
    }

    public function testSupplierServiceClientThrowsClientException(): void
    {
        // Arrange
        $order = OrderFactory::createPrefilled();
        $customer = CustomerFactory::createPrefilled();
        $order->setCustomer($customer);
        /** @var ShipmentInterface $shipment */
        $shipment = $order->getShipments()->first();
        $supplier = SupplierFactory::create([
            'id' => 1,
            'name' => 'Test supplier',
            'identifier' => 'test-supplier',
        ]);

        $this->supplierIdentifier->method('resolveForShipment')->willReturn($supplier);

        $this->supplierServiceClient->method('createShipmentWithUuid')
            ->willThrowException(new RuntimeException());

        // Assert
        $this->expectException(RuntimeException::class);

        // Act
        $this->supplierServiceShipmentCreator->create(
            $order,
            $shipment,
            self::STORE_NAME
        );
    }

    public function testSupplierServiceClientReturnsNullForHttpConflictResponseStatus(): void
    {
        // Arrange
        $order = OrderFactory::createPrefilled();
        $customer = CustomerFactory::createPrefilled();
        $order->setCustomer($customer);
        /** @var ShipmentInterface $shipment */
        $shipment = $order->getShipments()->first();

        $supplier = SupplierFactory::create([
            'id' => 1,
            'name' => 'Test supplier',
            'identifier' => 'test-supplier',
        ]);

        $this->supplierIdentifier->method('resolveForShipment')->willReturn($supplier);

        $response = $this->createMock(ResponseInterface::class);
        $response->method('getStatusCode')
            ->willReturn(409);

        $clientException = $this->createMock(ClientExceptionInterface::class);
        $clientException->method('getResponse')
            ->willReturn($response);

        $this->supplierServiceClient->method('createShipmentWithUuid')
            ->willThrowException($clientException);

        // Act
        $supplierServiceOrder = $this->supplierServiceShipmentCreator->create(
            $order,
            $shipment,
            self::STORE_NAME
        );

        // Assert
        $this->assertNull($supplierServiceOrder);
    }
}
