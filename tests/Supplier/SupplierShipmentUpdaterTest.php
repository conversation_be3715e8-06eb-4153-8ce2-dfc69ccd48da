<?php

declare(strict_types=1);

namespace App\Tests\Supplier;

use App\Entity\Addressing\Country;
use App\Entity\Channel\Channel;
use App\Entity\Order\Order;
use App\Entity\Order\OrderItem;
use App\Entity\Order\OrderItemUnit;
use App\Entity\Order\PreferredOrderItem;
use App\Entity\Product\ProductType;
use App\Entity\Product\ProductVariant;
use App\Entity\Shipping\Shipment;
use App\Entity\Supplier\Supplier;
use App\Event\Enum\ShipmentEventName;
use App\Event\SupplierOnShipmentHasChangedEvent;
use App\Exception\InvalidStateException;
use App\Repository\ProductVariantRepositoryInterface;
use App\StateMachine\OrderShippingStates;
use App\Supplier\CreateSupplierServiceShipmentDispatcherInterface;
use App\Supplier\OrderItemsUpdater;
use App\Supplier\SupplierShipmentUpdater;
use App\Tests\Util\Factory\ChannelFactory;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\ProductFactory;
use App\Tests\Util\Factory\SupplierFactory;
use Doctrine\ORM\EntityManagerInterface;
use InvalidArgumentException;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use Sylius\Resource\Factory\Factory;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

class SupplierShipmentUpdaterTest extends TestCase
{
    private SupplierShipmentUpdater $orderService;
    private ProductVariantRepositoryInterface&MockObject $productVariantRepository;
    private CreateSupplierServiceShipmentDispatcherInterface&MockObject $createSupplierServiceShipmentDispatcherMock;
    private EntityManagerInterface&MockObject $entityManager;
    private EventDispatcherInterface&MockObject $eventDispatcherMock;

    protected function setUp(): void
    {
        $this->productVariantRepository = $this->createMock(ProductVariantRepositoryInterface::class);
        $this->createSupplierServiceShipmentDispatcherMock = $this->createMock(CreateSupplierServiceShipmentDispatcherInterface::class);
        $this->entityManager = $this->createMock(EntityManagerInterface::class);
        $this->eventDispatcherMock = $this->createMock(EventDispatcherInterface::class);

        $this->orderService = new SupplierShipmentUpdater(
            $this->entityManager,
            $this->eventDispatcherMock,
            $this->createSupplierServiceShipmentDispatcherMock,
            new Factory(Shipment::class),
            new OrderItemsUpdater($this->productVariantRepository)
        );
    }

    public function testMultipleShipmentsOnChannelAreNotAllowed(): void
    {
        // Arrange
        $order = OrderFactory::create([
            'channel' => ChannelFactory::createPrefilled(['multipleShipmentsAllowed' => true]),
        ]);

        // Assert
        $this->expectException(InvalidArgumentException::class);

        // Act
        $this->orderService->updateSupplierOnOrder($order, SupplierFactory::createPrefilled());
    }

    public function testItCanUpdateSupplierOnOrderItem(): void
    {
        $country = new Country();
        $order = new Order();
        $channel = new Channel();
        $channel->setCode('dok_nl');
        $order->setChannel($channel);
        $order->setShippingState(OrderShippingStates::STATE_AWAITING_PAYMENT);
        $shipment = new Shipment();
        $oldSupplier = new Supplier();
        $shipment->setSupplier($oldSupplier);
        $order->addShipment($shipment);

        $supplier = new Supplier();
        $productVariant = new ProductVariant();
        $productVariant->setCode('7_5_old_supplier');
        $productVariant->setCountry($country);

        $product = ProductFactory::create();
        ProductFactory::setType($product, ProductType::MEDICATION->value);
        $productVariant->setProduct($product);

        $item = new OrderItem();
        $item->setVariant($productVariant);
        $order->addItem($item);

        $newProductVariant = new ProductVariant();
        $newProductVariant->setCode('7_5_new_supplier');
        $this->productVariantRepository
            ->method('findOneByCodePrefixAndSupplier')
            ->with(
                $channel,
                '7_5_',
                $supplier,
            )
            ->willReturn($newProductVariant);

        $this->eventDispatcherMock->expects($this->once())
            ->method('dispatch')
            ->with(
                new SupplierOnShipmentHasChangedEvent($shipment, $shipment, $oldSupplier, $supplier),
                ShipmentEventName::SupplierOnShipmentHasChanged->name
            );

        $this->orderService->updateSupplierOnOrder($order, $supplier);

        $orderItem = $order->getItems()->first();
        self::assertSame($orderItem->getVariant(), $newProductVariant);
    }

    public function testItCanUpdateSupplierOnPreferredOrderItem(): void
    {
        $country = new Country();
        $order = new Order();
        $channel = new Channel();
        $channel->setCode('dok_nl');
        $order->setChannel($channel);
        $order->setShippingState(OrderShippingStates::STATE_AWAITING_PAYMENT);

        $shipment = new Shipment();
        $oldSupplier = new Supplier();
        $shipment->setSupplier($oldSupplier);
        $order->addShipment($shipment);

        $supplier = new Supplier();
        $preferredProductVariant = new ProductVariant();
        $preferredProductVariant->setCode('7_5_old_supplier');
        $preferredProductVariant->setCountry($country);

        $product = ProductFactory::create();
        ProductFactory::setType($product, ProductType::MEDICATION->value);
        $preferredProductVariant->setProduct($product);

        $consultProduct = ProductFactory::create();
        ProductFactory::setType($consultProduct, ProductType::CONSULT->value);
        $consultVariant = ProductFactory::addVariant($consultProduct);
        $item = new OrderItem();
        $item->setVariant($consultVariant);
        $newPreferredOrderItem = new PreferredOrderItem($item, $preferredProductVariant);
        $item->addPreferredItem($newPreferredOrderItem);
        $order->addItem($item);

        $newProductVariant = new ProductVariant();
        $newProductVariant->setCode('7_5_new_supplier');
        $this->productVariantRepository
            ->method('findOneByCodePrefixAndSupplier')
            ->with(
                $channel,
                '7_5_',
                $supplier,
            )
            ->willReturn($newProductVariant);

        $this->eventDispatcherMock->expects($this->once())
            ->method('dispatch')
            ->with(
                new SupplierOnShipmentHasChangedEvent($shipment, $shipment, $oldSupplier, $supplier),
                ShipmentEventName::SupplierOnShipmentHasChanged->name
            );

        $this->orderService->updateSupplierOnOrder($order, $supplier);

        $orderItem = $order->getItems()->first();
        /** @var PreferredOrderItem $preferredOrderItem */
        $preferredOrderItem = $orderItem->getPreferredItems()->first();
        self::assertSame($preferredOrderItem->getVariant(), $newProductVariant);
    }

    /**
     * @dataProvider validShippingStateProvider
     */
    public function testItCanUpdateSupplierShipmentWhenAwaitingPayment(string $shippingState): void
    {
        $order = new Order();
        $shipment = new Shipment();
        $supplier = new Supplier();
        $channel = new Channel();
        $supplier->setIdentifier('old-supplier');
        $shipment->setSupplier($supplier);
        $order->setShippingState($shippingState);
        $order->addShipment($shipment);
        $order->setChannel($channel);

        $newSupplier = new Supplier();
        $newSupplier->setIdentifier('new-supplier');

        $this->orderService->updateSupplierOnOrder($order, $newSupplier);

        /** @var Shipment $shipment */
        $shipment = $order->getShipments()->first();
        self::assertSame($newSupplier, $shipment->getSupplier());
    }

    public function testItCanUpdateSupplierShipmentWhenShipmentIsCancelled(): void
    {
        // Arrange
        $order = new Order();
        $shipment = new Shipment();
        $supplier = new Supplier();
        $channel = new Channel();
        $supplier->setIdentifier('old-supplier');
        $shipment->setSupplier($supplier);
        $orderItem = new OrderItem();
        $unit = new OrderItemUnit($orderItem);
        $shipment->addUnit($unit);
        $order->setShippingState(OrderShippingStates::STATE_CANCELLED);
        $order->addShipment($shipment);
        $order->setChannel($channel);

        $newSupplier = new Supplier();
        $newSupplier->setIdentifier('new-supplier');

        $newShipmentId = 1337;

        $this->entityManager
            ->expects($this->once())
            ->method('persist')
            ->willReturnCallback(function (Shipment $shipment) use ($newShipmentId) {
                $reflection = new ReflectionClass(Shipment::class);
                $property = $reflection->getProperty('id');
                $property->setAccessible(true);
                $property->setValue($shipment, $newShipmentId);
            });

        $this->createSupplierServiceShipmentDispatcherMock->expects(self::once())
            ->method('dispatch')
            ->with(self::callback(static fn (Shipment $shipment) => $shipment->getId() === $newShipmentId));

        // Act
        $this->orderService->updateSupplierOnOrder($order, $newSupplier);

        // Assert
        $shipment = $order->getShipments()->first();
        self::assertInstanceOf(Shipment::class, $shipment);
        self::assertSame($newSupplier, $shipment->getSupplier());
        self::assertSame($unit, $shipment->getUnits()->first());
    }

    public function validShippingStateProvider(): array
    {
        return [
            'Order with shipping state awaiting payment' => [OrderShippingStates::STATE_AWAITING_PAYMENT],
            'Order with shipping state awaiting prescription' => [OrderShippingStates::STATE_AWAITING_PRESCRIPTION],
        ];
    }

    /**
     * @dataProvider invalidOrderShippingStateProvider
     */
    public function testCheckInvalidOrderShippingStates(string $shippingState): void
    {
        $order = new Order();
        $order->setChannel(ChannelFactory::createPrefilled());
        $order->setShippingState($shippingState);
        $shipment = new Shipment();
        $oldSupplier = new Supplier();
        $shipment->setSupplier($oldSupplier);
        $order->addShipment($shipment);

        $supplier = new Supplier();

        $this->expectException(InvalidStateException::class);
        $this->expectExceptionMessage('Order has invalid shippingState state. It needs to be one of awaiting_payment, awaiting_prescription, cancelled');

        $this->orderService->updateSupplierOnOrder($order, $supplier);
    }

    public function invalidOrderShippingStateProvider(): array
    {
        return [
            [OrderShippingStates::STATE_PROCESSING],
            [OrderShippingStates::STATE_READY],
            [OrderShippingStates::STATE_SHIPPED],
            [OrderShippingStates::STATE_PENDING],
            [OrderShippingStates::STATE_CART],
            [OrderShippingStates::STATE_RETURNED],
        ];
    }

    public function testCheckInvalidSupplierThrowsException(): void
    {
        $order = new Order();
        $order->setShippingState(OrderShippingStates::STATE_AWAITING_PAYMENT);
        $order->setChannel(ChannelFactory::createPrefilled());

        $supplier = new Supplier();

        $shipment = new Shipment();
        $shipment->setSupplier($supplier);
        $shipment->setState(OrderShippingStates::STATE_AWAITING_PAYMENT);

        $order->addShipment($shipment);

        $this->expectException(InvalidArgumentException::class);

        $this->orderService->updateSupplierOnOrder($order, $supplier);
    }
}
