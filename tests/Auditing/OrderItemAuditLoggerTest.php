<?php

declare(strict_types=1);

namespace App\Tests\Auditing;

use App\Api\Command\Admin\Order\UpdateOrderItemOperation;
use App\Auditing\AuditLoggerInterface;
use App\Auditing\OrderItemAuditLogger;
use App\Auditing\OrderItemAuditLoggerInterface;
use App\Auditing\OrderMessage;
use App\Entity\Order\OrderItem;
use App\Tests\Util\Factory\OrderFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class OrderItemAuditLoggerTest extends TestCase
{
    private AuditLoggerInterface&MockObject $auditLoggerMock;
    private OrderItemAuditLogger $orderItemAuditLogger;

    protected function setUp(): void
    {
        parent::setUp();

        $this->auditLoggerMock = $this->createMock(AuditLoggerInterface::class);
        $this->orderItemAuditLogger = new OrderItemAuditLogger($this->auditLoggerMock);
    }

    public function testLogAddItem(): void
    {
        // Arrange
        $order = OrderFactory::createPrefilled();

        // Assert
        $this->auditLoggerMock->expects(self::once())
            ->method('log')
            ->with(
                $order,
                OrderMessage::ORDER_ITEM_CHANGED,
                ['operation' => OrderItemAuditLoggerInterface::OPERATION_ADDED],
            );

        // Act
        $this->orderItemAuditLogger->logAddItem($order);
    }

    public function testLogRemoveItem(): void
    {
        // Arrange
        $order = OrderFactory::createPrefilled();

        // Assert
        $this->auditLoggerMock->expects(self::once())
            ->method('log')
            ->with(
                $order,
                OrderMessage::ORDER_ITEM_CHANGED,
                ['operation' => OrderItemAuditLoggerInterface::OPERATION_REMOVED],
            );

        // Act
        $this->orderItemAuditLogger->logRemoveItem($order);
    }

    /**
     * @dataProvider logUpdateItemDataProvider
     */
    public function testLogUpdateItem(string $modification, string $operation): void
    {
        // Arrange
        $order = OrderFactory::createPrefilled();

        // Assert
        $this->auditLoggerMock->expects(self::once())
            ->method('log')
            ->with(
                $order,
                OrderMessage::ORDER_ITEM_CHANGED,
                ['operation' => $operation],
            );

        // Act
        $this->orderItemAuditLogger->logUpdateItem($order, $modification);
    }

    /**
     * @dataProvider getUpdateItemOperationModificationDataProvider
     */
    public function testGetUpdateItemOperationModification(
        UpdateOrderItemOperation $operation,
        ?string $expectedModification,
    ): void {
        // Arrange
        $order = OrderFactory::createPrefilled();
        /** @var OrderItem $item */
        $item = $order->getItems()->first();
        $item->setUsageAdvice('test usage advice');

        // Act
        $modification = $this->orderItemAuditLogger->getUpdateItemOperationModification($order, $operation);

        // Assert
        self::assertSame($expectedModification, $modification);
    }

    private function logUpdateItemDataProvider(): iterable
    {
        yield [
            'modification' => OrderItemAuditLoggerInterface::MODIFICATION_QUANTITY_AND_USAGE_ADVICE,
            'operation' => 'updated usage advice and quantity of',
        ];
        yield [
            'modification' => OrderItemAuditLoggerInterface::MODIFICATION_USAGE_ADVICE,
            'operation' => 'updated usage advice of',
        ];
        yield [
            'modification' => OrderItemAuditLoggerInterface::MODIFICATION_QUANTITY,
            'operation' => 'updated quantity of',
        ];
    }

    private function getUpdateItemOperationModificationDataProvider(): iterable
    {
        $operation = new UpdateOrderItemOperation(1, '7_25_test-variant-code-consult', 2, 'test usage advice');

        yield [$operation, OrderItemAuditLoggerInterface::MODIFICATION_QUANTITY];

        $operation = new UpdateOrderItemOperation(1, '7_25_test-variant-code-consult', 2, 'updated usage advice');

        yield [$operation, OrderItemAuditLoggerInterface::MODIFICATION_QUANTITY_AND_USAGE_ADVICE];

        $operation = new UpdateOrderItemOperation(1, '7_25_test-variant-code-consult', 1, 'updated usage advice');

        yield [$operation, OrderItemAuditLoggerInterface::MODIFICATION_USAGE_ADVICE];

        $operation = new UpdateOrderItemOperation(1, '7_25_test-variant-code-consult', 1, 'test usage advice');

        yield [$operation, null];
    }
}
