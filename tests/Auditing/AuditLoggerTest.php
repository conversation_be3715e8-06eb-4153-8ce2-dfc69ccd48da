<?php

declare(strict_types=1);

namespace App\Tests\Auditing;

use App\Auditing\AuditLogger;
use App\Auditing\AuditSubjectInterface;
use App\Auditing\CustomerMessage;
use App\Auditing\MarketingSubscriptionMessage;
use App\Auditing\MessageInterface;
use App\Auditing\OrderMessage;
use App\Entity\Auditing\UserType;
use App\Entity\Customer\CustomerAuditEntry;
use App\Entity\Customer\TrustLevel;
use App\Entity\MarketingSubscription\MarketingSubscriptionAuditEntry;
use App\Entity\Order\OrderAuditEntry;
use App\Entity\User\AdminUser;
use App\Tests\Util\Factory\CustomerFactory;
use App\Tests\Util\Factory\MarketingSubscriptionFactory;
use App\Tests\Util\Factory\OrderFactory;
use Doctrine\ORM\EntityManagerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Bundle\SecurityBundle\Security;
use Webmozart\Assert\InvalidArgumentException;

final class AuditLoggerTest extends TestCase
{
    private AuditLogger $auditLogger;
    private Security&MockObject $securityMock;
    private EntityManagerInterface&MockObject $entityManagerMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->securityMock = $this->createMock(Security::class);
        $this->entityManagerMock = $this->createMock(EntityManagerInterface::class);
        $this->auditLogger = new AuditLogger($this->securityMock, $this->entityManagerMock);
    }

    /**
     * @param class-string $entryClass
     * @dataProvider auditOrderLogDataProvider
     */
    public function testItCanAuditLogAnOrder(
        AuditSubjectInterface $object,
        string $entryClass,
        MessageInterface $message,
        array $context,
        string $expectedMessage,
        string $expectedMessageWithInterpolatedContext,
        ?AdminUser $adminUser = null,
    ): void {
        // arrange
        $this->securityMock->method('getUser')
            ->willReturn($adminUser);

        $this->entityManagerMock->expects(self::once())
            ->method('persist')
            ->with(self::isInstanceOf($entryClass));

        $this->entityManagerMock->expects(self::once())
            ->method('flush');

        // Act
        $auditEntry = $this->auditLogger->log($object, $message, $context);

        // Assert
        self::assertSame($expectedMessage, $auditEntry->getMessage());
        self::assertSame($expectedMessageWithInterpolatedContext, $auditEntry->getMessageWithInterpolatedContext());

        if ($message->getType() === UserType::ADMIN_USER) {
            self::assertInstanceOf(AdminUser::class, $auditEntry->getUser());
            self::assertSame('admin-user', $auditEntry->getUsername());
        }
    }

    public function auditOrderLogDataProvider(): iterable
    {
        $order = OrderFactory::createPrefilled();

        $adminUser = new AdminUser();
        $adminUser->setUsername('admin-user');

        $context = [
            'from_shipment.id' => '1',
            'from_supplier.identifier' => 'ndsm-apotheek',
            'from_supplier.name' => 'NDSM Apotheek',
            'to_shipment.id' => '2',
            'to_supplier.identifier' => 'apotheek-bad-nieuweschans',
            'to_supplier.name' => 'Apotheek Bad Nieuweschans BV',
        ];

        yield 'It can create an audit log for a supplier change' => [
            'object' => $order,
            'entryClass' => OrderAuditEntry::class,
            'message' => OrderMessage::SWITCH_SUPPLIER,
            'context' => $context,
            'expectedMessage' => 'changed the supplier (shipment: {from_shipment.id}) from {from_supplier.name} to (shipment: {to_shipment.id}) {to_supplier.name}',
            'expectedMessageWithInterpolatedContext' => 'changed the supplier (shipment: 1) from NDSM Apotheek to (shipment: 2) Apotheek Bad Nieuweschans BV',
            'adminUser' => $adminUser,
        ];

        yield 'It can create an audit log with missing context' => [
            'object' => $order,
            'entryClass' => OrderAuditEntry::class,
            'message' => OrderMessage::SWITCH_SUPPLIER,
            'context' => [],
            'expectedMessage' => 'changed the supplier (shipment: {from_shipment.id}) from {from_supplier.name} to (shipment: {to_shipment.id}) {to_supplier.name}',
            'expectedMessageWithInterpolatedContext' => 'changed the supplier (shipment: {from_shipment.id}) from {from_supplier.name} to (shipment: {to_shipment.id}) {to_supplier.name}',
            'adminUser' => $adminUser,
        ];

        $customer = CustomerFactory::createPrefilled();

        yield 'It can create a customer trust level audit log' => [
            'object' => $customer,
            'entryClass' => CustomerAuditEntry::class,
            'message' => CustomerMessage::SWITCH_TRUST_LEVEL,
            'context' => [
                'from_trust_level' => TrustLevel::Trusted->value,
                'to_trust_level' => TrustLevel::Fraudulent->value,
            ],
            'expectedMessage' => 'changed the trust level from {from_trust_level} to {to_trust_level}',
            'expectedMessageWithInterpolatedContext' => 'changed the trust level from trusted to fraudulent',
            'adminUser' => $adminUser,
        ];

        $marketingSubscription = MarketingSubscriptionFactory::createPrefilled();

        yield 'It can create an audit log for a marketing subscription' => [
            'object' => $marketingSubscription,
            'entryClass' => MarketingSubscriptionAuditEntry::class,
            'message' => MarketingSubscriptionMessage::ReplaceMarketingSubscription,
            'context' => [],
            'expectedMessage' => 'marketing subscription updated',
            'expectedMessageWithInterpolatedContext' => 'marketing subscription updated',
            'adminUser' => $adminUser,
        ];
    }

    /**
     * @dataProvider invalidDataProvider
     */
    public function testItExpectsValidData(
        AuditSubjectInterface $order,
        MessageInterface $message,
        string $expectExceptionMessage,
    ): void {
        // Assert
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage($expectExceptionMessage);

        // Act
        $this->auditLogger->log($order, $message, []);
    }

    public function invalidDataProvider(): iterable
    {
        $order = OrderFactory::createPrefilled();

        yield 'The UserType \'admin_user\' expects a loggedIn adminUser.' => [
            'order' => $order,
            'message' => OrderMessage::SWITCH_SUPPLIER,
            'expectExceptionMessage' => 'Expected an instance of App\Entity\User\AdminUser. Got: NULL',
        ];
    }
}
