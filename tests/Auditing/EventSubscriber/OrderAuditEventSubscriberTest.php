<?php

declare(strict_types=1);

namespace App\Tests\Auditing\EventSubscriber;

use App\Auditing\AuditLoggerInterface;
use App\Auditing\EventSubscriber\OrderAuditEventSubscriber;
use App\Auditing\Exception\InvalidArgumentException;
use App\Auditing\OrderMessage;
use App\Entity\Order\Order;
use App\Event\SupplierOnShipmentHasChangedEvent;
use App\Tests\Util\Factory\OrderFactory;
use App\Tests\Util\Factory\ShipmentFactory;
use App\Tests\Util\Factory\SupplierFactory;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

final class OrderAuditEventSubscriberTest extends TestCase
{
    /**
     * @var AuditLoggerInterface<Order>&MockObject
     */
    private AuditLoggerInterface&MockObject $auditLogger;
    private OrderAuditEventSubscriber $orderAuditEventSubscriber;

    protected function setUp(): void
    {
        parent::setUp();

        $this->auditLogger = $this->createMock(AuditLoggerInterface::class);

        $this->orderAuditEventSubscriber = new OrderAuditEventSubscriber($this->auditLogger);
    }

    public function testGetSubscribedEventsCallsExistingMethods(): void
    {
        foreach ($this->orderAuditEventSubscriber::getSubscribedEvents() as $subscribedEvent) {
            self::assertTrue(method_exists($this->orderAuditEventSubscriber::class, $subscribedEvent));
        }
    }

    public function testAuditEntryIsCreatedOnSupplierChanged(): void
    {
        // Arrange
        $order = OrderFactory::createPrefilled();
        $shipment = ShipmentFactory::create(['order' => $order]);
        $oldShipment = ShipmentFactory::create(['id' => 45454, 'order' => $order]);

        $previousSupplier = SupplierFactory::createPrefilled(['identifier' => 'previous-supplier', 'name' => 'Previous Supplier']);
        $newSupplier = SupplierFactory::createPrefilled(['identifier' => 'new-supplier', 'name' => 'New Supplier']);

        $event = new SupplierOnShipmentHasChangedEvent($shipment, $oldShipment, $previousSupplier, $newSupplier);

        // Assert
        $this->auditLogger->expects(self::once())
            ->method('log')
            ->with(
                $order,
                OrderMessage::SWITCH_SUPPLIER,
                [
                    'from_shipment.id' => 45454,
                    'from_supplier.identifier' => 'previous-supplier',
                    'from_supplier.name' => 'Previous Supplier',
                    'to_shipment.id' => $shipment->getId(),
                    'to_supplier.identifier' => 'new-supplier',
                    'to_supplier.name' => 'New Supplier',
                ]
            );

        // Act
        $this->orderAuditEventSubscriber->supplierOnShipmentHasChanged($event);
    }

    public function testAuditEntryIsCreatedOnSupplierChangedHasValidOrder(): void
    {
        // Arrange
        $shipment = ShipmentFactory::create([]);

        $previousSupplier = SupplierFactory::createPrefilled(['identifier' => 'previous-supplier', 'name' => 'Previous Supplier']);
        $newSupplier = SupplierFactory::createPrefilled(['identifier' => 'new-supplier', 'name' => 'New Supplier']);

        $event = new SupplierOnShipmentHasChangedEvent($shipment, $shipment, $previousSupplier, $newSupplier);

        // Assert
        self::expectException(InvalidArgumentException::class);
        self::expectExceptionMessage('Expected an instance of App\Entity\Order\Order. Got: NULL');

        // Act
        $this->orderAuditEventSubscriber->supplierOnShipmentHasChanged($event);
    }
}
