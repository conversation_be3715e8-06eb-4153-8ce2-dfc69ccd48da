<?php

declare(strict_types=1);

namespace App\Supplier\Dto;

use App\Entity\Order\Order as OrderEntity;
use App\Entity\Product\ProductInterface;
use App\Entity\Product\ProductType;
use App\Entity\Shipping\ShipmentInterface;
use App\Supplier\Dto\Customer as CustomerDTO;
use App\Supplier\Dto\OrderItem as OrderItemDTO;
use App\Supplier\Dto\Supplier as SupplierDTO;
use Superbrave\PharmacyServiceClient\Model\OrderItem;
use Superbrave\PharmacyServiceClient\Model\Quantity;
use Superbrave\PharmacyServiceClient\Model\Request\Customer;
use Superbrave\PharmacyServiceClient\Model\SupplierProduct;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Webmozart\Assert\Assert;

final class Order
{
    private string $reference;

    private string $supplier;

    /** @var OrderItem[] */
    private array $orderItems;

    private Customer $customer;

    private string $notifyUrl;

    private ?string $prescriptionFilename;

    private ?string $additionReference;

    private ?string $prescriptionReference;

    public function __construct(
        string $reference,
        string $supplier,
        array $orderItems,
        Customer $customer,
        string $notifyUrl,
        ?string $prescriptionFilename = null,
        ?string $additionReference = null,
        ?string $prescriptionReference = null,
    ) {
        $this->reference = $reference;
        $this->supplier = $supplier;
        $this->orderItems = $orderItems;
        $this->customer = $customer;
        $this->notifyUrl = $notifyUrl;
        $this->prescriptionFilename = $prescriptionFilename;
        $this->additionReference = $additionReference;
        $this->prescriptionReference = $prescriptionReference;
    }

    public static function fromEntity(
        ShipmentInterface $shipment,
        UrlGeneratorInterface $router,
        string $supplierIdentifier,
    ): ?self {
        $supplierDTO = new SupplierDTO($supplierIdentifier);

        /** @var OrderEntity $order */
        $order = $shipment->getOrder();

        $customerDTO = CustomerDTO::fromEntity($order);
        if ($customerDTO === null) {
            return null;
        }

        Assert::stringNotEmpty($order->getLocaleCode());

        $filteredOrderItems = self::getFilteredOrderItems($order, $shipment);

        $consultSystemReference = $order->getConsultSystemReference()?->toString();
        if ($order->isFollowUpOrder()) {
            $parentOrder = $order->getParentOrder();
            Assert::isInstanceOf(
                $parentOrder,
                OrderEntity::class,
                "Followup order with id '{$order->getId()}' should have a parent order."
            );

            $consultSystemReference = $parentOrder->getConsultSystemReference()?->toString();
        }

        return new self(
            (string) $order->getNumber(),
            $supplierDTO->getSupplier(),
            $filteredOrderItems,
            $customerDTO->toSupplierServiceCustomer(),
            $router->generate('api_custom_notify_callback', [
                'tokenValue' => $order->getTokenValue(),
                'shipmentId' => $shipment->getId(),
            ], UrlGeneratorInterface::ABSOLUTE_URL),
            $order->getPrescriptionFilename(),
            $order->getParentOrder()?->getNumber(),
            $consultSystemReference,
        );
    }

    public function getReference(): string
    {
        return $this->reference;
    }

    public function getSupplier(): string
    {
        return $this->supplier;
    }

    public function getOrderItems(): array
    {
        return $this->orderItems;
    }

    public function getCustomer(): Customer
    {
        return $this->customer;
    }

    public function getNotifyUrl(): string
    {
        return $this->notifyUrl;
    }

    public function getPrescriptionFilename(): ?string
    {
        return $this->prescriptionFilename;
    }

    public function getAdditionReference(): ?string
    {
        return $this->additionReference;
    }

    public function getPrescriptionReference(): ?string
    {
        return $this->prescriptionReference;
    }

    private static function getFilteredOrderItems(OrderEntity $order, ShipmentInterface $shipment): array
    {
        $filteredOrderItems = [];

        foreach ($shipment->getOrderItems() as $orderItem) {
            $product = $orderItem->getProduct();

            // Only add order items that contain product variants that are linked to the supplier of the shipment.
            if ($product?->isOfType(ProductType::MEDICATION)
                && $orderItem->getVariant()?->getSupplier() !== $shipment->getSupplierFromUnits()
            ) {
                continue;
            }

            // Add a single order item for the prescription if order has prescription service
            if ($product?->isOfType(ProductType::CONSULT) && $order->hasServiceProduct(
                ProductInterface::SERVICE_PRESCRIPTION_CODE
            )) {
                $filteredOrderItems[] = new OrderItem(
                    ProductInterface::SERVICE_PRESCRIPTION_CODE,
                    new SupplierProduct('', 'Signed prescription'),
                    new Quantity(1, 'item'),
                    null
                );

                break;
            }

            /*
             * The {@see CreateSupplierServiceShipment} shipment also contains consult products since they have
             * shippingRequired=true. But, we don't actually 'ship' consults, so filter them out.
             * Also, make sure that service products are not sent to the supplier.
             */
            if ($product?->isOfType(ProductType::CONSULT) || $product?->isOfType(ProductType::SERVICE)) {
                continue;
            }

            $orderItemDTO = OrderItemDTO::fromEntity($orderItem);
            if (!$orderItemDTO instanceof OrderItemDTO) {
                continue;
            }

            $filteredOrderItems[] = new OrderItem(
                $orderItemDTO->getReference(),
                $orderItemDTO->getSupplierProduct(),
                $orderItemDTO->getQuantity(),
                $orderItemDTO->getUsageAdvice()
            );
        }

        return $filteredOrderItems;
    }
}
