Feature: Approve medication on order through the consult system
  To prescribe medication and let the customer receive their treatment
  As a doctor
  I need to be able to approve the orders from the consult system.

  The orders are approved in an external system that handles doctor consultations.

  Background:
    Given there is a channel "dok_de"
    And there is a product named "Flucloxacillin" with code "flucloxacillin" of type "medication"
    And the product with code "flucloxacillin" has the following product variants:
      | code                                           | name                           | prescriptionRequired | form   | dosage | packSize   | supplierIdentifier        | costPrice | maximumQuantityPerOrder |
      | 2635_27674_apotheek_bad_nieuweschans_worldwide | Flucloxacillin 500 mg 40 caps. | true                 | tablet | 25mg   | 4 piece(s) | apotheek-bad-nieuweschans | 2265      | 2                       |
      | 2635_27674_prime_pharmacy_worldwide            | Flucloxacillin 500 mg 40 caps. | true                 | tablet | 25mg   | 4 piece(s) | prime-pharmacy            | 1150      | 2                       |
    And the product variant with code "2635_27674_apotheek_bad_nieuweschans_worldwide" has the following channel prices:
      | channelCode | enabled | price | currency |
      | dok_de      | true    | 6770  | EUR      |
    And the product variant with code "2635_27674_prime_pharmacy_worldwide" has the following channel prices:
      | channelCode | enabled | price | currency |
      | dok_de      | true    | 6770  | EUR      |
    And there is a product named "Viagra" with code "viagra" of type "medication"
    And the product with code "viagra" has the following product variants:
      | code                            | name                | prescriptionRequired | form   | dosage | packSize   | supplierIdentifier        | costPrice | maximumQuantityPerOrder |
      | 7_5_bad_nieuweschans_worldwide  | Viagra 25mg 4 tabl. | true                 | tablet | 25mg   | 4 piece(s) | apotheek-bad-nieuweschans | 2265      | 2                       |
      | 7_6_ndsm_apotheek_worldwide     | Viagra 25mg 4 tabl. | true                 | tablet | 25mg   | 4 piece(s) | ndsm-apotheek             | 2265      | 1                       |
      | 7_50_bad_nieuweschans_worldwide | Viagra 50mg 4 tabl. | true                 | tablet | 50mg   | 4 piece(s) | apotheek-bad-nieuweschans | 4265      | 1                       |
    And the product variant with code "7_5_bad_nieuweschans_worldwide" has the following channel prices:
      | channelCode | enabled | price | currency |
      | dok_de      | true    | 6770  | EUR      |
    And the product variant with code "7_6_ndsm_apotheek_worldwide" has the following channel prices:
      | channelCode | enabled | price | currency |
      | dok_de      | true    | 6770  | EUR      |
    And the product variant with code "7_50_bad_nieuweschans_worldwide" has the following channel prices:
      | channelCode | enabled | price | currency |
      | dok_de      | true    | 10770 | EUR      |
    And there is a product named "Solgar" with code "solgar" of type "medication"
    And the product with code "solgar" has the following product variants:
      | code                               | name                     | prescriptionRequired | form   | dosage | packSize   | supplierIdentifier | costPrice | maximumQuantityPerOrder |
      | 4030_36605_ndsm_apotheek_worldwide | Solgar Magnesium Citrate | false                | tablet | 25mg   | 4 piece(s) | ndsm-apotheek      | 2265      | 2                       |
    And the product variant with code "4030_36605_ndsm_apotheek_worldwide" has the following channel prices:
      | channelCode | enabled | price | currency |
      | dok_de      | true    | 6770  | EUR      |
    And there is a product named "Sildenafil" with code "sildenafil" of type "medication"
    And the product with code "sildenafil" has the following product variants:
      | code                                           | name                     | prescriptionRequired | form   | dosage | packSize   | supplierIdentifier        | costPrice |
      | 2845_27724_apotheek_bad_nieuweschans_worldwide | Sildenafil 25 mg 4 Tabl. | true                 | tablet | 25mg   | 4 piece(s) | apotheek-bad-nieuweschans | 490       |
    And the product variant with code "2845_27724_apotheek_bad_nieuweschans_worldwide" has the following channel prices:
      | channelCode | enabled | price | currency |
      | dok_de      | true    | 3690  | EUR      |
    And there is a product association type named "Consultation for" with code "consult_products"
    And there is a consult product named "Erectile dysfunction" linked through association "consult_products" to product with code "viagra"
    And there is a consult product named "Erectile dysfunction" linked through association "consult_products" to product with code "sildenafil"
    And there is a consult product named "Skin Conditions" linked through association "consult_products" to product with code "flucloxacillin"
    And the product variant with code "consult_erectile_dysfunction" has the following channel prices:
      | channelCode | enabled | price | currency |
      | dok_de      | true    | 0     | EUR      |
    And the product variant with code "consult_skin_conditions" has the following channel prices:
      | channelCode | enabled | price | currency |
      | dok_de      | true    | 0     | EUR      |
    And there is a customer with email "<EMAIL>" in customer pool "dokteronline"

  Scenario: Successfully approve an order
    Given there is an order in channel "dok_de" placed by the customer with email "<EMAIL>" from customer pool "dokteronline" with the following items:
      | productVariantCode             | quantity | parentProductVariantCode     |
      | consult_erectile_dysfunction   | 1        |                              |
      | 7_5_bad_nieuweschans_worldwide | 1        | consult_erectile_dysfunction |
    And the order is paid and created in the consult system
    When the consult system approves the order with items:
      | code                           | name                 | usageAdvice | quantity | amount | currency | parentItem                   | prescriptionRequired | productType |
      | consult_erectile_dysfunction   | Erectile dysfunction |             | 1        | 0      | EUR      |                              | true                 | consult     |
      | 7_5_bad_nieuweschans_worldwide | Viagra 5             | Filled      | 1        | 6770   | EUR      | consult_erectile_dysfunction | true                 | medication  |
    Then the order has a property "prescriptionState" with value "approved"
    And the order has a property "shippingState" with value "ready"
    And the order should have a property "items[0].variant.code" with value "consult_erectile_dysfunction"
    And the order should have a property "items[1].variant.code" with value "7_5_bad_nieuweschans_worldwide"
    And the order should not have a property "items[2]"
    And an event is queued to create an order shipment in the supplier service

  Scenario: Successfully approve an OTC order without an usage advice
    Given there is an order in channel "dok_de" placed by the customer with email "<EMAIL>" from customer pool "dokteronline" with the following items:
      | productVariantCode                 | quantity | parentProductVariantCode     |
      | consult_erectile_dysfunction       | 1        |                              |
      | 7_6_ndsm_apotheek_worldwide        | 1        | consult_erectile_dysfunction |
      | 4030_36605_ndsm_apotheek_worldwide | 1        |                              |
    And the order is paid and created in the consult system
    And the order should have a property "shipments[0].state" with value "awaiting_prescription"
    And the order should have a property "shipments[0].supplier.identifier" with value "ndsm-apotheek"
    When the consult system approves the order with items:
      | code                               | name                     | usageAdvice     | quantity | amount | currency | parentItem                   | prescriptionRequired | productType |
      | consult_erectile_dysfunction       | Erectile dysfunction     |                 | 1        | 0      | EUR      |                              | true                 | consult     |
      | 7_6_ndsm_apotheek_worldwide        | Viagra 25mg 4 tabl.      | 2 tablets a day | 1        | 6770   | EUR      | consult_erectile_dysfunction | true                 | medication  |
      | 4030_36605_ndsm_apotheek_worldwide | Solgar Magnesium Citrate |                 | 1        | 6770   | EUR      |                              | false                | medication  |
    And the order should have a property "prescriptionState" with value "approved"
    And the order should have a property "shippingState" with value "ready"
    And the order should have a property "shipments[0].state" with value "ready"
    And the order should have a property "shipments[0].supplier.identifier" with value "ndsm-apotheek"
    And an event is queued to create an order shipment in the supplier service

  Scenario: Successfully approve an order with extra medication
    Given there is an order in channel "dok_de" placed by the customer with email "<EMAIL>" from customer pool "dokteronline" with the following items:
      | productVariantCode             | quantity | parentProductVariantCode     |
      | consult_erectile_dysfunction   | 1        |                              |
      | 7_5_bad_nieuweschans_worldwide | 1        | consult_erectile_dysfunction |
    And the order is paid and created in the consult system
    When the consult system approves the order with items:
      | code                            | name                 | usageAdvice | quantity | amount | currency | parentItem                   | prescriptionRequired | productType |
      | consult_erectile_dysfunction    | Erectile dysfunction |             | 1        | 0      | EUR      |                              | true                 | consult     |
      | 7_5_bad_nieuweschans_worldwide  | Viagra 5             | Filled      | 1        | 6770   | EUR      | consult_erectile_dysfunction | true                 | medication  |
      | 7_50_bad_nieuweschans_worldwide | Viagra 50            | Filled      | 1        | 6770   | EUR      | consult_erectile_dysfunction | true                 | medication  |
    Then the order has a property "prescriptionState" with value "approved"
    And the order has a property "shippingState" with value "awaiting_payment"
    And the order should have a property "items[0].variant.code" with value "consult_erectile_dysfunction"
    And the order should have a property "items[1].variant.code" with value "7_5_bad_nieuweschans_worldwide"
    And the order should have a property "items[2].variant.code" with value "7_50_bad_nieuweschans_worldwide"
    And the order should not have a property "items[3]"
    And I should have an audit log with message "has added an order item"

  Scenario: Successfully approve an order when waiting for customer response
    Given there is an order in channel "dok_de" placed by the customer with email "<EMAIL>" from customer pool "dokteronline" with the following items:
      | productVariantCode             | quantity | parentProductVariantCode     |
      | consult_erectile_dysfunction   | 1        |                              |
      | 7_5_bad_nieuweschans_worldwide | 1        | consult_erectile_dysfunction |
    And the order is paid and created in the consult system
    When the doctor send the message "Who r u?" on the order
    And the consult system approves the order with items:
      | code                           | name                 | usageAdvice     | quantity | amount | currency | parentItem                   | prescriptionRequired | productType |
      | consult_erectile_dysfunction   | Erectile dysfunction |                 | 1        | 0      | EUR      |                              | true                 | consult     |
      | 7_5_bad_nieuweschans_worldwide | Viagra 25mg 4 tabl.  | 2 tablets a day | 1        | 6770   | EUR      | consult_erectile_dysfunction | true                 | medication  |
    Then the order should have a property "prescriptionState" with value "approved"
    And the order should have a property "shippingState" with value "ready"
    And an event is queued to create an order shipment in the supplier service

  Scenario: Successfully approve an order with cheaper medication
    Given there is an order in channel "dok_de" placed by the customer with email "<EMAIL>" from customer pool "dokteronline" with the following items:
      | productVariantCode             | quantity | parentProductVariantCode     |
      | consult_erectile_dysfunction   | 1        |                              |
      | 7_5_bad_nieuweschans_worldwide | 2        | consult_erectile_dysfunction |
    And the order is authorized and created in the consult system
    And the order has a property "payments[0].state" with value "authorized"
    When the consult system approves the order with items:
      | code                           | name                 | usageAdvice | quantity | amount | currency | parentItem                   | prescriptionRequired | productType |
      | consult_erectile_dysfunction   | Erectile dysfunction |             | 1        | 0      | EUR      |                              | true                 | consult     |
      | 7_5_bad_nieuweschans_worldwide | Viagra 5             | Filled      | 1        | 6770   | EUR      | consult_erectile_dysfunction | true                 | medication  |
    Then the order has a property "prescriptionState" with value "approved"
    And the order has a property "shippingState" with value "ready"
    And the order should have a property "items[0].variant.code" with value "consult_erectile_dysfunction"
    And the order should have a property "items[1].variant.code" with value "7_5_bad_nieuweschans_worldwide"
    And the order should have a property "shipments[0].supplier.identifier" with value "apotheek-bad-nieuweschans"
    And the order should not have a property "items[2]"
    And I should have an audit log with message "has updated usage advice and quantity of an order item"
    Then the order shipments are created in the supplier service
    # The complete payment message with the 6 days delay is queued
    And an event is queued to complete payments
    And the shipment with index 0 has the property "state" with value "pending"
    And the order has a property "payments[0].state" with value "authorized"
    When the order shipment with index 0 is shipped with carrier "DHL" and code "123456789"
    Then a refund payment is created for the order with the amount of 6770 EUR
    And 2 events are queued to complete payments

  Scenario: Successfully approve an order with multiple different items
    Given the customer intends to pay with payment method "visa_dokteronline"
    And there is an order in channel "dok_de" placed by the customer with email "<EMAIL>" from customer pool "dokteronline" with the following items:
      | productVariantCode                             | quantity | parentProductVariantCode     |
      | consult_erectile_dysfunction                   | 1        |                              |
      | 7_5_bad_nieuweschans_worldwide                 | 1        | consult_erectile_dysfunction |
      | consult_skin_conditions                        | 1        |                              |
      | 2635_27674_apotheek_bad_nieuweschans_worldwide | 1        | consult_skin_conditions      |
    And the payment with reference "test_id" is captured in the gateway
    And the order is created in the consult system
    When the consult system approves the order with items:
      | code                                | name                           | usageAdvice | quantity | amount | currency | parentItem                   | prescriptionRequired | productType |
      | consult_erectile_dysfunction        | Erectile dysfunction           |             | 1        | 0      | EUR      |                              | true                 | consult     |
      | 7_50_bad_nieuweschans_worldwide     | Viagra 5                       | Filled      | 1        | 6770   | EUR      | consult_erectile_dysfunction | true                 | medication  |
      | consult_skin_conditions             | Erectile dysfunction           |             | 1        | 0      | EUR      |                              | true                 | consult     |
      | 2635_27674_prime_pharmacy_worldwide | Flucloxacillin 500 mg 40 caps. | Filled      | 2        | 6770   | EUR      | consult_skin_conditions      | true                 | medication  |
    Then the order has a property "prescriptionState" with value "approved"
    And the order should have a property "items[0].variant.code" with value "consult_erectile_dysfunction"
    And the order should have a property "items[1].variant.code" with value "consult_skin_conditions"
    And the order should have a property "items[2].variant.code" with value "2635_27674_prime_pharmacy_worldwide"
    And the order should have a property "items[3].variant.code" with value "7_50_bad_nieuweschans_worldwide"
    And the order should not have a property "items[4]"

  Scenario: Successfully approve an order after the doctor prescribes more expensive medication
    Given there is an order in channel "dok_de" placed by the customer with email "<EMAIL>" from customer pool "dokteronline" with the following items:
      | productVariantCode             | quantity | parentProductVariantCode     |
      | consult_erectile_dysfunction   | 1        |                              |
      | 7_5_bad_nieuweschans_worldwide | 1        | consult_erectile_dysfunction |
    And the order is paid and created in the consult system
    When the consult system approves the order with items:
      | code                           | name                 | usageAdvice     | quantity | amount | currency | parentItem                   | prescriptionRequired | productType |
      | consult_erectile_dysfunction   | Erectile dysfunction |                 | 1        | 0      | EUR      |                              | true                 | consult     |
      | 7_5_bad_nieuweschans_worldwide | Viagra 25mg 4 tabl.  | 2 tablets a day | 2        | 6770   | EUR      | consult_erectile_dysfunction | true                 | medication  |
    And the order should have a property "prescriptionState" with value "approved"
    And the order should have a property "shippingState" with value "awaiting_payment"
    And the order should have a property "paymentState" with value "partially_paid"
    And the order should have a property "payments[0].state" with value "completed"
    And the order should have a property "payments[1].state" with value "new"
    And the order should have a property "total" with value "13540"
    And no event is queued to create an order shipment in the supplier service

  Scenario: Regression for: DV-8712, successfully approve an order with updated consult items
    Given the customer intends to pay with payment method "visa_dokteronline"
    And there is an order in channel "dok_de" placed by the customer with email "<EMAIL>" from customer pool "dokteronline" with the following items:
      | productVariantCode              | quantity | parentProductVariantCode     |
      | consult_erectile_dysfunction    | 1        |                              |
      | 7_50_bad_nieuweschans_worldwide | 1        | consult_erectile_dysfunction |
    And the payment with reference "test_id" is captured in the gateway
    And the order is created in the consult system with uuid "3ecdcf93-bc9c-4555-a49a-23df7827dced"
    When the consult system approves the order with items:
      | code                           | name                 | usageAdvice | quantity | amount | currency | parentItem                   | prescriptionRequired | productType |
      | consult_erectile_dysfunction   | Erectile dysfunction |             | 1        | 0      | EUR      |                              | true                 | consult     |
      | 7_5_bad_nieuweschans_worldwide | Viagra 5             | Filled      | 1        | 6770   | EUR      | consult_erectile_dysfunction | true                 | medication  |
    Then the order has a property "prescriptionState" with value "approved"
    And the order has a property "shippingState" with value "ready"
    And the order should have a property "items[0].variant.code" with value "consult_erectile_dysfunction"
    And the order should have a property "items[1].variant.code" with value "7_5_bad_nieuweschans_worldwide"
    And the order should not have a property "items[2]"
    And an event is queued to create an order shipment in the supplier service

  Scenario: Regression for: DV-8712, successfully approve an order with removed and updated items
    Given the channel "dok_de" has the following settings:
      | multipleShipments |
      | false             |
    And the customer intends to pay with payment method "visa_dokteronline"
    And there is an order in channel "dok_de" placed by the customer with email "<EMAIL>" from customer pool "dokteronline" with the following items:
      | productVariantCode                             | quantity | parentProductVariantCode     |
      | consult_erectile_dysfunction                   | 1        |                              |
      | 7_50_bad_nieuweschans_worldwide                | 1        | consult_erectile_dysfunction |
      | 7_5_bad_nieuweschans_worldwide                 | 1        | consult_erectile_dysfunction |
      | consult_skin_conditions                        | 1        |                              |
      | 2635_27674_apotheek_bad_nieuweschans_worldwide | 1        | consult_skin_conditions      |
    And the payment with reference "test_id" is captured in the gateway
    And the order is created in the consult system
    When the consult system approves the order with items:
      | code                                           | name                           | usageAdvice | quantity | amount | currency | parentItem              | prescriptionRequired | productType |
      | consult_skin_conditions                        | Consultation for Skin problems |             | 1        | 0      | EUR      |                         | true                 | consult     |
      | 2635_27674_apotheek_bad_nieuweschans_worldwide | Flucloxacillin 500 mg 40 caps. | Filled      | 1        | 6770   | EUR      | consult_skin_conditions | true                 | medication  |
    Then the order has a property "prescriptionState" with value "approved"
    And an event is queued to create an order shipment in the supplier service
    And the order should have a property "items[0].variant.code" with value "consult_skin_conditions"
    And the order should have a property "items[1].variant.code" with value "2635_27674_prime_pharmacy_worldwide"
    And I should have an audit log with message "has removed an order item"
    And the order should not have a property "items[2]"

  Scenario: Regression DV-9885, successfully approve an order with an expired discount
    Given the channel "dok_de" has promotion "test-promotion" with coupon "test-coupon"
    And coupon with code "test-coupon" has not reached its usage limit and has usage limit of 10
    And the promotion "test-promotion" has not reached its time limit
    And the promotion "test-promotion" has a fixed discount of 1000
    And the customer intends to pay with payment method "visa_dokteronline"
    And there is an order in channel "dok_de" placed by the customer with email "<EMAIL>" from customer pool "dokteronline" with coupon "test-coupon" and the following items:
      | productVariantCode              | quantity | parentProductVariantCode     |
      | consult_erectile_dysfunction    | 1        |                              |
      | 7_50_bad_nieuweschans_worldwide | 1        | consult_erectile_dysfunction |
      | 7_5_bad_nieuweschans_worldwide  | 1        | consult_erectile_dysfunction |
    And I apply coupon "test-coupon" to the cart
    And the payment with reference "test_id" is captured in the gateway
    When the order is created in the consult system
    And the order should have a property "orderPromotionTotal" with value "-1000"
    And the order should have a property "total" with value "16540"
    And the order should have a property "payments" with count 1
    Given the checkout is completed at "-1 minute"
    And the promotion "test-promotion" has reached its time limit at "-5 seconds"
    When the consult system approves the order with items:
      | code                            | name                 | usageAdvice | quantity | amount | currency | parentItem                   | prescriptionRequired | productType |
      | consult_erectile_dysfunction    | Erectile dysfunction |             | 1        | 0      | EUR      |                              | true                 | consult     |
      | 7_5_bad_nieuweschans_worldwide  | Viagra 5             | Filled      | 1        | 6770   | EUR      | consult_erectile_dysfunction | true                 | medication  |
      | 7_50_bad_nieuweschans_worldwide | Viagra 50            | Filled      | 1        | 10770  | EUR      | consult_erectile_dysfunction | true                 | medication  |
    Then the order has a property "prescriptionState" with value "approved"
    And the order should have a property "total" with value "16540"
    And the order should have a property "payments" with count 1
    And an event is queued to create an order shipment in the supplier service

  Scenario: Regression DV-9885, successfully approve an order with a discount that has reached its usage limit
    Given the channel "dok_de" has promotion "test-promotion" with coupon "test-coupon"
    And coupon with code "test-coupon" has not reached its usage limit and has usage limit of 1
    And the promotion "test-promotion" has not reached its time limit
    And the promotion "test-promotion" has a fixed discount of 1000
    And the customer intends to pay with payment method "visa_dokteronline"
    And there is an order in channel "dok_de" placed by the customer with email "<EMAIL>" from customer pool "dokteronline" with coupon "test-coupon" and the following items:
      | productVariantCode              | quantity | parentProductVariantCode     |
      | consult_erectile_dysfunction    | 1        |                              |
      | 7_50_bad_nieuweschans_worldwide | 1        | consult_erectile_dysfunction |
      | 7_5_bad_nieuweschans_worldwide  | 1        | consult_erectile_dysfunction |
    And I apply coupon "test-coupon" to the cart
    And the payment with reference "test_id" is captured in the gateway
    When the order is created in the consult system
    Then the order should have a property "orderPromotionTotal" with value "-1000"
    And the order should have a property "total" with value "16540"
    And the order should have a property "payments" with count 1
    And coupon with code "test-coupon" should have a usage of 1
    When the consult system approves the order with items:
      | code                            | name                 | usageAdvice | quantity | amount | currency | parentItem                   | prescriptionRequired | productType |
      | consult_erectile_dysfunction    | Erectile dysfunction |             | 1        | 0      | EUR      |                              | true                 | consult     |
      | 7_5_bad_nieuweschans_worldwide  | Viagra 5             | Filled      | 1        | 6770   | EUR      | consult_erectile_dysfunction | true                 | medication  |
      | 7_50_bad_nieuweschans_worldwide | Viagra 50            | Filled      | 1        | 10770  | EUR      | consult_erectile_dysfunction | true                 | medication  |
    Then the order has a property "prescriptionState" with value "approved"
    And the order should have a property "total" with value "16540"
    And coupon with code "test-coupon" should have a usage of 1
    And the order should have a property "payments" with count 1
    And an event is queued to create an order shipment in the supplier service

  Scenario: Regression DV-10929, approve an already cancelled order
    Given there is an order in channel "dok_de" placed by the customer with email "<EMAIL>" from customer pool "dokteronline" with the following items:
      | productVariantCode             | quantity | parentProductVariantCode     |
      | consult_erectile_dysfunction   | 1        |                              |
      | 7_5_bad_nieuweschans_worldwide | 1        | consult_erectile_dysfunction |
    And the order is paid and created in the consult system
    And the order is cancelled by customer service with reason "Cancelled at customers request"
    When the consult system approves the order with items:
      | code                           | name                 | usageAdvice | quantity | amount | currency | parentItem                   | prescriptionRequired | productType |
      | consult_erectile_dysfunction   | Erectile dysfunction |             | 1        | 0      | EUR      |                              | true                 | consult     |
      | 7_5_bad_nieuweschans_worldwide | Viagra 5             | Filled      | 1        | 6770   | EUR      | consult_erectile_dysfunction | true                 | medication  |
    Then the order has a property "prescriptionState" with value "approved"
    And the order has a property "state" with value "cancelled"

