Feature: Product feed
  To be able to list all available products
  As channable
  I want to get a feed of all products

  Background:
    Given there is a channel "dok_nl"
    And there is a channel "blueclinic_nl"
    And there is a channel "dok_de"
    And there is a channel "dok_gb"
    And the supplier with identifier "apotheek-culemborg" is disabled

    # Cheapest for dok_nl = 600
    # Cheapest for dok_de = 900
    # Cheapest for dok_gb = 700
    # Cheapest for dok_blueclinic = 900
    Given there is a product named "Viagra" with code "7" of type "medication" and has the following translations:
      | locale | name      |
      | fr     | Viagra FR |
      | pl     | Viagra PL |
      | nl     | Viagra    |
      | de     | Viagra    |
      | dk     | Viagra DK |
    And the product with code "7" is enabled in the following channels:
      | code          |
      | dok_nl        |
      | blueclinic_nl |
      | dok_de        |
      | dok_gb        |
    And the product with code "7" has the following product variants:
      | code                         | name                | prescriptionRequired | form    | dosage | packSize | supplierIdentifier | costPrice | maximumQuantityPerOrder | enabled |
      | 7_5_ndsm_apotheek_worldwide  | Viagra 25mg 4 tabl. | true                 | tablet  | 20 mg  | 8 pieces | ndsm-apotheek      | 410       | 8                       | true    |
      | 7_5_prime_pharmacy_worldwide | Viagra 25mg 4 tabl. | true                 | capsule | 20 mg  | 8 pieces | prime-pharmacy     | 1810      | 8                       | true    |
    And the product variant with code "7_5_ndsm_apotheek_worldwide" has the following channel prices:
      | channelCode   | enabled | price | currency |
      | dok_nl        | true    | 600   | EUR      |
      | blueclinic_nl | true    | 900   | EUR      |
      | dok_de        | true    | 1900  | EUR      |
      | dok_gb        | true    | 800   | EUR      |
    And the product variant with code "7_5_prime_pharmacy_worldwide" has the following channel prices:
      | channelCode   | enabled | price | currency |
      | dok_nl        | true    | 900   | EUR      |
      | blueclinic_nl | true    | 900   | EUR      |
      | dok_de        | true    | 900   | EUR      |
      | dok_gb        | true    | 700   | EUR      |

    # Cheapest for dok_nl = 750
    # Cheapest for dok_gb = 800
    # dok_blueclinic does not exists
    # dok_de is out of stock, so it has no starting price
    Given there is a product named "Vardenafil" with code "3635" of type "medication" and has the following translations:
      | locale | name          | withImage |
      | nl     | Vardenafil NL | false     |
      | de     | Vardenafil DE | false     |
    And the product with code "3635" is enabled in the following channels:
      | code          |
      | dok_nl        |
      | blueclinic_nl |
      | dok_de        |
      | dok_gb        |
    And the product with code "3635" has the following product variants:
      | code                                    | name                     | prescriptionRequired | form    | dosage | packSize | supplierIdentifier        | costPrice | maximumQuantityPerOrder | enabled |
      | 3635_33403_ndsm_apotheek_worldwide      | Vardenafil 20 mg 8 tabl. | true                 | tablet  | 20 mg  | 8 pieces | ndsm-apotheek             | 5410      | 8                       | true    |
      | 3635_33403_prime_pharmacy_worldwide     | Vardenafil 20 mg 8 tabl. | true                 | capsule | 20 mg  | 8 pieces | prime-pharmacy            | 5410      | 8                       | true    |
      | 3635_33403_bad_nieuweschans_worldwide   | Vardenafil 20 mg 8 tabl. | true                 | tablet  | 20 mg  | 8 pieces | apotheek-bad-nieuweschans | 5410      | 8                       | true    |
      | 3635_33403_apotheek_culemborg_worldwide | Vardenafil 20 mg 8 tabl. | true                 | tablet  | 20 mg  | 8 pieces | apotheek-culemborg        | 5410      | 8                       | true    |
    And the product variant with code "3635_33403_ndsm_apotheek_worldwide" has the following channel prices:
      | channelCode | enabled | price | currency |
      | dok_nl      | true    | 1600  | EUR      |
      | dok_de      | false   | 950   | EUR      |
      | dok_gb      | false   | 850   | EUR      |
    And the product variant with code "3635_33403_prime_pharmacy_worldwide" has the following channel prices:
      | channelCode | enabled | price | currency |
      | dok_nl      | false   | 100   | EUR      |
      | dok_de      | false   | 1900  | EUR      |
      | dok_gb      | false   | 800   | EUR      |
    And the product variant with code "3635_33403_bad_nieuweschans_worldwide" has the following channel prices:
      | channelCode | enabled | price | currency |
      | dok_nl      | true    | 750   | EUR      |
      | dok_de      | false   | 950   | EUR      |
      | dok_gb      | false   | 800   | EUR      |
    And the product variant with code "3635_33403_apotheek_culemborg_worldwide" has the following channel prices:
      | channelCode | enabled | price | currency |
      | dok_nl      | true    | 750   | EUR      |
      | dok_de      | false   | 950   | EUR      |
      | dok_gb      | true    | 870   | EUR      |

    # Cheapest for dok_nl = 750
    # Cheapest for dok_de = 935
    # Cheapest for dok_gb = 870, but is not inStock due to the supplier being disabled
    # Cheapest for dok_blueclinic = 990
    Given there is a product named "Paracetemol" with code "paracetemol" of type "medication" and has the following translations:
      | locale | name        | withImage |
      | nl     | Paracetemol | true      |
      | de     | Paracetemol | false     |
    And the product with code "paracetemol" is enabled in the following channels:
      | code          |
      | dok_nl        |
      | blueclinic_nl |
      | dok_de        |
      | dok_gb        |
    And the product with code "paracetemol" has the following product variants:
      | code                  | name                                  | prescriptionRequired | form | dosage | packSize | supplierIdentifier | costPrice |
      | paracetemol_kleurrijk | Paracetamol/Codeine 500/10mg 30 tabl. | false                | na   | na     | 1 pieces | apotheek-kleurrijk | 1250      |
    And the product variant with code "paracetemol_kleurrijk" has the following channel prices:
      | channelCode   | enabled | price | currency |
      | dok_nl        | true    | 750   | EUR      |
      | dok_de        | true    | 935   | EUR      |
      | dok_gb        | true    | 800   | EUR      |
      | blueclinic_nl | true    | 990   | EUR      |

    And there is a product association type named "Consultation for" with code "consult_products"
    And there is a consult product named "Erectile dysfunction" linked through association "consult_products" to product with code "3635"
    And there is a consult product named "Erectile dysfunction" linked through association "consult_products" to product with code "7"
    And the product with code "consult_erectile_dysfunction" has the following translations:
      | locale | name                 | withImage |
      | nl     | Erectiele dysfunctie | false     |
      | de     | Erektile Dysfunktion | false     |
    And the product with code "consult_erectile_dysfunction" is enabled in the following channels:
      | code          |
      | dok_nl        |
      | blueclinic_nl |
      | dok_de        |
      | dok_gb        |
    And the product variant with code "consult_erectile_dysfunction" has the following channel prices:
      | channelCode   | enabled | price | currency |
      | dok_nl        | true    | 2100  | EUR      |
      | blueclinic_nl | true    | 2900  | EUR      |
      | dok_de        | true    | 0     | EUR      |
      | dok_gb        | true    | 0     | EUR      |

  Scenario: List all the products for de-DE within Dokteronline
    Given prepr endpoint returns response from files "product_pages_de-DE-0-100.json,product_pages_de-DE-100-200.json" with empty page
    When I am within the business unit "dokteronline"
    And I fetch the product feed with "de-DE"
    Then I should get a response with status code 200
    And I see that the feed has 4 products
    And I see that the feed has the product: "7" with the following data:
    """yaml
    code: "7"
    name: "Viagra"
    taxons: ["Erektionsstörung"]
    inStock: true
    startingPrice:
      amount: 900
      currency: EUR
      displayAmount: "9.00"
    shipping:
      price:
        amount: 0
        currency: EUR
        displayAmount: "0.00"
      countryCode: DE
    attributes:
      form: ["Kapsel", "Tablette"]
      usage: "Every day"
      possibleWeightloss: "None"
      effectivenessDelay: "10 minuten"
      effectivenessPeriod: "30 minuten"
      modeOfAction: "Oral"
      effectivenessPeak: "15 minuten"
      effect: "Relaxes muscles around the airways"
    condition: new
    productPageUrl: "https://dokteronline.commerce.ehvg.dev/de-de/produkt/viagra/"
    metaDescription: "Mehr Info zu Viagra auf Dokteronline - Zuverlässige Verschreibung von registriertem Arzt - Schnell und diskret nach Hause geliefert"
    productImageUrl: "https://images.images.com"
    """
    And I see that the feed has the product: "consult_erectile_dysfunction" with the following data:
    """yaml
    code: "consult_erectile_dysfunction"
    name: "Erektile Dysfunktion"
    taxons: []
    inStock: true
    startingPrice:
      amount: 0
      currency: EUR
      displayAmount: "0.00"
    shipping:
      price:
        amount: 0
        currency: EUR
        displayAmount: "0.00"
      countryCode: DE
    attributes:
      form: []
    condition: new
    """
    And I see that the feed has the product: "paracetemol" with the following data:
    """yaml
    code: "paracetemol"
    name: "Paracetemol"
    taxons: []
    inStock: true
    startingPrice:
      amount: 935
      currency: EUR
      displayAmount: "9.35"
    shipping:
      price:
        amount: 495
        currency: EUR
        displayAmount: "4.95"
      countryCode: DE
    attributes:
      form: []
    condition: new
    """
    And I see that the feed has the product: "3635" with the following data:
    """yaml
    code: "3635"
    name: "Vardenafil DE"
    taxons: ["Erektionsstörung"]
    inStock: false
    startingPrice:
      amount: 0
      currency: EUR
      displayAmount: "0.00"
    shipping:
      price:
        amount: 0
        currency: EUR
        displayAmount: "0.00"
      countryCode: DE
    attributes:
      form: ["Tablette"]
    condition: new
    productPageUrl: "https://dokteronline.commerce.ehvg.dev/de-de/produkt/vardenafil/"
    metaDescription: "Mehr Info zu Vardenafil auf Dokteronline - Zuverlässige Verschreibung von registriertem Arzt - Schnell und diskret nach Hause geliefert"
    """

  Scenario: List all the products for nl-NL within Blueclinic
    Given prepr endpoint has no results
    When I am within the business unit "blueclinic"
    And I fetch the product feed with "nl-NL"
    Then I should get a response with status code 200
    And I see that the feed has 3 products
    And I see that the feed has the product: "consult_erectile_dysfunction" with the following data:
    """yaml
    code: "consult_erectile_dysfunction"
    name: "Erectiele dysfunctie"
    taxons: []
    inStock: true
    startingPrice:
      amount: 2900
      currency: EUR
      displayAmount: "29.00"
    shipping:
      price:
        amount: 0
        currency: EUR
        displayAmount: "0.00"
      countryCode: NL
    attributes:
      form: []
    condition: new
    """
    And I see that the feed has the product: "paracetemol" with the following data:
    """yaml
    code: "paracetemol"
    name: "Paracetemol"
    taxons: []
    inStock: true
    productImageUrl: "https://images.images.com"
    startingPrice:
      amount: 990
      currency: EUR
      displayAmount: "9.90"
    shipping:
      price:
        amount: 295
        currency: EUR
        displayAmount: "2.95"
      countryCode: NL
    attributes:
      form: []
    condition: new
    """
    And I see that the feed has the product: "7" with the following data:
    """yaml
    code: "7"
    name: "Viagra"
    taxons: []
    inStock: true
    productImageUrl: "https://images.images.com"
    startingPrice:
      amount: 900
      currency: EUR
      displayAmount: "9.00"
    shipping:
      price:
        amount: 0
        currency: EUR
        displayAmount: "0.00"
      countryCode: NL
    attributes:
      form: ["Capsule","Tablet"]
    condition: new
    """

  Scenario: List all the products for nl-NL within Dokteronline
    Given prepr endpoint has no results
    When I am within the business unit "dokteronline"
    And I fetch the product feed with "nl-NL"
    Then I should get a response with status code 200
    And I see that the feed has 4 products
    And I see that the feed has the product: "consult_erectile_dysfunction" with the following data:
    """yaml
    code: "consult_erectile_dysfunction"
    name: "Erectiele dysfunctie"
    taxons: []
    inStock: true
    startingPrice:
      amount: 2100
      currency: EUR
      displayAmount: "21.00"
    shipping:
      price:
        amount: 0
        currency: EUR
        displayAmount: "0.00"
      countryCode: NL
    attributes:
      form: []
    condition: new
    """
    And I see that the feed has the product: "paracetemol" with the following data:
    """yaml
    code: "paracetemol"
    name: "Paracetemol"
    taxons: []
    inStock: true
    productImageUrl: "https://images.images.com"
    startingPrice:
      amount: 750
      currency: EUR
      displayAmount: "7.50"
    shipping:
      price:
        amount: 495
        currency: EUR
        displayAmount: "4.95"
      countryCode: NL
    attributes:
      form: []
    condition: new
    """
    And I see that the feed has the product: "7" with the following data:
    """yaml
    code: "7"
    name: "Viagra"
    taxons: []
    inStock: true
    productImageUrl: "https://images.images.com"
    startingPrice:
      amount: 600
      currency: EUR
      displayAmount: "6.00"
    shipping:
      price:
        amount: 0
        currency: EUR
        displayAmount: "0.00"
      countryCode: NL
    attributes:
      form: ["Capsule","Tablet"]
    condition: new
    """
    And I see that the feed has the product: "3635" with the following data:
    """yaml
    code: "3635"
    name: "Vardenafil NL"
    taxons: []
    inStock: true
    startingPrice:
      amount: 750
      currency: EUR
      displayAmount: "7.50"
    shipping:
      price:
        amount: 0
        currency: EUR
        displayAmount: "0.00"
      countryCode: NL
    attributes:
      form: ["Tablet"]
    condition: new
    """

  Scenario: List all the products for en-GB within Doctoronline
    Given prepr endpoint has no results
    When I am within the business unit "doctoronline"
    And I fetch the product feed with "en-GB"
    Then I should get a response with status code 200
    And I see that the feed has 4 products
    And I see that the feed has the product: "consult_erectile_dysfunction" with the following data:
    """yaml
    code: "consult_erectile_dysfunction"
    name: "Erectile dysfunction"
    taxons: []
    inStock: true
    startingPrice:
      amount: 0
      currency: GBP
      displayAmount: "0.00"
    shipping:
      price:
        amount: 0
        currency: GBP
        displayAmount: "0.00"
      countryCode: GB
    attributes:
      form: []
    condition: new
    """
    And I see that the feed has the product: "paracetemol" with the following data:
    """yaml
    code: "paracetemol"
    name: "Paracetemol"
    taxons: []
    inStock: true
    startingPrice:
      amount: 800
      currency: GBP
      displayAmount: "8.00"
    shipping:
      price:
        amount: 421
        currency: GBP
        displayAmount: "4.21"
      countryCode: GB
    attributes:
      form: ["na"]
    condition: new
    """
    And I see that the feed has the product: "7" with the following data:
    """yaml
    code: "7"
    name: "Viagra"
    taxons: []
    inStock: true
    startingPrice:
      amount: 700
      currency: GBP
      displayAmount: "7.00"
    shipping:
      price:
        amount: 0
        currency: GBP
        displayAmount: "0.00"
      countryCode: GB
    attributes:
      form: ["Capsule","Tablet"]
    condition: new
    """
    And I see that the feed has the product: "3635" with the following data:
    """yaml
    code: "3635"
    name: "Vardenafil"
    taxons: []
    inStock: false
    startingPrice:
      amount: 870
      currency: GBP
      displayAmount: "8.70"
    shipping:
      price:
        amount: 0
        currency: GBP
        displayAmount: "0.00"
      countryCode: GB
    attributes:
      form: ["Tablet"]
    condition: new
    """
