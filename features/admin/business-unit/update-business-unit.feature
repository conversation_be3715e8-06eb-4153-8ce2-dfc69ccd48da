Feature: Update business units
  In order to manage the business units
  As an admin
  I need to be able to update a business unit

  Background:
    Given there is a Sylius admin user with username "admin"
    And the Sylius admin user with username "admin" has the following permissions:
      | permission                     |
      | app_admin_business_unit_update |
      | app_admin_business_unit_index  |
    And I am authenticated as a Sylius admin with username "admin"

  Scenario: Updating business units in the list
    When I update business unit "dokteronline" with the following information:
      """yaml
      business_unit:
        companyName: Dokteronline
        representative: test-representative
        taxId: test-tax-id
        address:
          countryCode: NL
          street: test-street
          city: test-city
          postcode: test-postcode
      """
    Then I should get a response with status code "302 Found"
    When I view the business unit details of business unit with code "dokteronline"
    Then I should see the following business unit information:
      | test-representative |
      | test-tax-id         |
      | test-street         |
      | test-city           |
      | test-postcode       |

  Scenario: Updating a business unit without the proper permissions results in forbidden error
    Given there is a Sylius admin user with username "admin"
    And the Sylius admin user with username "admin" has the following permissions:
      | permission                            |
      | app_admin_business_unit_no_permission |
    And I am authenticated as a Sylius admin with username "admin"
    When I update business unit "dokteronline" with the following information:
      """yaml
      business_unit:
        companyName: Dokteronline
        representative: test-representative
        taxId: test-tax-id
        address:
          countryCode: NL
          street: test-street
          city: test-city
          postcode: test-postcode
      """
    Then I should get a response with status code "403 Forbidden"
