sylius_admin:
    resource: "@SyliusAdminBundle/Resources/config/routing.yml"
    prefix: /admin

app_supplier:
    resource: |
        alias: app.supplier
        templates: "@SyliusAdmin/Crud"
        grid: app_admin_supplier
        except: ['show']
        section: admin
        permission: true
        vars:
            all:
                templates:
                    form: "Admin/Supplier/_form.html.twig"
    type: sylius.resource
    prefix: /admin

app_fraud_postcode:
    resource: |
        alias: app.fraud_postcode
        templates: "@SyliusAdmin/Crud"
        grid: app_admin_fraud_postcode
        except: ['show','bulkDelete']
        section: admin
        permission: true
        vars:
            all:
                templates:
                    form: "Admin/Fraud/_postcode_form.html.twig"
    type: sylius.resource
    prefix: /admin
    defaults:
        _sylius_rbac:
            parent: fraud_check_postcode_config
            enabled: true


app_fraud_product:
    resource: |
        alias: app.fraud_product
        templates: "@SyliusAdmin/Crud"
        grid: app_admin_fraud_product
        except: ['show','bulkDelete']
        section: admin
        permission: true
        vars:
            all:
                templates:
                    form: "Admin/Fraud/_product_form.html.twig"
    type: sylius.resource
    prefix: /admin
    defaults:
        _sylius_rbac:
            parent: fraud_check_product_config
            enabled: true

app_fraud_payment_method:
    resource: |
        alias: app.fraud_payment_method
        templates: "@SyliusAdmin/Crud"
        grid: app_admin_fraud_payment_method
        except: ['show','bulkDelete']
        section: admin
        permission: true
        vars:
            all:
                templates:
                    form: "Admin/Fraud/_payment_method_form.html.twig"
    type: sylius.resource
    prefix: /admin
    defaults:
        _sylius_rbac:
            parent: fraud_check_payment_method_config
            enabled: true

app_refund_payment:
    resource: |
        alias: app.refund_payment
        section: admin
        permission: true
        templates: "@SyliusAdmin\\Crud"
        only: ['index', 'update']
        vars:
            all:
                templates:
                    form: "Admin/RefundPayment/_form.html.twig"
        grid: app_admin_refund_payments
    type: sylius.resource
    prefix: /admin

app_financial_export_page:
    path: /admin/financial-export
    methods: [ GET, POST ]
    defaults:
        _controller: App\Admin\Controller\FinancialExport\FinancialExportController
        _sylius:
            permission: true

app_order_fraud_check_page:
    resource: |
        alias: app.fraud_check_order
        section: admin
        permission: true
        templates: "@SyliusAdmin/Crud"
        only: ['index']
        grid: app_order_fraud_check_page
    type: sylius.resource
    prefix: /admin
    defaults:
        _sylius_rbac:
            parent: fraudcheck
            enabled: true

app_admin_refund_payment_state_update:
    path: /admin/refund-payments/{id}/update
    methods: [ GET ]
    defaults:
        _controller: App\Admin\Controller\RefundPayment\UpdateRefundPaymentStateController

app_admin_order_shipment_mark_for_reshipment:
    path: /admin/orders/{orderId}/shipments/{shipmentId}/mark-for-reshipment
    methods: [ GET ]
    defaults:
        _controller: App\Admin\Controller\Order\MarkForReshipmentController

app_admin_order_pause:
    path: /admin/orders/{id}/pause
    methods: [ GET ]
    defaults:
        _controller: App\Admin\Controller\Order\PauseOrderController
        _sylius:
            permission: true
        _sylius_rbac:
            parent: orders
            label: app_admin_order_pause
            enabled: true

app_admin_order_continue:
    path: /admin/orders/{id}/continue
    methods: [ GET ]
    defaults:
        _controller: App\Admin\Controller\Order\ContinueOrderController
        _sylius:
            permission: true
        _sylius_rbac:
            parent: orders
            label: app_admin_order_continue
            enabled: true

sylius_admin_order_cancel:
    path: /admin/orders/{id}/cancel
    methods: [ PUT, POST ]
    defaults:
        _controller: App\Admin\Controller\Order\CancelOrderController
        _sylius:
            permission: true
        _sylius_rbac:
            parent: orders
            label: sylius_admin_order_cancel
            enabled: true

sylius_admin_order_update_supplier:
    path: /admin/orders/{id}/supplier/switch
    methods: [ PUT, POST ]
    defaults:
        _controller: App\Admin\Controller\Order\SwitchSupplierOnOrderController
        _sylius:
            permission: true
        _sylius_rbac:
            parent: orders
            label: app_admin_order_switch_supplier
            enabled: true

sylius_admin_order_update_supplier_order_items:
    path: /admin/orders/{id}/supplier/switch/order-items
    methods: [ POST ]
    defaults:
        _controller: App\Admin\Controller\Order\SwitchSupplierOnOrderItemsController
        _sylius:
            permission: true
        _sylius_rbac:
            parent: orders
            label: app_admin_order_switch_supplier
            enabled: true

sylius_admin_refund_order_lines:
    path: /admin/orders/{id}/refund/order-items
    methods: [ POST ]
    defaults:
        _controller: App\Admin\Controller\Order\RefundOrderLinesController

sylius_admin_order_note_create:
    path: /admin/orders/{id}/note
    methods: [ POST ]
    defaults:
        _controller: App\Admin\Controller\Order\CreateOrderNoteController
        _sylius:
            permission: true

sylius_admin_order_switch_doctor:
    path: /admin/orders/{id}/doctor/switch
    methods: [ POST ]
    defaults:
        _controller: App\Admin\Controller\Order\SwitchDoctorOnOrderController
        _sylius:
            permission: true
        _sylius_rbac:
            parent: orders
            label: app_admin_order_switch_doctor
            enabled: true

sylius_admin_order_items_update:
    path: /admin/orders/{id}/items
    methods: [ POST ]
    defaults:
        _controller: App\Admin\Controller\Order\UpdateOrderItemsController
        _sylius:
            permission: true
        _sylius_rbac:
            parent: orders
            label: permissions.app_admin_order_items_update
            enabled: true

sylius_admin_order_items_remove:
    path: /admin/orders/{orderId}/items/{orderItemId}
    methods: [ DELETE ]
    defaults:
        _controller: App\Admin\Controller\Order\RemoveOrderItemsController
        _sylius:
            permission: true
        _sylius_rbac:
            parent: orders
            label: permissions.app_admin_order_items_remove
            enabled: true

# Same as vendor route, but with a different transition (force_pay instead of complete).
sylius_admin_order_payment_complete:
    path: /admin/orders/{orderId}/payments/{id}/complete
    methods: [ PUT ]
    defaults:
        _controller: sylius.controller.payment::applyStateMachineTransitionAction
        _sylius:
            event: complete
            permission: true
            repository:
                method: findOneByOrderId
                arguments:
                    id: $id
                    orderId: $orderId
            state_machine:
                graph: sylius_payment
                transition: force_pay
            redirect: referer

app_order_fraud_check_approve:
    path: /admin/orders/{id}/fraud-check/approve
    methods: [ PUT ]
    defaults:
        _controller: App\Admin\Controller\FraudCheck\ApproveController
        _sylius:
            permission: true

app_order_fraud_check_hold:
    path: /admin/orders/{id}/fraud-check/hold
    methods: [ PUT ]
    defaults:
        _controller: App\Admin\Controller\FraudCheck\HoldController
        _sylius:
            permission: true

app_order_fraud_check_cancel:
    path: /admin/orders/{id}/fraud-check/cancel
    methods: [ PUT ]
    defaults:
        _controller: App\Admin\Controller\FraudCheck\CancelController
        _sylius:
            permission: true

app_order_fraud_check_postcode_update_comment:
    path: /admin/orders/{id}/fraud-check/postcode/comment/update
    methods: [ PUT ]
    defaults:
        _controller: App\Admin\Controller\FraudCheck\UpdatePostcodeCommentController
        _sylius:
            permission: true
        _sylius_rbac:
            parent: fraudcheck
            label: app_admin_fraud_postcode_update
            enabled: true

app_customer_fraud_check_trust_level:
    path: /admin/customers/{id}/trust-level/{trustLevel}
    methods: [ POST ]
    defaults:
        _controller: App\Admin\Controller\FraudCheck\TrustCustomerController
        _sylius:
            permissions: true
        _sylius_rbac:
            parent: fraudcheck
            label: app_customer_fraud_check_trust_level
            enabled: true

sylius_admin_shipment_ship:
    path: /shipments/{id}/ship
    methods: [ ]

sylius_admin_order_shipment_ship:
    path: /orders/{id}/ship
    methods: [ ]

sylius_admin_order_resend_confirmation_email:
    path: /orders/{id}/resend-confirmation-email
    methods: [ ]

sylius_admin_shipment_resend_confirmation_email:
    path: /shipments/{id}/resend-confirmation-email
    methods: [ ]

sylius_admin_ajax_order_items_get_suppliers:
    path: /admin/ajax/orders/{orderId}/suppliers
    methods: [ GET ]
    defaults:
        _controller: App\Admin\Controller\Order\GetAvailableSuppliersController

app_admin_business_unit:
    resource: |
        alias: app.business_unit
        except: ['show']
        section: admin
        templates: '@SyliusAdmin\\Crud'
        grid: app_admin_business_unit
        permission: true
        vars:
            all:
                subheader: sylius.ui.manage_business_units
                templates:
                    form: 'Admin/BusinessUnit/_form.html.twig'
            index:
                icon: building
    type: sylius.resource
    prefix: /admin

app_admin_business_unit_show:
    path: /admin/business-units/{id}
    methods: [ GET ]
    defaults:
        _controller: app.controller.business_unit::showAction
        _sylius:
            section: admin
            permission: true
            template: 'Admin/BusinessUnit/show.html.twig'

app_admin_customer_pool:
    resource: |
        alias: app.customer_pool
        only: ['index']
        section: admin
        templates: '@SyliusAdmin\\Crud'
        grid: app_admin_customer_pool
        permission: true
        vars:
            index:
                 icon: user secret
    type: sylius.resource
    prefix: /admin

app_admin_csc:
    path: /admin/csc
    defaults:
        _controller: App\Admin\Controller\CscController::__invoke
        _sylius:
            section: admin
            permission: true
            alias: app.admin_csc
