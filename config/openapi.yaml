openapi: 3.1.0
info:
  title: Commerce API
  version: 0.0.1
  description: |
    This document describes the commerce API for platforms created by the [eHealth Ventures Group](https://ehealthventuresgroup.com).

    Our APIs are defined in OpenAPI version 3.x.
  contact:
    name: API support
    email: <EMAIL>

servers:
  - url: 'https://dokteronline.commerce.ehvg.dev/api'
    description: Development environment.
  - url: 'https://{branch}.checkout.sbtest.nl/api'
    description: Test environment.
    variables:
      branch:
        default: dv-0000
  - url: 'https://dokteronline.commerce.sbaccept.nl/api'
    description: Acceptance environment.
  - url: 'https://commerce.dokteronline.com/api'
    description: Production environment.

paths:
  /shop/taxons:
    get:
      summary: List taxons for channel
      description: Retrieve the list of taxons for a specific channel.
      x-openapi-bundle:
        controller: 'App\Api\Controller\ListController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'TaxonList'
      parameters:
        - $ref: '#/components/parameters/ChannelContext'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageItemCount'
      responses:
        '200':
          description: The list of taxons is successfully retrieved.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaxonList'
        default:
          $ref: '#/components/responses/UnexpectedError'
      tags:
        - Catalog

  /v2/shop/taxons/{code}:
    get:
      summary: Get taxon
      description: Retrieve a taxon by its code identifier.
      parameters:
        - $ref: '#/components/parameters/TaxonCode'
      responses:
        '200':
          description: The taxon is successfully retrieved.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Taxon'
        default:
          $ref: '#/components/responses/UnexpectedError'
      tags:
        - Catalog

  /shop/products:
    get:
      summary: List products
      description: Retrieve the list of products.
      x-openapi-bundle:
        controller: 'App\Api\Controller\ListController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'ProductInList'
      parameters:
        - $ref: '#/components/parameters/ChannelContextFilter'
        - $ref: '#/components/parameters/LocaleContext'
        - $ref: '#/components/parameters/FilterByRelatedConsultProductCode'
        - $ref: '#/components/parameters/FilterByProductAttributeType'
        - $ref: '#/components/parameters/FilterByPrescriptionRequired'
        - $ref: '#/components/parameters/FilterByProductName'
        - $ref: '#/components/parameters/FilterByInStock'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageItemCount'
      responses:
        '200':
          description: The list of products is successfully retrieved.
          headers:
            Link:
              $ref: '#/components/headers/Link'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProductInList'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - Catalog

  /shop/products/{code}:
    get:
      summary: Get product with variants
      description: |
        Retrieve a product by its code identifier for a specific channel.
        Will return the product information, options and variants of the specified product.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Product\GetProductController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Product'
      parameters:
        - $ref: '#/components/parameters/ChannelContext'
        - $ref: '#/components/parameters/CartContext'
        - $ref: '#/components/parameters/LocaleContext'
      responses:
        '200':
          description: The product is successfully retrieved.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Product'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - Catalog

    parameters:
      - $ref: '#/components/parameters/ProductCode'

  /shop/product-variants:
    get:
      summary: List product variants
      description: |
        Retrieve all product variants.
        Results can be filtered based on product variant name and supplier.
      x-openapi-bundle:
        controller: 'App\Api\Controller\ListController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'ProductVariant'
      parameters:
        - $ref: '#/components/parameters/ChannelContext'
        - $ref: '#/components/parameters/LocaleContext'
        - $ref: '#/components/parameters/FilterByProductVariantNameDeprecated' # TODO Remove in DV-3871? Must change in DOK beheer.
        - $ref: '#/components/parameters/FilterByProductVariantName'           # TODO Implement in DV-3871. Should change in DOK beheer.
        - $ref: '#/components/parameters/FilterByProductVariantCodes'
        - $ref: '#/components/parameters/SupplierIdentifier'                   # TODO Remove in DV-3871? Must change in DOK beheer.
        - $ref: '#/components/parameters/FilterBySupplierIdentifier'           # TODO Implement in DV-3871. Should change in DOK beheer.
        - $ref: '#/components/parameters/ProductVariantEmbed'
        - $ref: '#/components/parameters/ProductVariantSortBy'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageItemCount'
      responses:
        '200':
          description: The product variants are successfully retrieved.
          headers:
            Link:
              $ref: '#/components/headers/Link'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProductVariant'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - Catalog

  /shop/prepr-integration/taxons:
    get:
      summary: List taxons
      description: Retrieve the list of taxons.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Prepr\ListController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'PreprTaxonList'
      parameters:
        - $ref: '#/components/parameters/LocaleContext'
        - $ref: '#/components/parameters/FilterByChangedSince'
        - $ref: '#/components/parameters/PreprFilterTaxonsByName'
        - $ref: '#/components/parameters/SkipItems'
        - $ref: '#/components/parameters/LimitItems'
      responses:
        '200':
          description: The list of taxons is successfully retrieved.
          content:
            application/prepr+json:
              schema:
                $ref: '#/components/schemas/PreprTaxonList'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - 'Catalog: Prepr CMS'

  /shop/prepr-integration/products:
    get:
      summary: List products
      description: Retrieve the list of products.
      operationId: preprListProducts
      x-openapi-bundle:
        controller: 'App\Api\Controller\Prepr\ListController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'PreprProductList'
      parameters:
        - $ref: '#/components/parameters/LocaleContext'
        - $ref: '#/components/parameters/FilterByProductAttributeType'
        - $ref: '#/components/parameters/FilterByChangedSince'
        - $ref: '#/components/parameters/PreprFilterProductsByName'
        - $ref: '#/components/parameters/SkipItems'
        - $ref: '#/components/parameters/LimitItems'
      responses:
        '200':
          description: The list of products is successfully retrieved.
          content:
            application/prepr+json:
              schema:
                $ref: '#/components/schemas/PreprProductList'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - 'Catalog: Prepr CMS'

  /shop/feeds/products/{localeCode}:
    get:
      summary: List products
      description: Retrieve the list of products for a locale.
      operationId: feedListProducts
      x-openapi-bundle:
        controller: 'App\Api\Controller\Feed\ProductFeedController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'FeedProductList'
      parameters:
        - $ref: '#/components/parameters/LocaleContextInPath'
      responses:
        '200':
          description: The list of products is successfully retrieved.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProductAsFeed'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - 'Catalog: Feeds'

  /shop/product-enrichment:
    get:
      summary: List products for website
      description: |
        Retrieve the products for the specified product codes.
        The data can be used to enrich existing product data already available from the CMS.
      x-openapi-bundle:
        controller: 'App\Api\Controller\ListController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'ProductInEnrichmentList'
      parameters:
        - $ref: '#/components/parameters/ProductCodes'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageItemCount'
      responses:
        '200':
          description: The product data is successfully retrieved.
          headers:
            Link:
              $ref: '#/components/headers/Link'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProductInEnrichmentList'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - 'Catalog (Dokteronline)'

  /shop/channels/{code}:
    get:
      summary: Get channel
      description: Retrieve the data of a channel by its unique code.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Channel\GetChannelController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Channel'
      responses:
        '200':
          description: The channel is successfully retrieved.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Channel'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - 'Channel'

    parameters:
      - $ref: '#/components/parameters/ChannelCode'

  /shop/countries:
    get:
      summary: List countries
      description: Retrieve the list with available countries. Optionally, a channel can be provided to filter the list.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Address\GetCountriesController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Country'
      parameters:
        - $ref: '#/components/parameters/ChannelContextFilter'
        - $ref: '#/components/parameters/LocaleContext'
      responses:
        '200':
          description: The countries are successfully retrieved.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Country'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - 'Channel'

  /shop/payment-methods:
    get:
      summary: List payment methods
      description: |
        Returns a list of all payment methods enabled for the specified channel.

        ### Payment methods available for a specific order
        To retrieve payment methods applicable to a specific order:

        - Provide the `tokenValue` of the order as a query parameter.
        - Include the customer's access token in the Authorization header.

        If the `tokenValue` does not belong to the authenticated customer, or no authentication is provided,
        the response will include **all** enabled payment methods for the channel.
      operationId: listPaymentMethods
      x-openapi-bundle:
        controller: 'App\Api\Controller\PaymentMethod\GetPaymentMethodsController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'PaymentMethod.List'
      parameters:
        - $ref: '#/components/parameters/ChannelContext'
        - $ref: '#/components/parameters/LocaleContext'
        - $ref: '#/components/parameters/TokenValueFilter'
      responses:
        '200':
          description: The payment methods are successfully retrieved.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PaymentMethod.List'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Channel
        - Checkout
        - 'Account: Orders'

  /shop/carts:
    post:
      summary: Create new cart
      description: Create a new cart session.
      operationId: createCart
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Cart\PickupCart'
        additionalRouteAttributes:
          responseSuccessStatusCode: 201
          responseSerializationSchemaObject: 'Cart'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCart'
      responses:
        '201':
          description: The cart session is successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Cart

    parameters:
      - $ref: '#/components/parameters/ChannelContext'

  /shop/carts/{tokenValue}:
    get:
      summary: Get cart
      description: Retrieve a cart session by its unique token value.
      operationId: getCart
      x-openapi-bundle:
        controller: 'App\Api\Controller\Cart\GetCartController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Cart'
      responses:
        '200':
          description: The cart session is successfully retrieved.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'
        '404':
          $ref: '#/components/responses/CartNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Cart

    parameters:
      - $ref: '#/components/parameters/ChannelContext'
      - $ref: '#/components/parameters/LocaleContext'
      - $ref: '#/components/parameters/CartTokenValue'

  /shop/carts/{tokenValue}/items:
    post:
      summary: Add items to cart
      description: Add one or more items to the cart session identified by its unique token value.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Cart\AddItems'
        additionalRouteAttributes:
          is_granted: 'ORDER_ALLOWED'
          is_granted_exception: 'App\Api\Exception\CartNotFoundException'
          responseSerializationSchemaObject: 'Cart'
          validationGroups: 'Default,Cart,CheckStock'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              minItems: 1
              items:
                $ref: '#/components/schemas/AddItemToCart'
            examples:
              singleProductVariant:
                summary: An example of adding a single product variant to the cart session.
                value:
                  - productVariantCode: propecia_tablet_1mg_28_pieces
                    quantity: 1
              multipleProductVariants:
                summary: An example of adding multiple product variants to the cart session.
                value:
                  - productVariantCode: propecia_tablet_1mg_28_pieces
                    quantity: 1
                  - productVariantCode: finasteride_tablet_1mg_28_pieces
                    quantity: 1
              multipleProductVariantsWithParentChildRelation:
                summary: An example of adding multiple product variants with parent-child relation to the cart session.
                value:
                  - productVariantCode: consult_hairloss
                    quantity: 1
                  - productVariantCode: propecia_tablet_1mg_28_pieces
                    parentProductVariantCode: consult_hairloss
                    quantity: 1
      responses:
        '200':
          description: One or more items are successfully added to the cart session.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'
              examples:
                singleProductVariant:
                  summary: An example response of adding a single product variant to the cart session.
                  value:
                    tokenValue: 'pmyIXe7ANZ'
                    state: 'cart'
                    channel:
                      code: 'dok_gb'
                      addPrescriptionMedicationDirectlyToCart: true
                      baseCurrency:
                        code: 'GBP'
                    checkoutState: 'cart'
                    currencyCode: 'GBP'
                    localeCode: 'en'
                    items:
                      - id: 1
                        variant:
                          code: 'propecia_tablet_1mg_28_pieces'
                          name: 'Propecia 1 mg 28 tabl.'
                          productAttributes:
                            type: 'medication'
                          maximumQuantityPerOrder: 1
                          supplier:
                            name: 'Apotheek Bad Nieuweschans'
                          prescriptionRequired: true
                          price:
                            amount: 7690
                            currency: 'GBP'
                        productName: 'Propecia'
                        variantName: 'Propecia 1 mg 28 tabl.'
                        warnings: []
                        quantity: 1
                        unitPrice: 7690
                        subTotal: 7690
                        total: 7690
                    payments: []
                    shipments: []
                    itemsTotal: 7690
                    orderPromotionTotal: 0
                    shippingTotal: 0
                    taxTotal: 692
                    total: 7690
                multipleProductVariants:
                  summary: An example response of adding multiple product variants to the cart session.
                  value:
                    tokenValue: 'pmyIXe7ANZ'
                    state: 'cart'
                    channel:
                      code: 'dok_gb'
                      addPrescriptionMedicationDirectlyToCart: true
                      baseCurrency:
                        code: 'GBP'
                    checkoutState: 'cart'
                    currencyCode: 'GBP'
                    localeCode: 'en'
                    items:
                      - id: 1
                        variant:
                          code: 'propecia_tablet_1mg_28_pieces'
                          name: 'Propecia 1 mg 28 tabl.'
                          productAttributes:
                            type: 'medication'
                          maximumQuantityPerOrder: 1
                          supplier:
                            name: 'Apotheek Bad Nieuweschans'
                          prescriptionRequired: true
                          price:
                            amount: 7690
                            currency: 'GBP'
                        productName: 'Propecia'
                        variantName: 'Propecia 1 mg 28 tabl.'
                        warnings: []
                        quantity: 1
                        unitPrice: 7690
                        subTotal: 7690
                        total: 7690
                      - id: 2
                        variant:
                          code: 'finasteride_tablet_1mg_28_pieces'
                          name: 'Finasteride 1 mg 28 tabl.'
                          productAttributes:
                            type: 'medication'
                          maximumQuantityPerOrder: 1
                          supplier:
                            name: 'Apotheek Bad Nieuweschans'
                          prescriptionRequired: true
                          price:
                            amount: 6790
                            currency: 'GBP'
                        productName: 'Finasteride 1 mg'
                        variantName: 'Finasteride 1 mg 28 tabl.'
                        warnings: []
                        quantity: 1
                        unitPrice: 6790
                        subTotal: 6790
                        total: 6790
                    payments: []
                    shipments: []
                    itemsTotal: 14480
                    orderPromotionTotal: 0
                    shippingTotal: 0
                    taxTotal: 1303
                    total: 14480
                multipleProductVariantsWithRelation:
                  summary: |
                    An example response of adding multiple product variants with parent-child relation to
                    the cart session.
                  value:
                    tokenValue: 'pmyIXe7ANZ'
                    state: 'cart'
                    channel:
                      code: 'dok_gb'
                      addPrescriptionMedicationDirectlyToCart: true
                      baseCurrency:
                        code: 'GBP'
                    checkoutState: 'cart'
                    currencyCode: 'GBP'
                    localeCode: 'en'
                    items:
                      - id: 1
                        variant:
                          code: 'consult_hair_loss'
                          name: 'Consult voor haarverlies'
                          productAttributes:
                            type: 'consult'
                          maximumQuantityPerOrder: 1
                          prescriptionRequired: true
                          price:
                            amount: 2900
                            currency: 'GBP'
                        productName: 'Consult voor haarverlies'
                        variantName: 'Consult voor haarverlies'
                        warnings: []
                        quantity: 1
                        unitPrice: 2900
                        subTotal: 2900
                        total: 2900
                      - id: 2
                        variant:
                          code: 'propecia_tablet_1mg_28_pieces'
                          name: 'Propecia 1 mg 28 tabl.'
                          productAttributes:
                            type: 'medication'
                          maximumQuantityPerOrder: 1
                          supplier:
                            name: 'Apotheek Bad Nieuweschans'
                          prescriptionRequired: true
                          price:
                            amount: 7690
                            currency: 'GBP'
                        parentOrderItem:
                          id: 1
                          variant:
                            code: 'consult_hair_loss'
                        productName: 'Propecia'
                        variantName: 'Propecia 1 mg 28 tabl.'
                        warnings: []
                        quantity: 1
                        unitPrice: 7690
                        subTotal: 7690
                        total: 7690
                    payments: []
                    shipments: []
                    itemsTotal: 10590
                    orderPromotionTotal: 0
                    shippingTotal: 0
                    taxTotal: 1301
                    total: 14480
                multipleProductVariantsWithRelationForConsultFlow:
                  summary: |
                    An example response of adding multiple product variants with parent-child relation to
                    the cart session in the consult flow.
                  value:
                    tokenValue: 'pmyIXe7ANZ'
                    state: 'cart'
                    channel:
                      code: 'dok_nl'
                      addPrescriptionMedicationDirectlyToCart: false
                      baseCurrency:
                        code: 'EUR'
                    checkoutState: 'cart'
                    currencyCode: 'EUR'
                    localeCode: 'nl'
                    items:
                      - id: 1
                        variant:
                          code: 'consult_hair_loss'
                          name: 'Consult voor haarverlies'
                          productAttributes:
                            type: 'consult'
                          maximumQuantityPerOrder: 1
                          prescriptionRequired: true
                          price:
                            amount: 2900
                            currency: 'GBP'
                        preferredVariants:
                          - code: 'propecia_tablet_1mg_28_pieces'
                            name: 'Propecia 1 mg 28 tabl.'
                            maximumQuantityPerOrder: 1
                            price:
                              amount: 7690
                              currency: 'EUR'
                        productName: 'Consult voor haarverlies'
                        variantName: 'Consult voor haarverlies'
                        warnings: []
                        quantity: 1
                        unitPrice: 2900
                        subTotal: 2900
                        total: 2900
                    payments: []
                    shipments: []
                    itemsTotal: 2900
                    orderPromotionTotal: 0
                    shippingTotal: 0
                    taxTotal: 0
                    total: 2900
        '400':
          description: |
            The request contains invalid data. This can be one of the following:
            * The request body contains invalid JSON syntax.
            * The provided channel does not exist.
            * The provided locale does not exist.
            * Various validation errors based on the request body.
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
              examples:
                invalidRequestBody:
                  summary: An example error when the request body contains errors.
                  value:
                    type: 'about:blank'
                    title: 'The request body contains errors.'
                    status: 400
                    detail: 'Validation of JSON request body failed.'
                    violations:
                      - constraint: 'required'
                        message: 'The property productVariantCode is required'
                        property: 'productVariantCode'
                productVariantNotFound:
                  summary: An example error when a product variant is not found.
                  value:
                    type: 'about:blank'
                    title: 'The request body contains errors.'
                    status: 400
                    detail: 'productVariantCode: The product variant is not found.'
                    violations:
                      - constraint: 'product_variant_exists'
                        message: 'The product variant is not found.'
                        property: 'productVariantCode'
                productVariantParentRequired:
                  summary: An example error when a product variant parent-child relation is required.
                  description: |
                    This occurs when the product variant being added to the cart requires a parent product variant
                    to also be added to the cart.
                  value:
                    type: 'about:blank'
                    title: 'The request body contains errors.'
                    status: 400
                    detail: 'productVariantCode: The product variant is not found.'
                    violations:
                      - constraint: 'required'
                        message: 'The parent product variant is required for the product variant to be added.'
                        property: 'parentProductVariantCode'
                invalidProductVariantParent:
                  summary: An example error when a product variant parent is invalid.
                  description: |
                    This occurs when the provided `productVariantCode` and `parentProductVariantCode` are not
                    linked through a product association.
                  value:
                    type: 'about:blank'
                    title: 'The request body contains errors.'
                    status: 400
                    detail: 'parentProductVariantCode: The product variant is not found.'
                    violations:
                      - constraint: 'is_valid_for_product_variant_code'
                        message: 'The parent product variant is invalid for the given product variant.'
                        property: 'orderItemOperations[0].item.parentProductVariantCode'
                invalidQuantityError:
                  summary: An example error when the provided quantity is invalid.
                  value:
                    type: 'about:blank'
                    title: 'The request body contains errors.'
                    status: 400
                    detail: 'quantity: Quantity of an order item cannot be higher than 1.'
                    violations:
                      - constraint: 'maximum_quantity_per_order_for_product_variant'
                        message: 'The quantity of the product variant cannot be higher than 1.'
                        property: '[0].quantity'
                consultRequiredError:
                  summary: An example error when the resulting order does not contain a consult order item.
                  value:
                    type: 'about:blank'
                    title: 'The request body contains errors.'
                    status: 400
                    detail: 'The order should have at least one consult order item.'
                    violations:
                      - constraint: 'consult_required'
                        message: 'The order should have at least one consult order item.'
                        property: 'orderItemOperations'
        '404':
          $ref: '#/components/responses/CartNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Cart

    parameters:
      - $ref: '#/components/parameters/ChannelContext'
      - $ref: '#/components/parameters/LocaleContext'
      - $ref: '#/components/parameters/CartTokenValue'

  /shop/carts/{tokenValue}/items/{itemId}:
    post:
      summary: Modify quantity of item in cart
      description: Modify the quantity of an item in the cart session.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Cart\ModifyItemQuantity'
        additionalRouteAttributes:
          is_granted: 'ORDER_ALLOWED'
          is_granted_exception: 'App\Api\Exception\CartNotFoundException'
          responseSerializationSchemaObject: 'Cart'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                quantity:
                  type: integer
                  minimum: 1
              required:
                - quantity
      responses:
        '200':
          description: The cart is successfully updated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
          # TODO find a way to merge examples without copy-pasting
#          examples:
#            invalidQuantityError:
#              summary: An example error when the provided quantity is invalid.
#              value:
#                type: 'https://tools.ietf.org/html/rfc2616#section-10'
#                title: 'An error occurred'
#                detail: 'quantity: Quantity of an order item cannot be lower than 1.'
#                violations:
#                  - propertyPath: 'quantity'
#                    message: 'Quantity of an order item cannot be lower than 1.'
#                    code: '76454e69-502c-46c5-9643-f447d837c4d5'
        '404':
          $ref: '#/components/responses/CartNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Cart

    delete:
      summary: Remove item from cart
      description: Remove an item from the cart session identified by its unique token value.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Cart\RemoveItem'
        additionalRouteAttributes:
          is_granted: 'ORDER_ALLOWED'
          is_granted_exception: 'App\Api\Exception\CartNotFoundException'
          responseSerializationSchemaObject: 'Cart'
      responses:
        '200':
          description: |
            The item is successfully removed from the cart session. A successful response is also returned
            when the item is not available in the cart session.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'
        '404':
          $ref: '#/components/responses/CartNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Cart

    parameters:
      - $ref: '#/components/parameters/CartTokenValue'
      - $ref: '#/components/parameters/CartItemId'

  /shop/carts/{tokenValue}/child-items:
    delete:
      summary: Remove child items from cart
      description: |
        Remove child order items of the provided product variant codes from the cart session.

        This will also remove preferred variants attached to the order item of a parent product variant.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Cart\RemoveChildCartItems'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Cart'
      requestBody:
        content:
          application/json:
            schema:
              type: array
              minItems: 1
              items:
                type: object
                properties:
                  parentProductVariantCode:
                    type: string
                    example: 'consult_hair_loss'
                required:
                  - parentProductVariantCode
      responses:
        '200':
          description: |
            The child order items are successfully removed from the cart session. A successful response is also returned
            when no child items were available in the cart session.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'
              examples:
                afterRemovingProductVariantsWithRelation:
                  summary: |
                    An example response after removing child order items from the cart session.
                  description: |
                    This is an example response after removing product variants from the cart session related to
                    the product variant with code `consult_hair_loss`.

                    Also see the response from the "Add items to cart" endpoint for the initial state.
                  value:
                    tokenValue: 'pmyIXe7ANZ'
                    state: 'cart'
                    channel:
                      code: 'dok_gb'
                      addPrescriptionMedicationDirectlyToCart: true
                      baseCurrency:
                        code: 'GBP'
                    checkoutState: 'cart'
                    currencyCode: 'GBP'
                    localeCode: 'en'
                    items:
                      - id: 1
                        variant:
                          code: 'consult_hair_loss'
                          name: 'Consult for hair loss'
                          maximumQuantityPerOrder: 1
                          price:
                            amount: 2900
                            currency: 'GBP'
                        productName: 'Consult for hair loss'
                        variantName: 'Consult for hair loss'
                        warnings: []
                        quantity: 1
                        unitPrice: 2900
                        subTotal: 2900
                        total: 2900
                    payments: []
                    shipments: []
                    itemsTotal: 2900
                    orderPromotionTotal: 0
                    shippingTotal: 0
                    taxTotal: 609
                    total: 2900
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/CartNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Cart

    parameters:
      - $ref: '#/components/parameters/CartTokenValue'

  /shop/carts/{tokenValue}/coupon:
    post:
      summary: Apply coupon to cart
      description: Apply a coupon to the cart session identified by its unique token value.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Cart\ApplyCoupon'
        additionalRouteAttributes:
          is_granted: 'ORDER_ALLOWED'
          is_granted_exception: 'App\Api\Exception\CartNotFoundException'
          responseSerializationSchemaObject: 'Cart'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                couponCode:
                  type: string
              required:
                - couponCode
      responses:
        '200':
          description: The coupon is successfully applied to the cart session.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/CartNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Cart

    delete:
      summary: Remove coupon from cart
      description: Remove the applied coupon from the cart session identified by its unique token value.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Cart\RemoveCoupon'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Cart'
      responses:
        '200':
          description: The coupon is successfully removed from the cart session.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/CartNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Cart

    parameters:
      - $ref: '#/components/parameters/CartTokenValue'

  /shop/carts/{tokenValue}/validate:
    post:
      summary: Validate the cart
      description: |
        Validate the cart session before proceeding with the checkout. This process checks if (the combination of)
        cart items are available for ordering.

        When a change is made to the cart session it also needs to be revalidated through this API endpoint.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Cart\ValidateCartController'
        deserializationObject: 'App\Api\Command\Cart\ValidateCart'
        additionalRouteAttributes:
          is_granted: 'ORDER_ALLOWED'
          is_granted_exception: 'App\Api\Exception\CartNotFoundException'
      requestBody:
        content:
          application/json:
            schema:
              type: object
      responses:
        '204':
          description: The cart session is successfully validated. The cart session is ready for the checkout process.
        '404':
          $ref: '#/components/responses/CartNotFoundProblemDetailsResponse'
        '409':
          description: |
            The cart session is not successfully validated. See the response or the `warning` property on the cart items
            for more details.
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/InvalidCartProblemDetails'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Cart

    parameters:
      - $ref: '#/components/parameters/CartTokenValue'

  /shop/checkout/{tokenValue}/questionnaire:
    post:
      summary: Register medical questionnaire
      description: Register a medical questionnaire related to the order.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Checkout\RegisterQuestionnaireReference'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Cart'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                uuid:
                  type: string
                  format: uuid
              required:
                - uuid
      responses:
        '200':
          description: The questionnaire is successfully registered on the cart session.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/CartNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Checkout

    parameters:
      - $ref: '#/components/parameters/CartTokenValue'

  /shop/checkout/{tokenValue}/questionnaire/complete:
    post:
      summary: Complete medical questionnaire
      description: |
        Marks the medical questionnaire as completed.

        This verifies if all the questions in the medical questionnaire are registered on the order.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Checkout\CompleteQuestionnaire'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Cart'
      responses:
        '200':
          description: The questionnaire linked to the cart session is successfully verified.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'
        '400':
          description: The medical questionnaire is incomplete or not registered in the cart session.
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
              example:
                type: 'about:blank'
                title: 'An error occurred.'
                status: 400
                detail: 'The registered medical questionnaire is incomplete.'
        '404':
          $ref: '#/components/responses/CartNotFoundProblemDetailsResponse'
        '503':
          description: Unable to successfully communicate with the medical questionnaire service.
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
              example:
                type: 'about:blank'
                title: 'An error occurred.'
                status: 503
                detail: 'Unable to verify medical questionnaire.'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Checkout

    parameters:
      - $ref: '#/components/parameters/CartTokenValue'

  /shop/checkout/{tokenValue}/address:
    post:
      summary: Provide billing and shipping address
      description: |
        Provide the billing and shipping addresses for the order. An authenticated customer should not provide an email address.

        **Note:** Providing a billing and shipping address as unauthenticated customer is not available at this time.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Checkout\Address\SetBillingAndShippingAddress'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Cart'
          problemDetailsViolationObject: 'App\Address\Validation\Smarty\ExceptionHandling\Violation'
      parameters:
        - $ref: '#/components/parameters/SkipAddressValidation'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: The required email address if the customer is not authenticated.
                billingAddress:
                  $ref: '#/components/schemas/Address'
                shippingAddress:
                  $ref: '#/components/schemas/ShippingAddress'
              required:
                - billingAddress
                - shippingAddress
            examples:
              authenticatedCustomer:
                summary: An example when the customer is authenticated.
                value:
                  billingAddress:
                    firstName: John
                    lastName: Doe
                    phoneNumber: '***********'
                    street: Bucketblock 1
                    postcode: 1234 AB
                    city: Old Gasstove
                    provinceName: Noord Brabant
                    countryCode: NL
                  shippingAddress:
                    firstName: John
                    lastName: Doe
                    phoneNumber: '***********'
                    street: Bucketblock 1
                    postcode: 1234 AB
                    city: Old Gasstove
                    provinceName: Noord Brabant
                    countryCode: NL
              unauthenticatedCustomer:
                summary: An example when the customer is not authenticated.
                value:
                  email: <EMAIL>
                  billingAddress:
                    firstName: John
                    lastName: Doe
                    phoneNumber: '***********'
                    street: Bucketblock 1
                    postcode: 1234 AB
                    city: Old Gasstove
                    provinceName: Noord Brabant
                    countryCode: NL
                  shippingAddress:
                    firstName: John
                    lastName: Doe
                    phoneNumber: '***********'
                    street: Bucketblock 1
                    postcode: 1234 AB
                    city: Old Gasstove
                    provinceName: Noord Brabant
                    countryCode: NL
      responses:
        '200':
          description: The billing and shipping address are successfully added to the cart session.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'
        '400':
          $ref: '#/components/responses/InvalidAddressRequestProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/CartNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Checkout

    parameters:
      - $ref: '#/components/parameters/ChannelContext'
      - $ref: '#/components/parameters/LocaleContext'
      - $ref: '#/components/parameters/CartTokenValue'

  /shop/checkout/{tokenValue}/payment-methods:
    get:
      deprecated: true
      summary: List available payment methods
      description: Retrieve the available payment methods for the cart session.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Checkout\GetPaymentMethodsController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'PaymentMethod'
      responses:
        '200':
          description: The payment methods are successfully retrieved.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PaymentMethod'
        '404':
          $ref: '#/components/responses/CartNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Checkout

    parameters:
      - $ref: '#/components/parameters/CartTokenValue'

  /shop/checkout/{tokenValue}/payments/{paymentId}:
    post:
      summary: Select payment method for payment
      description: Select a payment method for a payment on the cart session.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Payment\UpdatePaymentOnCart'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Cart'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePayment'
            examples:
              iDeal:
                summary: Select iDeal as payment method
                value:
                  paymentMethod: ideal
                  issuerCode: RABONL2U
              creditCard:
                summary: Select credit card as payment method
                value:
                  paymentMethod: credit_card
      responses:
        '200':
          description: The payment method is successfully selected.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Cart'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/CartNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Checkout

    parameters:
      - $ref: '#/components/parameters/CartTokenValue'
      - $ref: '#/components/parameters/PaymentId'

  /shop/checkout/{tokenValue}/terms-conditions:
    get:
      summary: List terms and conditions
      description: Retrieve the terms and conditions that need to be agreed upon when completing the order.
      x-openapi-bundle:
        controller: 'App\Api\Controller\TermQuestion\GetTermQuestionsController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'TermQuestion'
      parameters:
        - $ref: '#/components/parameters/LocaleContext'
        - $ref: '#/components/parameters/ChannelContext'
      responses:
        '200':
          description: The terms questions are successfully retrieved.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TermQuestion'
              example:
                - code: terms_and_conditions
                  name: I agree to the contents and applicability of the general terms and conditions.
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/CartNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Checkout

    parameters:
      - $ref: '#/components/parameters/CartTokenValue'

  /shop/checkout/{tokenValue}/complete:
    post:
      summary: Complete order
      description: Complete the order by providing the agreed upon terms and conditions.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Checkout\CompleteOrder'
        additionalRouteAttributes:
          is_granted: 'ORDER_ALLOWED'
          is_granted_exception: 'App\Api\Exception\CartNotFoundException'
          responseSerializationSchemaObject: 'Order'
      requestBody:
        content:
          application/json:
            schema:
              oneOf:
                - type: object
                  properties:
                    termsAnswers:
                      type: array
                      description: The list of codes provided by the terms and conditions endpoint.
                      items:
                        type: string
                      deprecated: true
                      example:
                        - terms_and_conditions
                    notes:
                      type: string
                      deprecated: true
                      example: This is a note.
                  required:
                    - termsAnswers
                - type: object
                  properties:
                    terms:
                      type: object
                      description: |
                        A map of terms and conditions codes and their translations as seen and accepted by the customer.
                      patternProperties:
                        '^[_a-z]+$':
                          type: string
                          example: 'I agree to the contents and applicability of the general terms and conditions.'
                      additionalProperties: false
                      minProperties: 1
                      example:
                        terms_and_conditions: 'I agree to the contents and applicability of the general terms and conditions.'
                  required:
                    - terms
      responses:
        '200':
          description: The order is successfully created.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Order'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/CartNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Checkout

    parameters:
      - $ref: '#/components/parameters/CartTokenValue'

  /shop/checkout/{tokenValue}/completion-information:
    get:
      summary: Retrieve basic order completion information
      description: Retrieve basic order completion information.
      operationId: getOrderCompletionInformation
      x-openapi-bundle:
        controller: 'App\Api\Controller\Order\GetOrderCompletionInformationController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'OrderCompletionInformation'
      parameters:
        - $ref: '#/components/parameters/OrderId'
      responses:
        '200':
          description: The order is successfully retrieved with minimal data to display on a "Thank you" page.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderCompletionInformation'
        '404':
          $ref: '#/components/responses/OrderNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Checkout

    parameters:
      - $ref: '#/components/parameters/CartTokenValue'

  /shop/marketing-subscriptions:
    post:
      summary: Create subscription
      description: Create a new subscription. Eg. to subscribe a user to an email newsletter.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        additionalRouteAttributes:
          responseSuccessStatusCode: 204
        deserializationObject: 'App\Api\Command\MarketingSubscription\CreateMarketingSubscription'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMarketingSubscription'
      responses:
        '204':
          description: The subscription request is successfully processed.
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
      tags:
        - Marketing subscriptions

  /shop/marketing-subscriptions/{uuid}:
    get:
      summary: Get subscription
      description: Retrieve a subscription by its UUID.
      x-openapi-bundle:
        controller: 'App\Api\Controller\MarketingSubscription\GetMarketingSubscriptionController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'MarketingSubscription'
      responses:
        '200':
          description: The subscription is successfully retrieved.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketingSubscription'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
      tags:
        - Marketing subscriptions

    post:
      summary: Update subscription
      description: Update a subscription identified by its UUID.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\MarketingSubscription\UpdateMarketingSubscription'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'MarketingSubscription'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MarketingSubscription'
      responses:
        '200':
          description: The subscription update is successfully processed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketingSubscription'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
      tags:
        - Marketing subscriptions

    parameters:
      - $ref: '#/components/parameters/MarketingSubscriptionUuid'

  /admin/marketing-subscriptions/{uuid}:
    put:
      operationId: replaceMarketingSubscription
      summary: Replace subscription
      description: Replace a subscription identified by its UUID.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\MarketingSubscription\ReplaceMarketingSubscription'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'MarketingSubscription'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReplaceMarketingSubscription'
      responses:
        '200':
          description: The subscription update is successfully processed.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketingSubscription'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - oauthMachineToMachine:
            - update:marketing_subscription
      tags:
        - Marketing subscriptions

    parameters:
      - $ref: '#/components/parameters/MarketingSubscriptionUuid'

  /shop/marketing-subscriptions/{uuid}/verify:
    post:
      summary: Verify subscription email
      description: Verify the email address of the subscription identified by its UUID.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\MarketingSubscription\VerifyMarketingSubscription'
        additionalRouteAttributes:
          responseSuccessStatusCode: 204
      responses:
        '204':
          description: The subscription verification is successfully processed.
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        '409':
          description: The subscription could not be verified.
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
              example:
                type: 'about:blank'
                title: 'An error occurred.'
                status: 409
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
      tags:
        - Marketing subscriptions

    parameters:
      - $ref: '#/components/parameters/MarketingSubscriptionUuid'

  /shop/marketing-subscriptions/{uuid}/unsubscribe:
    post:
      summary: Unsubscribe from all marketing channels
      description: Unsubscribe the email address of the subscription identified by its UUID from all marketing channels.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\MarketingSubscription\UnsubscribeMarketingSubscription'
        additionalRouteAttributes:
          responseSuccessStatusCode: 204
      responses:
        '204':
          description: The unsubscription is successfully processed.
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - {}
      tags:
        - Marketing subscriptions

    parameters:
      - $ref: '#/components/parameters/MarketingSubscriptionUuid'

  /shop/account/register:
    post:
      summary: Register
      description: Registers a new customer.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Account\RegisterCustomer'
        additionalRouteAttributes:
          responseSuccessStatusCode: 204
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerAccountRegistration'
      responses:
        '204':
          description: The customer is successfully registered. You can proceed with authenticating the customer.
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - Account
    parameters:
      - $ref: '#/components/parameters/ChannelContext'
      - $ref: '#/components/parameters/LocaleContext'

  /shop/account/authenticate:
    post:
      summary: Authenticate
      description: Authenticate a customer. Upon successful authentication, a JSON Web Token is returned.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerAccountAuthentication'
      responses:
        '200':
          description: The customer is successfully authenticated and a JSON Web Token is returned.
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                    example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          description: |
            The customer is unsuccessfully authenticated because the provided email address and/or password are invalid.
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
              example:
                type: 'about:blank'
                title: 'Invalid credentials.'
                status: 401
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - Account

    parameters:
      - $ref: '#/components/parameters/ChannelContext'

  /shop/account/reset-password:
    post:
      summary: Request password reset
      description: Request a password reset for a customer account.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Account\Password\RequestPasswordReset'
        additionalRouteAttributes:
          responseSuccessStatusCode: 202
      parameters:
        - $ref: '#/components/parameters/ChannelContext'
        - $ref: '#/components/parameters/LocaleContext'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
              required:
                - email
      responses:
        '202':
          description: The password reset request has been accepted.
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - Account

  /shop/account/reset-password/{resetPasswordToken}:
    put:
      summary: Reset password
      description: Reset the password for a customer account.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Account\Password\ResetPassword'
        additionalRouteAttributes:
          responseSuccessStatusCode: 204
      parameters:
        - $ref: '#/components/parameters/ChannelContext'
        - $ref: '#/components/parameters/LocaleContext'
        - name: resetPasswordToken
          in: path
          required: true
          description: The reset password token sent to the customer by e-mail.
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerAccountPasswordReset'
      responses:
        '204':
          description: The password is successfully updated.
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - Account

  /shop/account:
    get:
      summary: Get account information
      description: Retrieve the account and customer information for the authenticated user.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Account\GetAccountController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'CustomerWithEmbeddedUser'
      responses:
        '200':
          description: The account and customer information is successfully retrieved.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerWithEmbeddedUser'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - Account

    post:
      summary: Update account information
      description: Update the account and customer information for the authenticated user.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Account\UpdateAccountInformation'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'CustomerWithEmbeddedUser'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerAccountInformation'
      responses:
        '200':
          description: The customer information is successfully updated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerWithEmbeddedUser'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - Account

    delete:
      summary: Delete account
      description: Delete the account of the authenticated user.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Account\DeleteAccount'
        additionalRouteAttributes:
          responseSuccessStatusCode: 204
      responses:
        '204':
          description: The account is successfully deleted.
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '409':
          description: The account cannot be deleted due to open orders.
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
              example:
                type: 'about:blank'
                title: 'An error occurred.'
                status: 409
                detail: 'The account cannot be deleted due to open orders.'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - Account

  /shop/account/password:
    put:
      summary: Change password
      description: Change the password of an authenticated customer.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        additionalRouteAttributes:
          responseSuccessStatusCode: 204
        deserializationObject: 'App\Api\Command\Account\Password\ChangePassword'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerAccountPasswordChange'
      responses:
        '204':
          description: The password is successfully updated.
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - Account

  /shop/account/addresses:
    get:
      summary: List addresses
      description: |
        Retrieve addresses of the authenticated customer. Optionally, the channel can be provided to filter
        the list of addresses.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Address\GetAddressesController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'AddressBookAddress'
      parameters:
        - $ref: '#/components/parameters/ChannelContextFilter'
        - $ref: '#/components/parameters/FilterByPickupPoint'
      responses:
        '200':
          description: The addresses are successfully retrieved.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AddressBookAddress'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - 'Account: Address book'

    post:
      summary: Create address
      description: Create a new address in the authenticated customer's address book.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Address\CreateAddress'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'AddressBookAddress'
          problemDetailsViolationObject: 'App\Address\Validation\Smarty\ExceptionHandling\Violation'
      parameters:
        - $ref: '#/components/parameters/SkipAddressValidation'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddressBookAddress'
      responses:
        '200':
          description: The address in the customer's address book is successfully updated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddressBookAddress'
        '400':
          $ref: '#/components/responses/InvalidAddressRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - 'Account: Address book'

  /shop/account/addresses/{id}:
    get:
      summary: Get address
      description: Retrieve an address from the authenticated customer's address book by its unique ID.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Address\GetAddressController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'AddressBookAddress'
      responses:
        '200':
          description: The address is successfully retrieved.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddressBookAddress'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - 'Account: Address book'

    put:
      summary: Update address
      description: Update the address in the authenticated customer's address book.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Address\UpdateAddress'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'AddressBookAddress'
          problemDetailsViolationObject: 'App\Address\Validation\Smarty\ExceptionHandling\Violation'
      parameters:
        - $ref: '#/components/parameters/SkipAddressValidation'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddressBookAddress'
      responses:
        '200':
          description: The address in the customer's address book is successfully updated.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddressBookAddress'
        '400':
          $ref: '#/components/responses/InvalidAddressRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - 'Account: Address book'

    delete:
      summary: Remove address
      description: Remove the address from the authenticated customers address book.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Address\RemoveAddress'
        additionalRouteAttributes:
          responseSuccessStatusCode: 204
      responses:
        '204':
          description: The address is successfully removed from the customer's address book.
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - 'Account: Address book'

    parameters:
      - $ref: '#/components/parameters/AddressId'

  /shop/orders:
    get:
      summary: List orders
      description: Retrieve the orders for the authenticated customer.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Order\GetOrdersController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Order'
          additionalSerializationContext: 'account'
      responses:
        '200':
          description: The orders are successfully retrieved.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Order'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - 'Account: Orders'

  /v2/shop/orders/{tokenValue}:
    delete:
      summary: Delete cart
      description: Delete a cart session by its unique token value.
      responses:
        '204':
          description: The cart is successfully deleted.
        '404':
          $ref: '#/components/responses/CartNotFoundError'
        default:
          $ref: '#/components/responses/UnexpectedError'
      security:
        - {}
        - customerJsonWebToken: []
      tags:
        - Cart

    parameters:
      - $ref: '#/components/parameters/CartTokenValue'

  /shop/orders/{tokenValue}:
    get:
      summary: Get order
      description: Retrieve an order for the authenticated customer.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Order\GetOrderController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Order'
          additionalSerializationContext: 'account'
      responses:
        '200':
          description: The order is successfully retrieved.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Order'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/OrderNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - 'Account: Orders'

    parameters:
      - $ref: '#/components/parameters/tokenValue'

  /shop/orders/{tokenValue}/messages:
    get:
      summary: List messages on order
      description: Retrieve the messages of the communication between the customer and the attending doctor.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Order\Communication\ListController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'CommunicationMessage'
      responses:
        '200':
          description: The messages are successfully retrieved.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CommunicationMessage'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/OrderNotFoundProblemDetailsResponse'
        '503':
          description: Unable to successfully retrieve messages from the Consult System.
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
              example:
                type: 'about:blank'
                title: 'An error occurred.'
                status: 503
                detail: 'Unable to retrieve messages.'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - 'Account: Orders'

    post:
      summary: Send message about order
      description: Send a message to the attending doctor about the order.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Order\Communication\CreateCommunicationMessage'
        additionalRouteAttributes:
          is_granted: 'IS_OWNER'
          is_granted_exception: 'App\Api\Exception\OrderNotFoundException'
          responseSerializationSchemaObject: 'CommunicationMessage'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCommunicationMessage'
      responses:
        '200':
          description: The message is successfully sent.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CommunicationMessage'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/OrderNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        '503':
          description: Unable to successfully send a message to the Consult System.
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
              example:
                type: 'about:blank'
                title: 'An error occurred.'
                status: 503
                detail: 'Unable to send message.'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - 'Account: Orders'

    parameters:
      - $ref: '#/components/parameters/tokenValue'

  /shop/orders/{tokenValue}/payment-methods:
    get:
      deprecated: true
      summary: List available payment methods
      description: Retrieve the available payment methods for the order.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Order\GetPaymentMethodsController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'PaymentMethod'
      responses:
        '200':
          description: The payment methods are successfully retrieved.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PaymentMethod'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/OrderNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - 'Account: Orders'

    parameters:
      - $ref: '#/components/parameters/tokenValue'

  /shop/orders/{tokenValue}/payments/{paymentId}:
    post:
      summary: Select payment method for payment
      description: Select a payment method for a payment on the order.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Payment\UpdatePaymentOnOrder'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Order'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePayment'
            examples:
              iDeal:
                summary: Select iDeal as payment method
                value:
                  paymentMethod: ideal
                  issuerCode: RABONL2U
              creditCard:
                summary: Select credit card as payment method
                value:
                  paymentMethod: credit_card
      responses:
        '200':
          description: The payment method is successfully selected.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Order'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/OrderNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - 'Account: Orders'

    parameters:
      - $ref: '#/components/parameters/tokenValue'
      - $ref: '#/components/parameters/PaymentId'

  /shop/orders/{tokenValue}/payments/{paymentId}/cancel:
    post:
      summary: Cancel payment
      description: Cancel a payment on the order.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Payment\CancelPayment'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Order'
      responses:
        '200':
          description: The payment is successfully cancelled.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Order'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/OrderNotFoundProblemDetailsResponse'
        '409':
          description: The payment cannot be cancelled.
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
              example:
                type: 'about:blank'
                title: 'An error occurred.'
                status: 409
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - 'Account: Orders'

    parameters:
      - $ref: '#/components/parameters/tokenValue'
      - $ref: '#/components/parameters/PaymentId'

  /shop/orders/{tokenValue}/payment:
    post:
      summary: Start payment
      description: Start payment for the amount that has yet to be paid for this order.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Order\PayOrder'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'Order'
      responses:
        '200':
          description: The payment is successfully started.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Order'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/OrderNotFoundProblemDetailsResponse'
        '503':
          description: Unable to successfully communicate with payment-service.
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
              example:
                type: 'about:blank'
                title: 'An error occurred.'
                status: 503
                detail: 'Unable to start payment.'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - 'Account: Orders'

    parameters:
      - $ref: '#/components/parameters/tokenValue'

  /shop/orders/{tokenValue}/prescription:
    get:
      summary: Download prescription
      description: Retrieve the prescription of the order by its unique token value.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Order\PrescriptionController'
      responses:
        '200':
          description: The prescription is successfully retrieved.
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - 'Account: Orders'

    parameters:
      - $ref: '#/components/parameters/tokenValue'

  /shop/orders/{tokenValue}/cancel:
    post:
      summary: Cancel order
      description: Cancels the order with a reason provided by the customer.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Order\CancelOrder'
        additionalRouteAttributes:
          is_granted: 'IS_OWNER'
          is_granted_exception: 'App\Api\Exception\OrderNotFoundException'
          responseSerializationSchemaObject: 'Order'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: Reason for cancelling the order.
                  example: No longer needed
      responses:
        '200':
          description: The order is successfully cancelled.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Order'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        '404':
          $ref: '#/components/responses/OrderNotFoundProblemDetailsResponse'
        '415':
          $ref: '#/components/responses/InvalidRequestContentTypeProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - customerJsonWebToken: []
      tags:
        - 'Account: Orders'

      parameters:
        - $ref: '#/components/parameters/tokenValue'

  /admin/orders:
    get:
      summary: List orders
      description: Retrieve the orders ready for approval by a medical specialist (eg. doctor) or ready for shipment.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Admin\Order\GetOrdersController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'OrderWithEmbeddedCustomer'
      parameters:
        - $ref: '#/components/parameters/ChannelContextFilter'
        - $ref: '#/components/parameters/FilterOrderByPrescriptionRequired'
        - $ref: '#/components/parameters/ModifiedAfter'
      responses:
        '200':
          description: The orders are successfully retrieved.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OrderWithEmbeddedCustomer'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - adminJsonWebToken: []
      tags:
        - Admin

  /admin/orders/{tokenValue}:
    get:
      summary: Get order
      description: Retrieve order by its unique token value.
      x-openapi-bundle:
        controller: 'App\Api\Controller\Admin\Order\GetOrderController'
        additionalRouteAttributes:
          responseSerializationSchemaObject: 'OrderWithEmbeddedCustomer'
      responses:
        '200':
          description: The order is successfully retrieved.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderWithEmbeddedCustomer'
        '400':
          $ref: '#/components/responses/InvalidRequestProblemDetailsResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedProblemDetailsResponse'
        '403':
          $ref: '#/components/responses/ForbiddenProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      security:
        - adminJsonWebToken: []
      tags:
        - Admin

    parameters:
      - $ref: '#/components/parameters/tokenValue'

  /notify/payment/unsafe/{gatewayName}:
    get:
      operationId: get_unsafe_notify_callback_for_payments_by_gateway
      summary: Notify payment status by gateway without a body
      description: |
        Notify the store about updates in the external payment service provider.

        The system will request the status from the payment service provider and update the payment of the order.
        The relevant payment is extracted from the url.
      x-openapi-bundle:
        controller: 'App\Payum\Controller\NotifyController::doUnsafeAction'
      responses:
        '202':
          description: The notification is successfully accepted.
        '404':
          description: The gateway for the provided name is not found.
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
              example:
                type: 'about:blank'
                title: 'An error occurred.'
                status: 404
                detail: 'The requested gateway could not be found.'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - Notifications

    post:
      operationId: post_unsafe_notify_callback_for_payments_by_gateway
      summary: Notify payment status by gateway
      description: |
        Notify the store about updates in the external payment service provider.

        The system will request the status from the payment service provider and update the payment of the order.
        The relevant payment is extracted from the request body.
      x-openapi-bundle:
        controller: 'App\Payum\Controller\NotifyController::doUnsafeAction'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties: true
      responses:
        '202':
          description: The notification is successfully accepted.
        '404':
          description: The gateway for the provided name is not found.
          content:
            application/problem+json:
              schema:
                $ref: '#/components/schemas/ProblemDetails'
              example:
                type: 'about:blank'
                title: 'An error occurred.'
                status: 404
                detail: 'The requested gateway could not be found.'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - Notifications

    parameters:
      - $ref: '#/components/parameters/GatewayName'

  /notify/payment/{paymentToken}:
    post:
      operationId: notify_callback_for_payments_by_token
      summary: Notify payment status by token
      description: |
        Notify the store about updates in the external payment service provider.

        The system will request the status from the payment service provider and update the payment of the order.
      x-openapi-bundle:
        controller: 'App\Payum\Controller\NotifyController::doAction'
      responses:
        '202':
          description: The notification is successfully accepted.
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - Notifications

    parameters:
      - $ref: '#/components/parameters/PaymentToken'

  /notify/orders/{tokenValue}/shipment/{shipmentId}:
    post:
      operationId: notify_callback
      summary: Notify shipment updates
      description: |
        Notify the store about updates in the external shipment system.
        The system will request the update from the external system and update the shipment of the order.
      x-openapi-bundle:
        controller: 'App\Api\Controller\CommandController'
        deserializationObject: 'App\Api\Command\Notify\OrderShipmentStatus'
        additionalRouteAttributes:
          responseSuccessStatusCode: 204
      parameters:
        - $ref: '#/components/parameters/tokenValue'
        - $ref: '#/components/parameters/shipmentId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderStatusUpdate'
      responses:
        '204':
          description: The notification is successfully processed.
        '404':
          $ref: '#/components/responses/ResourceNotFoundProblemDetailsResponse'
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - Notifications

  /notify/orders/consult-request:
    post:
      operationId: notify_callback_consult_request
      summary: Notify the store about consult request updates
      description: |
        Notify the store about updates in the external consult request system.
        The system will receive the update from the external system and update the consult status of the order.
      x-openapi-bundle:
        controller: 'App\Api\Controller\AsyncDelayedCommandController'
        deserializationObject: 'App\Api\Command\Notify\ConsultRequestStatus'
        additionalRouteAttributes:
          responseSuccessStatusCode: 204
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotifyConsultRequestUpdate'
      responses:
        '204':
          description: The notification is successfully processed.
        default:
          $ref: '#/components/responses/DefaultProblemDetailsResponse'
      tags:
        - Notifications

webhooks:
  CreateOrUpdateCustomerWithMarketingSubscription: # TODO Implement in DV-3399
    post:
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                eventName:
                  type: string
                  enum:
                    - MarketingSubscriptionWasCreated # A MarketingSubscription object is created and sent as customer.
                    - MarketingSubscriptionWasUpdated # A MarketingSubscription object is updated and sent as customer.
                    - EmailOptInWasRequested          # An email opt-in is requested, a verification e-mail should be sent.
                    - CustomerWasCreated              # A Customer object is created. The customer is sent together with the linked Marketingsubcription object.
                    - CustomerWasUpdated              # A Customer object is updated. The customer is sent together with the linked Marketingsubcription object.
                    - PasswordWasChanged              # A Customer object is updated. The customer is sent together with the linked Marketingsubcription object.
                customer:
                  anyOf:
                    - $ref: '#/components/schemas/MarketingSubscriptionAsWebhook'
                    - $ref: '#/components/schemas/CustomerAsWebhook'
              required:
                - eventName
                - customer

  PasswordReset:
    post:
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                eventName:
                  type: string
                  enum:
                    - PasswordResetWasRequested
                localization:
                  allOf:
                    - title: Localizable
                      description: The locale and country where the customer requested the password reset.
                    - $ref: '#/components/schemas/Localizable'
                customer:
                  $ref: '#/components/schemas/CustomerAsPasswordResetWebhook'
              required:
                - eventName
                - localization
                - customer

  CreateOrUpdateCartOrOrder:
    post:
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                eventName:
                  type: string
                  enum:
                    - CartWasAbandoned
                    - OrderWasCreated
                    - OrderWasUpdated
                    - OrderPaymentWasRequested
                    - OrderWasMarkedForReshipment
                    - OrderWasPaid
                    - OrderWasCancelled
                    - OrderWasFulfilled
                    - OrderPrescriptionResponseWasRequestedByDoctor
                    - OrderPrescriptionWasPrescribedByDoctor
                    - OrderPrescriptionWasDeclinedByDoctor
                    - OrderShipmentWasRegisteredAtSupplier
                    - OrderShipmentWasRecalledBySupplier
                    - OrderShipmentWasSentBySupplier
                    - OrderShipmentWasReturnedToSupplier
                    - OrderShipmentWasCancelledBySupplier
                    - OrderAftercareDoctorHasResponded
                order:
                  $ref: '#/components/schemas/OrderAsWebhook'
              required:
                - eventName
                - order

  RemoveItemFromCartOrOrder:
    post:
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                eventName:
                  type: string
                  enum:
                    - ItemWasRemovedFromOrder
                orderItem:
                  $ref: '#/components/schemas/OrderItemAsRemoveWebhook'
              required:
                - eventName
                - orderItem

  ProductUpdate:
    post:
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                eventName:
                  type: string
                  enum:
                    - ProductBackInStock
                product:
                  $ref: '#/components/schemas/ProductAsWebhook'
              required:
                - eventName
                - product

components:
  parameters:
    CartTokenValue:
      name: tokenValue
      description: The unique identifier of the cart session.
      in: path
      required: true
      schema:
        type: string
        example: pmyIXe7ANZ

    CartItemId:
      name: itemId
      description: The unique identifier of an item in the cart session.
      in: path
      required: true
      schema:
        type: integer
        minimum: 1

    tokenValue:
      name: tokenValue
      description: The unique identifier of the order.
      in: path
      required: true
      schema:
        type: string
        example: pmyIXe7ANZ

    TokenValueFilter:
      name: tokenValue
      description: The unique identifier of the cart session or order.
      in: query
      schema:
        type: string
        example: pmyIXe7ANZ

    MarketingSubscriptionUuid:
      name: uuid
      description: The unique identifier of the marketing subscription.
      in: path
      required: true
      schema:
        type: string
        format: uuid

    shipmentId:
      name: shipmentId
      description: The unique identifier of the shipment.
      in: path
      required: true
      schema:
        type: string
        example: 'e4f6ea32-021f-4f96-b6b4-9fba260c24c4'

    ChannelCode:
      name: code
      description: The unique code of the channel.
      in: path
      required: true
      schema:
        type: string
        example: 'dok_nl'

    ChannelContext:
      name: Sylius-Channel-Code
      in: header
      description: The code of the channel for the context of the request.
      required: true
      schema:
        type: string
        example: 'dok_nl'

    ChannelContextFilter:
      name: Sylius-Channel-Code
      in: header
      description: |
        The code of the channel for the context of the request.
        Filter results for the specified channel.
      required: false
      schema:
        type: string
        example: 'dok_nl'

    CustomerId:
      name: id
      in: path
      description: The unique ID of the customer.
      required: true
      schema:
        type: integer

    GatewayName:
      name: gatewayName
      in: path
      description: The name of the gateway for the payment service provider.
      required: true
      schema:
        type: string
        example: 'stripe'

    LocaleContext:
      name: Accept-Language
      in: header
      description: |
        One or more language tags (eg. ISO 639-1 language codes) of the customer's language.
        When the header is not specified, the default locale from the channel will be used or fall back to English.
      required: false
      schema:
        type: string
        externalDocs:
          url: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Language

    LocaleContextInPath:
      name: localeCode
      in: path
      description: |
        A combination of the ISO 639-1 language code and the ISO 3166-1 alpha-2 country code.
      required: true
      schema:
        type: string
        pattern: '^[a-z]{2}-[A-Z]{2}$'
      example: 'en-GB'

    ModifiedAfter:
      name: modifiedAfter
      in: query
      description: Filters the results by only returning the items modified after the specified date-time.
      required: false
      schema:
        type: string
        format: date-time
        example: '2021-06-01T18:00:00Z'

    PaymentId:
      name: paymentId
      in: path
      description: The ID of a payment on the cart session or order.
      required: true
      schema:
        type: integer
        minimum: 1

    PaymentToken:
      name: paymentToken
      in: path
      description: The unique token of a payment.
      required: true
      schema:
        type: string

    ProductCode:
      name: code
      in: path
      description: The unique code of the product.
      required: true
      schema:
        type: string
        example: '1'

    TaxonCode:
      name: code
      in: path
      description: The unique code of the taxon.
      required: true
      schema:
        type: string
        example: 'hair_loss'

    ProductCodes:
      name: productCodes[]
      in: query
      description: The unique codes of the requested products.
      required: false
      allowReserved: true
      schema:
        type: array
        items:
          type: string
        example: ['1','2','3']

    OrderId:
      name: id
      in: query
      description: The order id.
      required: true
      schema:
        type: integer
        example: 1

    CartContext:
      name: Order-Token-Value
      in: header
      description: |
        The token value of the order (cart) if it exists
        Filters product variants based on the supplier of the products already in the cart
      required: false
      schema:
        type: string

    PageNumber:
      name: page
      in: query
      required: false
      description: The requested page number.
      schema:
        type: integer
        minimum: 1
        default: 1

    PageItemCount:
      name: itemsPerPage
      in: query
      required: false
      description: The number of items per page.
      schema:
        type: integer
        maximum: 100
        default: 30

    SkipItems:
      name: skip
      in: query
      required: false
      description: Skip the amount of items in the pagination.
      schema:
        type: integer
        minimum: 0
        default: 0

    LimitItems:
      name: limit
      in: query
      required: false
      description: The number of items per request.
      schema:
        type: integer
        maximum: 50
        default: 30

    FilterByChangedSince:
      name: changed_since
      in: query
      required: false
      description: Filter all items changed since the provided timestamp.
      schema:
        type: integer
        description: An UTC timestamp.
        minimum: 0

    ProductVariantEmbed:
      name: _embed[]
      in: query
      required: false
      allowReserved: true
      description: Embed additional data into the product variant objects.
      schema:
        type: array
        items:
          type: string
          enum:
            - product
            - productAssociations

    ProductVariantSortBy:
      name: sortBy[]
      in: query
      required: false
      allowReserved: true
      schema:
        type: array
        items:
          type: string
          description: |
            The sorting of a column with its direction.
            Consists of the column name to sort and the sorting direction.
          enum:
            - name:asc
            - name:desc
            - category:asc
            - category:desc
          default: name:asc

    FilterByRelatedConsultProductCode:
      name: filterByConsult
      in: query
      required: false
      description: Filter the products by the related consult product code.
      schema:
        type: string
        example: 'consult_erectile_dysfunction'

    FilterByProductAttributeType:
      name: filterByProductAttributeType
      in: query
      required: false
      description: Filter the products by its' attribute type.
      schema:
        type: string
        enum:
          - 'consult'
          - 'medication'
          - 'service'

    FilterByProductName:
      name: filterByProductName
      in: query
      required: false
      description: |
        Filter the products by product name. The language of the product name is determined
        by the `Accept-Language` header.
      schema:
        type: string
        minLength: 3

    PreprFilterProductsByName:
      name: q
      in: query
      required: false
      description: |
        Filter the products by product name. The language of the product name is determined
        by the `Accept-Language` header.
      schema:
        type: string
        minLength: 3

    PreprFilterTaxonsByName:
      name: q
      in: query
      required: false
      description: |
        Filter the taxons by name. The language of the taxon name is determined
        by the `Accept-Language` header.
      schema:
        type: string
        minLength: 3

    FilterByPrescriptionRequired:
      name: filterByPrescriptionRequired
      in: query
      required: false
      description: |
        Filter the products by whether the products are required with prescription
      schema:
        type: boolean

    FilterOrderByPrescriptionRequired:
      name: filterByPrescriptionRequired
      in: query
      description: |
        Filter the orders by prescription required or prescription not required.

        All the orders are returned when this filter isn't included in the request.
      required: false
      allowEmptyValue: false
      schema:
        type: boolean

    FilterByProductVariantNameDeprecated:
      deprecated: true
      name: search
      in: query
      required: false
      description: |
        Filter the product variants by name. The language of the product variant name is determined
        by the `Accept-Language` header.
      schema:
        type: string
        minLength: 3
      example: 'viagra'

    FilterByProductVariantName:
      name: filterByName
      in: query
      required: false
      description: |
        Filter the product variants by name. The language of the product variant name is determined
        by the `Accept-Language` header.
      schema:
        type: string
        minLength: 3
      example: 'viagra'

    FilterByProductVariantCodes:
      name: filterByCodes[]
      in: query
      required: false
      allowReserved: true
      description: Filter the product variants by code(s).
      schema:
        type: array
        items:
          type: string
          example: '7_5_viagra_25ml_4_tabl'

    FilterByInStock:
      name: filterByInStock
      in: query
      required: false
      description: |
        Filters the products by in stock or out of stock.

        All the products are returned when this filter isn't included in the request.
      schema:
        type: boolean

    FilterByPickupPoint:
      name: filterByPickupPoint
      in: query
      description: Only show (`true`) or exclude (`false`) pickup points in the list of addresses.
      schema:
        type: boolean

    SupplierIdentifier:
      deprecated: true
      name: supplier
      in: query
      required: false
      schema:
        type: string
      description: A supplier identifier which can be used to filter on supplier.
      example: 'apotheek-bad-nieuweschans'

    FilterBySupplierIdentifier:
      name: filterBySupplier
      in: query
      required: false
      description: |
        Filter the product variant by supplier. Expects the full supplier identifier.

        This filter is ignored when channel allows multiple suppliers.
      schema:
        type: string
        minLength: 3
      example: 'apotheek-bad-nieuweschans'

    AddressId:
      name: id
      in: path
      description: The unique ID of the address.
      required: true
      schema:
        type: integer

    SkipAddressValidation:
      name: skipAddressValidation
      in: query
      description: Skip the address validation.
      required: false
      schema:
        type: boolean

  schemas:
    AddItemToCart:
      type: object
      properties:
        productVariantCode:
          type: string
          example: propecia_tablet_1mg_28_pieces
        parentProductVariantCode:
          type: string
          description: |
            The product variant code that is specified as possible parent of the `productVariantCode`.
            This allows nesting of related order items or adding a preferred variant to an existing order item.
          example: consult_hair_loss
        quantity:
          type: integer
          minimum: 1
      additionalProperties: false
      required:
        - productVariantCode
        - quantity

    CreateCart:
      type: object
      properties:
        localeCode:
          type: string
          description: |
            The ISO 639-1 language code of the customer's language. When the localeCode is not specified,
            the default locale from the channel will be used.
          minLength: 2
          maxLength: 2
          example: 'en'
          externalDocs:
            url: https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes
        affiliateId:
          type: string

    Channel:
      type: object
      properties:
        code:
          type: string
          example: 'dok_nl'
        addPrescriptionMedicationDirectlyToCart:
          type: boolean
          description: Indicates whether prescription medication can be directly added to the cart.
        allowMultipleConsultsInCart:
          type: boolean
          default: true
          description: Indicates whether multiple consult products are allowed in the cart for this channel.
        pickupPointsAllowed:
          type: boolean
          default: false
          description: Indicates whether (DHL) pickup points are allowed to be selected as shipping address in this channel.
        baseCurrency:
          $ref: '#/components/schemas/Currency'
      required:
        - code
        - addPrescriptionMedicationDirectlyToCart
        - allowMultipleConsultsInCart
#        - pickupPointsAllowed # TODO change me to required once implemented
        - baseCurrency

    Country:
      type: object
      properties:
        code:
          type: string
          description: The unique ISO 3166-1 alpha-2 country code.
          example: NL
        name:
          type: string
          description: The localized name of the country.
          example: Netherlands
      required:
        - code
        - name

    CommunicationMessage:
      type: object
      properties:
        id:
          type: string
        author:
          type: object
          properties:
            type:
              type: string
              enum:
                - doctor
                - client
            name:
              type: string
              example: 'Dr. Gregory House'
        message:
          type: string
        createdAt:
          type: string
          format: date-time

    CreateCommunicationMessage:
      type: object
      properties:
        message:
          type: string
      required:
        - message

    OrderStatusUpdate:
      type: object
      properties:
        order:
          type: object
          properties:
            uuid:
              type: string
              example: 'b802d2f3-ff07-4712-a7fb-a77d09fdbad9'
            status:
              type: string
              enum:
                - new
                - reship
                - shipped
                - returned
                - cancelled
                - delivered
              example: 'shipped'
            eventType:
              type: string
              example: 'order_shipment.event.order_was_shipped'

    Currency:
      type: object
      properties:
        code:
          type: string
          example: EUR

    Customer:
      allOf:
      - $ref: '#/components/schemas/BaseCustomer'
      - type: object
        properties:
          email:
            type: string
            format: email
            example: <EMAIL>
          fullName:
            type: string
            example: John Doe
            readOnly: true
          gender:
            type: string
            description: |
              The gender of the customer.

              This can be one of the following values:
              - `m`: Male
              - `f`: Female
            enum:
              - m
              - f
          phoneNumber:
            type: string
            description: The phone number of the customer.
          subscribedToNewsletter:
            type: boolean
            default: false
          marketingSubscriptionUuid:
            type: string
            format: uuid
            readOnly: true

        required:
          - email
          - firstName
          - lastName
          - gender
          - birthday
          - phoneNumber

    CustomerWithId:
      allOf:
        - type: object
          properties:
            id:
              type: integer
              description: The unique ID of the customer.
          required:
            - id
        - $ref: '#/components/schemas/Customer'

    CustomerAccountInformation:
      allOf:
        - $ref: '#/components/schemas/BaseCustomer'
        - type: object
          subscribedToNewsletter:
            type: boolean
            example: true

    BaseCustomer:
      type: object
      properties:
        firstName:
          type: string
          minLength: 1
          maxLength: 35
          example: John
        lastName:
          type: string
          minLength: 1
          maxLength: 35
          example: Doe
        birthday:
          type: string
          format: date
        phoneNumber:
          type: string
          description: The phone number of the customer.

    CustomerAccountRegistration:
      allOf:
        - $ref: '#/components/schemas/Customer'
        - type: object
          properties:
            source:
              type: string
              default: account
              description: The source of the account creation.
            password:
              type: string
              minLength: 8
              writeOnly: true
              example: sEcretPassw0rd
            confirmNewPassword:
              type: string
              minLength: 8
              writeOnly: true
              example: sEcretPassw0rd
          required:
            - password
            - confirmNewPassword

    CustomerAccountAuthentication:
      type: object
      properties:
        email:
          type: string
          format: email
          example: <EMAIL>
        password:
          type: string
          example: test
      required:
        - email
        - password

    CustomerAccountPasswordReset:
      type: object
      properties:
        newPassword:
          type: string
          minLength: 8
        confirmNewPassword:
          type: string
          minLength: 8
      required:
        - newPassword
        - confirmNewPassword

    CustomerAccountPasswordChange:
      type: object
      properties:
        currentPassword:
          type: string
          example: sEcretPassw0rd
        newPassword:
          type: string
          minLength: 8
          example: neWsEcretPassw0rd
        confirmNewPassword:
          type: string
          minLength: 8
          example: neWsEcretPassw0rd
      required:
        - currentPassword
        - newPassword
        - confirmNewPassword

    CustomerWithEmbeddedUser:
      allOf:
        - $ref: '#/components/schemas/CustomerWithId'
        - type: object
          properties:
            defaultAddress:
              $ref: '#/components/schemas/Address'
            user:
              type: object
              properties:
                verified:
                  type: boolean
                  description: If the email address of the user is verified.

    CustomerAsWebhook:
      allOf:
        - $ref: '#/components/schemas/CustomerWithId'
        - type: object
          properties:
            phoneNumber:
              type: string
              example: '***********'
          required:
            - phoneNumber
        - $ref: '#/components/schemas/Localizable'
        - type: object
          properties:
            accountCompletionToken:
              type:
                - string
                - 'null'
              description: |
                Contains a (password reset) token created to complete the account by filling out a password.

                This token is generated when the customer/account is created by the Commerce System.
                Once the token is used by the customer, this property is emptied.
            totalOrderCount:
              type: integer
            totalOrderValue:
              type: integer
            totalOrderValueCurrencyCode:
              type: string
              description: The ISO 4217 currency code.
              example: EUR
              externalDocs:
                url: https://en.wikipedia.org/wiki/ISO_4217
            lastOrderAt:
              type: string
              format: date-time
          required:
            - accountCompletionToken
            - totalOrderCount
            - totalOrderValue
            - totalOrderValueCurrencyCode
        - $ref: '#/components/schemas/MarketingSubscriptionEmbeddedInCustomerAsWebhook'
        - $ref: '#/components/schemas/Timestampable'

    CustomerAsPasswordResetWebhook:
      type: object
      properties:
        firstName:
          type: string
          example: John
        lastName:
          type: string
          example: Doe
        email:
          type: string
          format: email
          example: <EMAIL>
        passwordResetToken:
          type: string
      required:
        - firstName
        - lastName
        - email
        - passwordResetToken

    Localizable:
      type: object
      properties:
        localeCode:
          type: string
          description: The ISO 639-1 language code.
          minLength: 2
          maxLength: 2
          example: nl
          externalDocs:
            url: https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes
        countryCode:
          type: string
          description: The ISO 3166-1 alpha-2 country code.
          minLength: 2
          maxLength: 2
          example: NL
          externalDocs:
            url: https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
      required:
        - localeCode
        - countryCode

    Timestampable:
      type: object
      properties:
        createdAt:
          type: string
          format: date-time
          readOnly: true
        updatedAt:
          type: string
          format: date-time
          readOnly: true
      required:
        - createdAt
        - updatedAt

    Cart:
      allOf:
        - $ref: '#/components/schemas/OrderCheckout'
        - $ref: '#/components/schemas/OrderLocalization'
        - $ref: '#/components/schemas/OrderMedicalQuestionnaire'
        - type: object
          description: A shopping cart session.
          properties:
            items:
              type: array
              items:
                $ref: '#/components/schemas/CartItem'
            promotionCoupon:
              $ref: '#/components/schemas/PromotionCoupon'
            shippingAddress:
              $ref: '#/components/schemas/ShippingAddress'
            billingAddress:
              $ref: '#/components/schemas/Address'
            payments:
              type: array
              items:
                $ref: '#/components/schemas/Payment'
            shipments:
              type: array
              items:
                $ref: '#/components/schemas/Shipment'
          required:
            - items
            - payments
            - shipments
        - $ref: '#/components/schemas/OrderTotals'

    BaseOrder:
      allOf:
        - type: object
          description: A placed order.
          properties:
            number:
              type: string
        - $ref: '#/components/schemas/OrderCheckout'

    Order:
      allOf:
        - $ref: '#/components/schemas/BaseOrder'
        - $ref: '#/components/schemas/OrderLocalization'
        - $ref: '#/components/schemas/OrderStates'
        - type: object
          properties:
            items:
              type: array
              items:
                $ref: '#/components/schemas/OrderItem'
              description: |
                Contains the items on the order.

                Removed items are still on the order with a property `modifiedSinceOriginalOrder: removed`.
            promotionCoupon:
              $ref: '#/components/schemas/PromotionCoupon'
            shippingAddress:
              $ref: '#/components/schemas/ShippingAddress'
            billingAddress:
              $ref: '#/components/schemas/Address'
            payments:
              type: array
              items:
                $ref: '#/components/schemas/Payment'
            shipments:
              type: array
              items:
                $ref: '#/components/schemas/Shipment'
        - $ref: '#/components/schemas/OrderTotals'
        - $ref: '#/components/schemas/OrderCancellation'
        - type: object
          properties:
            followUpOrder:
              $ref: '#/components/schemas/OrderEmbeddedAsFollowUp'

    OrderCancellation:
      type: object
      properties:
        cancellation:
          description: Information about the cancellation of the order.
          oneOf:
            - type: object
              properties:
                by:
                  type: string
                  enum:
                    - customer
                    - doctor
                    - system
                reason:
                  type: string
                  description: |
                    The value of the selected reason from a list of reasons or a custom filled out reason by the customer.
                  example: wrong_order
              required:
                - by
                - reason
            - type: object
              properties:
                by:
                  type: string
                  enum:
                    - customer-service
              required:
                - by

    OrderLocalization:
      type: object
      properties:
        currencyCode:
          type: string
          description: The ISO 4217 currency code.
          example: EUR
          externalDocs:
            url: https://en.wikipedia.org/wiki/ISO_4217
        localeCode:
          type: string
          description: The ISO 639-1 language code.
          example: en
          externalDocs:
            url: https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes
      required:
        - currencyCode
        - localeCode

    OrderCheckout:
      type: object
      properties:
        tokenValue:
          type: string
          example: pmyIXe7ANZ
        state:
          type: string
          enum:
            - cart
            - new
            - cancelled
            - fulfilled
          example: 'cart'
        channel:
          $ref: '#/components/schemas/Channel'
        checkoutState:
          type: string
          enum:
            - cart
            - medical_questionnaire_skipped
            - medical_questionnaire_completed
            - preferred_products_selected
            - delivery_service_selected
            - addressed
            - shipping_selected
            - shipping_skipped
            - payment_skipped
            - payment_selected
            - completed
        checkoutCompletedAt:
          type: string
          format: date-time
      required:
        - tokenValue
        - state
        - channel
        - checkoutState

    OrderMedicalQuestionnaire:
      type: object
      properties:
        medicalQuestionnaire:
          type: object
          properties:
            uuid:
              type: string
              format: uuid
          required:
            - uuid

    OrderStates:
      type: object
      properties:
        paymentState:
          type: string
          enum:
            - cart
            - awaiting_payment
            - partially_authorized
            - authorized
            - partially_paid
            - paid
            - refunded
            - cancelled
        prescriptionState:
          type: string
          enum:
            - cart
            - awaiting_payment
            - ready_for_consult
            - pending
            - waiting_for_response
            - approved
            - declined
            - cancelled
            - skipped
        shippingState:
          type: string
          enum:
            - cart
            - awaiting_payment
            - awaiting_prescription
            - ready
            - pending
            - shipped
            - partially_shipped
            - returned
            - partially_returned
            - cancelled
        aftercareState:
          type: string
          enum:
            - cart
            - awaiting_shipment
            - opened
            - waiting_for_doctor_response
            - doctor_responded
            - closed
            - skipped
      required:
        - paymentState
        - prescriptionState
        - shippingState

    OrderTotals:
      type: object
      properties:
        itemsTotal: # Total of order items WITH adjustments.
          type: integer
        shippingTotal:
          type: integer
        orderPromotionTotal:
          type: integer
        taxTotal:
          type: integer
        subtotal: # Total of order items WITHOUT adjustments (unit price * quantity for all order items).
          type: integer
        total:
          type: integer
      required:
        - itemsTotal
        - shippingTotal
        - orderPromotionTotal
        - taxTotal
        - subtotal
        - total

    OrderItemAsRemoveWebhook:
      type: object
      properties:
        id:
          type: integer
          description: The unique ID of a cart- or order item.

    CustomerEmbeddedInOrder:
      type: object
      properties:
        email:
          type: string
          format: email
          example: <EMAIL>
        totalOrderCount:
          type: integer
        totalOrderValue:
          type: integer
      required:
        - email
        - totalOrderCount
        - totalOrderValue

    OrderAsWebhook:
      allOf:
        - type: object
          properties:
            id:
              type: integer
          required:
            - id
        - $ref: '#/components/schemas/BaseOrder'
        - type: object
          properties:
            customer:
              $ref: '#/components/schemas/CustomerEmbeddedInOrder'
        - $ref: '#/components/schemas/OrderStates'
        - $ref: '#/components/schemas/OrderLocalization'
        - type: object
          description: A cart session or a placed order.
          properties:
            cancelledBy:
              type: string
              description: The type of user that has cancelled the order.
              enum:
                - customer
                - customer-service
                - doctor
                - system
            doctorName:
              type: string
              description: The name of the assigned or approving doctor.
              example: 'Dr. Gregory House'
            items:
              type: array
              items:
                $ref: '#/components/schemas/OrderItemAsWebhook'
            paymentMethodName:
              deprecated: true
              type: string
              description: The name of the payment method of the last payment
              example: 'iDeal'
            paymentMethod:
                type: object
                properties:
                    name:
                      type: string
                      description: The name of the payment method of the last payment
                      example: 'iDeal'
                    code:
                      type: string
                      description: The code of the payment method of the last payment
                      example: 'ideal'
            shippingAddress:
              $ref: '#/components/schemas/ShippingAddress'
            billingAddress:
              $ref: '#/components/schemas/Address'
            shipments:
              type: array
              items:
                $ref: '#/components/schemas/ShipmentAsWebhook'
          required:
            - items
            - shipments
        - $ref: '#/components/schemas/OrderTotals'
        - type: object
          properties:
            orderPromotions:
              type: array
              items:
                $ref: '#/components/schemas/OrderPromotionAsWebhook'
          required:
            - orderPromotions
        - $ref: '#/components/schemas/Timestampable'

    OrderPromotionAsWebhook:
      type: object
      properties:
        type:
          type: string
          enum:
            - order_promotion
            - order_item_promotion
            - order_shipping_promotion
        promotion:
          type: object
          properties:
            code:
              type: string
              example: 'christmas_2022'
            name:
              type: string
              example: 'Christmas discount 2022'
          required:
            - code
            - name
        couponCode:
          type: string
          example: 'KERST2022'
        amount:
          type: integer
          example: 500
        currencyCode:
          type: string
          example: 'EUR'
      required:
        - type
        - promotion
        - amount
        - currencyCode

    OrderEmbeddedAsFollowUp:
      allOf:
        - $ref: '#/components/schemas/BaseOrder'
        - type: object
          properties:
            channel:
              properties:
                code:
                  example: 'blueclinic_nl'

    OrderWithEmbeddedCustomer:
      allOf:
        - $ref: '#/components/schemas/Order'
        - $ref: '#/components/schemas/OrderMedicalQuestionnaire'
        - type: object
          properties:
            customer:
              $ref: '#/components/schemas/CustomerWithId'
            prescriptionStateUpdatedAt:
              type: string
              format: date-time
          required:
            - customer
            - prescriptionStateUpdatedAt

    OrderCompletionInformation:
      type: object
      description: Basic order data which is needed after a payment.
      properties:
        number:
          type: string
        paymentState:
          type: string
          enum:
            - cart
            - awaiting_payment
            - partially_authorized
            - authorized
            - partially_paid
            - paid
            - refunded
            - cancelled
        payments:
          type: array
          items:
            $ref: '#/components/schemas/Payment'
        customer:
          type: object
          properties:
            email:
              type: string

    BaseOrderItem:
      type: object
      properties:
        id:
          type: integer
          minimum: 1
        variant:
          $ref: '#/components/schemas/ProductVariantEmbeddedAsVariantInOrderItem'
        productName:
          type: string
          example: Paracetamol
        variantName:
          type: string
          example: Paracetamol 500mg 30 tablets
        mainTaxon:
          type: object
          properties:
            code:
              type: string
              example: headache

    OrderItemTotals:
      type: object
      properties:
        quantity:
          type: integer
          minimum: 1
        unitPrice:
          type: integer
        subtotal:
          type: integer
        total:
          type: integer

    CartItem:
      allOf:
        - $ref: '#/components/schemas/BaseOrderItem'
        - type: object
          properties:
            preferredVariants:
              type: array
              items:
                $ref: '#/components/schemas/ProductVariantEmbeddedAsPreferredVariantInCartItem'
              maxItems: 1
            warnings:
              type: array
              description: |
                Warnings to be shown in the shopping cart. When warnings are set on an order item the user
                is not allowed to proceed with the checkout.
              items:
                type: string
          required:
            - warnings
        - $ref: '#/components/schemas/OrderItemTotals'
        - type: object
          properties:
            parentOrderItem:
              $ref: '#/components/schemas/ParentOrderItem'

    OrderItem:
      allOf:
        - $ref: '#/components/schemas/BaseOrderItem'
        - type: object
          properties:
            preferredVariants:
              type: array
              description: |
                Contains the variants preferred by the customer and after doctor approval the prescribed variants.

                Removed preferred variants are still on the order with a property `modifiedSinceOriginalOrder: removed`.
              items:
                $ref: '#/components/schemas/ProductVariantEmbeddedAsPreferredVariantInOrderItem'
        - $ref: '#/components/schemas/OrderItemTotals'
        - type: object
          properties:
            usageAdvice:
              type: string
            refundReason:
              type: string
              description: Indicates if and why the order item is refunded.
              enum:
                - out_of_stock
                - incorrect_delivery
                - damaged_delivery
                - returned
            modifiedSinceOriginalOrder:
              type: string
              enum:
                - added
                - removed
              description: |
                Indicates if the order item is added or removed from the original order. This occurs when a
                doctor prescribes different medication.

                Unmodified order items will not have this property.
            parentOrderItem:
              $ref: '#/components/schemas/ParentOrderItem'

    OrderItemAsWebhook:
      allOf:
        - $ref: '#/components/schemas/BaseOrderItem'
        - $ref: '#/components/schemas/OrderItemTotals'
        - type: object
          properties:
            previouslyPrescribed:
              type: string
              description: Indicates if the customer has received this product on a previous order.
              enum:
                - Yes
                - No
            previouslyPrescribedIntervalDays:
              type: integer
              description: The amount of days ago this product was prescribed on a previous order.
            parentOrderItem:
              $ref: '#/components/schemas/ParentOrderItem'

    ParentOrderItem:
      type: object
      properties:
        id:
          type: integer
          minimum: 1
        variant:
          $ref: '#/components/schemas/ProductVariantEmbeddedAsVariantInParentOrderItem'

    ProductCode:
      type: object
      properties:
        code:
          type: string
          description: The unique identifier of this product.
          example: '7'
      required:
        - code

    ProductName:
      type: object
      properties:
        name:
          type: string
          description: The name translation of this product based on the locale provided with the request.
          example: Viagra
      required:
        - name

    BaseProduct:
      allOf:
        - $ref: '#/components/schemas/ProductCode'
        - type: object
          properties:
            slug:
              type: string
              description: The slug translation of this product based on the locale provided with the request.
              pattern: '[a-z-]+'
              example: viagra
        - $ref: '#/components/schemas/ProductName'

    ProductEmbedded:
      allOf:
        - $ref: '#/components/schemas/BaseProduct'
        - $ref: '#/components/schemas/ProductVariantSelectionMethod'
        - type: object
          properties:
            defaultVariant:
              $ref: '#/components/schemas/ProductVariantEmbeddedAsDefaultVariantInProduct'
            images:
              type: array
              items:
                $ref: '#/components/schemas/ProductImage'

    Product:
      allOf:
        - $ref: '#/components/schemas/BaseProduct'
        - $ref: '#/components/schemas/ProductVariantSelectionMethod'
        - type: object
          properties:
            channel:
              $ref: '#/components/schemas/Channel'
            options:
              type: array
              description: The list of product options sorted by position.
              items:
                $ref: '#/components/schemas/ProductOption'
            startingPrice:
              $ref: '#/components/schemas/Price'
            inStock:
              type: boolean
              example: true
            variants:
              type: array
              items:
                $ref: '#/components/schemas/ProductVariantEmbeddedInProduct'
            associations:
              type: object
              additionalProperties:
                $ref: '#/components/schemas/ProductAssociation'
              example:
                consult_product:
                  associatedProducts:
                    - code: consult_erectile_dysfunction
                      slug: consult-erectile-dysfunction
                      name: Consult for erectile dysfunction
                      defaultVariant:
                        code: consult_erectile_dysfunction
                        name: Consult for erectile dysfunction
                        maximumQuantityPerOrder: 1
                        price:
                          amount: 2900
                          currency: EUR
                      images: []

    ProductAsFeed:
      allOf:
        - $ref: '#/components/schemas/ProductCode'
        - $ref: '#/components/schemas/ProductName'
        - type: object
          properties:
            taxons:
              type: array
              description: The taxons of the product.
              example: 'Erectile dysfunction'
              items:
                type: string
            inStock:
              type: boolean
              description: The availability of the product.
              example: true
            condition:
              type: string
              description: The condition of the product.
              enum:
                - new
            productPageUrl:
              type: string
              format: url
              description: The URL to the product page.
              example: 'https://www.example.com/products/viagra'
            productImageUrl:
              type: string
              format: url
              description: The URL to the product image.
              example: 'https://www.example.com/assets/viagra.jpg'
            metaDescription:
              type: string
              description: The meta description of the product.
              example: 'Find Viagra on Dokteronline ✔️ Reliable prescription from a registered doctor. ✔️ Delivered quickly and discreetly to your door.'
            startingPrice:
              allOf:
                - type: object
                  properties:
                    displayAmount:
                      type: string
                      example: '29.00'
                  required:
                    - displayAmount
                - $ref: '#/components/schemas/Price'
                - example:
                    displayAmount: '44.00'
                    amount: 4400
                    currency: GBP
            shipping:
              type: object
              title: ProductShipping
              properties:
                countryCode:
                  type: string
                  description: The ISO 3166-1 alpha-2 country code based on the requested feed.
                  example: GB
                price:
                  allOf:
                    - type: object
                      properties:
                        displayAmount:
                          type: string
                          example: '2.95'
                      required:
                        - displayAmount
                    - $ref: '#/components/schemas/Price'
                    - example:
                        displayAmount: '2.95'
                        amount: 295
                        currency: GBP
              required:
                - countryCode
                - price
            attributes:
              type: object
              title: ProductAttributes
              properties:
                form:
                  type: array
                  items:
                      type: string
                      example: 'Tablet'
                usage:
                  type: string
                  example: 'Once a day'
                modeOfAction:
                  type: string
                  example: 'Oral'
                possibleWeightloss:
                  type: string
                  example: 'up to 20%'
                effectivenessDelay:
                  type: string
                  example: '30-60 minutes'
                effectivenessPeriod:
                  type: string
                  example: '4 hours'
                effectivenessPeak:
                  type: string
                  example: '1 hour'
                effect:
                  type: string
                  example: 'Relaxes muscles around the airways'
              required:
                - form
          required:
            - inStock
            - condition
            - startingPrice
            - shipping
            - attributes

    ProductAsWebhook:
      type: object
      properties:
        code:
          type: string
          description: The unique identifier of this product.
          example: '7'
        nameByLocale:
          type: object
          patternProperties:
            '^[a-z]{2}(-[a-z]{2})?$':
              type: string
              description: The name of product for a locale.
          additionalProperties: false
          example:
            en: Viagra
            nl: Viagra
        inStockByChannel:
          type: object
          patternProperties:
            '^[_a-z]+$':
              type: boolean
              description: The availability of the product for a channel.
          additionalProperties: false
          example:
            dok_nl: false
            dok_de: true
      additionalProperties: false

    ProductVariantSelectionMethod:
      type: object
      properties:
        variantSelectionMethod:
          type: string
          description: |
            The method of selecting a product variant for this product.

            The following methods are available:

            - `choice`: A list of all variants is displayed to user
            - `match`: Each product option is displayed as choice. The User selects the values and we match them to a variant
          enum:
            - choice
            - match
      required:
        - variantSelectionMethod

    ProductAssociationWithBaseProducts:
      type: object
      properties:
        associatedProducts:
          type: array
          items:
            $ref: '#/components/schemas/BaseProduct'

    ProductAssociation:
      type: object
      properties:
        associatedProducts:
          type: array
          items:
            $ref: '#/components/schemas/ProductEmbedded'

    ProductInList:
      allOf:
        - $ref: '#/components/schemas/ProductEmbedded'
        - type: object
          properties:
            mainTaxon:
              $ref: '#/components/schemas/TaxonEmbedded'
            taxons:
              type: array
              items:
                $ref: '#/components/schemas/TaxonEmbedded'
        - $ref: '#/components/schemas/Timestampable'

    EmbeddedProductInProductVariant:
      allOf:
        - $ref: '#/components/schemas/BaseProduct'
        - type: object
          properties:
            images:
              type: array
              items:
                $ref: '#/components/schemas/ProductImage'
            mainTaxon:
              $ref: '#/components/schemas/TaxonEmbedded'
            taxons:
              type: array
              items:
                $ref: '#/components/schemas/TaxonEmbedded'
            type:
              type: string
              description: The product type.
              example: 'consult'
              readonly: true
          required:
            - images
            - mainTaxon
            - taxons

    PreprProductList:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/PreprProduct'
        total:
          type: integer
          minimum: 0
      required:
        - items
        - total

    PreprProduct:
      type: object
      properties:
        id:
          type: string
          description: The unique code of the product.
          example: '7'
        body:
          type: string
          description: The title of the product.
          example: 'Viagra'
        image_url:
          type: string
          format: url
        data:
          type: object
          description: |
            Key-value pairs with information from the product that are included in the GraphQL API of the Prepr CMS.
          properties:
            product_type:
              type: string
              description: The type of product.
              example: 'medication'
            prescription_required:
              type: boolean
              description: Whether the product requires a prescription
              example: true
          patternProperties:
            '^(country_availability_)[a-z]{2}$':
              type: boolean
              description: |
                The availability of the product in a country.

                The last 2 letters of this property is the ISO-3166 country code.
            '^(starting_price_)[a-z]{2}$':
              type: integer
              description: |
                The starting price in cents for a country.

                The last 2 letters of this property is the ISO-3166 country code.
              minimum: 0
            '^(starting_price_currency_)[a-z]{2}$':
              type: string
              description: |
                The currency of the starting price for a country.

                The last 2 letters of this property is the ISO-3166 country code.
              example: 'EUR'
          example:
            product_type: 'medication'
            country_availability_de: true
            starting_price_de: 6940
            starting_price_currency_de: 'EUR'
        created_on:
          type: string
          format: date-time
        changed_on:
          type: string
          format: date-time
      required:
        - id
        - body
        - data
        - created_on
        - changed_on

    ProductOption:
      type: object
      properties:
        code:
          type: string
          example: 'form'
        position:
          type: integer
          format: int32
        translations:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Translation'
          example:
            en:
              name: Form
            nl:
              name: Vorm
        values:
          type: array
          items:
            $ref: '#/components/schemas/ProductOptionValue'

    ProductOptionValue:
      type: object
      properties:
        code:
          type: string
          example: effervescent_tablet
        translations:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ProductOptionValueTranslation'
          example:
            en:
              name: Effervescent tablet
            nl:
              name: Bruistablet

    ProductImage:
      type: object
      properties:
        path:
          type: string
          format: url

    ProductVariantWithCode:
      type: object
      properties:
        code:
          type: string
      required:
        - code

    BaseProductVariant:
      allOf:
        - $ref: '#/components/schemas/ProductVariantWithCode'
        - type: object
          properties:
            name:
              type: string
              description: The name translation of this product variant based on the locale provided with the request.
            caption:
              type: string
              description: The caption translation of this product variant based on the locale provided with the request.
            maximumQuantityPerOrder:
              type: integer
              minimum: 1
            image:
              $ref: '#/components/schemas/ProductImage'
            leaflet:
              type: string
              format: url
              description: The PDF leaflet of this product variant based on the locale provided with the request.
              example: https://downloads.dokteronline.com/leaflets/en/viagra-leaflet.pdf
          required:
            - name
            - maximumQuantityPerOrder

    ProductVariantSupplier:
      type: object
      properties:
        supplier:
          $ref: '#/components/schemas/Supplier'

    ProductVariantAttributes:
      description: Extra attributes of the product variant.
      type: object
      properties:
        attributes:
            type: object
            zIndex:
              type: string
              example: 'Z0007'

    ProductVariant:
      allOf:
        - $ref: '#/components/schemas/BaseProductVariant'
        - type: object
          properties:
            prescriptionRequired:
              type: boolean
              description: Indicates if the product variant requires a prescription from a doctor consultation.
            price:
              allOf:
                - description: The price of the product variant in the provided channel.
                - $ref: '#/components/schemas/Price'
        - $ref: '#/components/schemas/ProductVariantSupplier'
        - $ref: '#/components/schemas/ProductVariantAttributes'
        - type: object
          properties:
            defaultUsageAdvice:
              type: string
              description: |
                The default usage advice to be used as a template for doctors. The translation is based on
                the locale provided with the request.
            _embedded:
              type: object
              description: Properties embeddable by specifing them in the `_embed` query parameter.
              properties:
                product:
                  $ref: '#/components/schemas/EmbeddedProductInProductVariant'
                productAssociations:
                  type: object
                  additionalProperties:
                    $ref: '#/components/schemas/ProductAssociationWithBaseProducts'
                  example:
                    consult_product:
                      associatedProducts:
                        - code: consult_erectile_dysfunction
                          slug: consult-erectile-dysfunction
                          name: Consult for erectile dysfunction

    ProductVariantEmbeddedAsPreferredVariantInCartItem:
      allOf:
        - $ref: '#/components/schemas/ProductVariantEmbedded'
        - type: object
          properties:
            product:
              type: object
              properties:
                code:
                  type: string

    ProductVariantEmbeddedAsPreferredVariantInOrderItem:
      allOf:
        - $ref: '#/components/schemas/ProductVariantEmbedded'
        - $ref: '#/components/schemas/ProductVariantSupplier'
        - type: object
          properties:
            product:
              type: object
              properties:
                code:
                  type: string
            modifiedSinceOriginalOrder:
              type: string
              enum:
                - added
                - removed
              description: |
                Indicates if the preferred variant is added or removed from the original order. This occurs when a
                doctor prescribes different medication.

                Unmodified preferred variants will not have this property.
            quantity:
              type: integer
              minimum: 1
            usageAdvice:
              type: string
          required:
            - quantity

    ProductVariantEmbeddedAsVariantInParentOrderItem:
      allOf:
        - $ref: '#/components/schemas/ProductVariantWithCode'

    ProductVariantEmbeddedAsVariantInOrderItem:
      allOf:
        - $ref: '#/components/schemas/ProductVariantEmbedded'
        - $ref: '#/components/schemas/ProductVariantSupplier'
        - type: object
          properties:
            prescriptionRequired:
              type: boolean
              description: Indicates if the product variant requires a prescription from a doctor consultation.
            productAttributes:
              deprecated: true
              type: object
              properties:
                type:
                  description: The type of product or service.
                  type: string
                  enum:
                    - 'consult'
                    - 'medication'
                    - 'service'
            product:
              type: object
              properties:
                code:
                  type: string
                attributes:
                  type: object
                  properties:
                    type:
                      description: The type of product or service.
                      type: string
                      enum:
                        - 'consult'
                        - 'medication'
                        - 'service'
                  required:
                    - type
              required:
                - code
                - attributes
          required:
            - prescriptionRequired
            - product

    ProductVariantEmbedded:
      allOf:
        - $ref: '#/components/schemas/BaseProductVariant'
        - type: object
          properties:
            price:
              allOf:
                - description: |
                    The price for the specified channel.
                    The channel is determined by providing the channel code with the request or
                    based on the channel on the order.
                - $ref: '#/components/schemas/Price'

    ProductVariantEmbeddedAsDefaultVariantInProduct:
      allOf:
        - $ref: '#/components/schemas/ProductVariantEmbedded'

    ProductVariantEmbeddedInProduct:
      allOf:
        - $ref: '#/components/schemas/BaseProductVariant'
        - type: object
          properties:
            optionValues:
              type: object
              additionalProperties:
                type: string
                description: The map with linked option value codes. The key is the code of the related product option.
              example:
                form: effervescent_tablet
            prescriptionRequired:
              type: boolean
              description: Indicates if the product variant requires a prescription from a doctor consultation.
            price:
              allOf:
                - description: The price of the product variant in the provided channel.
                - $ref: '#/components/schemas/Price'
        - $ref: '#/components/schemas/ProductVariantSupplier'

    ProductInEnrichmentList:
      type: object
      properties:
        code:
          type: string
          description: The unique identifier of this product.
        enabledForChannels:
          type: array
          items:
            type: string
            example: dok_nl
        startingPrices:
          type: object
          description: |
            The list with channels the product is enabled in with its product variant starting price.

            A product is out of stock when no starting price exists for an enabled channel.
          additionalProperties:
            $ref: '#/components/schemas/Price'
          example:
            dok_nl:
              amount: 1234
              currency: EUR
        taxons:
          type: array
          items:
            $ref: '#/components/schemas/TaxonWithProductTaxonChannels'

    Price:
      type: object
      title: Price
      properties:
        amount:
          type: integer
          description: The amount in cents.
          example: 2900
        currency:
          type: string
          description: The ISO 4217 currency code.
          example: EUR
      required:
        - amount
        - currency

    PromotionCoupon:
      type: object
      properties:
        code:
          type: string
        expiresAt:
          type: string
          format: date-time
          readOnly: true

    BaseAddress:
      type: object
      properties:
        street:
          type: string
          example: Bucketblock 1
        postcode:
          type: string
          example: 1234 AB
          maxLength: 10
        city:
          type: string
          maxLength: 35
          example: Old Gasstove
        countryCode:
          type: string
          description: The ISO 3166-1 alpha-2 country code.
          minLength: 2
          maxLength: 2
          example: NL
          externalDocs:
            url: https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
      required:
        - street
        - postcode
        - city
        - countryCode

    Address:
      allOf:
        - type: object
          title: Address
          properties:
            id:
              type: integer
              example: 1
              readOnly: true
            firstName:
              type: string
              maxLength: 35
              example: John
            lastName:
              type: string
              maxLength: 35
              example: Doe
            fullName:
              type: string
              example: John Doe
              readOnly: true
            phoneNumber:
              type: string
              example: '***********'
              maxLength: 20
            company:
              type: string
              example: EHVG
#          required:
#            - id
        - $ref: '#/components/schemas/BaseAddress'
        - type: object
          properties:
            provinceCode:
              type: string
              example: NB
              deprecated: true
            provinceName:
              type: string
              example: Noord-Brabant
              deprecated: true
            createdAt:
              type: string
              format: date-time
              readOnly: true
            updatedAt:
              type: string
              format: date-time
              readOnly: true
#          required:
#            - createdAt
#            - updatedAt

    ShippingAddress:
      anyOf:
        - $ref: '#/components/schemas/Address'
        - $ref: '#/components/schemas/ServicePointAddress'

    ServicePointAddress:
      allOf:
        - $ref: '#/components/schemas/Address'
        - type: object
          title: ServicePointAddress
          properties:
            additionalAddressInformation:
              type: string
              description: The ID of the (DHL) ServicePoint or the PostNumber of the customer. PostNumber is only used in Germany.
              maxLength: 100
              examples: [
                'DHL Parcelshop NL-110603',
                '987654321'
              ]
            servicePoint:
              type: object
              title: ServicePoint
              properties:
                locationIds:
                  type: array
                  description: The location IDs of the (DHL) ServicePoint.
                  items:
                    type: object
                    properties:
                      locationId:
                        type: string
                        description: The ID of the location for the specified business unit within DHL.
                        example: '8004-NL-110151'
                      provider:
                        type: string
                        description: The business unit of the DHL ServicePoint location.
                        examples:
                          - 'parcel'
                          - 'express'
            pickupPointAddress:
              description: The address of the (DHL) ServicePoint as shown to the customer. This address is not sent to a supplier.
              allOf:
                - type: object
                  properties:
                    name:
                      type: string
                      example: 'Akash Multi Plaza 14B'
                  required:
                    - name
                - $ref: '#/components/schemas/BaseAddress'

    AddressBookAddress:
      allOf:
        - $ref: '#/components/schemas/ShippingAddress'
        - type: object
          properties:
            defaultAddress:
              type: boolean
              default: false
              description: Indicates if this address is the default address of the customer.
          required:
            - defaultAddress

    UpdatePayment:
      type: object
      properties:
        paymentMethod:
          type: string
          description: |
            The unique code of the payment method. The codes can be retrieved through the
            'List available payment methods' endpoint.
        issuerCode:
          deprecated: true
          type: string
          enum:
            - ABNANL2A
            - ASNBNL21
            - BUNQNL2A
            - FRBKNL2L
            - INGBNL2A
            - KNABNL2H
            - MOYONL21
            - RABONL2U
            - RBRBNL21
            - SNSBNL2A
            - TRIONL2U
            - FVLBNL22
            - REVOGB2L
          description: |
            The Bank Identification Code (BIC) of a bank that is supported by the iDeal payment method.
      additionalProperties: false
      required:
        - paymentMethod

    Payment:
      type: object
      properties:
        id:
          type: integer
          minimum: 1
        state:
          type: string
          enum:
            - cart
            - new
            - processing
            - authorized
            - completed
            - cancelled
            - failed
            - refunded
        method:
          $ref: '#/components/schemas/PaymentMethod'
        paymentProvider:
          $ref: '#/components/schemas/PaymentProvider'
        amount:
          type: integer
          description: The amount in cents.
          example: 2900
        details:
          type: object
          # additionalProperties: true
      required:
        - id
        - state
        - amount

    PaymentMethod:
      allOf:
        - $ref: '#/components/schemas/PaymentMethod.Base'
        - type: object
          properties:
            issuers:
              deprecated: true
              type: array
              items:
                type: string
              description: The Bank Identification Code (BIC) list of the banks that are supported by the iDeal payment method.

    PaymentMethod.List:
      allOf:
        - $ref: '#/components/schemas/PaymentMethod.Base'
        - type: object
          properties:
            available:
              type: boolean
              default: true
              description: |
                Indicates if the payment method is available for the order.

                This property is only available when the `tokenValue` query parameter is provided.
            unavailableDueTo:
              type: array
              items:
                type: string
                enum:
                  - 'different_address_not_allowed'
                  - 'maximum_order_amount_reached'
              description: |
                Provides the reason why a specific payment method is unavailable. One or more of the following reasons may apply:

                - `different_address_not_allowed`: The billing and shipping addresses differ, which is not permitted for this payment method.
                - `maximum_order_amount_reached`: The order total exceeds the maximum amount allowed for this payment method.

                **Note:** This property is only included in the response when the tokenValue query parameter is provided.
          required:
            - available

    PaymentMethod.Base:
      type: object
      properties:
        code:
          type: string
          description: The identifier of the payment method.
          example: ideal
        name:
          type: string
          description: The human readable name of the payment method.
          example: iDeal
        instructions:
          type: string
          description: Additional instructions on how to pay with this payment method.
        icon:
          type: string
          description: The identifier for the icon of the payment method.
          example: ideal
      required:
        - code
        - name
        - icon

    PaymentProvider:
      type: object
      properties:
        identifier:
          type: string
          description: |
            The provider used for the payment. This can be used to determine the correct SDK where the customer should lead to
            after selecting the payment method. The provider is known when the payment is started.
          enum:
            - mollie_payment
            - mollie_order
            - stripe
            - cm_payments
            - paypal
            - klarna
        paymentMethodIdentifier:
          type: string
          description: |
            The identifier used to determine the payment method at the provider. This can be used to select the payment method
            at the provider. The identifier is known when the payment is started and is not the same as the payment method code.
          example: google_pay

    Shipment:
      type: object
      properties:
        id:
          type: integer
          minimum: 1
        state:
          type: string
          enum:
            - cart
            - awaiting_payment
            - awaiting_prescription
            - ready
            - pending
            - shipped
            - returned
            - cancelled
        method:
          $ref: '#/components/schemas/ShippingMethod'
        items:
          type: array
          description: The names and types of the product variants that are part of this shipment.
          items:
            type: object
            properties:
              name:
                type: string
                example: "Viagra Connect 50 mg 8 tabl."
              type:
                type: string
                example: "medication"
          example:
            - name: "Viagra Connect 50 mg 8 tabl."
              type: "medication"
            - name: "Paracetamol"
              type: "medication"
        tracking:
          type: string
        trackAndTraceLink:
          type: string
      required:
        - id
        - state
        - method

    ShipmentAsWebhook:
      type: object
      properties:
        id:
          type: integer
          minimum: 1
        state:
          type: string
          enum:
            - cart
            - awaiting_payment
            - awaiting_prescription
            - ready
            - pending
            - processing
            - shipped
            - returned
            - cancelled
        methodName:
          type: string
          description: The name of the shipment method or carrier.
          example: DHL
        supplierName:
          type: string
          description: The name of the supplier sending the shipment.
          example: 'Apotheek Bad Nieuweschans'
        trackingCode:
          type: string
          description: The track and trace code for the customer to track the shipment process at the carrier.
        trackingUrl:
          type: string
          format: url
          description: The URL to track and trace page of the carrier. This URL also contains the track and trace code.

    ShippingMethod:
      type: object
      properties:
        translations:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Translation'
          example:
            en:
              name: DHL

    Supplier:
      type: object
      properties:
        identifier:
          type: string
          example: 'apotheek-bad-nieuweschans'
        name:
          type: string
          example: 'Apotheek Bad Nieuweschans'

    TermQuestion:
      type: object
      properties:
        code:
          type: string
        name:
          type: string

    Translation:
      type: object
      properties:
        name:
          type: string

    ProductOptionValueTranslation:
      allOf:
        - $ref: '#/components/schemas/Translation'
        - type: object
          properties:
            value:
              type: string

    TaxonList:
      type: array
      items:
        $ref: '#/components/schemas/Taxon'

    PreprTaxonList:
      type: object
      properties:
        items:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                description: The unique code of the taxon.
              body:
                type: string
                description: The title of the taxon.
              created_on:
                type: string
                format: date-time
              changed_on:
                type: string
                format: date-time
            required:
              - id
              - body
              - created_on
              - changed_on
        total:
          type: integer
          minimum: 0
      required:
        - items
        - total

    Taxon:
      type: object
      properties:
        code:
          type: string
          description: The unique code of the taxon.
          example: hair_loss
        translations:
          type: object
          description: The translations of the taxon identified by the ISO 639-1 language code.
          additionalProperties:
            $ref: '#/components/schemas/TaxonTranslation'
          example:
            en:
              name: Hair loss
              slug: hair-loss
              description: |
                Hair loss is a common and distressing problem for many men and women. Read more or order hair loss
                treatments from our partner pharmacies here.
            nl:
              name: Haarverlies
              slug: haarverlies
              description: |
                Lees hier meer informatie over haaruitval en haarverlies en welke behandelingen er mogelijk zijn om
                haarverlies te verminderen.
      required:
        - code
        - translations

    TaxonWithProductTaxonChannels:
      allOf:
        - $ref: '#/components/schemas/Taxon'
        - type: object
          properties:
            productTaxonChannels:
              type: object
              description: |
                The taxon related product information for a channel identified by the unique code of the channel.
              additionalProperties:
                $ref: '#/components/schemas/ProductTaxonChannel'
              example:
                dok_nl:
                  relevance: 10
                  label: new

    TaxonTranslation:
      allOf:
        - $ref: '#/components/schemas/Translation'
        - type: object
          properties:
            slug:
              type: string
              pattern: '[a-z-]+'
            description:
              type: string

    ProductTaxonChannel:
      type: object
      properties:
        relevance:
          type: integer
        label:
          type: string

    TaxonEmbedded:
      type: object
      properties:
        code:
          type: string
          description: The unique code of the taxon.
          example: hair_loss
        slug:
          type: string
          pattern: '[a-z-]+'
          example: hair-loss
        name:
          type: string
          example: Hair loss

    Error:
      type: object
      properties:
        code:
          type: integer
        message:
          type: string
      required:
        - message

    UpdateShipment:
      type: object
      properties:
        trackAndTraceCode:
          type: string
          description: |
            Track and trace code which can be used to track the shipment.
          example: 'AX1234-ERWQEW-2222'
        carrierCode:
          type: string
          description: |
            The carrier code should match the shipping method code in the checkout service.
          example: 'DHL_EXPRESS'

    CreateMarketingSubscription:
      allOf:
        - type: object
          properties:
            uuid:
              type: string
              format: uuid
              readOnly: true
            email:
              type: string
              format: email
            firstName:
              type: string
              example: John
            lastName:
              type: string
              example: Doe
          required:
            - email
        - $ref: '#/components/schemas/Localizable'
        - type: object
          properties:
            optInEmailSource:
              type: string
              example: website

    ReplaceMarketingSubscription:
      allOf:
        - $ref: '#/components/schemas/Localizable'
        - type: object
          properties:
            optInEmail:
              $ref: '#/components/schemas/UpdateEmailSubscription'
          required:
            - optInEmail

    MarketingSubscription:
      type: object
      properties:
        uuid:
          type: string
          format: uuid
          readOnly: true
        email:
          type: string
          format: email
          readOnly: true
        optInEmail:
          $ref: '#/components/schemas/EmailSubscription'
        optInShareProductData:
          $ref: '#/components/schemas/EmailSubscriptionShareProductData'
        localeCode:
          type: string
          description: The ISO 639-1 language code.
          minLength: 2
          maxLength: 2
          example: nl
          externalDocs:
            url: https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes
        countryCode:
          type: string
          description: The ISO 3166-1 alpha-2 country code.
          minLength: 2
          maxLength: 2
          example: NL
          externalDocs:
            url: https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
        createdAt:
          type: string
          format: date-time
          readOnly: true
        updatedAt:
          type: string
          format: date-time
          readOnly: true
      required:
#        - email
        - optInEmail

    MarketingSubscriptionAsWebhook:
      allOf:
        - type: object
          properties:
            email:
              type: string
              format: email
              example: <EMAIL>
            firstName:
              type: string
              example: John
            lastName:
              type: string
              example: Doe
          required:
            - email
        - $ref: '#/components/schemas/Localizable'
        - $ref: '#/components/schemas/MarketingSubscriptionEmbeddedInCustomerAsWebhook'
        - $ref: '#/components/schemas/Timestampable'

    MarketingSubscriptionEmbeddedInCustomerAsWebhook:
      type: object
      properties:
        marketingSubscriptionUuid:
          type: string
          description: The unique identifier to manage all marketing subscriptions of a user.
          format: uuid
        optInSms: # Not implementing this for now.
          type: string
          description: The SMS opt-in of the user. `Yes` when the user has a double opt-in SMS subscription.
          enum:
            - Yes
            - No
        optInSmsSubscription: # Not implementing this for now.
          $ref: '#/components/schemas/SmsSubscriptionAsWebhook'
        optInDirectMail:
          type: string
          enum:
            - Yes
            - No
        optInDirectMailSubscription: # Not implementing this for now.
          $ref: '#/components/schemas/DirectMailSubscriptionAsWebhook'
        optInEmail:
          type: string
          description: The email opt-in of the user. `Yes` when the user has a double opt-in email subscription.
          enum:
            - Yes
            - No
        optInEmailSubscription:
          $ref: '#/components/schemas/EmailSubscriptionAsWebhook'
        optInShareProductData:
          $ref: '#/components/schemas/EmailSubscriptionShareProductDataAsWebhook'
      required:
        - marketingSubscriptionUuid
        - optInDirectMail
        - optInEmail
        - optInEmailSubscription

    SmsSubscription:
      type: object
      properties:
        state:
          type: string
          enum:
            - single
            - double
            - none
        subscriptionTypes:
          type: object
          description: The types of marketing sms subscriptions a user can subscribe to.
          properties:
            service:
              type: boolean
          required:
            - service
      required:
        - state
        - subscriptionTypes

    SmsSubscriptionAsWebhook:
      type: object
      properties:
        state:
          type: string
          enum:
            - single
            - double
            - none
        subscriptionTypes:
          type: object
          description: The types of marketing sms subscriptions a user can subscribe to.
          properties:
            service:
              type: string
              enum:
                - Yes
                - No
          required:
            - service
      required:
        - state
        - subscriptionTypes

    DirectMailSubscription:
      type: object
      properties:
        state:
          type: string
          enum:
            - single
            - none
        subscriptionTypes:
          type: object
          description: The types of marketing direct mail subscriptions a user can subscribe to.
          properties:
            promotions:
              type: boolean
          required:
            - promotions
      required:
        - state
        - subscriptionTypes

    DirectMailSubscriptionAsWebhook:
      type: object
      properties:
        state:
          type: string
          enum:
            - single
            - none
        subscriptionTypes:
          type: object
          description: The types of marketing direct mail subscriptions a user can subscribe to.
          properties:
            promotions:
              type: string
              enum:
                - Yes
                - No
          required:
            - promotions
      required:
        - state
        - subscriptionTypes

    EmailSubscription:
      type: object
      properties:
        state:
          type: string
          enum:
            - single
            - double
            - none
          readOnly: true
        subscriptionTypes:
          $ref: '#/components/schemas/EmailSubscriptionTypes'
        subscriptionTimeout:
          type: string
          enum:
            - +1 month
            - +3 months
            - +6 months
            - ''
          writeOnly: true
        subscriptionTimeoutUntil:
          type: string
          description: The temporary unsubscription date-time after which a user wants to receive emails again.
          format: date-time
          readOnly: true
        subscribedAt:
          type: string
          description: The date-time a user subscribed for an email subscription with double opt-in.
          format: date-time
          readOnly: true
        unsubscribedAt:
          type: string
          description: The date-time a user unsubscribed from all email subscriptions.
          format: date-time
          readOnly: true
        source:
          type: string
          description: The source responsible for the e-mail opt-in. This can be any value.
          maxLength: 100
          example: website
          readOnly: true
      required:
#        - state
        - subscriptionTypes
#        - subscriptionTimeout
#        - subscriptionTimeoutUntil
#        - subscribedAt
#        - unsubscribedAt
#        - source

    BaseEmailSubscriptionShareProductData:
      type: object
      description: Base schema for product data sharing opt-in.
      properties:
        subscribedAt:
          type: string
          format: date-time
          description: The date-time a user subscribed for sharing product data with double opt-in.
          readOnly: true
        unsubscribedAt:
          type: string
          format: date-time
          description: The date-time a user unsubscribed from sharing product data.
          readOnly: true

    EmailSubscriptionShareProductData:
      allOf:
        - $ref: '#/components/schemas/BaseEmailSubscriptionShareProductData'
        - type: object
          description: The product data sharing opt-in of a user for marketing purposes.
          properties:
            subscribed:
              type: boolean
              description: Indicates if the user has opted in to share product data for marketing purposes.
          required:
            - subscribed

    EmailSubscriptionShareProductDataAsWebhook:
      allOf:
        - $ref: '#/components/schemas/BaseEmailSubscriptionShareProductData'
        - type: object
          description: The product data sharing opt-in of a user for marketing purposes as received via webhook.
          properties:
            subscribed:
              type: string
              enum:
                - Yes
                - No
              description: Indicates if the user has opted in to share product data for marketing purposes.
          required:
            - subscribed

    EmailSubscriptionTypes:
      type: object
      description: The types of marketing emails subscriptions a user can subscribe to.
      properties:
        service:
          type: boolean
        productInformation:
          type: boolean
        promotions:
          type: boolean
      required:
        - service
        - productInformation
        - promotions

    UpdateEmailSubscription:
      type: object
      properties:
        subscriptionTypes:
          $ref: '#/components/schemas/EmailSubscriptionTypes'
        subscribedAt:
          type: string
          description: The date-time a user subscribed for an email subscription with double opt-in.
          format: date-time
        unsubscribedAt:
          type: [string, 'null']
          description: The date-time a user unsubscribed from all email subscriptions.
          format: date-time
        source:
          type: string
          description: The source responsible for the e-mail opt-in. This can be any value.
          maxLength: 100
          example: website
      required:
        - subscriptionTypes
        - subscribedAt
        - unsubscribedAt
        - source

    EmailSubscriptionAsWebhook:
      type: object
      properties:
        state:
          type: string
          enum:
            - single
            - double
            - none
        subscriptionTypes:
          type: object
          description: The types of marketing emails subscriptions a user can subscribe to.
          properties:
            service:
              type: string
              enum:
                - Yes
                - No
            productInformation:
              type: string
              enum:
                - Yes
                - No
            promotions:
              type: string
              enum:
                - Yes
                - No
          required:
            - service
            - productInformation
            - promotions
        subscriptionTimeoutUntil:
          type:
            - string
            - 'null'
          description: The temporary unsubscription date-time after which a user wants to receive emails again.
          format: date-time
        subscribedAt:
          type:
            - string
            - 'null'
          description: The date-time a user subscribed for an email subscription with double opt-in.
          format: date-time
        unsubscribedAt:
          type:
            - string
            - 'null'
          description: The date-time a user unsubscribed from all email subscriptions.
          format: date-time
        source:
          type: string
          description: The source responsible for the e-mail opt-in. This can be any value.
          maxLength: 100
          example: website
      required:
        - state
        - subscriptionTypes
        - subscriptionTimeoutUntil
        - subscribedAt
        - unsubscribedAt
        - source

    ProblemDetails:
      type: object
      properties:
        type:
          type: string
          format: url
          default: 'about:blank'
          description: |
            A URI reference that identifies the problem type.
            It should point to human-readable documentation.
        title:
          type: string
          description: A short, human-readable summary of the problem type.
          example: An error occurred.
        status:
          type: integer
          format: int32
          description: The HTTP status code generated by the origin server for this occurrence of the problem.
          example: 400
        detail:
          type: string
          description: A human-readable explanation specific to this occurrence of the problem.
        instance:
          type: string
          format: uri
          description: A URI reference that identifies the specific occurrence of the problem.
      required:
        - type
        - title
        - status
      externalDocs:
        description: 'RFC 7807: Problem Details for HTTP APIs'
        url: 'https://datatracker.ietf.org/doc/html/rfc7807'

    InvalidContentTypeProblemDetails:
      allOf:
        - $ref: '#/components/schemas/ProblemDetails'
      example:
        type: 'about:blank'
        title: 'The content type is not supported.'
        status: 415
        detail: "The request content-type must be 'application/json'."

    InvalidRequestBodyProblemDetails:
      allOf:
        - $ref: '#/components/schemas/ProblemDetails'
        - type: object
          properties:
            violations:
              type: array
              items:
                $ref: '#/components/schemas/Violation'
          required:
            - violations
      example:
        type: 'about:blank'
        title: 'The request body contains errors.'
        status: 400
        detail: "The request body should be valid JSON."
        violations:
          - constraint: 'valid_json'
            message: "Parse error on line 1:\n\n^\nExpected one of: 'STRING', 'NUMBER', 'NULL', 'TRUE', 'FALSE', '{', '['"

    InvalidAddressRequestBodyProblemDetails:
      allOf:
        - $ref: '#/components/schemas/ProblemDetails'
        - type: object
          properties:
            violations:
              type: array
              items:
                $ref: '#/components/schemas/AddressViolation'
          required:
            - violations
      example:
        type: 'about:blank'
        title: 'The request body contains errors.'
        status: 400
        detail: "The request body should be valid JSON."
        violations:
          - constraint: 'valid_json'
            message: "Parse error on line 1:\n\n^\nExpected one of: 'STRING', 'NUMBER', 'NULL', 'TRUE', 'FALSE', '{', '['"

    Violation:
      type: object
      title: Violation
      properties:
        constraint:
          type: string
          description: The type of constraint that was violated.
        message:
          type: string
          description: A human-readable explanation of the violation and/or how to correct it.
        property:
          type: string
          description: The property of the request body that contains the specific occurrence of the violation.
      required:
        - constraint
        - message

    AddressViolation:
      allOf:
        - $ref: '#/components/schemas/Violation'
        - type: object
          title: AddressViolation
          properties:
            correction:
              type: string
              description: The corrected value for the property as suggested by the address validation.

    InvalidCartProblemDetails:
      allOf:
        - $ref: '#/components/schemas/ProblemDetails'
        - type: object
          properties:
            warnings:
              type: array
              items:
                $ref: '#/components/schemas/Warning'
          required:
            - warnings

    Warning:
      type: object
      properties:
        constraint:
          type: string
          description: The type of constraint that was violated.
        message:
          type: string
          description: A human-readable explanation of the warning and/or how to correct it.
        orderItem:
          type: integer
          format: int64
          description: The unique ID of the order item.

    NotifyConsultRequestUpdate:
      type: object
      properties:
        consultRequest:
          type: object
          properties:
            uuid:
              type: string
              format: uuid
              example: 'b802d2f3-ff07-4712-a7fb-a77d09fdbad9'
            state:
              type: string
              enum:
                - new
                - pending
                - pending_approved
                - approved
                - pending_declined
                - declined
                - cancelled
              example: 'approved'
            lastMessageInteractionState:
              type: string
              enum:
                - none
                - doctor
                - client
          required:
            - uuid
            - state
            - lastMessageInteractionState
        eventType:
          type: string
          example: 'consult_request.event.consult_request_was_assigned'
      required:
        - consultRequest
        - eventType

  responses:
    CartNotFoundError:
      description: The requested cart session is not found.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            message: Cart not found.

    InvalidRequestContentTypeProblemDetailsResponse:
      description: The request content-type is not supported.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/InvalidContentTypeProblemDetails'

    InvalidRequestProblemDetailsResponse:
      description: |
        The received request is invalid. This can be one of the following:
        * The request body contains invalid JSON syntax.
        * Validation errors based on the request body.
        * The provided channel does not exist.
        * The provided locale does not exist.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/InvalidRequestBodyProblemDetails'
          examples:
            invalidJsonInRequestBody:
              summary: An example error when the request body contains invalid JSON.
              value:
                type: 'about:blank'
                title: 'The request body contains errors.'
                status: 400
                detail: 'The request body should be valid JSON.'
                violations:
                  - constraint: 'valid_json'
                    message: "Parse error on line 1:\n\n^\nExpected one of: 'STRING', 'NUMBER', 'NULL', 'TRUE', 'FALSE', '{', '['"
            invalidRequestBody:
              summary: An example error when the request body contains errors.
              value:
                type: 'about:blank'
                title: 'The request body contains errors.'
                status: 400
                detail: 'Validation of JSON request body failed.'
                violations:
                  - constraint: 'required'
                    message: 'The property name is required'
                    property: 'name'
            providedChannelDoesNotExist:
              summary: An example error when a channel is not found.
              value:
                type: 'about:blank'
                title: 'The request body contains errors.'
                status: 400
                detail: "The channel with code 'dok_nl' does not exist."

    InvalidAddressRequestProblemDetailsResponse:
      description: |
        The received request is invalid. This can be one of the following:
        * The request body contains invalid JSON syntax.
        * Validation errors based on the request body.
        * The provided address is invalid.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/InvalidAddressRequestBodyProblemDetails'
          examples:
            invalidJsonInRequestBody:
              summary: An example error when the request body contains invalid JSON.
              value:
                type: 'about:blank'
                title: 'The request body contains errors.'
                status: 400
                detail: 'The request body should be valid JSON.'
                violations:
                  - constraint: 'valid_json'
                    message: "Parse error on line 1:\n\n^\nExpected one of: 'STRING', 'NUMBER', 'NULL', 'TRUE', 'FALSE', '{', '['"
            invalidRequestBody:
              summary: An example error when the request body contains errors.
              value:
                type: 'about:blank'
                title: 'The request body contains errors.'
                status: 400
                detail: 'Validation of JSON request body failed.'
                violations:
                  - constraint: 'required'
                    message: 'The property name is required'
                    property: 'name'
            shippingAddressCountryNotAvailable:
              summary: The provided shipping country is not allowed for the cart session within the current channel.
              value:
                type: 'about:blank'
                title: 'The request body contains errors.'
                status: 400
                detail: 'The provided shipping country is not allowed for the cart session within the current channel.'
                violations:
                  - constraint: 'allowed_country'
                    message: 'Shipping address country not available for this channel.'
                    property: 'shippingAddress.countryCode'
            addressInvalid:
              summary: An example error when the provided address is invalid and a correction was not available.
              value:
                type: 'about:blank'
                title: 'The request body contains errors.'
                status: 400
                detail: 'The provided address is invalid.'
                violations:
                  - constraint: 'valid_address'
                    message: 'The provided street is invalid.'
                    property: 'shippingAddress.street'
            addressInvalidButCorrected:
              summary: An example error when the provided address is invalid but corrected.
              value:
                type: 'about:blank'
                title: 'The request body contains errors.'
                status: 400
                detail: 'The provided address is invalid but corrected.'
                violations:
                  - constraint: 'valid_address'
                    message: 'The provided street is invalid but corrected.'
                    property: 'shippingAddress.street'
                    correction: 'Bijster 18'

    UnauthorizedProblemDetailsResponse:
      description: The authentication token is missing or invalid.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          example:
            type: 'about:blank'
            title: 'Unauthenticated.'
            status: 401
            detail: 'The authentication token is missing or invalid.'

    ForbiddenProblemDetailsResponse:
      description: You are not allowed to perform this action.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          example:
            type: 'about:blank'
            title: 'You are not allowed to perform this action.'
            status: 403

    CartNotFoundProblemDetailsResponse:
      description: The requested cart session could not be found.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          example:
            type: 'about:blank'
            title: 'An error occurred.'
            status: 404
            detail: 'The requested cart session could not be found.'

    OrderNotFoundProblemDetailsResponse:
      description: The requested order could not be found.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          example:
            type: 'about:blank'
            title: 'An error occurred.'
            status: 404
            detail: 'The requested order could not be found.'

    ResourceNotFoundProblemDetailsResponse:
      description: The requested resource could not be found.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          example:
            type: 'about:blank'
            title: 'An error occurred.'
            status: 404
            detail: 'The requested resource could not be found.'

    DefaultProblemDetailsResponse:
      description: An error occurred.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
          example:
            type: 'about:blank'
            title: 'An error occurred.'
            status: 500

    UnexpectedError:
      description: An unexpected error has occurred. Try again or contact us when the error persists.
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ProblemDetails'
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

  headers:
    Link:
      description: The header containing pagination for the endpoint based on the RFC-8288 Web linking specification.
      schema:
        type: string
        example: |
          <https://shop.dokteronline.com/api/shop/products?page=1>; rel="first",
          <https://shop.dokteronline.com/api/shop/products?page=1>; rel="prev",
          <https://shop.dokteronline.com/api/shop/products?page=3>; rel="next",
          <https://shop.dokteronline.com/api/shop/products?page=4>; rel="last"
        externalDocs:
          url: https://datatracker.ietf.org/doc/html/rfc8288

  securitySchemes:
    customerJsonWebToken:
      type: 'http'
      scheme: 'bearer'
      bearerFormat: 'JWT'
      description: 'The JWT returned from the customer authentication endpoint.'

    adminJsonWebToken:
      type: 'http'
      scheme: 'bearer'
      bearerFormat: 'JWT'
      description: 'The JWT returned from the authorization server.'

    oauthMachineToMachine:
      type: oauth2
      description: Authentication method for authenticating external systems.
      flows:
        clientCredentials:
          tokenUrl: https://auth.ehealthventuresgroup.com/oauth/token
          scopes:
            update:marketing_subscriptions: Update the marketing subscription of a customer.

tags:
  - name: Catalog
    description: All endpoints related to viewing the catalog of products and product variants.
  - name: 'Catalog: Feeds'
    description: |
      Catalog endpoints related to the providing data to external systems.
  - name: 'Catalog: Prepr CMS'
    description: Catalog integration endpoints for Prepr CMS.
  - name: 'Catalog (Dokteronline)'
    description: |
      Catalog endpoints created specifically for the Dokteronline platform.
      Must not be used for other platforms.
  - name: Channel
    description: All endpoints related to retrieving channel related data.
  - name: Cart
    description: All actions related to managing a cart.
  - name: Checkout
    description: All actions/steps to checkout a cart session.
  - name: Marketing subscriptions
    description: All actions related to managing the marketing subscriptions of a visitor or customer.
  - name: Account
    description: All actions related to the account of a customer.
  - name: 'Account: Address book'
    description: All actions related to the addresses of a customer.
  - name: 'Account: Orders'
    description: All actions related to the orders of a customer.
  - name: Admin
    description: Administrative endpoints to manage processing of the orders.
  - name: Notifications
    description: Endpoints to notify the shop of updates in linked external systems.

externalDocs:
  description: Read the documentation here
  url: https://docs.emedvertise.com
