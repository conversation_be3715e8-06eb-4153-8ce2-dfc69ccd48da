# @see https://github.com/Sylius/SyliusResourceBundle/blob/master/docs/index.md
sylius_resource:
    mapping:
        paths:
            - '%kernel.project_dir%/src/Entity'

    resources:
        app.gateway_config:
            classes:
                model: App\Entity\Payment\GatewayConfig
                repository: App\Repository\GatewayConfigRepository
        app.supplier:
            classes:
                model: App\Entity\Supplier\Supplier
                form: App\Admin\Form\Type\SupplierType
                repository: App\Repository\SupplierRepository
        app.refund_payment:
            classes:
                model: App\Entity\Refund\RefundPayment
                form: App\Admin\Form\Type\RefundPaymentType
        app.order_note:
            classes:
                model: App\Entity\Order\OrderNote
                repository: App\Repository\OrderNoteRepository
        app.fraud_check_order:
            classes:
                model: App\Entity\Order\Order
                repository: App\Repository\OrderRepository
        app.fraud_postcode:
            classes:
                model: App\Entity\Fraud\FraudPostcode
                form: App\Admin\Form\Type\Fraud\PostcodeConfigType
                repository: App\Repository\FraudPostcodeRepository
        app.fraud_product:
            classes:
                model: App\Entity\Fraud\FraudProduct
                form: App\Admin\Form\Type\Fraud\ProductConfigType
                repository: App\Repository\FraudProductRepository
        app.fraud_payment_method:
            classes:
                model: App\Entity\Fraud\FraudPaymentMethod
                form: App\Admin\Form\Type\Fraud\PaymentMethodConfigType
                repository: App\Repository\FraudPaymentMethodRepository
        app.business_unit:
            classes:
                model: App\Entity\BusinessUnit\BusinessUnit
                form: App\Admin\Form\Type\BusinessUnitType
                repository: App\Repository\BusinessUnitRepository
                controller: Sylius\Bundle\ResourceBundle\Controller\ResourceController
        app.customer_pool:
            classes:
                model: App\Entity\CustomerPool\CustomerPool
                form: App\Admin\Form\Type\CustomerPoolType
                repository: App\Repository\CustomerPoolRepository
                controller: Sylius\Bundle\ResourceBundle\Controller\ResourceController
